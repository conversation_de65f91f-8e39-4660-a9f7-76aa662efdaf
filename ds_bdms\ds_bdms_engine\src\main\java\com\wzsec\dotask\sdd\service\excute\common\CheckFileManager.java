package com.wzsec.dotask.sdd.service.excute.common;

import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.LzoDeflateFileRead;
import com.wzsec.utils.StringUtils;
import com.wzsec.utils.file.PDFUtil;
import com.wzsec.utils.file.TxtUtil;
import com.wzsec.utils.file.office.OfficeUtil;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.LineNumberReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件检测公共方法管理
 *
 * <AUTHOR>
 * @date 2020年4月26日
 */
public class CheckFileManager {

    /**
     * 读取文件返回拆分后的String集合
     *
     * @param file
     * <AUTHOR>
     * @date 2020年4月26日
     */
    public static List<String> getFileData(File file) throws Exception {
        List<String> documentList = new ArrayList<>();
        String fileStr = null;
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
            fileStr = OfficeUtil.getWordStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".ppt") || fileName.endsWith(".pptx")) {
            fileStr = OfficeUtil.getPPTStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".pdf")) {
            fileStr = PDFUtil.getPDFStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".txt")) {
            fileStr = TxtUtil.getTxtStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx")) {
            fileStr = StringUtils.join(OfficeUtil.getExcelStr(file.getAbsolutePath(), null), ',');
        } else if (fileName.endsWith(".csv")) {
            fileStr = TxtUtil.getTxtStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".lzo_deflate")) {
            fileStr = LzoDeflateFileRead.getDataStr(file.getAbsolutePath());
        } else {
            fileStr = TxtUtil.getTxtStr(file.getAbsolutePath());
//            return null;
        }
//
//        else {
//            return null;
//        }
        if (fileStr != null || fileStr.length() > 0) {
//            System.out.println("==================:" + fileStr);
//            documentList.addAll(Arrays.asList(fileStr.split("。|，|,| |；|;|：|\n|\t|-|=|:|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|@|#|￥|%|……|&|\\*|!|\\$|\\^")));
//            documentList.addAll(Arrays.asList(fileStr.split("。|，|,| |；|;|：|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^")));
//        TODO    documentList.addAll(Arrays.asList(fileStr.split("。|，|: |,|；|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|@|#|￥|……|&|\\*|!|\\$|\\^")));

            String[] split = fileStr.split("。|，|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");
            for (String s : split) {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(s)) {
                    documentList.add(s.trim());
                }
            }

        }
        return documentList;
    }

    /**
     * 读取文件返回拆分后的String集合
     *
     * @param file
     * <AUTHOR>
     * @date 2020年4月26日
     */
    public static List<String> getExtractFileData(File file, int fileLineNum) throws Exception {
        List<String> documentList = new ArrayList<>();
        String fileStr = null;
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
            fileStr = OfficeUtil.getWordStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".ppt") || fileName.endsWith(".pptx")) {
            fileStr = OfficeUtil.getPPTStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".pdf")) {
            fileStr = PDFUtil.getPDFStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".txt")) {
            fileLineNum = getFileLineNum(file.getAbsolutePath());
            if (fileLineNum > Const.SOURCE_FILE_LINENUM) {
                List<Integer> fileExtractLineNum = getFileExtractLineNum(fileLineNum);
                fileStr = TxtUtil.getTxtExtractStr(file.getAbsolutePath(), fileExtractLineNum);
            } else {
                fileStr = TxtUtil.getTxtStr(file.getAbsolutePath());
            }
        } else if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx")) {
            fileStr = StringUtils.join(OfficeUtil.getExcelStr(file.getAbsolutePath(), null), ',');
        } else if (fileName.endsWith(".csv")) {
            fileStr = TxtUtil.getTxtStr(file.getAbsolutePath());
        } else if (fileName.endsWith(".lzo_deflate")) {
            fileLineNum = getFileLineNum(file.getAbsolutePath());
            System.out.println("文件" + file.getName() + "行数：" + fileLineNum);
            if (fileLineNum > Const.SOURCE_FILE_LINENUM) {
                List<Integer> fileExtractLineNum = getFileExtractLineNum(fileLineNum);
                fileStr = LzoDeflateFileRead.getDataExtractStr(file.getAbsolutePath(), fileExtractLineNum);
            } else {
                fileStr = LzoDeflateFileRead.getDataStr(file.getAbsolutePath());
            }
        } else {
            fileLineNum = getFileLineNum(file.getAbsolutePath());
            if (fileLineNum > Const.SOURCE_FILE_LINENUM) {
                List<Integer> fileExtractLineNum = getFileExtractLineNum(fileLineNum);
                fileStr = TxtUtil.getTxtExtractStr(file.getAbsolutePath(), fileExtractLineNum);
            } else {
                fileStr = TxtUtil.getTxtStr(file.getAbsolutePath());
            }
//            return null;
        }
        if (fileStr != null || fileStr.length() > 0) {

//      documentList.addAll(Arrays.asList(fileStr.split("。|，|,| |；|;|：|\n|\t|-|=|:|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|@|#|￥|%|……|&|\\*|!|\\$|\\^")));
//      documentList.addAll(Arrays.asList(fileStr.split("。|，|,|；|：|:|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|@|#|￥|……|&|\\*|!|\\$|\\^")));

            String[] split = fileStr.split("。|，|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");
            for (String s : split) {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(s)) {
                    documentList.add(s.trim());
                }
            }
        }
        return documentList;
    }

    /**
     * @param ram:文件总行数
     * @Description:获取文件的行数
     * <AUTHOR>
     * @date 2020年10月27日
     */
    public static List<Integer> getFileExtractLineNum(int ram) {
        int[] sjs = new int[100];
        Random r = new Random();
        for (int i = 0; i < sjs.length; i++) {
            sjs[i] = r.nextInt(ram);
        }
        //Arrays.sort(sjs);
        List<Integer> list2 = Arrays.stream(sjs).boxed().collect(Collectors.toList());
        return list2;
    }

    /**
     * @param filePath:文件
     * @Description:获取文件的行数
     * <AUTHOR>
     * @date 2020年10月27日
     */
    public static int getFileLineNum(String filePath) {
        // long count = Files.lines(Paths.get(file.getAbsolutePath())).count();
        try (LineNumberReader lineNumberReader = new LineNumberReader(new FileReader(filePath))) {
            lineNumberReader.skip(Long.MAX_VALUE);
            int lineNumber = lineNumberReader.getLineNumber();
            return lineNumber + 1;//实际上是读取换行符数量 , 所以需要+1
        } catch (IOException e) {
            return -1;
        }
    }


    /**
     * @param ruleDtoList:规则集合
     * @param dataList:数据集合
     * @Description:执行自动识别敏感数据类型任务
     * <AUTHOR>
     * @date 2020年4月26日
     */
    public static Map<String, Map<String, String>> getFileDataTypeMap(List<RuleDto> ruleDtoList, List<String> dataList) {
        Map<String, Map<String, String>> fileDataTypeExampleMap = new HashMap<>();
        // 存储敏感数据和数量 <敏感数据类型,数量>
        Map<String, String> fileDataTypeMap = new HashMap<>();
        // 存储数据样例Map<敏感数据类型类型,样例>
        Map<String, String> dataExampleMap = new TreeMap<>();
        try {
            if (dataList != null && dataList.size() > 0) {
                for (String data : dataList) { // 遍历检测数据
                    //对拆分后的数据依次遍历
                    for (RuleDto ruleDto : ruleDtoList) {
                        boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
                        if (checkSensResult) {
                            String dataType = ruleDto.getApplytypecname();//记录敏感数据类型
                            if (fileDataTypeMap.containsKey(dataType)) {
                                fileDataTypeMap.put(dataType, String.valueOf(Integer.parseInt(fileDataTypeMap.get(dataType)) + 1));
                            } else {
                                fileDataTypeMap.put(dataType, String.valueOf(1));
                            }
                            if (!dataExampleMap.containsKey(dataType)) {
                                dataExampleMap.put(dataType, data);//记录敏感数据样例，没样数据只存一个样例
                            }
                            break;//已经发现该数据是敏感数据，停止改数据的检测
                        }
                    }
                }
            }
            fileDataTypeExampleMap.put("fileDataType", fileDataTypeMap);
            fileDataTypeExampleMap.put("dataExample", dataExampleMap);
        } catch (Exception e) {
            System.out.println("获取文件敏感数据类型出现错误");
            e.printStackTrace();
        }
        return fileDataTypeExampleMap;
    }

}
