package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskHbasetaskresult;
import com.wzsec.modules.mask.service.MaskHbasetaskresultService;
import com.wzsec.modules.mask.service.dto.MaskHbasetaskresultQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2024-10-14
*/
// @Api(tags = "hbase结果展示管理")
@RestController
@RequestMapping("/api/maskHbasetaskresult")
public class MaskHbasetaskresultController {

    private final MaskHbasetaskresultService maskHbasetaskresultService;

    public MaskHbasetaskresultController(MaskHbasetaskresultService maskHbasetaskresultService) {
        this.maskHbasetaskresultService = maskHbasetaskresultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskHbasetaskresult:list')")
    public void download(HttpServletResponse response, MaskHbasetaskresultQueryCriteria criteria) throws IOException {
        maskHbasetaskresultService.download(maskHbasetaskresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询hbase结果展示")
    // @ApiOperation("查询hbase结果展示")
    @PreAuthorize("@el.check('maskHbasetaskresult:list')")
    public ResponseEntity<Object> getMaskHbasetaskresults(MaskHbasetaskresultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskHbasetaskresultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增hbase结果展示")
    // @ApiOperation("新增hbase结果展示")
    @PreAuthorize("@el.check('maskHbasetaskresult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskHbasetaskresult resources){
        return new ResponseEntity<>(maskHbasetaskresultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改hbase结果展示")
    // @ApiOperation("修改hbase结果展示")
    @PreAuthorize("@el.check('maskHbasetaskresult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskHbasetaskresult resources){
        maskHbasetaskresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除hbase结果展示")
    // @ApiOperation("删除hbase结果展示")
    @PreAuthorize("@el.check('maskHbasetaskresult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskHbasetaskresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
