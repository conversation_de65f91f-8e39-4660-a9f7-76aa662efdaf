package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskAuditJarLogResult;
import com.wzsec.modules.mask.service.MaskAuditJarLogResultService;
import com.wzsec.modules.mask.service.dto.MaskAuditJarLogResultQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-03-18
*/
// @Api(tags = "脱敏Jar日志审计管理")
@RestController
@RequestMapping("/api/maskAuditJarLogResult")
public class MaskAuditJarLogResultController {

    private final MaskAuditJarLogResultService maskAuditJarLogResultService;

    public MaskAuditJarLogResultController(MaskAuditJarLogResultService maskAuditJarLogResultService) {
        this.maskAuditJarLogResultService = maskAuditJarLogResultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskAuditJarLogResult:list')")
    public void download(HttpServletResponse response, MaskAuditJarLogResultQueryCriteria criteria) throws IOException {
        maskAuditJarLogResultService.download(maskAuditJarLogResultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询脱敏Jar日志审计")
    // @ApiOperation("查询脱敏Jar日志审计")
    @PreAuthorize("@el.check('maskAuditJarLogResult:list')")
    public ResponseEntity<Object> getMaskAuditJarLogResults(MaskAuditJarLogResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskAuditJarLogResultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增脱敏Jar日志审计")
    // @ApiOperation("新增脱敏Jar日志审计")
    @PreAuthorize("@el.check('maskAuditJarLogResult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskAuditJarLogResult resources){
        return new ResponseEntity<>(maskAuditJarLogResultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改脱敏Jar日志审计")
    // @ApiOperation("修改脱敏Jar日志审计")
    @PreAuthorize("@el.check('maskAuditJarLogResult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskAuditJarLogResult resources){
        maskAuditJarLogResultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除脱敏Jar日志审计")
    // @ApiOperation("删除脱敏Jar日志审计")
    @PreAuthorize("@el.check('maskAuditJarLogResult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        maskAuditJarLogResultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
