package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Entity
@Data
@Table(name="sdd_mask_hbasetaskconfig")
public class MaskHbasetaskconfig implements Serializable {

    /** 主键ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 工单号 */
    @Column(name = "worksheet")
    private String worksheet;

    /** 批次号 */
    @Column(name = "batchnumber")
    private String batchnumber;

    /** 表空间 */
    @Column(name = "dbname")
    private String dbname;

    /** 表 */
    @Column(name = "tabname")
    private String tabname;

    /** 策略名 */
    @Column(name = "strategyname")
    private String strategyname;

    /** 队列 */
    @Column(name = "queuename")
    private String queuename;

    /** 行数 */
    @Column(name = "count")
    private String count;

    /** 输出类型(1到表，2到文件） */
    @Column(name = "outputtype")
    private String outputtype;

    /** 输出表名 */
    @Column(name = "outputtablename")
    private String outputtablename;

    /** 数据分隔符 */
    @Column(name = "datasplit")
    private String datasplit;

    /** 数据输出目录 */
    @Column(name = "dataoutputdir")
    private String dataoutputdir;

    /** 任务状态：(0初始创建,1执行中,2执行成功,3执行失败,4提交失败) */
    @Column(name = "status")
    private String status;

    /** 提交人 */
    @Column(name = "username")
    private String username;

    /** 字段脱敏信息 */
    @Column(name = "strategystr")
    private String strategystr;

    /** 创建人 */
    @Column(name = "createuserid")
    private Integer createuserid;

    /** 创建时间 */
    @Column(name = "createtime")
    private String createtime;

    /** 更新人 */
    @Column(name = "updateuserid")
    private Integer updateuserid;

    /** 更新时间 */
    @Column(name = "updatetime")
    private String updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskHbasetaskconfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
