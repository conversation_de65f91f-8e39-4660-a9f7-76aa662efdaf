package com.wzsec.modules.sdd.category.service.impl;

import com.wzsec.modules.sdd.category.domain.Category;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.category.repository.CategoryRepository;
import com.wzsec.modules.sdd.category.service.CategoryService;
import com.wzsec.modules.sdd.category.service.dto.CategoryDto;
import com.wzsec.modules.sdd.category.service.mapper.CategoryMapper;
import com.wzsec.utils.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
// 默认不使用缓存
//import org.springframework.cache.annotation.CacheConfig;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-06-04
 */
@Service
//@CacheConfig(cacheNames = "category")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class CategoryServiceImpl implements CategoryService {

    private final CategoryRepository categoryRepository;

    private final CategoryMapper categoryMapper;

    private final RuleService ruleService;

    public CategoryServiceImpl(CategoryRepository categoryRepository, CategoryMapper categoryMapper, RuleService ruleService) {
        this.categoryRepository = categoryRepository;
        this.categoryMapper = categoryMapper;
        this.ruleService = ruleService;
    }

    /**
     * 根据pid获取父级分类
     *
     * <AUTHOR> Kunxiang
     * @date 2020-06-04
     */
    private List<CategoryDto> getCategoryDtoListByCategoryList(List<Category> categoryList, List<Category> allCategoryList) {
        List<CategoryDto> categoryDtoList = new ArrayList<>();
        for (Category category : categoryList) {
            CategoryDto categoryDto = categoryMapper.toDto(category);//当前
            setPCategoryDtoAndRule(categoryDto, allCategoryList);//将获取类别对象后的大类对象set给子类
            categoryDtoList.add(categoryDto);
        }
        return categoryDtoList;
    }

    /**
     * 根据pid获取父级分类
     *
     * <AUTHOR> Kunxiang
     * @date 2020-06-04
     */
    private void setPCategoryDtoAndRule(CategoryDto categoryDto, List<Category> allCategoryList) {
        if (Const.CATEGORY_TYPE_1.equals(categoryDto.getType())) {
            CategoryDto pCategoryDto1 = getPCategoryDto(categoryDto.getId(), allCategoryList);
            categoryDto.setPcategory1(pCategoryDto1);//1.设置类别
        } else if (Const.CATEGORY_TYPE_2.equals(categoryDto.getType())) {
            categoryDto.setPcategory2(getPCategoryDto(categoryDto.getId(), allCategoryList));//1.设置大类
            CategoryDto pCategoryDto1 = getPCategoryDto(categoryDto.getPid(), allCategoryList);
            categoryDto.setPcategory1(pCategoryDto1);//2.设置类别
        } else if (Const.CATEGORY_TYPE_3.equals(categoryDto.getType())) {
            if (categoryDto.getRuleid() != null) {
                RuleDto ruleDto = ruleService.findById(categoryDto.getRuleid());
                categoryDto.setRule(ruleDto);//1.设置规则
            }
            categoryDto.setPcategory3(getPCategoryDto(categoryDto.getId(), allCategoryList));//2.设置子类
            CategoryDto pCategoryDto2 = getPCategoryDto(categoryDto.getPid(), allCategoryList);
            categoryDto.setPcategory2(pCategoryDto2);//3.设置大类
            CategoryDto pCategoryDto1 = getPCategoryDto(pCategoryDto2.getPid(), allCategoryList);
            categoryDto.setPcategory1(pCategoryDto1);//4.设置类别
        } else if (Const.CATEGORY_TYPE_4.equals(categoryDto.getType())) {
            if (categoryDto.getRuleid() != null) {
                RuleDto ruleDto = ruleService.findById(categoryDto.getRuleid());
                categoryDto.setRule(ruleDto);//1.设置规则
            }
            categoryDto.setPcategory4(getPCategoryDto(categoryDto.getId(), allCategoryList));
            CategoryDto pCategoryDto3 = getPCategoryDto(categoryDto.getPid(), allCategoryList);//2.设置子类
            categoryDto.setPcategory3(pCategoryDto3);//2.设置子类
            CategoryDto pCategoryDto2 = getPCategoryDto(pCategoryDto3.getPid(), allCategoryList);
            categoryDto.setPcategory2(pCategoryDto2);//3.设置大类
            CategoryDto pCategoryDto1 = getPCategoryDto(pCategoryDto2.getPid(), allCategoryList);
            categoryDto.setPcategory1(pCategoryDto1);//4.设置类别
        }
    }

    private CategoryDto getPCategoryDto(Long id, List<Category> allCategoryList) {
        if (id != null) {
            for (Category category : allCategoryList) {
                if (id.equals(category.getId()))
                    return categoryMapper.toDto(category);
            }
        }
        return null;
    }

    @Override
    //@Cacheable
    public List<CategoryDto> queryAll() {
        List<Category> categoryList = categoryRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, null, criteriaBuilder));
        List<Category> allCategoryList = categoryRepository.findAll();
        List<CategoryDto> categoryDtoList = getCategoryDtoListByCategoryList(categoryList, allCategoryList);
        return categoryDtoList;
//        return categoryMapper.toDto(categoryList);
    }

    @Override
//    @Cacheable(key = "'pid:'+#p0")
    public List<Category> findByPid(Long pid) {
        return categoryRepository.findByPid(pid);
    }

    @Override
//    @Cacheable(key = "'pid:'+#p0")
    public List<Category> findByType(String type) {
        return categoryRepository.findByType(type);
    }

    @Override
//    @Cacheable(key = "'tree'")
    public Object getParentCategorysByType(String type) {
        List<Map<String, Object>> list = new LinkedList<>();
        if (Const.CATEGORY_TYPE_1.equals(type)) {
            type = null;
        } else if (Const.CATEGORY_TYPE_2.equals(type)) {
            type = Const.CATEGORY_TYPE_1;
        } else if (Const.CATEGORY_TYPE_3.equals(type)) {
            type = Const.CATEGORY_TYPE_2;
        }
        categoryRepository.findByType(type).forEach(category -> {
                    if (category != null) {
                        Map<String, Object> map = new HashMap<>(16);
                        map.put("id", category.getId());
                        map.put("label", category.getCategory() + ":" + category.getCategoryname());
                        list.add(map);
                    }
                }
        );
        return list;
    }

    @Override
//    @Cacheable(key = "'tree'")
    public Object getCategorysJsonByType(String type) {
        List<Map<String, Object>> list = new LinkedList<>();
        categoryRepository.findByType(type).forEach(category -> {
                    if (category != null) {
                        Map<String, Object> map = new HashMap<>(16);
                        map.put("id", category.getId());
                        map.put("value", category.getCategory());
                        map.put("label", category.getCategory() + ":" + category.getCategoryname());
                        list.add(map);
                    }
                }
        );
        return list;
    }


    @Override
    //@Cacheable(key = "#p0")
    public CategoryDto findById(Long id) {
        Category category = categoryRepository.findById(id).orElseGet(Category::new);
        ValidationUtil.isNull(category.getId(), "Category", "id", id);
        return categoryMapper.toDto(category);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public CategoryDto create(Category resources) {
        resources.setCreateuser(SecurityUtils.getUsername());
        return categoryMapper.toDto(categoryRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(Category resources) {
        Category category = categoryRepository.findById(resources.getId()).orElseGet(Category::new);
        ValidationUtil.isNull(category.getId(), "Category", "id", resources.getId());
        category.copy(resources);
        category.setUpdateuser(SecurityUtils.getUsername());
        categoryRepository.save(category);
    }

}