package com.wzsec.modules.api.algo;

import java.util.Random;

/**
 * @description: 数字范围随机
 * @author: JOY
 * @date: 2022-03-04
 **/
public class NumRangeRandom {
    public static String encrypt(String strData) {
        String val = "";
        Random random = new Random();
        for (int i = 0; i < strData.length(); i++) {
            val += String.valueOf(random.nextInt(10));
        }
        return val;
    }

    public static void main(String[] args) {
        String strData = "1234567890";
        System.out.println(encrypt(strData));
    }

}



