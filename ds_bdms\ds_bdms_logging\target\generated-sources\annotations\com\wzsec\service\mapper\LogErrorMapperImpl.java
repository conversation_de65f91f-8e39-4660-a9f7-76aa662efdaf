package com.wzsec.service.mapper;

import com.wzsec.domain.Log;
import com.wzsec.service.dto.LogErrorDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:46:47+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class LogErrorMapperImpl implements LogErrorMapper {

    @Override
    public LogErrorDTO toDto(Log entity) {
        if ( entity == null ) {
            return null;
        }

        LogErrorDTO logErrorDTO = new LogErrorDTO();

        logErrorDTO.setAddress( entity.getAddress() );
        logErrorDTO.setAlarmInformation( entity.getAlarmInformation() );
        logErrorDTO.setAlarmThreshold( entity.getAlarmThreshold() );
        logErrorDTO.setBrowser( entity.getBrowser() );
        logErrorDTO.setCreateTime( entity.getCreateTime() );
        logErrorDTO.setDescription( entity.getDescription() );
        logErrorDTO.setId( entity.getId() );
        logErrorDTO.setMethod( entity.getMethod() );
        logErrorDTO.setParams( entity.getParams() );
        logErrorDTO.setRequestIp( entity.getRequestIp() );
        logErrorDTO.setSystematics( entity.getSystematics() );
        logErrorDTO.setUsername( entity.getUsername() );

        return logErrorDTO;
    }

    @Override
    public List<LogErrorDTO> toDto(List<Log> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LogErrorDTO> list = new ArrayList<LogErrorDTO>( entityList.size() );
        for ( Log log : entityList ) {
            list.add( toDto( log ) );
        }

        return list;
    }

    @Override
    public Log toEntity(LogErrorDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Log log = new Log();

        log.setAddress( dto.getAddress() );
        log.setAlarmInformation( dto.getAlarmInformation() );
        log.setAlarmThreshold( dto.getAlarmThreshold() );
        log.setBrowser( dto.getBrowser() );
        log.setCreateTime( dto.getCreateTime() );
        log.setDescription( dto.getDescription() );
        log.setId( dto.getId() );
        log.setMethod( dto.getMethod() );
        log.setParams( dto.getParams() );
        log.setRequestIp( dto.getRequestIp() );
        log.setSystematics( dto.getSystematics() );
        log.setUsername( dto.getUsername() );

        return log;
    }

    @Override
    public List<Log> toEntity(List<LogErrorDTO> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Log> list = new ArrayList<Log>( dtoList.size() );
        for ( LogErrorDTO logErrorDTO : dtoList ) {
            list.add( toEntity( logErrorDTO ) );
        }

        return list;
    }
}
