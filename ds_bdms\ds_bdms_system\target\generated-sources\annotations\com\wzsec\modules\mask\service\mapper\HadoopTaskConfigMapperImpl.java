package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HadoopTaskConfig;
import com.wzsec.modules.mask.service.dto.HadoopTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class HadoopTaskConfigMapperImpl implements HadoopTaskConfigMapper {

    @Override
    public HadoopTaskConfigDto toDto(HadoopTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        HadoopTaskConfigDto hadoopTaskConfigDto = new HadoopTaskConfigDto();

        hadoopTaskConfigDto.setCreatetime( entity.getCreatetime() );
        hadoopTaskConfigDto.setCreateuser( entity.getCreateuser() );
        hadoopTaskConfigDto.setDatainputdir( entity.getDatainputdir() );
        hadoopTaskConfigDto.setDataoutputdir( entity.getDataoutputdir() );
        hadoopTaskConfigDto.setDatasourcetype( entity.getDatasourcetype() );
        hadoopTaskConfigDto.setDatasplit( entity.getDatasplit() );
        hadoopTaskConfigDto.setFieldpostionalgoconfig( entity.getFieldpostionalgoconfig() );
        hadoopTaskConfigDto.setFileformat( entity.getFileformat() );
        hadoopTaskConfigDto.setId( entity.getId() );
        hadoopTaskConfigDto.setPlatform( entity.getPlatform() );
        hadoopTaskConfigDto.setQueuename( entity.getQueuename() );
        hadoopTaskConfigDto.setRemark( entity.getRemark() );
        hadoopTaskConfigDto.setSparefield1( entity.getSparefield1() );
        hadoopTaskConfigDto.setSparefield2( entity.getSparefield2() );
        hadoopTaskConfigDto.setSparefield3( entity.getSparefield3() );
        hadoopTaskConfigDto.setSparefield4( entity.getSparefield4() );
        hadoopTaskConfigDto.setStatus( entity.getStatus() );
        hadoopTaskConfigDto.setStrategyid( entity.getStrategyid() );
        hadoopTaskConfigDto.setTabdatabase( entity.getTabdatabase() );
        hadoopTaskConfigDto.setTabname( entity.getTabname() );
        hadoopTaskConfigDto.setTaskname( entity.getTaskname() );
        hadoopTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        hadoopTaskConfigDto.setUpdateuser( entity.getUpdateuser() );
        hadoopTaskConfigDto.setUsername( entity.getUsername() );

        return hadoopTaskConfigDto;
    }

    @Override
    public List<HadoopTaskConfigDto> toDto(List<HadoopTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HadoopTaskConfigDto> list = new ArrayList<HadoopTaskConfigDto>( entityList.size() );
        for ( HadoopTaskConfig hadoopTaskConfig : entityList ) {
            list.add( toDto( hadoopTaskConfig ) );
        }

        return list;
    }

    @Override
    public HadoopTaskConfig toEntity(HadoopTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        HadoopTaskConfig hadoopTaskConfig = new HadoopTaskConfig();

        hadoopTaskConfig.setCreatetime( dto.getCreatetime() );
        hadoopTaskConfig.setCreateuser( dto.getCreateuser() );
        hadoopTaskConfig.setDatainputdir( dto.getDatainputdir() );
        hadoopTaskConfig.setDataoutputdir( dto.getDataoutputdir() );
        hadoopTaskConfig.setDatasourcetype( dto.getDatasourcetype() );
        hadoopTaskConfig.setDatasplit( dto.getDatasplit() );
        hadoopTaskConfig.setFieldpostionalgoconfig( dto.getFieldpostionalgoconfig() );
        hadoopTaskConfig.setFileformat( dto.getFileformat() );
        hadoopTaskConfig.setId( dto.getId() );
        hadoopTaskConfig.setPlatform( dto.getPlatform() );
        hadoopTaskConfig.setQueuename( dto.getQueuename() );
        hadoopTaskConfig.setRemark( dto.getRemark() );
        hadoopTaskConfig.setSparefield1( dto.getSparefield1() );
        hadoopTaskConfig.setSparefield2( dto.getSparefield2() );
        hadoopTaskConfig.setSparefield3( dto.getSparefield3() );
        hadoopTaskConfig.setSparefield4( dto.getSparefield4() );
        hadoopTaskConfig.setStatus( dto.getStatus() );
        hadoopTaskConfig.setStrategyid( dto.getStrategyid() );
        hadoopTaskConfig.setTabdatabase( dto.getTabdatabase() );
        hadoopTaskConfig.setTabname( dto.getTabname() );
        hadoopTaskConfig.setTaskname( dto.getTaskname() );
        hadoopTaskConfig.setUpdatetime( dto.getUpdatetime() );
        hadoopTaskConfig.setUpdateuser( dto.getUpdateuser() );
        hadoopTaskConfig.setUsername( dto.getUsername() );

        return hadoopTaskConfig;
    }

    @Override
    public List<HadoopTaskConfig> toEntity(List<HadoopTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HadoopTaskConfig> list = new ArrayList<HadoopTaskConfig>( dtoList.size() );
        for ( HadoopTaskConfigDto hadoopTaskConfigDto : dtoList ) {
            list.add( toEntity( hadoopTaskConfigDto ) );
        }

        return list;
    }
}
