package com.wzsec.modules.api.algo;

import java.util.Random;

/*
 *@ClassName: NUMBERROUND
 *@Description: 小数随机取整、适用于金额、经纬度等数字脱敏
 *<AUTHOR>
 *@date 2020年11月29日
 */
public class NUMBERROUND {

    /**
     * 对小数随机取整（四舍五入、向上取整、向下取整)
     *
     * @param StrData
     * @return
     */
    public static String encrypt(String StrData) {
        Double data = Double.parseDouble(StrData);
        int randNum = new Random().nextInt(3); // 3种情况随机取整
        int result = 0;
        if (randNum == 0) {
            result = (int) Math.round(data); // 四舍五入
        } else if (randNum == 1) {
            result = (int) Math.ceil(data); // 向上取整
        } else {
            result = (int) Math.floor(data); // 向下取整
        }

        return String.valueOf(result);
    }

    public static void main(String[] args) {
        System.out.println(encrypt("123.46"));
        System.out.println(encrypt("101.56"));
        System.out.println(encrypt("0.56"));
        System.out.println(encrypt("460002449355933"));
    }

}
