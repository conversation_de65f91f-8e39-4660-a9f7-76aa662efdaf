package com.wzsec.modules.sdd.strategy.service.mapper;

import com.wzsec.modules.sdd.strategy.domain.Strategy;
import com.wzsec.modules.sdd.strategy.service.dto.StrategyDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class StrategyMapperImpl implements StrategyMapper {

    @Override
    public StrategyDto toDto(Strategy entity) {
        if ( entity == null ) {
            return null;
        }

        StrategyDto strategyDto = new StrategyDto();

        strategyDto.setCname( entity.getCname() );
        strategyDto.setCreatetime( entity.getCreatetime() );
        strategyDto.setCreateuser( entity.getCreateuser() );
        strategyDto.setDes( entity.getDes() );
        strategyDto.setEname( entity.getEname() );
        strategyDto.setId( entity.getId() );
        strategyDto.setNote( entity.getNote() );
        strategyDto.setRuleids( entity.getRuleids() );
        strategyDto.setSparefield1( entity.getSparefield1() );
        strategyDto.setSparefield2( entity.getSparefield2() );
        strategyDto.setSparefield3( entity.getSparefield3() );
        strategyDto.setSparefield4( entity.getSparefield4() );
        strategyDto.setSparefield5( entity.getSparefield5() );
        strategyDto.setState( entity.getState() );
        strategyDto.setUpdatetime( entity.getUpdatetime() );
        strategyDto.setUpdateuser( entity.getUpdateuser() );

        return strategyDto;
    }

    @Override
    public List<StrategyDto> toDto(List<Strategy> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<StrategyDto> list = new ArrayList<StrategyDto>( entityList.size() );
        for ( Strategy strategy : entityList ) {
            list.add( toDto( strategy ) );
        }

        return list;
    }

    @Override
    public Strategy toEntity(StrategyDto dto) {
        if ( dto == null ) {
            return null;
        }

        Strategy strategy = new Strategy();

        strategy.setCname( dto.getCname() );
        strategy.setCreatetime( dto.getCreatetime() );
        strategy.setCreateuser( dto.getCreateuser() );
        strategy.setDes( dto.getDes() );
        strategy.setEname( dto.getEname() );
        strategy.setId( dto.getId() );
        strategy.setNote( dto.getNote() );
        strategy.setRuleids( dto.getRuleids() );
        strategy.setSparefield1( dto.getSparefield1() );
        strategy.setSparefield2( dto.getSparefield2() );
        strategy.setSparefield3( dto.getSparefield3() );
        strategy.setSparefield4( dto.getSparefield4() );
        strategy.setSparefield5( dto.getSparefield5() );
        strategy.setState( dto.getState() );
        strategy.setUpdatetime( dto.getUpdatetime() );
        strategy.setUpdateuser( dto.getUpdateuser() );

        return strategy;
    }

    @Override
    public List<Strategy> toEntity(List<StrategyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Strategy> list = new ArrayList<Strategy>( dtoList.size() );
        for ( StrategyDto strategyDto : dtoList ) {
            list.add( toEntity( strategyDto ) );
        }

        return list;
    }
}
