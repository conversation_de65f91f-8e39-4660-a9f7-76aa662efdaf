package com.wzsec.dotask.mask.service.impl;

import com.wzsec.dotask.mask.service.HbaseTaskConfigService;
import com.wzsec.dotask.mask.service.excute.hbase.HBaseDataMask2HdfsMap;
import com.wzsec.dotask.mask.service.excute.hbase.HBaseDataMask2TableMap;
import com.wzsec.dotask.mask.service.excute.hbase.HBaseDataMask2TableReducer;
import com.wzsec.modules.mask.domain.*;
import com.wzsec.modules.mask.repository.MaskHbasetaskconfigRepository;
import com.wzsec.modules.mask.service.MaskHbasetaskconfigService;
import com.wzsec.modules.mask.service.MaskHbasetaskresultService;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.utils.*;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.filter.PageFilter;
import org.apache.hadoop.hbase.mapreduce.TableMapReduceUtil;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.*;

/**
 * <AUTHOR>
 * @discription
 * @date 2020/12/3
 */
@Service
public class DoHbaseTaskConfigServiceImpl implements HbaseTaskConfigService {

    private final static Logger log = LoggerFactory.getLogger(DoHbaseTaskConfigServiceImpl.class);

    private final MaskHbasetaskconfigService maskHbasetaskconfigService;
    private final MaskHbasetaskconfigRepository maskHbasetaskconfigRepository;
    private final MaskHbasetaskresultService maskHbasetaskresultService;
    private final DatasourceService dataSourceService;
    private final SdkApplyconfigRepository sdkApplyconfigRepository;
    private final SdkOperationrecordRepository sdkOperationrecordRepository;


    public DoHbaseTaskConfigServiceImpl(MaskHbasetaskconfigService maskHbasetaskconfigService, MaskHbasetaskconfigRepository maskHbasetaskconfigRepository, MaskHbasetaskresultService maskHbasetaskresultService, DatasourceService dataSourceService,SdkApplyconfigRepository sdkApplyconfigRepository,SdkOperationrecordRepository sdkOperationrecordRepository) {
        this.maskHbasetaskconfigService = maskHbasetaskconfigService;
        this.maskHbasetaskconfigRepository = maskHbasetaskconfigRepository;
        this.maskHbasetaskresultService = maskHbasetaskresultService;
        this.dataSourceService = dataSourceService;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }

    /**
     * @throws IOException
     * @Description:执行HBase脱敏任务(以mapreduce方式执行)
     * <AUTHOR>
     * @date 2020-01-18
     */
    @PostMapping(value = "/hbasemasktask")
    public void execution(Long id, String submituser, Long userid) {

        boolean isSuccess = true;
        log.info(Const.STRMASKLOGFLAG + "开始执行HBase脱敏任务,任务Id:" + id);
        log.info(String.format(Const.STRMASKLOGFLAG + "传递HBase参数信息:id:%s,userid:%s", id, userid));
        String username = "";
        String batchnumber = "";
        String dbname = "";
        String originaltablename = "";
        String strategyStr = "";
        String outputtype = "";
        String queuename = "";
        String datasplit = "";
        String count = "";
        String dataoutputpath = "";
        String outputtablename = "";
        String strategyname = "";
        String sourceId = "";
        String hbaseDirverName = "";
        String hbaseUrl = "";
        String status = null;
        String taskstatus = null;
        String startTime = null;
        String endTime = null;
        String totalTime = null;
        String updateTime = null;
        String datalinestr = null;
        String maskdatalinestr = null;
        Long resultid = null;

        MaskHbasetaskconfig hbaseTaskConfig = maskHbasetaskconfigRepository.findById(Math.toIntExact(id)).orElseGet(MaskHbasetaskconfig::new);
        String engine = hbaseTaskConfig.getSparefield1();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String eip = ipPort[0];
        String eport = ipPort[1];

        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(eip+":"+eport);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();
        try {
            startTime = StrUtils.DateToStr(new Date());
            log.info(Const.STRMASKLOGFLAG + String.format("HBase脱敏任务开始时间:%s", startTime));

            //job作业提交插入信息
            MaskHbasetaskresult hbaseTaskResult = new MaskHbasetaskresult();
            hbaseTaskResult.setBatchnumber(hbaseTaskConfig.getBatchnumber());
            hbaseTaskResult.setCount(hbaseTaskConfig.getCount());
            hbaseTaskResult.setCreatetime(startTime);
            hbaseTaskResult.setCreateuserid(Math.toIntExact(userid));
            hbaseTaskResult.setDataoutputdir(hbaseTaskConfig.getDataoutputdir());
            hbaseTaskResult.setDatasplit(hbaseTaskConfig.getDatasplit());
            hbaseTaskResult.setDbname(hbaseTaskConfig.getDbname());
            hbaseTaskResult.setOutputtablename(hbaseTaskConfig.getOutputtablename());
            hbaseTaskResult.setOutputtype(hbaseTaskConfig.getOutputtype());
            hbaseTaskResult.setRemark(hbaseTaskConfig.getRemark());
            hbaseTaskResult.setJobstatus(Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTING);
            hbaseTaskResult.setJobstarttime(startTime);
            hbaseTaskResult.setStrategyname(hbaseTaskConfig.getStrategyname());
            hbaseTaskResult.setStrategyname(hbaseTaskConfig.getStrategystr());
            hbaseTaskResult.setTabname(hbaseTaskConfig.getTabname());
            hbaseTaskResult.setWorksheet(hbaseTaskConfig.getWorksheet());
            maskHbasetaskresultService.create(hbaseTaskResult);
            log.info(Const.STRMASKLOGFLAG + "提交HBase脱敏任务,任务Id：" + id + ";作业信息已插入数据库,作业Id：" + resultid);

            batchnumber = hbaseTaskConfig.getBatchnumber();
            count = hbaseTaskConfig.getCount();
            dataoutputpath = hbaseTaskConfig.getDataoutputdir();
            datasplit = hbaseTaskConfig.getDatasplit();
            dbname = hbaseTaskConfig.getDbname();
            outputtablename = hbaseTaskConfig.getOutputtablename();
            queuename = hbaseTaskConfig.getQueuename();
            strategyname = hbaseTaskConfig.getStrategyname();
            outputtype = hbaseTaskConfig.getOutputtype();
            originaltablename = hbaseTaskConfig.getTabname();
            username = hbaseTaskConfig.getUsername();
            strategyStr = hbaseTaskConfig.getStrategystr();

            sourceId = hbaseTaskConfig.getSparefield1();
            DatasourceDto datasourceconfig = dataSourceService.findById(Long.valueOf(sourceId));
            hbaseDirverName = datasourceconfig.getDriverprogram();
            hbaseUrl = datasourceconfig.getSrcurl();

            if (Const.TASK_DATATYPE_FILE.equals(outputtype)) {
                if (dataoutputpath.endsWith(File.separator)) {
                    dataoutputpath = dataoutputpath + StrUtils.DateToNewStr(new Date());
                } else {
                    dataoutputpath = dataoutputpath + File.separator + StrUtils.DateToNewStr(new Date());
                }
            }

            log.info(Const.STRMASKLOGFLAG + "获取HBase脱敏任务相关参数 batchnumber:" + batchnumber + Const.BLANK + "count:"
                    + count + Const.BLANK + "dataoutputpath:" + dataoutputpath + Const.BLANK + "datasplit:" + datasplit
                    + Const.BLANK + "dbname:" + dbname + Const.BLANK + "outputtablename:" + outputtablename
                    + Const.BLANK + "queuename:" + queuename + Const.BLANK + "strategyname:" + strategyname
                    + Const.BLANK + "outputtype:" + outputtype + Const.BLANK + "originaltablename:" + originaltablename
                    + Const.BLANK + "username:" + username + Const.BLANK + "strategyStr:" + strategyStr);

            if (Const.TASK_DATATYPE_DB.equals(outputtype)) {
                // hbase table到table
                isSuccess = hbaseDataMask2TableToStart(username, batchnumber, originaltablename, strategyStr, queuename,
                        datasplit, count, outputtablename, hbaseDirverName, hbaseUrl);
                if (isSuccess) {
                    String NEW_LINE = System.getProperty("line.separator");
                    StringBuffer dataline = new StringBuffer();
                    StringBuffer maskdataline = new StringBuffer();
                    HashMap<String, String> maskfrontbackdataMap = HBaseDataMask2TableMap.getMaskfrontbackdataMap();
                    for (Map.Entry<String, String> entry : maskfrontbackdataMap.entrySet()) {
                        dataline.append(entry.getKey()).append(NEW_LINE);
                        maskdataline.append(entry.getValue()).append(NEW_LINE);
                    }
                    datalinestr = dataline.toString().substring(0, dataline.toString().length() - 1);
                    log.info(dataline.toString().substring(0, dataline.toString().length() - 1));
                    maskdatalinestr = maskdataline.toString().substring(0, maskdataline.toString().length() - 1);
                    log.info(maskdataline.toString().substring(0, maskdataline.toString().length() - 1));
                    //结果状态
                    status = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE;
                    //任务状态
                    taskstatus = Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_SUCCESS;
                }
            } else if (Const.TASK_DATATYPE_FILE.equals(outputtype)) {
                // hbase table到hdfs
                isSuccess = hbaseDataMask2HdfsToStart(username, batchnumber, originaltablename, strategyStr, queuename,
                        datasplit, count, dataoutputpath, hbaseDirverName, hbaseUrl);
                if (isSuccess) {
                    String NEW_LINE = System.getProperty("line.separator");
                    StringBuffer dataline = new StringBuffer();
                    StringBuffer maskdataline = new StringBuffer();
                    HashMap<String, String> maskfrontbackdataMap = HBaseDataMask2HdfsMap.getMaskfrontbackdataMap();
                    for (Map.Entry<String, String> entry : maskfrontbackdataMap.entrySet()) {
                        dataline.append(entry.getKey()).append(NEW_LINE);
                        maskdataline.append(entry.getValue()).append(NEW_LINE);
                    }
                    datalinestr = dataline.toString().substring(0, dataline.toString().length() - 1);
                    log.info(dataline.toString().substring(0, dataline.toString().length() - 1));
                    maskdatalinestr = maskdataline.toString().substring(0, maskdataline.toString().length() - 1);
                    log.info(maskdataline.toString().substring(0, maskdataline.toString().length() - 1));
                    //结果状态
                    status = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE;
                    //任务状态
                    taskstatus = Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_SUCCESS;
                }
            }
            log.info(Const.STRMASKLOGFLAG + "HBase脱敏提交是否成功:" + isSuccess);
            // System.out.println("HBase脱敏提交是否成功:" + isSuccess);
            if (!isSuccess) {
                //结果状态
                status = Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE;
                //任务状态
                taskstatus = Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_FAIL;

                sdkOperationrecord.setObjectname(hbaseTaskConfig.getBatchnumber());
                sdkOperationrecord.setOperation("Hbase脱敏任务执行失败");
            } else {
                //结果状态
                status = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE;
                //任务状态
                taskstatus = Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_SUCCESS;
                sdkOperationrecord.setObjectname(hbaseTaskConfig.getBatchnumber());
                sdkOperationrecord.setOperation("Hbase脱敏任务执行成功");
            }
            /*
             * //更新HBase任务结果表
             * isSuccess=hbaseTaskConfigService.updateHBaseTaskResult(status,
             * startTime, endTime, totalTime, updateTime, resultid);
             * //System.out.println("更新HBase任务结果表是否成功:" + isSuccess);
             * log.info(Const.STRMASKLOGFLAG+"更新HBase任务结果表是否成功:" + isSuccess);
             * //更新HBase任务配置表
             * isSuccess=hbaseTaskConfigService.updateHBaseTaskConfig(
             * taskstatus, id); //System.out.println("更新HBase任务配置表是否成功:" +
             * isSuccess);
             */
            log.info(Const.STRMASKLOGFLAG + "更新HBase任务配置表是否成功:" + isSuccess);
        } catch (Exception e) {
            //结果状态
            status = Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE;
            //任务状态
            taskstatus = Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_FAIL;
            sdkOperationrecord.setObjectname(hbaseTaskConfig.getBatchnumber());
            sdkOperationrecord.setOperation("Hbase脱敏任务执行失败");
            String loginfo = "执行HBase脱敏任务失败";
            log.error(Const.STRMASKLOGFLAG + loginfo + ",异常信息:" + e.getMessage());
//            errorLogService.saveErrorLog(DateUtil.getNowTime(), loginfo, "脱敏执行引擎", loginfo, "高", "信息");
            e.printStackTrace();
        } finally {
            //插入SDK操作记录
            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
            sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_HBASE);
            sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
            sdkOperationrecordRepository.save(sdkOperationrecord);

            // 更新结果信息
            endTime = StrUtils.DateToStr(new Date());
            // 执行总时间
            totalTime = String.valueOf(StrUtils.getTimeSecondsByBothDate(startTime, endTime));
            // 更新时间
            updateTime = StrUtils.DateToStr(new Date());
            // 更新HBase任务结果表
            maskHbasetaskconfigRepository.updateHBaseTaskResult(status, startTime, endTime, totalTime, updateTime,
                    datalinestr, maskdatalinestr, resultid);
            // System.out.println("更新HBase任务结果表是否成功:" + isSuccess);
            log.info(Const.STRMASKLOGFLAG + "更新HBase任务结果表是否成功");
            // 更新HBase任务配置表
            maskHbasetaskconfigRepository.updateHBaseTaskConfig(taskstatus, id);
            log.info(Const.STRMASKLOGFLAG + "更新HBase任务配置表是否成功");
            log.info(String.format(Const.STRMASKLOGFLAG + "HBase脱敏任务结束时间:%s,任务用时:%s秒", endTime, totalTime));
            log.info(Const.STRMASKLOGFLAG + "结束执行HBase脱敏任务,任务Id:" + id);
        }
    }

    /**
     * @throws IOException
     * @Description:执行HBase脱敏任务(输出hdfs方式)
     * <AUTHOR>
     * @date 2020-01-16
     */
    public static boolean hbaseDataMask2HdfsToStart(String username, String batchnumber, String originaltablename,
                                                    String maskingConfigInfoStr, String queuename, String splitStr, String count, String dataoutputpath,
                                                    String hbaseDirverName, String hbaseUrl)
            throws Exception {

        // System.out.println("开始-------------------");
        log.info(Const.STRMASKLOGFLAG + "开始执行HBase脱敏MapReduce任务,结果输出类型为:HDFS方式");

        Configuration config = HBaseConfiguration.create();

        config.set(hbaseDirverName, hbaseUrl);
        //config.set("hbase.zookeeper.quorum", ConfigurationManager.getProperty("hbase.zookeeper.quorum"));
        // config.set("hbase.zookeeper.quorum",
        // "**********:2181,**********:2181,**********:2181,*********0:2181,*********1:2181");

        config.set("splitStr", splitStr);

        config.set("maskingConfigInfoStr", maskingConfigInfoStr);

        if (!"".equals(queuename)) {
            config.set("mapred.job.queue.name", queuename);
        }

        Scan scan = new Scan();

        if (count != null && !"".equals(count)) {
            scan.setFilter(new PageFilter(Long.parseLong(count)));
        }

		/*if (count != null && !"".equals(count)) {
			scan.setFilter(new PageFilter(Long.parseLong(count)));
			scan.setMaxResultSize(Long.parseLong(count));
			config.set("count", count);
			log.info("设置脱敏行数："+count);
		}else{
			config.set("count", "0");
		}*/
        scan.setMaxVersions();
        scan.setCaching(500);
        scan.setCacheBlocks(false);

        Job job = Job.getInstance(config, "HBase2HdfsMr_" + batchnumber);
        job.setJarByClass(HBaseDataMask2HdfsMap.class);
        // scan.setFilter(new PageFilter(Long.parseLong(count)));
        TableMapReduceUtil.initTableMapperJob(originaltablename, scan, HBaseDataMask2HdfsMap.class, Text.class,
                Text.class, job);
        FileOutputFormat.setOutputPath(job, new Path(dataoutputpath));
        // FileOutputFormat.setOutputPath(job, new
        // Path("hdfs://*********1:9000/user/ubd_sec/result"));
        // FileOutputFormat.setOutputPath(job, new
        // Path("/tmp/mr/mySummaryFile"));
        boolean b = job.waitForCompletion(true);
        if (b) {
            log.info(Const.STRMASKLOGFLAG + "HBASE TO HDFS OK");
            // System.out.println("hbase to hdfs ok");
        }
        return b;
    }

    /**
     * @throws IOException
     * @Description:执行HBase脱敏任务(输出table方式)
     * <AUTHOR>
     * @date 2020-04-02
     */
    public static boolean hbaseDataMask2TableToStart(String username, String batchnumber, String originaltablename,
                                                     String maskingConfigInfoStr, String queuename, String splitStr, String count, String outputtablename,
                                                     String hbaseDirverName, String hbaseUrl)
            throws Exception {
        // System.out.println("开始-------------------");
        log.info(Const.STRMASKLOGFLAG + "开始执行HBase脱敏MapReduce任务,结果输出类型为:表方式");
        Configuration config = HBaseConfiguration.create();

        config.set(hbaseDirverName, hbaseUrl);
        //config.set("hbase.zookeeper.quorum", ConfigurationManager.getProperty("hbase.zookeeper.quorum"));
        // config.set("hbase.zookeeper.quorum",
        // "**********:2181,**********:2181,**********:2181,*********0:2181,*********1:2181");

        // config.set("splitStr", splitStr);

        config.set("maskingConfigInfoStr", maskingConfigInfoStr);

        if (!"".equals(queuename)) {
            config.set("mapred.job.queue.name", queuename);
        }

        ArrayList<String> columnClusterList = new ArrayList<String>();
        /*
         * columnClusterList.add("cf1"); columnClusterList.add("cf2");
         */
        // 创建表
        Connection connection = ConnectionFactory.createConnection(config);
        TableName tableName = TableName.valueOf(outputtablename);
        // 将表名传递给HTableDescriptor
        HTableDescriptor hTableDescriptor = new HTableDescriptor(tableName);
        String[] split = maskingConfigInfoStr.split(";");
        for (String s : split) {
            String columncluster = s.split("\\|")[0];
            if (!columnClusterList.contains(columncluster)) {
                columnClusterList.add(columncluster);
            }
        }
        /*
         * System.out.println("maskingConfigInfoStr:"+maskingConfigInfoStr);
         * System.out.println("columnClusterList: "+columnClusterList.size());
         */
        for (String columncluster : columnClusterList) {
            HColumnDescriptor mycg = new HColumnDescriptor(columncluster);
            // 将列族添加进表中
            hTableDescriptor.addFamily(mycg);
        }

        Admin admin = connection.getAdmin();
        admin.createTable(hTableDescriptor);

        log.info(Const.STRMASKLOGFLAG + "创建HBase表:" + tableName);

        Scan scan = new Scan();
        if (count != null && !"".equals(count)) {
            scan.setFilter(new PageFilter(Long.parseLong(count)));
        }

		/*if (count != null && !"".equals(count)) {
			scan.setFilter(new PageFilter(Long.parseLong(count)));
			scan.setMaxResultSize(Long.parseLong(count));
			config.set("count", count);
			log.info("设置脱敏行数："+count);
		}else{
			config.set("count", "0");
		}*/
        // scan.setMaxResultSize(Long.parseLong(count));
        scan.setMaxVersions();
        scan.setCaching(500);
        scan.setCacheBlocks(false);

        Job job = Job.getInstance(config, "HBase2HdfsMr_" + batchnumber);
        job.setJarByClass(HBaseDataMask2TableMap.class);
        // set input and set mapper
        TableMapReduceUtil.initTableMapperJob(originaltablename, // input table
                scan, // Scan instance to control CF and attribute selection
                HBaseDataMask2TableMap.class, // mapper class
                Text.class, // mapper output key
                Put.class, // mapper output value
                job);

        // set reducer and output
        TableMapReduceUtil.initTableReducerJob(outputtablename, // output table
                HBaseDataMask2TableReducer.class, // reducer class
                job);

        boolean b = job.waitForCompletion(true);
        if (b) {
            log.info(Const.STRMASKLOGFLAG + "HBASE TO TABLE OK");
            // System.out.println("hbase to table ok");
        }
        return b;
    }
}
