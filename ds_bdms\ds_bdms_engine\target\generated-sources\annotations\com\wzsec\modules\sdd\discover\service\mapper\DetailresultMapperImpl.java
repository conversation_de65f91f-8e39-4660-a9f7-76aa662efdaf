package com.wzsec.modules.sdd.discover.service.mapper;

import com.wzsec.modules.sdd.discover.domain.Detailresult;
import com.wzsec.modules.sdd.discover.service.dto.DetailresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DetailresultMapperImpl implements DetailresultMapper {

    @Override
    public DetailresultDto toDto(Detailresult entity) {
        if ( entity == null ) {
            return null;
        }

        DetailresultDto detailresultDto = new DetailresultDto();

        detailresultDto.setCreatetime( entity.getCreatetime() );
        if ( entity.getDatacount() != null ) {
            detailresultDto.setDatacount( String.valueOf( entity.getDatacount() ) );
        }
        detailresultDto.setDatatype( entity.getDatatype() );
        detailresultDto.setDborpath( entity.getDborpath() );
        detailresultDto.setExample( entity.getExample() );
        detailresultDto.setId( entity.getId() );
        detailresultDto.setSourcetype( entity.getSourcetype() );
        if ( entity.getSparefield1() != null ) {
            detailresultDto.setSparefield1( String.valueOf( entity.getSparefield1() ) );
        }
        detailresultDto.setSparefield2( entity.getSparefield2() );
        detailresultDto.setSparefield3( entity.getSparefield3() );
        detailresultDto.setSparefield4( entity.getSparefield4() );
        detailresultDto.setSparefield5( entity.getSparefield5() );
        detailresultDto.setTableorfile( entity.getTableorfile() );
        detailresultDto.setTaskname( entity.getTaskname() );

        return detailresultDto;
    }

    @Override
    public List<DetailresultDto> toDto(List<Detailresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DetailresultDto> list = new ArrayList<DetailresultDto>( entityList.size() );
        for ( Detailresult detailresult : entityList ) {
            list.add( toDto( detailresult ) );
        }

        return list;
    }

    @Override
    public Detailresult toEntity(DetailresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        Detailresult detailresult = new Detailresult();

        detailresult.setCreatetime( dto.getCreatetime() );
        if ( dto.getDatacount() != null ) {
            detailresult.setDatacount( Integer.parseInt( dto.getDatacount() ) );
        }
        detailresult.setDatatype( dto.getDatatype() );
        detailresult.setDborpath( dto.getDborpath() );
        detailresult.setExample( dto.getExample() );
        detailresult.setId( dto.getId() );
        detailresult.setSourcetype( dto.getSourcetype() );
        if ( dto.getSparefield1() != null ) {
            detailresult.setSparefield1( Integer.parseInt( dto.getSparefield1() ) );
        }
        detailresult.setSparefield2( dto.getSparefield2() );
        detailresult.setSparefield3( dto.getSparefield3() );
        detailresult.setSparefield4( dto.getSparefield4() );
        detailresult.setSparefield5( dto.getSparefield5() );
        detailresult.setTableorfile( dto.getTableorfile() );
        detailresult.setTaskname( dto.getTaskname() );

        return detailresult;
    }

    @Override
    public List<Detailresult> toEntity(List<DetailresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Detailresult> list = new ArrayList<Detailresult>( dtoList.size() );
        for ( DetailresultDto detailresultDto : dtoList ) {
            list.add( toEntity( detailresultDto ) );
        }

        return list;
    }
}
