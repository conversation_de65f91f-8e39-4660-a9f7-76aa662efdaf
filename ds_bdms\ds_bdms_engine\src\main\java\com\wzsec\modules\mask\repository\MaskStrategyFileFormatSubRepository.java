package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskStrategyFileFormatSub;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-06-20
 */
public interface MaskStrategyFileFormatSubRepository extends JpaRepository<MaskStrategyFileFormatSub, Integer>, JpaSpecificationExecutor<MaskStrategyFileFormatSub> {

    /**
     * 根据策略id查询字段英文名
     *
     * @param strategyid strategyid
     * @return {@link List}<{@link String}>
     */
    @Query(value = "select columndesc from sdd_mask_strategy_file_format_sub  where strategyid = ?1 ORDER BY columnnum ASC ", nativeQuery = true)
    List<String> queryFiles(Integer strategyid);

    /**
     * 通过id获取策略算法信息
     *
     * @param strategyId
     * @return
     */
    @Query(value = "select s.*,a.algorithmname from sdd_mask_strategy_file_format_sub s " +
            "LEFT JOIN sdd_algorithm a on s.algorithmid = a.id where strategyid = ?1 order by columnnum ", nativeQuery = true)
    List<Map<String, Object>> getAllStrategyAlgoInfoByStrategyId(Integer strategyId);


    /**
     * 根据策略id查询字段英文名
     *
     * @param strategyid strategyid
     * @return {@link List}<{@link String}>
     */
    @Query(value = "select * from sdd_mask_strategy_file_format_sub  where strategyid = ?1 ORDER BY columnnum ASC ", nativeQuery = true)
    List<MaskStrategyFileFormatSub> queryFormatFile(Integer strategyid);
}
