package com.wzsec.modules.basefield.service.mapper;

import com.wzsec.modules.basefield.domain.Basefield;
import com.wzsec.modules.basefield.service.dto.BasefieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:00+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class BasefieldMapperImpl implements BasefieldMapper {

    @Override
    public BasefieldDto toDto(Basefield entity) {
        if ( entity == null ) {
            return null;
        }

        BasefieldDto basefieldDto = new BasefieldDto();

        basefieldDto.setAlgorithmid( entity.getAlgorithmid() );
        basefieldDto.setCategory( entity.getCategory() );
        basefieldDto.setChineseotherexplain( entity.getChineseotherexplain() );
        basefieldDto.setEnglishotherexplain( entity.getEnglishotherexplain() );
        basefieldDto.setFieldcname( entity.getFieldcname() );
        basefieldDto.setFieldename( entity.getFieldename() );
        basefieldDto.setFieldid( entity.getFieldid() );
        basefieldDto.setId( entity.getId() );
        basefieldDto.setInserttime( entity.getInserttime() );
        basefieldDto.setIsusable( entity.getIsusable() );
        basefieldDto.setNameexplain( entity.getNameexplain() );
        basefieldDto.setSenLevel( entity.getSenLevel() );
        basefieldDto.setSource( entity.getSource() );
        basefieldDto.setSparefield1( entity.getSparefield1() );
        basefieldDto.setSparefield2( entity.getSparefield2() );
        basefieldDto.setSparefield3( entity.getSparefield3() );
        basefieldDto.setSparefield4( entity.getSparefield4() );
        basefieldDto.setTabcname( entity.getTabcname() );
        basefieldDto.setTabdbname( entity.getTabdbname() );
        basefieldDto.setTabename( entity.getTabename() );
        basefieldDto.setTabid( entity.getTabid() );

        return basefieldDto;
    }

    @Override
    public List<BasefieldDto> toDto(List<Basefield> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BasefieldDto> list = new ArrayList<BasefieldDto>( entityList.size() );
        for ( Basefield basefield : entityList ) {
            list.add( toDto( basefield ) );
        }

        return list;
    }

    @Override
    public Basefield toEntity(BasefieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        Basefield basefield = new Basefield();

        basefield.setAlgorithmid( dto.getAlgorithmid() );
        basefield.setCategory( dto.getCategory() );
        basefield.setChineseotherexplain( dto.getChineseotherexplain() );
        basefield.setEnglishotherexplain( dto.getEnglishotherexplain() );
        basefield.setFieldcname( dto.getFieldcname() );
        basefield.setFieldename( dto.getFieldename() );
        basefield.setFieldid( dto.getFieldid() );
        basefield.setId( dto.getId() );
        basefield.setInserttime( dto.getInserttime() );
        basefield.setIsusable( dto.getIsusable() );
        basefield.setNameexplain( dto.getNameexplain() );
        basefield.setSenLevel( dto.getSenLevel() );
        basefield.setSource( dto.getSource() );
        basefield.setSparefield1( dto.getSparefield1() );
        basefield.setSparefield2( dto.getSparefield2() );
        basefield.setSparefield3( dto.getSparefield3() );
        basefield.setSparefield4( dto.getSparefield4() );
        basefield.setTabcname( dto.getTabcname() );
        basefield.setTabdbname( dto.getTabdbname() );
        basefield.setTabename( dto.getTabename() );
        basefield.setTabid( dto.getTabid() );

        return basefield;
    }

    @Override
    public List<Basefield> toEntity(List<BasefieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Basefield> list = new ArrayList<Basefield>( dtoList.size() );
        for ( BasefieldDto basefieldDto : dtoList ) {
            list.add( toEntity( basefieldDto ) );
        }

        return list;
    }
}
