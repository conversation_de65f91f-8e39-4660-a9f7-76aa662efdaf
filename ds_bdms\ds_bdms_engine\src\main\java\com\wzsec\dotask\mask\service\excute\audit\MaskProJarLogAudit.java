package com.wzsec.dotask.mask.service.excute.audit;

import com.wzsec.modules.mask.domain.MaskAuditJarLogResult;
import com.wzsec.modules.mask.service.MaskAuditJarLogResultService;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1Dto;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.*;

/**
 * Title: MaskProJarLogAudit
 * Decription:
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Slf4j
public class MaskProJarLogAudit {

    private final DatasourceService datasourceService;
    private final MaskAuditJarLogResultService maskAuditJarLogResultService;

    public MaskProJarLogAudit(DatasourceService datasourceService, MaskAuditJarLogResultService maskAuditJarLogResultService) {
        this.datasourceService = datasourceService;
        this.maskAuditJarLogResultService = maskAuditJarLogResultService;
    }

    /**
     * 审计脱敏Jar日志
     *
     * @param maskAuditTaskDto
     * @param checkTime
     * @param submituser
     */
    public boolean maskAuditForJarLog(MaskAuditTaskV1Dto maskAuditTaskDto, String checkTime, String submituser) {
        log.info("开始执行脱敏Jar日志审计任务");
        boolean resultStatus = false;
        try {
            String intervalDay = ConstEngine.sddEngineConfs.get("maskaudit.jarlog.interval.day").trim();
            String checkDate = ConstEngine.sddEngineConfs.get("maskaudit.jarlog.date").trim();
            if (StringUtils.isEmpty(checkDate)) {
                checkDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", Integer.parseInt(intervalDay));
            }

            DatasourceDto datasource = datasourceService.findById(maskAuditTaskDto.getOutsourceid().longValue());
            String type = datasource.getType();
            if (Const.FILE_LOCAL.equals(type)) {
                // 本地磁盘检测
                List<String> filePathList = new ArrayList<>();
                FileUtil.getFilePathList(maskAuditTaskDto.getOutfilepath() + File.separator + checkDate, filePathList);
                for (String filePath : filePathList) {
                    // 检测日志
                    dealSingleFile(filePath,checkTime);
                }
                resultStatus = true;
            }

        } catch (Exception e) {
            resultStatus = false;
            e.printStackTrace();
        }
        return resultStatus;
    }


    /**
     * 处理清洗过后的单行数据
     *
     * @param line
     */
    private HashMap<String, String> dealCleanedSingleLine(String line, String checkTime, HashMap<String, String> taskInfoMap) throws Exception {
        if (line.contains(Const.MASKAUDIT_JARLOG_TASKFLAG) && line.contains("开始执行脱敏任务")) {
            if (taskInfoMap != null) {
                MaskAuditJarLogResult maskAuditJarLogResult = new MaskAuditJarLogResult();
                maskAuditJarLogResult.setTaskname(taskInfoMap.get("任务号"));
                maskAuditJarLogResult.setStrategy(taskInfoMap.get("脱敏策略"));
                maskAuditJarLogResult.setAlgorithmpackagename(taskInfoMap.get("调用脱敏算法包"));
                maskAuditJarLogResult.setAlgorithmname(taskInfoMap.get("算法"));
                maskAuditJarLogResult.setIpaddress(taskInfoMap.get("主机ip"));
                maskAuditJarLogResult.setAccount(taskInfoMap.get("账号"));
                maskAuditJarLogResult.setStarttime(taskInfoMap.get("开始时间"));
                maskAuditJarLogResult.setEndtime("");
                maskAuditJarLogResult.setLinenumber("0");
                maskAuditJarLogResult.setIssuccessful("false");
                maskAuditJarLogResult.setChecktime(checkTime);
                //System.out.println(maskAuditJarLogResult.toString());
                maskAuditJarLogResultService.create(maskAuditJarLogResult);
                taskInfoMap = null;
            }

            taskInfoMap = new HashMap<>();
            String str = line.substring(line.lastIndexOf("开始执行脱敏任务") + 9, line.length());
            String[] taskinfo = str.split(",");
            for (String s : taskinfo) {
                try {
                    String[] taskinfoarr = s.split(":");
                    String keystr = taskinfoarr[0];
                    String valuestr = "";
                    if (taskinfoarr.length == 2) {
                        valuestr = ("开始时间".equals(keystr)) ? TimeUtils.strToDateLong(taskinfoarr[1]) : taskinfoarr[1];
                    }
                    taskInfoMap.put(keystr, valuestr);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("错误数据：" + s);
                }
            }
        } else if (line.contains(Const.MASKAUDIT_JARLOG_TASKFLAG) && line.contains("结束执行脱敏任务") && taskInfoMap != null && line.contains(taskInfoMap.get("任务号"))) {
            MaskAuditJarLogResult maskAuditJarLogResult = new MaskAuditJarLogResult();
            String str = line.substring(line.lastIndexOf("结束执行脱敏任务") + 9, line.length());
            String[] taskinfo = str.split(",");
            for (String s : taskinfo) {
                String[] taskinfoarr = s.split(":");
                String keystr = taskinfoarr[0];
                String valuestr = "";
                if ("脱敏行数".equals(keystr)) {
                    valuestr = "0";
                }
                if (taskinfoarr.length == 2) {
                    valuestr = ("结束时间".equals(keystr)) ? TimeUtils.strToDateLong(taskinfoarr[1]) : taskinfoarr[1];
                }
                taskInfoMap.put(keystr, valuestr);
            }
            maskAuditJarLogResult.setTaskname(taskInfoMap.get("任务号"));
            maskAuditJarLogResult.setStrategy(taskInfoMap.get("脱敏策略"));
            maskAuditJarLogResult.setAlgorithmpackagename(taskInfoMap.get("调用脱敏算法包"));
            maskAuditJarLogResult.setAlgorithmname(taskInfoMap.get("算法"));
            maskAuditJarLogResult.setIpaddress(taskInfoMap.get("主机ip"));
            maskAuditJarLogResult.setAccount(taskInfoMap.get("账号"));
            maskAuditJarLogResult.setStarttime(taskInfoMap.get("开始时间"));
            maskAuditJarLogResult.setEndtime(taskInfoMap.get("结束时间"));
            maskAuditJarLogResult.setLinenumber(taskInfoMap.get("脱敏行数"));
            maskAuditJarLogResult.setIssuccessful(taskInfoMap.get("是否成功"));
            maskAuditJarLogResult.setChecktime(checkTime);
            //System.out.println(maskAuditJarLogResult.toString());
            maskAuditJarLogResultService.create(maskAuditJarLogResult);
            taskInfoMap = null;
        } else if (line.contains("调用脱敏算法包") && line.contains("算法")) {
            String str = line.substring(line.lastIndexOf("调用脱敏算法包"), line.length());
            String[] taskinfo = str.split(",");
            for (String s : taskinfo) {
                String[] taskinfoarr = s.split(":");
                String keystr = taskinfoarr[0];
                String valuestr = "";
                if (taskinfoarr.length == 2) {
                    valuestr = ("算法".equals(keystr)) ? taskinfoarr[1].substring(taskinfoarr[1].lastIndexOf(".") + 1) : taskinfoarr[1];
                }
                if (taskInfoMap != null) {
                    if (!taskInfoMap.containsKey(keystr)) {
                        taskInfoMap.put(keystr, valuestr);
                    } else {
                        String value = taskInfoMap.get(keystr);
                        if (!value.contains(valuestr)) {
                            taskInfoMap.put(keystr, value + "," + valuestr);
                        }
                    }
                }
            }
        }
        return taskInfoMap;
    }


    /**
     * @Description:处理单个文件
     * <AUTHOR> by wangqi
     * @date 2021-01-19
     */
    public void dealSingleFile(String strInput,String checkTime) {
        BufferedReader br = null;
        try {
            String f_name = "";
            File f_Source = new File(strInput);
            f_name = f_Source.getName();
            br = new BufferedReader(new FileReader(f_Source));

            String sline = null;
            long startTime = System.currentTimeMillis();
            HashMap<String, String> taskInfoMap = null;//暂存一下
            while ((sline = br.readLine()) != null) {
//                System.out.println("处理行：" + sline);
                taskInfoMap = dealCleanedSingleLine(sline, checkTime, taskInfoMap);
            }
            if (taskInfoMap == null) {
                MaskAuditJarLogResult maskAuditJarLogResult = new MaskAuditJarLogResult();
                maskAuditJarLogResult.setChecktime(checkTime);
                maskAuditJarLogResult.setNote("未检测到数据脱敏相关操作");
                maskAuditJarLogResultService.create(maskAuditJarLogResult);
            }
            long endTime = System.currentTimeMillis();
            long useTime = endTime - startTime;
            System.out.println("处理文件'" + f_name + "'用时:" + useTime + "ms");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                br.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}
