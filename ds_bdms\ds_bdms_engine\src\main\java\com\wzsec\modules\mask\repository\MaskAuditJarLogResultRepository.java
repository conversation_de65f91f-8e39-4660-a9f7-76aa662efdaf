package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskAuditJarLogResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
* <AUTHOR>
* @date 2021-03-18
*/
public interface MaskAuditJarLogResultRepository extends JpaRepository<MaskAuditJarLogResult, Long>, JpaSpecificationExecutor<MaskAuditJarLogResult> {
}