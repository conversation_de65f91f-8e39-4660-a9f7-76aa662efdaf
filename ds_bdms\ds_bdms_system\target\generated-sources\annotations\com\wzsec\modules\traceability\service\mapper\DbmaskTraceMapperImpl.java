package com.wzsec.modules.traceability.service.mapper;

import com.wzsec.modules.traceability.domain.DbMaskTrace;
import com.wzsec.modules.traceability.service.dto.DbMaskTraceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:20+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DbmaskTraceMapperImpl implements DbmaskTraceMapper {

    @Override
    public DbMaskTrace toEntity(DbMaskTraceDto dto) {
        if ( dto == null ) {
            return null;
        }

        DbMaskTrace dbMaskTrace = new DbMaskTrace();

        dbMaskTrace.setId( dto.getId() );
        dbMaskTrace.setSourceid( dto.getSourceid() );
        dbMaskTrace.setDbname( dto.getDbname() );
        dbMaskTrace.setTablename( dto.getTablename() );
        dbMaskTrace.setIssensitive( dto.getIssensitive() );
        dbMaskTrace.setIsleakage( dto.getIsleakage() );
        dbMaskTrace.setDataprovider( dto.getDataprovider() );
        dbMaskTrace.setDatause( dto.getDatause() );
        dbMaskTrace.setState( dto.getState() );
        dbMaskTrace.setOperationuser( dto.getOperationuser() );
        dbMaskTrace.setOperationtime( dto.getOperationtime() );
        dbMaskTrace.setSparefield1( dto.getSparefield1() );
        dbMaskTrace.setSparefield2( dto.getSparefield2() );
        dbMaskTrace.setSparefield3( dto.getSparefield3() );
        dbMaskTrace.setSparefield4( dto.getSparefield4() );
        dbMaskTrace.setSparefield5( dto.getSparefield5() );

        return dbMaskTrace;
    }

    @Override
    public DbMaskTraceDto toDto(DbMaskTrace entity) {
        if ( entity == null ) {
            return null;
        }

        DbMaskTraceDto dbMaskTraceDto = new DbMaskTraceDto();

        dbMaskTraceDto.setId( entity.getId() );
        dbMaskTraceDto.setSourceid( entity.getSourceid() );
        dbMaskTraceDto.setDbname( entity.getDbname() );
        dbMaskTraceDto.setTablename( entity.getTablename() );
        dbMaskTraceDto.setIssensitive( entity.getIssensitive() );
        dbMaskTraceDto.setIsleakage( entity.getIsleakage() );
        dbMaskTraceDto.setDataprovider( entity.getDataprovider() );
        dbMaskTraceDto.setDatause( entity.getDatause() );
        dbMaskTraceDto.setState( entity.getState() );
        dbMaskTraceDto.setOperationuser( entity.getOperationuser() );
        dbMaskTraceDto.setOperationtime( entity.getOperationtime() );
        dbMaskTraceDto.setSparefield1( entity.getSparefield1() );
        dbMaskTraceDto.setSparefield2( entity.getSparefield2() );
        dbMaskTraceDto.setSparefield3( entity.getSparefield3() );
        dbMaskTraceDto.setSparefield4( entity.getSparefield4() );
        dbMaskTraceDto.setSparefield5( entity.getSparefield5() );

        return dbMaskTraceDto;
    }

    @Override
    public List<DbMaskTrace> toEntity(List<DbMaskTraceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DbMaskTrace> list = new ArrayList<DbMaskTrace>( dtoList.size() );
        for ( DbMaskTraceDto dbMaskTraceDto : dtoList ) {
            list.add( toEntity( dbMaskTraceDto ) );
        }

        return list;
    }

    @Override
    public List<DbMaskTraceDto> toDto(List<DbMaskTrace> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DbMaskTraceDto> list = new ArrayList<DbMaskTraceDto>( entityList.size() );
        for ( DbMaskTrace dbMaskTrace : entityList ) {
            list.add( toDto( dbMaskTrace ) );
        }

        return list;
    }
}
