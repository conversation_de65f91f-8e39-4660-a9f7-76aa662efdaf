package com.wzsec.modules.traceability.service.mapper;

import com.wzsec.modules.traceability.domain.DbMaskTrace;
import com.wzsec.modules.traceability.service.dto.DbMaskTraceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DbmaskTraceMapperImpl implements DbmaskTraceMapper {

    @Override
    public DbMaskTraceDto toDto(DbMaskTrace entity) {
        if ( entity == null ) {
            return null;
        }

        DbMaskTraceDto dbMaskTraceDto = new DbMaskTraceDto();

        dbMaskTraceDto.setDataprovider( entity.getDataprovider() );
        dbMaskTraceDto.setDatause( entity.getDatause() );
        dbMaskTraceDto.setDbname( entity.getDbname() );
        dbMaskTraceDto.setId( entity.getId() );
        dbMaskTraceDto.setIsleakage( entity.getIsleakage() );
        dbMaskTraceDto.setIssensitive( entity.getIssensitive() );
        dbMaskTraceDto.setOperationtime( entity.getOperationtime() );
        dbMaskTraceDto.setOperationuser( entity.getOperationuser() );
        dbMaskTraceDto.setSourceid( entity.getSourceid() );
        dbMaskTraceDto.setSparefield1( entity.getSparefield1() );
        dbMaskTraceDto.setSparefield2( entity.getSparefield2() );
        dbMaskTraceDto.setSparefield3( entity.getSparefield3() );
        dbMaskTraceDto.setSparefield4( entity.getSparefield4() );
        dbMaskTraceDto.setSparefield5( entity.getSparefield5() );
        dbMaskTraceDto.setState( entity.getState() );
        dbMaskTraceDto.setTablename( entity.getTablename() );

        return dbMaskTraceDto;
    }

    @Override
    public List<DbMaskTraceDto> toDto(List<DbMaskTrace> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DbMaskTraceDto> list = new ArrayList<DbMaskTraceDto>( entityList.size() );
        for ( DbMaskTrace dbMaskTrace : entityList ) {
            list.add( toDto( dbMaskTrace ) );
        }

        return list;
    }

    @Override
    public DbMaskTrace toEntity(DbMaskTraceDto dto) {
        if ( dto == null ) {
            return null;
        }

        DbMaskTrace dbMaskTrace = new DbMaskTrace();

        dbMaskTrace.setDataprovider( dto.getDataprovider() );
        dbMaskTrace.setDatause( dto.getDatause() );
        dbMaskTrace.setDbname( dto.getDbname() );
        dbMaskTrace.setId( dto.getId() );
        dbMaskTrace.setIsleakage( dto.getIsleakage() );
        dbMaskTrace.setIssensitive( dto.getIssensitive() );
        dbMaskTrace.setOperationtime( dto.getOperationtime() );
        dbMaskTrace.setOperationuser( dto.getOperationuser() );
        dbMaskTrace.setSourceid( dto.getSourceid() );
        dbMaskTrace.setSparefield1( dto.getSparefield1() );
        dbMaskTrace.setSparefield2( dto.getSparefield2() );
        dbMaskTrace.setSparefield3( dto.getSparefield3() );
        dbMaskTrace.setSparefield4( dto.getSparefield4() );
        dbMaskTrace.setSparefield5( dto.getSparefield5() );
        dbMaskTrace.setState( dto.getState() );
        dbMaskTrace.setTablename( dto.getTablename() );

        return dbMaskTrace;
    }

    @Override
    public List<DbMaskTrace> toEntity(List<DbMaskTraceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DbMaskTrace> list = new ArrayList<DbMaskTrace>( dtoList.size() );
        for ( DbMaskTraceDto dbMaskTraceDto : dtoList ) {
            list.add( toEntity( dbMaskTraceDto ) );
        }

        return list;
    }
}
