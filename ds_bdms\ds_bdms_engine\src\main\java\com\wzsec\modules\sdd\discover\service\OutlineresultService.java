package com.wzsec.modules.sdd.discover.service;

import com.wzsec.modules.sdd.discover.domain.Outlineresult;
import com.wzsec.modules.sdd.discover.service.dto.OutlineresultDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-04-03
 */
public interface OutlineresultService {

    /**
     * 查询所有数据不分页
     *
     * @return List<OutlineresultDto>
     */
    List<OutlineresultDto> queryAll();

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return OutlineresultDto
     */
    OutlineresultDto findById(Long id);

    /**
     * 创建
     *
     * @param resources /
     * @return OutlineresultDto
     */
    OutlineresultDto create(Outlineresult resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(Outlineresult resources);

}