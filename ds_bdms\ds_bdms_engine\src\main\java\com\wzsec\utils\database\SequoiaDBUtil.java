package com.wzsec.utils.database;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.MongoClient;
import com.sequoiadb.base.CollectionSpace;
import com.sequoiadb.base.DBCollection;
import com.sequoiadb.base.DBCursor;
import com.sequoiadb.base.Sequoiadb;
import com.sequoiadb.datasource.SequoiadbDatasource;
import com.sequoiadb.exception.BaseException;
import com.wzsec.proxy.common.utils.StringUtil;
import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.DateUtil;
import com.wzsec.utils.rule.Rule4AlgorithmUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bson.BSONObject;
import org.bson.BasicBSONObject;
import org.bson.Document;
import org.jcodings.util.Hash;
import org.springframework.beans.factory.annotation.Autowired;

import java.awt.*;
import java.sql.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 巨杉数据库操作类
 */
@Slf4j
public class SequoiaDBUtil {

    private static String JDBC_DRIVER = "com.sequoiadb.jdbc.Driver";

    /**
     * 插入数据
     *
     * @param connString
     * @param username
     * @param password
     */
    private static void insertSequoiaDB(String connString, String username, String password) {
        try {
            Sequoiadb sdb = new Sequoiadb(connString, username, password);
            CollectionSpace cs = sdb.createCollectionSpace("cs");
            DBCollection cl = cs.createCollection("cs01");
            List<BSONObject> strings = new ArrayList<>();
            // 创建一个插入的 bson 对象
            for (int i = 0;i<30;i++){
                BSONObject obj = new BasicBSONObject();
                obj.put("name", "彭某"+i);
                obj.put("age", i);
                obj.put("phone", "17811111111");
                strings.add(obj);
            }
            cl.insert(strings);
        } catch (BaseException e) {
            System.out.println("Sequoiadb driver error, error description:" + e.getErrorType());
        }
    }



    /**
     * 删除数据
     * @param connString
     * @param username
     * @param password
     * @param dbname
     */
    private static void delMetadata(String connString, String username, String password, String dbname) {
        try {
            Sequoiadb sdb = new Sequoiadb(connString, username, password);
            CollectionSpace cs = sdb.getCollectionSpace(dbname);
            String name1 = cs.getName();
            System.out.println(name1);
            List<String> collectionNames = cs.getCollectionNames();
            for (String collectionName : collectionNames) {
                int row = collectionName.indexOf(".");
                String name = collectionName.substring(row+1,collectionName.length() );
                DBCollection cl = cs.getCollection(name);
                BSONObject matcher = new BasicBSONObject();
                cl.delete(matcher);
                System.out.println(collectionName+"-----------------------------------------------------");
            }
        } catch (BaseException e) {
            System.out.println("Sequoiadb driver error, error description:" + e.getErrorType());
            throw new RuntimeException("异常了");
        }
    }

    /**
     * 扫描元数据
     * @param connString
     * @param username
     * @param password
     * @param dbname
     */
    private static Map<String,String> scanMetadata(String connString, String username, String password, String dbname) {
        Map<String, String> map = new HashMap<>();
        try {
            Sequoiadb sdb = new Sequoiadb(connString, username, password);
            CollectionSpace cs = sdb.getCollectionSpace(dbname);
            List<String> collectionNames = cs.getCollectionNames();
            for (String collectionName : collectionNames) {
                System.out.println(collectionName+"==========================================");
                int row = collectionName.lastIndexOf(".");
                String name = collectionName.substring(row+1,collectionName.length() );
                DBCollection cl = cs.getCollection(name);
                BSONObject matcher = new BasicBSONObject();
                DBCursor cursor = cl.query(matcher, null, null, null);
                while (cursor.hasNext()){
                    BSONObject next = cursor.getNext();
                    System.out.println(next);
                }

            }
        } catch (BaseException e) {
            System.out.println("Sequoiadb driver error, error description:" + e.getErrorType());
            throw new RuntimeException("获取元数据失败");
        }
        return map;
    }

    /**
     * 查询数据
     *
     * @param connString
     * @param username
     * @param password
     */
    private static void querySequoiaDB(String connString, String username, String password) {
        try {
            // 连接到 SequoiaDB
            Sequoiadb sdb = new Sequoiadb(connString, username, password);
            // 获取所有集合空间的名称
            List<String> collectionSpaces = sdb.getCollectionSpaceNames();
            System.out.println(collectionSpaces);
            CollectionSpace cs = sdb.getCollectionSpace("cs");
            List<String> collectionNames = cs.getCollectionNames();
            String join = String.join(",", collectionNames);
            System.out.println(join);

            DBCollection cl = cs.getCollection("cs002");
            BSONObject modifier = new BasicBSONObject();
            DBCursor cursor = cl.query(modifier, null, null, null);
            List<String> fieldsList = new ArrayList<>();
            while (cursor.hasNext()){
                BSONObject next = cursor.getNext();
                Object id = next.get("name");
                System.out.println(id);
                Set<String> strings = next.keySet();
                for (String key : strings) {
                    if (ObjectUtil.isEmpty(fieldsList)){
                        fieldsList.add(key);
                    }else if (ObjectUtil.isNotEmpty(fieldsList) && !fieldsList.contains(key)){
                        fieldsList.add(key);
                    }
                }
            }

            // 关闭数据库连接
            sdb.disconnect();

        } catch (BaseException e) {
            System.out.println("Sequoiadb driver error, error description: " + e.getErrorType());
        }
    }



    /**
     * 测试连接
     *
     * @param connString
     * @param username
     * @param password
     * @return
     */
    public static void queryConnectionTest(String connString, String username, String password) {
        try {
            // 尝试连接到 SequoiaDB
            Sequoiadb sdb = new Sequoiadb(connString, username, password);
            System.out.println("Connected successfully!");

            // 获取所有集合空间的名称
            List<String> collectionSpaces = sdb.getCollectionSpaceNames();
            System.out.println(collectionSpaces);
            for (String collectionSpace : collectionSpaces) {
                CollectionSpace cs = sdb.getCollectionSpace(collectionSpace);
                String name = cs.getName();
                if (name.equals("cs")){
                    List<String> collectionNames = cs.getCollectionNames();
                    for (String collectionName : collectionNames) {
                        DBCollection cl = cs.getCollection(tableNameStr(collectionName));
                        DBCursor query = cl.query();
                        while (query.hasNext()){
                            BSONObject next = query.getNext();
                            System.out.println(next);
                        }
                    }
                }
            }
            sdb.disconnect();
        } catch (BaseException e) {
            System.out.println("Sequoiadb driver error, error description: " + e.getErrorType());
            throw new RuntimeException("Sequoiadb driver error, error description:"+e.getErrorType());
        }
    }

    /**
     * 获取连接
     * @param srcurl
     * @param username
     * @param password
     * @return
     */
    public static Sequoiadb queryConnect(String srcurl, String username, String password) {
        Sequoiadb sdb = null;
        try {
            // 尝试连接到 SequoiaDB
             sdb = new Sequoiadb(srcurl, username, password);
            System.out.println("Connected successfully!");
        } catch (BaseException e) {
           throw new RuntimeException("连接失败");
        }
        return sdb;
    }

    /**
     * 测试连接
     * @param srcurl
     * @param username
     * @param password
     * @return
     */
    public static Map<String, Object> getConnect(String srcurl, String username, String password) {
        Map<String, Object> objMap = new HashMap<>();
        objMap.put("code",200);
        objMap.put("msg","连接成功");
        try {
            // 尝试连接到 SequoiaDB
            Sequoiadb sdb = new Sequoiadb(srcurl, username, password);
            System.out.println("Connected successfully!");
            sdb.disconnect();
        } catch (BaseException e) {
            System.out.println("Sequoiadb driver error, error description: " + e.getErrorType());
            objMap.put("code",500);
            objMap.put("msg","连接失败");
        }
        return objMap;
    }

    /**
     * 获取数据库中所有的库名和表名
     * @return
     */
    public static Map<String, String> getAllDbAndTabMap(String dbnames,String username,String password,String srcurl) {
        Map<String, String> map = new HashMap<>();
        try {
            Sequoiadb sdb = new Sequoiadb(srcurl, username, password);
            String[] split = dbnames.split(",");
            for (String spl : split) {
                CollectionSpace cs = sdb.getCollectionSpace(spl);
                List<String> collectionNames = cs.getCollectionNames();
                String dbname = String.join(", ", collectionNames);
                map.put(spl,dbname);
            }
        } catch (BaseException e) {
            throw new RuntimeException("获取元数据失败");
        }
        return map;
    }


    /**
     * 获取数据库表中所有字段
     * @param dbname   库
     * @param tabname  表
     * @param username 用户名
     * @param password 密码
     * @param srcurl   地址
     * @return
     */
    public static List<String> getFieldNameList(String dbname, String tabname, String username, String password, String srcurl) {
        List<String> fieldsList = new ArrayList<>();
        try {
            Sequoiadb sdb = new Sequoiadb(srcurl, username, password);
            String[] split = dbname.split(",");
            for (String spl : split) {
                CollectionSpace cs = sdb.getCollectionSpace(spl);
                DBCollection cl = cs.getCollection(tableNameStr(tabname));
                BSONObject modifier = new BasicBSONObject();
                DBCursor cursor = cl.query(modifier, null, null, null);
                while (cursor.hasNext()){
                    BSONObject next = cursor.getNext();
                    Set<String> keys = next.keySet();
                    for (String key : keys) {
                        if (ObjectUtil.isEmpty(fieldsList)){
                            fieldsList.add(key);
                        }else if (ObjectUtil.isNotEmpty(fieldsList) && !fieldsList.contains(key)){
                            fieldsList.add(key);
                        }
                    }
                }
            }
        } catch (BaseException e) {
            throw new RuntimeException("获取数据库所有字段失败");
        }
        return fieldsList;
    }

    /**
     * 查询表数据
     * @param dbname
     * @param tableName
     * @param username
     * @param password
     * @param srcurl
     * @return
     */
    public static Map<String, String> getTableInfoBySchema(String dbname, String tableName, String username, String password, String srcurl) {
        Map<String, String> tableInfoMap = new HashMap<>();
        try {
            Sequoiadb sdb = new Sequoiadb(srcurl, username, password);
            String[] split = dbname.split(",");
            for (String spl : split) {
                //执行创建集合
                tableName = tableNameStr(tableName);
                tableInfoMap.put("tableName", tableName);
            }
        } catch (BaseException e) {
            throw new RuntimeException("获取数据库所有字段失败");
        }
        return tableInfoMap;
    }

    public static String tableNameStr(String tableName){
        if (tableName.contains(".")){
            int row = tableName.lastIndexOf(".");
            tableName = tableName.substring(row+1,tableName.length());
        }
        return tableName;
    }

    /**
     * 获取表字段信息
     * @param dbName        库名
     * @param tableName     表名
     * @param username      用户名
     * @param password      密码
     * @param srcurl        地址
     * @return
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, String username, String password, String srcurl) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        List<String> fieldsList = new ArrayList<>();
        try {
            Sequoiadb sdb = new Sequoiadb(srcurl, username, password);
            CollectionSpace cs = sdb.getCollectionSpace(dbName);
            DBCollection cl = cs.getCollection(tableNameStr(tableName));

            BSONObject modifier = new BasicBSONObject();
            DBCursor cursor = cl.query(modifier, null, null, null);
            while (cursor.hasNext()){
                BSONObject next = cursor.getNext();
                Set<String> strings = next.keySet();
                for (String key : strings) {
                    if (ObjectUtil.isEmpty(fieldsList)){
                        fieldsList.add(key);
                    }else if (ObjectUtil.isNotEmpty(fieldsList) && !fieldsList.contains(key)){
                        fieldsList.add(key);
                    }
                }
            }
            for (String field : fieldsList) {
                Map<String, String> fieldInfoMap = new HashMap<>();
                fieldInfoMap.put("fieldName",field);
                fieldInfoList.add(fieldInfoMap);
            }
        } catch (BaseException e) {
            throw new RuntimeException("获取数据库所有字段失败");
        }
        return fieldInfoList;
    }

    /**
     * 获取数据库表数据
     * @param dbname
     * @param tabname
     * @param lineNum
     * @param username
     * @param password
     * @param srcurl
     * @return
     */
    public static List<String[]> getTabDataList(String dbname, String tabname, Integer lineNum, String username, String password, String srcurl) {
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            Sequoiadb sdb = new Sequoiadb(srcurl, username, password);
            CollectionSpace cs = sdb.getCollectionSpace(dbname);
            DBCollection cl = cs.getCollection(tableNameStr(tabname));

            BSONObject modifier = new BasicBSONObject();

            DBCursor cursor = cl.query(modifier, null, null,null);

            while (cursor.hasNext()){
                BSONObject next = cursor.getNext();
                Set<String> keys = next.keySet();
                for (String key : keys) {
                    Object obj = next.get(key);
                    String str = obj.toString();
                    String[] split = str.split("\n");
                    tabDataList.add(split);
                }
            }
        } catch (BaseException e) {
            throw new RuntimeException("获取数据库所有字段失败");
        }
        return tabDataList;
    }

    public static int getTabDataCount(String dbname, String tabname, String username, String password, String srcurl) {
        int count = 0;
        try {
            Sequoiadb sdb = new Sequoiadb(srcurl, username, password);
            CollectionSpace cs = sdb.getCollectionSpace(dbname);
            DBCollection cl = cs.getCollection(tableNameStr(tabname));

            BSONObject modifier = new BasicBSONObject();
            DBCursor cursor = cl.query(modifier, null, null,null);
            while (cursor.hasNext()){
                BSONObject next = cursor.getNext();
                System.out.println(next);
                count++;
            }
        } catch (BaseException e) {
            throw new RuntimeException("获取数据库所有字段失败");
        }
        return count;
    }


    /**
     * 校验库、表是否存在 存在false 不存在true
     * @param srcurl    地址
     * @param username  用户名
     * @param password  密码
     * @param dbname    库
     * @param tabname   表
     * @return
     */
    public static boolean checkWhetherTheCollectionExists(String srcurl, String username, String password, String dbname, String tabname) {
        if (StringUtils.isEmpty(srcurl) || StringUtils.isEmpty(srcurl) || StringUtils.isEmpty(srcurl) || StringUtils.isEmpty(srcurl)){
            throw new RuntimeException("关键参数为空");
        }
        Sequoiadb sdb = null;
        try {
            sdb = new Sequoiadb(srcurl, username, password);
        } catch (BaseException e) {
            throw new RuntimeException("获取SequoiaDB数据库连接失败");
        }
        CollectionSpace cs = null;
        try {
            cs = sdb.getCollectionSpace(dbname);
        } catch (BaseException e) {
            log.info("数据库{}已存在",dbname);
        }
        DBCollection cl = null;
        try {
            cl = cs.getCollection(tableNameStr(tabname));
        } catch (Exception e) {
            return true;
        }
        return false;
    }

    public static BasicDBObject convertMapToBasicDBObject(Map<String, Object> map) {
        BasicDBObject basicDBObject = new BasicDBObject();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            basicDBObject.put(entry.getKey(), entry.getValue());
        }
        return basicDBObject;
    }

    public static Map<String, Object> convertBSONObjectToMap(DBObject bsonObject) {
        Map<String, Object> map = new HashMap<>();
        for (String key : bsonObject.keySet()) {
            map.put(key, bsonObject.get(key));
        }
        return map;
    }

    /**
    * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
    * @param: map   连接数据库需要参数
    * @return:
    * @author: penglei
    * @date: 2024/11/14 16:28
    */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();
        String srcurl = map.get("srcurl");
        String username = map.get("username");
        String password = map.get("password");
        // 如果密码经过加密，则进行解密
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败");
                return msgMap;
            }
        }
        String dbname = map.get("dbname");
        String srcip = map.get("srcip");
        String srcport = map.get("srcport");

        Sequoiadb sdb = null;
        try {
            String jdbcUrl = srcip+":"+srcport;
            // 加载驱动并连接数据库
            sdb = new Sequoiadb(jdbcUrl, username, password);
            // 检查数据库是否存在
            boolean dbExists = false;
            List<String> collectionSpaceNames = sdb.getCollectionSpaceNames();
            for (String collectionSpaceName : collectionSpaceNames) {
                if (collectionSpaceName.equalsIgnoreCase(dbname)){
                    dbExists = true;
                }
            }
            if (dbExists) {
                // 创建数据库
                sdb.createCollectionSpace(dbname);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE); // 假设有一个成功创建数据库的代码
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST); // 假设有一个表示数据库已存在的代码
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }
        }  catch (Exception e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "连接失败：" + e.getMessage());
        } finally {
            if (sdb != null){
                sdb.disconnect();
            }
        }
        return msgMap;
    }


    public static void main(String[] args) {
        try {
            Sequoiadb sdb = new Sequoiadb("192.168.40.133:11810", "sdbadmin", "sdbadmin");
            DBCursor cursor = sdb.listCollectionSpaces();
            while (cursor.hasNext()){
                BSONObject next = cursor.getNext();
                System.out.println(next);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        System.out.println("执行完成");
    }
}
