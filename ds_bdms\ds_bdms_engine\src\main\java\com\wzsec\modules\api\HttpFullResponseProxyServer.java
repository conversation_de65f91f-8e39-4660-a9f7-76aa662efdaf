package com.wzsec.modules.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.modules.api.intercept.HttpProxyIntercept;
import com.wzsec.modules.api.intercept.HttpProxyInterceptInitializer;
import com.wzsec.modules.api.intercept.HttpProxyInterceptPipeline;
import com.wzsec.modules.api.intercept.common.FullResponseIntercept;
import com.wzsec.modules.api.rule.ResponseDataHandler;
import com.wzsec.modules.api.server.HttpProxyServer;
import com.wzsec.modules.api.server.HttpProxyServerConfig;
import com.wzsec.modules.api.util.ProtoUtil;
import com.wzsec.modules.sdd.api.domain.ApiUrlrecord;
import com.wzsec.modules.sdd.api.service.ApiRuleService;
import com.wzsec.modules.sdd.api.service.ApiUrlmappingService;
import com.wzsec.modules.sdd.api.service.ApiUrlrecordService;
import com.wzsec.modules.sdd.api.service.dto.ApiRuleDto;
import com.wzsec.modules.sdd.api.service.dto.ApiRuleQueryCriteria;
import com.wzsec.modules.sdd.api.service.dto.ApiUrlmappingDto;
import com.wzsec.utils.ConfigurationManager;
import com.wzsec.utils.Const;
import com.wzsec.utils.SpringContextHolder;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.FullHttpResponse;
import io.netty.handler.codec.http.HttpRequest;
import io.netty.handler.codec.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.List;

/**
 * 使用的SSL自签名证书可通过CertUtil生成
 *
 * <AUTHOR> Kunxiang
 * @description：中间人HTTP代理(在普通模式的基础上增加的额外拦截器)
 * @date ：2021/6/17 14:34
 */
@Slf4j
public class HttpFullResponseProxyServer {

    //监听端口
    private final int port = ConfigurationManager.getInteger("api.sdd.listen.port");

    //SSL支持
    private final Boolean startSSL = ConfigurationManager.getBoolean("api.sdd.ssl.enable");

    /**
     * <AUTHOR> Kunxiang
     * @description：启动中间人HTTP代理
     * @date ：2021/6/17 14:34
     */
    public void start() {

        HttpProxyServerConfig config = new HttpProxyServerConfig();
        //开启代理HTTPS支持
        //不开启的话HTTPS不会被拦截，而是直接转发原始报文
        config.setHandleSsl(true);//（作者功能未实现，设置false后代理HTTPS会报错null(HttpProxyInitializer)并不是放行 张坤祥 2021年6月22日19:27:18 ）
        new HttpProxyServer()
                // TODO 开启使用HTTPS支持，开启后HTTP无法使用
                //.startSSL(startSSL)
                .serverConfig(config)
                .proxyInterceptInitializer(new HttpProxyInterceptInitializer() {

                    @Override
                    public void init(HttpProxyInterceptPipeline pipeline) {

                        // 此处不要提出HttpProxyInterceptInitializer外，否则并发时会覆盖

                        //请求时初始化，响应时读取
                        final ApiUrlmappingDto apiUrlmappingDto = new ApiUrlmappingDto();

                        final ApiUrlrecord apiUrlrecord = new ApiUrlrecord();

                        final Long startTime = System.currentTimeMillis();

                        //拦截器设置转发地址
                        pipeline.addLast(new HttpProxyIntercept() {
                            /**
                             * 拦截代理服务器到目标服务器的请求头
                             */
                            @Override
                            public void beforeRequest(Channel clientChannel, HttpRequest httpRequest,
                                                      HttpProxyInterceptPipeline pipeline) throws Exception {
                                String protocol = httpRequest.protocolVersion().protocolName().toLowerCase();
                                String proxyUri = httpRequest.uri();
                                int paramStartIndex = proxyUri.indexOf("?");
                                String proxyApi = paramStartIndex >= 0 ? proxyUri.substring(0, paramStartIndex) : proxyUri;
                                apiUrlrecord.setRequesttime(new Timestamp(startTime));
                                apiUrlrecord.setRequesprotocol(protocol);
                                apiUrlrecord.setRequestmethond(httpRequest.method().name());
                                apiUrlrecord.setRequestip(clientChannel.remoteAddress().toString().substring(1));
                                apiUrlrecord.setRequesturl(apiUrlrecord.getRequesprotocol().toLowerCase() + "://" + clientChannel.localAddress().toString().substring(1) + httpRequest.uri());
                                apiUrlrecord.setRequestparam(null);
                                log.info("访问URI：{},方法：{}", proxyUri, httpRequest.method().name());

                                ApiUrlmappingDto aumd = SpringContextHolder.getBean(ApiUrlmappingService.class).findByProxyapi(proxyApi);
                                if (aumd != null) {//判断是否有映射，用于获取真实URL
                                    //复制属性到全局常量中，请求时初始化，响应时不再去查数据库
                                    BeanUtil.copyProperties(aumd, apiUrlmappingDto, CopyOptions.create().setIgnoreNullValue(true));

                                    String serverHost = apiUrlmappingDto.getServerhost();
                                    Integer serverPort = Integer.parseInt(apiUrlmappingDto.getServerport());
                                    Boolean serverIsSSL = Const.HTTPS.equals(apiUrlmappingDto.getProtocol());//被代理接口是否是HTTPS
                                    String serverApi = apiUrlmappingDto.getServerapi();
                                    String serverUri = proxyUri.replace(proxyApi, serverApi);//将访问的URL中虚拟API替换成真实API
                                    log.info("转发协议：{}，IP：{}，PORT：{}，URI：{}", apiUrlmappingDto.getProtocol(), serverHost, serverPort, serverUri);

                                    //设置转发的地址
                                    pipeline.setRequestProto(new ProtoUtil.RequestProto(serverHost, serverPort, serverIsSSL));//serverIsSSL 被代理接口是否是HTTPS
                                    httpRequest.setUri(serverUri);

                                    apiUrlrecord.setServerhost(serverHost);
                                    apiUrlrecord.setServerport(serverPort.toString());
                                    apiUrlrecord.setServerapi(serverApi);
                                    apiUrlrecord.setServerurl(apiUrlmappingDto.getProtocol().toLowerCase() + "://" + serverHost + ":" + serverPort + serverUri);
                                }
                                pipeline.beforeRequest(clientChannel, httpRequest);
                            }
                        });

                        //完全内容拦截处理
                        pipeline.addLast(new FullResponseIntercept() {

                            /**
                             * 匹配到的响应会解码成FullResponse
                             */
                            @Override
                            public boolean match(HttpRequest httpRequest, HttpResponse
                                    httpResponse, HttpProxyInterceptPipeline pipeline) {

                                //匹配处理URL,如果返回false则不会走handleResponse，如果考虑不记录响应数据可以在这里保存访问记录。
//                                return HttpUtil.checkUrl(pipeline.getHttpRequest(), "^/getJsonData")
//                                        && isHtml(httpRequest, httpResponse);
                                return true;
                            }

                            /**
                             * 完整内容拦截处理
                             */
                            @Override
                            public void handleResponse(HttpRequest httpRequest, FullHttpResponse
                                    httpResponse, HttpProxyInterceptPipeline pipeline) {
                                //处理响应数据
                                ByteBuf buf = httpResponse.content();
                                byte[] req = new byte[buf.readableBytes()];
                                buf.getBytes(0, req);
                                String maskDataStr_Before = new String(req), maskDataStr_After = null;
                                log.info("改写前响应数据:" + maskDataStr_Before);
                                log.info("改写前字节长度：" + req.length);

                                if (apiUrlmappingDto.getId() != null) {//说明已经配置了URL映射
                                    if (req.length > 0) {//判断是否存在响应数据
                                        if (Const.API_ISMASK_YES.equals(apiUrlmappingDto.getIsmask())) {//判断是否需要脱敏
                                            // 需要脱敏再进行加载，敏感数据检测规则集合
                                            ApiRuleQueryCriteria criteria = new ApiRuleQueryCriteria();
                                            criteria.setStatus(Const.STATE_ON);
                                            List<ApiRuleDto> apiRuleDtoList = SpringContextHolder.getBean(ApiRuleService.class).queryAll(criteria);
                                            try {
                                                // JSON格式数据递归获取所有value对敏感数据脱敏，非JSON格式数据根据指定拆分符拆分对敏感数据脱敏
                                                maskDataStr_After = new ResponseDataHandler(apiRuleDtoList).analysis(maskDataStr_Before, apiUrlmappingDto.getSplit());
                                                req = maskDataStr_After.getBytes();
                                                log.info("改写后响应数据:" + maskDataStr_After);
                                                log.info("改写后字节长度：" + req.length);
                                                apiUrlrecord.setMaskstatus(Const.API_MASKSTATUS_SUCCESS);
                                            } catch (Exception e) {
                                                apiUrlrecord.setMaskstatus(Const.API_MASKSTATUS_FAIL);
                                                log.error("脱敏失败：{}", e.getMessage());
                                            }
                                            httpResponse.content().clear();
                                            httpResponse.content().writeBytes(req);
                                        }
                                    }
                                    apiUrlrecord.setIsmask(apiUrlmappingDto.getIsmask());//存在的接口根据URL配置获取是否脱敏状态
                                } else {
                                    //未配置URL映射的接口返回错误信息
                                    apiUrlrecord.setIsmask(Const.API_ISMASK_NO); //未脱敏
                                    JSONObject result = new JSONObject();
                                    result.put("timestamp", startTime);
                                    result.put("status", 401);
                                    result.put("error", "Unauthorized");
                                    result.put("message", "This path is not supported");
                                    result.put("path", httpRequest.uri().indexOf("?") > 0 ? httpRequest.uri().substring(0, httpRequest.uri().indexOf("?")) : httpRequest.uri());
                                    result.put("source", "API mask proxy engine");
                                    maskDataStr_Before = result.toJSONString();
                                    req = maskDataStr_Before.getBytes();
                                    httpResponse.content().clear();
                                    httpResponse.content().writeBytes(req);
                                }
                                if (maskDataStr_Before != null) {
                                    apiUrlrecord.setResponseparam(maskDataStr_Before);
                                    apiUrlrecord.setResponsedatalength(new Long(maskDataStr_Before.length()));
                                }
                                if (maskDataStr_After != null) {
                                    apiUrlrecord.setMaskparam(maskDataStr_After);
                                    apiUrlrecord.setMaskdatalength(new Long(maskDataStr_After.length()));
                                }
                                apiUrlrecord.setHandletime(System.currentTimeMillis() - startTime);
                                SpringContextHolder.getBean(ApiUrlrecordService.class).create(apiUrlrecord);
                            }
                        });
                    }
                })
                .start(port);

    }

}
