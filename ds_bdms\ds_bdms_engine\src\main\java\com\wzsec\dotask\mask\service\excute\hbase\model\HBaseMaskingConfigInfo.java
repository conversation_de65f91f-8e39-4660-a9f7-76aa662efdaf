package com.wzsec.dotask.mask.service.excute.hbase.model;

/**
 *@Description:hbase脱敏配置信息
 *<AUTHOR>
 *@date 2020-1-17
 */
public class HBaseMaskingConfigInfo {

	private String columnfamily;
	private String column;
	private String algoName;
	private String algoParameter;

	public String getColumnfamily() {
		return columnfamily;
	}

	public void setColumnfamily(String columnfamily) {
		this.columnfamily = columnfamily;
	}

	public String getColumn() {
		return column;
	}

	public void setColumn(String column) {
		this.column = column;
	}

	public String getAlgoName() {
		return algoName;
	}

	public void setAlgoName(String algoName) {
		this.algoName = algoName;
	}

	public String getAlgoParameter() {
		return algoParameter;
	}

	public void setAlgoParameter(String algoParameter) {
		this.algoParameter = algoParameter;
	}

	/**
	*创建一个新的实例 HBaseMaskingConfigInfo.
	*/
	public HBaseMaskingConfigInfo(String columnfamily, String column, String algoName, String algoParameter) {
		super();
		this.columnfamily = columnfamily;
		this.column = column;
		this.algoName = algoName;
		this.algoParameter = algoParameter;
	}

	@Override
	public String toString() {
		return "HBaseMaskingConfigInfo [columnfamily=" + columnfamily + ", column=" + column + ", algoName=" + algoName
				+ ", algoParameter=" + algoParameter + "]";
	}

}
