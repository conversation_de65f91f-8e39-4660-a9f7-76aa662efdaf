package com.wzsec.dotask.sdd.service.excute.db;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import com.wzsec.dotask.sdd.service.excute.common.CheckFileManager;
import com.wzsec.dotask.sdd.service.excute.common.RuleManager;
import com.wzsec.modules.sdd.basefield.domain.Basefield;
import com.wzsec.modules.sdd.category.service.dto.CategoryDto;
import com.wzsec.modules.sdd.discover.domain.Detailresult;
import com.wzsec.modules.sdd.discover.domain.Outlineresult;
import com.wzsec.modules.sdd.discover.service.DetailresultService;
import com.wzsec.modules.sdd.discover.service.OutlineresultService;
import com.wzsec.modules.sdd.metadata.domain.MetaField;
import com.wzsec.modules.sdd.metadata.domain.MetaTable;
import com.wzsec.modules.sdd.metadata.repository.MetaFieldRepository;
import com.wzsec.modules.sdd.metadata.service.MetaFieldService;
import com.wzsec.modules.sdd.metadata.service.MetaTableService;
import com.wzsec.modules.sdd.metadata.service.MetadataService;
import com.wzsec.modules.sdd.metadata.service.dto.MetaFieldDto;
import com.wzsec.modules.sdd.metadata.service.dto.MetaTableDto;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.statistics.domain.StatisticstaskDetail;
import com.wzsec.modules.statistics.domain.StatisticstaskOutline;
import com.wzsec.modules.statistics.domain.TasksynrecordDetail;
import com.wzsec.modules.statistics.domain.TasksynrecordOutline;
import com.wzsec.modules.statistics.repository.StatisticstaskDetailRepository;
import com.wzsec.modules.statistics.repository.TasksynrecordDetailRepository;
import com.wzsec.modules.statistics.service.StatisticstaskDetailService;
import com.wzsec.modules.statistics.service.StatisticstaskOutlineService;
import com.wzsec.modules.statistics.service.TasksynrecordDetailService;
import com.wzsec.modules.statistics.service.TasksynrecordOutlineService;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import com.wzsec.utils.TimeUtils;
import com.wzsec.utils.TimestampUtil;
import com.wzsec.utils.database.DatabaseUtil;
import com.wzsec.utils.database.HBaseUtil;
import com.wzsec.utils.database.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.ResultScanner;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.io.IOException;
import java.math.BigInteger;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;


/**
 * 数据库敏感数据发现
 *
 * <AUTHOR>
 * @date 2020-4-22
 */
@Slf4j
@Transactional
public class DBSensitiveDataDiscovery {


    private final OutlineresultService outlineresultService;

    private final DetailresultService detailresultService;

    private final MetadataService metadataService;

    private final MetaTableService metaTableService;

    private final MetaFieldService metaFieldService;

    private final TasksynrecordOutlineService tasksynrecordOutlineService;

    private final TasksynrecordDetailService tasksynrecordDetailService;

    private final TasksynrecordDetailRepository tasksynrecordDetailRepository;

    private final StatisticstaskOutlineService statisticstaskOutlineService;

    private final StatisticstaskDetailService statisticstaskDetailService;

    private final StatisticstaskDetailRepository statisticstaskDetailRepository;

    private final MetaFieldRepository metaFieldRepository;

    private final DictDetailService dictDetailService;

    public DBSensitiveDataDiscovery(OutlineresultService outlineresultService,
                                    DetailresultService detailresultService,
                                    MetadataService metadataService,
                                    MetaTableService metaTableService,
                                    MetaFieldService metaFieldService,
                                    TasksynrecordOutlineService tasksynrecordOutlineService,
                                    TasksynrecordDetailService tasksynrecordDetailService,
                                    StatisticstaskOutlineService statisticstaskOutlineService,
                                    StatisticstaskDetailService statisticstaskDetailService,
                                    MetaFieldRepository metaFieldRepository,
                                    TasksynrecordDetailRepository tasksynrecordDetailRepository,
                                    StatisticstaskDetailRepository statisticstaskDetailRepository,
                                    DictDetailService dictDetailService
    ) {
        this.outlineresultService = outlineresultService;
        this.detailresultService = detailresultService;
        this.metadataService = metadataService;
        this.metaTableService = metaTableService;
        this.metaFieldService = metaFieldService;
        this.tasksynrecordDetailService = tasksynrecordDetailService;
        this.tasksynrecordOutlineService = tasksynrecordOutlineService;
        this.statisticstaskOutlineService = statisticstaskOutlineService;
        this.statisticstaskDetailService = statisticstaskDetailService;
        this.metaFieldRepository = metaFieldRepository;
        this.tasksynrecordDetailRepository = tasksynrecordDetailRepository;
        this.statisticstaskDetailRepository = statisticstaskDetailRepository;
        this.dictDetailService = dictDetailService;
    }

    private int executionTime = 0;


    /**
     * @Description:自动识别敏感数据类型
     * <AUTHOR>
     * @date 2020-4-22
     */
    public boolean doDBSensitiveDataDiscovery(String taskname, Long dataSourceId, String submituser, String srcname, String type, String srcurl, String username,
                                              String password, String dbnames, List<RuleDto> ruleList, List<CategoryDto> categoryList,
                                              Map<String, Basefield> baseFieldAllMap, String tasknumber, String taskType, String ip, String port) {
        try {

            List<StatisticstaskDetail> statisticstaskDetailList = new ArrayList<>();
            List<TasksynrecordDetail> taskSynreCordDetailList = new ArrayList<>();

            int deletecount = 0;
            int insertcount = 0;
            int updatecount = 0;
            List<String> dataTypeList = new ArrayList<>();

            System.out.println("开始获取数据库连接时间：" + TimeUtils.getNowTime());
            log.info("开始获取数据库连接时间：" + TimeUtils.getNowTime());
            // 获取数据库连接
            Object conn = DatabaseUtil.getConn(type, srcurl, username, password, dbnames);

            System.out.println("开始获取所有库名表名时间：" + TimeUtils.getNowTime());
            log.info("开始获取获取所有库名表名时间：" + TimeUtils.getNowTime());
            // 获取所有库名表名
            Map<String, String> dbTabMap = DatabaseUtil.getAllDbTabMap(type, conn, dbnames, username, password, srcurl, ip, port);
            System.out.println("开始循环库检测时间：" + TimeUtils.getNowTime());
            log.info("开始循环库检测时间：" + TimeUtils.getNowTime());


            if (dbTabMap.isEmpty()) {
                //该库下所有表被清空,删除所有关联数据
                List<Long> tabIds = metaTableService.findIdBySourceId(dataSourceId);
                List<Map<String, Object>> fieldNameByMtid = metaFieldService.findTableFieldNameByMtid(tabIds);
                for (Map<String, Object> map : fieldNameByMtid) {
                    String tableName = map.get("fieldname").toString();
                    String tableCName = map.get("fieldcname") != null ? map.get("fieldcname").toString() : null;
                    String fieldName = map.get("tablename").toString();
                    String fieldCName = map.get("tablecname") != null ? map.get("tablecname").toString() : null;
                    TasksynrecordDetail tasksynrecordDetail = getTaskSynRecordDetail(taskname, dbnames, tableName, tableCName, fieldName
                            , fieldCName, Const.FIELD_DELETE_EXPLAIN, null, tasknumber, submituser, null, null);
                    taskSynreCordDetailList.add(tasksynrecordDetail);
                }
                deletecount += fieldNameByMtid.size();

                metaTableService.deleteAllById(tabIds);
                metaFieldService.deleteAllByTabIds(tabIds);
            } else {

                for (String dbname : dbTabMap.keySet()) {//循环库检测
                    String[] tabnameArray = null;
                    int sensitive = 0;
                    String tabnames = dbTabMap.get(dbname);
                    System.out.println("检测库名:" + dbname);

                    Outlineresult outlineresult = new Outlineresult();
                    long startTime = System.currentTimeMillis();

                    // TODO 敏感数据发现概要结果记录
                    if (Const.DATA_SCAN_TASK_ALL.equals(taskType) || Const.DATA_SCAN_TASK_SENDATA.equals(taskType)) {
                        outlineresult.setTaskname(taskname);
                        outlineresult.setSubmituser(submituser);
                        outlineresult.setStarttime(TimestampUtil.getNowTime());
                        outlineresult.setLocation(srcurl);
                        outlineresult.setSparefield1(dataSourceId);
                        if (type.equals("Hive") || type.equals("Kafka")) {
                            outlineresult.setDatasourcetype(Const.OUTLINERESULT_DATASOURCETYPE_BIGDATA);//大数据组件
                        } else {
                            outlineresult.setDatasourcetype(Const.OUTLINERESULT_DATASOURCETYPE_DB);//库表
                        }
                        outlineresultService.create(outlineresult);
                    }


                    // TODO 检测表信息
                    if (tabnames != null && !"".equals(tabnames)) {
                        tabnameArray = tabnames.split(",");
                        System.out.println("开始循环表检测时间：" + TimeUtils.getNowTime());
                        log.info("开始循环表检测时间：" + TimeUtils.getNowTime());

                        //TODO 更新表同步时间
                        List<Long> tabIdList = metaTableService.findIdBySourceId(dataSourceId);
                        if (tabIdList.size() > 0) {
                            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String format = sf.format(new Date());
                            Date updateDate = sf.parse(format);
                            metaTableService.updateTimeById(tabIdList, updateDate);
                        }


                        // TODO ----- 循环表前删除已不存在的表信息及对应的字段信息 -----
                        List<String> tableNameForSourceList = new ArrayList<>();
                        Collections.addAll(tableNameForSourceList, tabnameArray);

                        List<String> tableNameForDatabaseList = new ArrayList<>();
                        Map<String, Long> tableInfoMap = new HashMap<>();
                        List<MetaTable> bySourceId = metaTableService.findBySourceId(dataSourceId);
                        for (MetaTable metaTable : bySourceId) {
                            tableNameForDatabaseList.add(metaTable.getTablename());
                            tableInfoMap.put(metaTable.getTablename(), metaTable.getId());
                        }

                        List<String> deleteTableList = relativeComplementRespect(tableNameForDatabaseList, tableNameForSourceList);
                        if (deleteTableList.size() > 0) {
                            List<Long> delTabIdList = new ArrayList<>();
                            for (String tableName : deleteTableList) {
                                Long tabId = tableInfoMap.get(tableName);
                                delTabIdList.add(tabId);
                            }

                            List<Map<String, Object>> fieldNameByMtid = metaFieldService.findTableFieldNameByMtid(delTabIdList);
                            for (Map<String, Object> map : fieldNameByMtid) {
                                String tableName = map.get("fieldname").toString();
                                String tableCName = map.get("fieldcname") != null ? map.get("fieldcname").toString() : null;
                                String fieldName = map.get("tablename").toString();
                                String fieldCName = map.get("tablecname") != null ? map.get("tablecname").toString() : null;
                                TasksynrecordDetail tasksynrecordDetail = getTaskSynRecordDetail(taskname, dbnames, tableName, tableCName, fieldName
                                        , fieldCName, Const.FIELD_DELETE_EXPLAIN, null, tasknumber, submituser, null, null);
                                taskSynreCordDetailList.add(tasksynrecordDetail);
                            }
                            deletecount += fieldNameByMtid.size();

                            metaTableService.deleteAllById(delTabIdList);
                            metaFieldService.deleteAllByTabIds(delTabIdList);

                        }


                        for (int i = 0; i < tabnameArray.length; i++) {//循环表检测
                            long tableStartTime = System.currentTimeMillis();
                            String tablename = tabnameArray[i];
                            //其中某张表同步出现错误，不影响后续表继续同步
                            try {
                                System.out.println("开始获取表数据时间：" + TimeUtils.getNowTime());
                                log.info("开始获取表数据时间：" + TimeUtils.getNowTime());
                                List<String[]> tabDataList = DatabaseUtil.getTabDataList(type, conn, dbname, tablename, 10, password, username, srcurl, ip, port);
                                System.out.println("开始获取表字段时间：" + TimeUtils.getNowTime());
                                log.info("开始获取表字段时间：" + TimeUtils.getNowTime());
                                //扫描的字段信息 英文字段
                                List<String> tabFieldList = DatabaseUtil.getTabFieldList(type, conn, dbname, tablename, username, password, srcurl, ip, port);
                                if (ObjectUtil.isEmpty(tabFieldList)) {
                                    continue;
                                }
                                List<Map<String, String>> tableFieldInfoList = new ArrayList<>();
                                if (type.equals("DM")) {
                                    tableFieldInfoList = DatabaseUtil.getTableFieldInfoBySchema(type, dbname, tablename, conn, srcurl, tabFieldList, username, password, ip, port);
                                } else {
                                    tableFieldInfoList = DatabaseUtil.getTableFieldInfoBySchema(type, dbname, tablename, conn, srcurl, username, password, ip, port);
                                }
                                //中文字段
                                List<String> tabCFieldList = tableFieldInfoList.stream().map(t -> t.get("fieldCName")).collect(Collectors.toList());
                                // TODO 获取原有字段结构与数据库已有字段进行比对删除
                                try {
                                    String mtid = tableInfoMap.get(tablename).toString();
                                    List<MetaField> fieldNameByMtid = metaFieldService.findFieldNameByMtid(mtid);

                                    //扫描已存在表的源数据表ID
                                    Map<String, Long> filedInfoMap = new HashMap<>();
                                    List<String> fieldList = new ArrayList<>();
                                    for (MetaField metaField : fieldNameByMtid) {
                                        Long id = metaField.getId();
                                        String fieldname = metaField.getFieldname();
                                        filedInfoMap.put(fieldname, id);
                                        fieldList.add(fieldname);
                                    }

                                    List<String> delFieldList = relativeComplementRespect(fieldList, tabFieldList);
                                    if (delFieldList.size() > 0) {
                                        List<Long> delfieldIdList = new ArrayList<>();
                                        for (String fieldName : delFieldList) {
                                            Long id = filedInfoMap.get(fieldName);
                                            delfieldIdList.add(id);
                                        }
                                        metaFieldService.deleteAllById(delfieldIdList);
                                        deletecount += delFieldList.size();
                                    }

                                } catch (Exception e) {
                                    Console.log(tablename + "为新增表,走新增表逻辑");
                                }


                                Map<String, Object> fieldTypeMap = new HashMap<>();
                                Map<String, Object> dataExampleMap = new HashMap<>();
                                Map<String, Object> dataTypeRuleMap = new HashMap<>();

                                //TODO 敏感数据识别 ============================================== 开始
                                if (Const.DATA_SCAN_TASK_ALL.equals(taskType) || Const.DATA_SCAN_TASK_SENDATA.equals(taskType)) {
                                    sensitivityIdentification(dbname, tablename, type, taskname, srcurl, dataSourceId, sensitive,
                                            ruleList, tabFieldList, tabDataList, dataTypeList, conn, fieldTypeMap, dataExampleMap,
                                            dataTypeRuleMap, username, password, ip, port,tabCFieldList);
                                }

                                //TODO 敏感数据识别 ============================================== 结束


                                //TODO 元数据扫描 ==============================================  开始
                                if (Const.DATA_SCAN_TASK_ALL.equals(taskType) || Const.DATA_SCAN_TASK_METADATA.equals(taskType)) {
                                    Map<String, Object> dataMap = getSaveMeatadata(type, dbname, tablename, conn, taskname, srcname, dataSourceId,
                                            submituser, tabDataList, tabFieldList, fieldTypeMap, dataExampleMap, dataTypeRuleMap, baseFieldAllMap,
                                            srcurl, tasknumber, username, password, ip, port);
                                    if (ObjectUtil.isEmpty(dataMap)) {
                                        continue;
                                    }

                                    //统计查询指标项
                                    insertcount += Integer.valueOf(dataMap.get("insertcount").toString());
                                    updatecount += Integer.valueOf(dataMap.get("updatecount").toString());
                                    StatisticstaskDetail statisticstaskDetail = (StatisticstaskDetail) dataMap.get("statisticstaskDetail");
                                    List<TasksynrecordDetail> recordDetail = (List<TasksynrecordDetail>) dataMap.get("tasksynrecordDetailList");
                                    statisticstaskDetailList.add(statisticstaskDetail);
                                    taskSynreCordDetailList.addAll(recordDetail);
                                }
                                //TODO 元数据扫描 ============================================== 结束

                                long tableEndTime = System.currentTimeMillis();
                                long tableTotalTime = tableEndTime - tableStartTime;
                                log.info("表： " + tablename + " 用时：" + tableTotalTime);
                                System.out.println("表： " + tablename + " 用时：" + tableTotalTime);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                                log.info("库：" + dbname + "下表：" + tablename + "执行扫描任务出现异常，错误信息：" + ex.getMessage());
                                throw new RuntimeException("库：" + dbname + "下表：" + tablename + "执行扫描任务出现异常，错误信息：" + ex.getMessage());
                            }
                        }
                    }


                    // 用时计算
                    long endTime = System.currentTimeMillis();
                    String usetime = String.valueOf(Math.round(endTime - startTime) / 1000);
                    if (Const.DATA_SCAN_TASK_ALL.equals(taskType) || Const.DATA_SCAN_TASK_SENDATA.equals(taskType)) {
                        outlineresult.setEndtime(TimestampUtil.getNowTime());

                        outlineresult.setUsetime(usetime);
                        executionTime = Math.round(endTime - startTime) / 1000;
                        outlineresult.setTypenum(String.valueOf(dataTypeList.size()));
                        outlineresult.setSparefield2(String.valueOf(tabnameArray.length));
                        outlineresult.setSparefield3(String.valueOf(sensitive));
                        outlineresultService.update(outlineresult);
                    }


                    //TODO 元数据同步完毕后，异步保存统计查询所需的记录信息 异步保存记录
                    if (taskSynreCordDetailList.size() > 0) {
                        asyncSaveTaskSynRecordDetail(taskSynreCordDetailList);
                    }
                    if (statisticstaskDetailList.size() > 0) {
                        asyncSaveStatisticsTaskDetail(statisticstaskDetailList);
                    }


                    //TODO 元数据扫描记录
                    if (Const.DATA_SCAN_TASK_ALL.equals(taskType) || Const.DATA_SCAN_TASK_METADATA.equals(taskType)) {
                        String syn_outline = "新增" + insertcount + "条元数据,更新" + updatecount + "条元数据," +
                                "删除" + deletecount + "条元数据，用时:" + usetime + "秒";
                        //插入表 t_tasksynrecord_outline 记录元数据同步概要信息
                        TasksynrecordOutline tasksynrecordOutline = new TasksynrecordOutline();
                        tasksynrecordOutline.setTaskname(taskname);
                        tasksynrecordOutline.setTasknumber(tasknumber);
                        tasksynrecordOutline.setSourcetype(type);
                        tasksynrecordOutline.setSrcname(srcname);
                        tasksynrecordOutline.setDbname(dbname);
                        tasksynrecordOutline.setSynoutline(syn_outline);
                        tasksynrecordOutline.setCreateuser(submituser);
                        tasksynrecordOutline.setCreatetime(Timestamp.valueOf(DateUtil.now()));
                        tasksynrecordOutlineService.create(tasksynrecordOutline);

                        //插入表 sdd_statisticstask_outline 记录扫描元数据的信息
                        StatisticstaskOutline statisticstaskOutline = new StatisticstaskOutline();
                        statisticstaskOutline.setTaskname(taskname);
                        statisticstaskOutline.setSrcname(srcname);
                        statisticstaskOutline.setDbname(dbname);
                        statisticstaskOutline.setSparefield1(type);
                        statisticstaskOutline.setSparefield2(srcurl);
                        statisticstaskOutline.setTablecount(String.valueOf(tabnameArray.length));//扫描表的数量
                        statisticstaskOutline.setCreateuser(submituser);
                        statisticstaskOutline.setSparefield3(tasknumber);//任务批次号
                        statisticstaskOutline.setCreatetime(Timestamp.valueOf(DateUtil.now()));
                        statisticstaskOutlineService.create(statisticstaskOutline);
                    }
                }

            }

            if (conn instanceof Connection) {
                DatabaseUtil.closeCon(null, null, (Connection) conn);
            }
            return true;
        } catch (Exception e) {
            log.error("检测" + srcurl + "数据源出现异常！", e);
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 敏感数据扫描
     *
     * @param dbname
     * @param tablename
     * @param type
     * @param taskname
     * @param srcurl
     * @param dataSourceId
     * @param sensitive
     * @param ruleList
     * @param tabFieldList
     * @param tabDataList
     * @param dataTypeList
     * @param conn
     * @param fieldTypeMap
     * @param dataExampleMap
     * @param dataTypeRuleMap
     * @throws Exception
     */
    private void sensitivityIdentification(String dbname, String tablename, String type, String taskname, String srcurl,
                                           Long dataSourceId, int sensitive,
                                           List<RuleDto> ruleList, List<String> tabFieldList, List<String[]> tabDataList,
                                           List<String> dataTypeList, Object conn,
                                           Map<String, Object> fieldTypeMap, Map<String, Object> dataExampleMap, Map<String, Object> dataTypeRuleMap,
                                           String username, String password, String ip, String port,List<String> tabCFieldList
    ) throws Exception {
        Console.log("开始根据 {} 表字段数据规则检测时间：{}", tablename, TimeUtils.getNowTime());
        log.info("开始根据表 {} 字段数据规则检测时间：{}", tablename, TimeUtils.getNowTime());

        Map<String, Map<String, Object>> fieldTypedataExampleMap = getFieldTypeDataExampleMap(type, conn, dbname, tablename, ruleList, tabFieldList, tabDataList, username, password, srcurl, ip, port,tabCFieldList);
        try {
            fieldTypeMap = fieldTypedataExampleMap.get("fieldType");
            dataExampleMap = fieldTypedataExampleMap.get("dataExample");
            dataTypeRuleMap = fieldTypedataExampleMap.get("dataTypeRule");
        } catch (Exception e) {
            Console.log(e.getMessage());
        }
        Console.log("开始 {} 插入结果详情时间：", tablename, TimeUtils.getNowTime());
        log.info("开始 {} 插入结果详情时间：{}", tablename, TimeUtils.getNowTime());
        int count = DatabaseUtil.getTabDataCount(type, conn, dbname, tablename, username, password, srcurl, ip, port);

        //插入结果详情表
        if (fieldTypeMap.size() > 0) {
            sensitive++;
            //存在敏感数据
            for (String field : fieldTypeMap.keySet()) {
                Detailresult detailresult = new Detailresult();
                if (!dataTypeList.contains(fieldTypeMap.get(field))) {
                    dataTypeList.add((String) fieldTypeMap.get(field));
                }
                detailresult.setSparefield1(Integer.valueOf(String.valueOf(dataSourceId)));
                if (type.equals("Hive") || type.equals("Kafka")) {
                    detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_BIGDATA);//大数据组件
                } else {
                    detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_DB);//库表
                }
                detailresult.setTaskname(taskname);
                detailresult.setDborpath(srcurl);
                detailresult.setTableorfile(tablename);
                detailresult.setDatatype((String) fieldTypeMap.get(field));
                detailresult.setSparefield2(field);
                detailresult.setDatacount(count);
                String exampleInfo = (String) dataExampleMap.get(fieldTypeMap.get(field));
                if (exampleInfo.length() > 500) {
                    detailresult.setExample(exampleInfo.substring(0, 500));
                } else {
                    detailresult.setExample(exampleInfo);
                }
                try {
                    detailresultService.create(detailresult);//按照字段级别，存储发现的敏感数据结果
                } catch (Exception e) {
                    log.info("插入失败数据：" + detailresult.toString());
                    e.printStackTrace();
                }
            }
        } else {
            Detailresult detailresult = new Detailresult();
            detailresult.setSparefield1(Integer.valueOf(String.valueOf(dataSourceId)));
            if (type.equals("Hive") || type.equals("Kafka")) {
                detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_BIGDATA);//大数据组件
            } else {
                detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_DB);//库表
            }
            detailresult.setTaskname(taskname);
            detailresult.setDborpath(srcurl);
            detailresult.setTableorfile(tablename);
            //detailresult.setDatatype(typeStr);
            //detailresult.setSparefield2(field);
            //detailresult.setDatacount(String.valueOf(count));
            //detailresult.setExample(dataExampleMap.get(fieldTypeMap.get(field)));
            detailresultService.create(detailresult);//按照字段级别，存储发现的敏感数据结果
        }
    }

    /**
     * hbase敏感数据发现
     *
     * @param taskname        taskname
     * @param dataSourceId    数据源id
     * @param submituser      submituser
     * @param srcname         srcname
     * @param type            类型
     * @param srcurl          srcurl
     * @param username        用户名
     * @param password        密码
     * @param dbnames         dbname
     * @param ruleList        规则列表
     * @param categoryList    类别列表
     * @param baseFieldAllMap 基础字段映射
     * @return boolean
     */              //TODO >>>>>>>>>>>>>>>>>>>>>>>>>>>>> HBase >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.hbase namespace
    public boolean doHbaseSensitiveDataDiscovery(String taskname, Long dataSourceId, String submituser, String srcname,
                                                 String type, String srcurl, String username, String password, String dbnames,
                                                 List<RuleDto> ruleList, List<CategoryDto> categoryList,
                                                 Map<String, Basefield> baseFieldAllMap, String tasknumber,
                                                 String ip, String port) {

        try {
            List<String> dataTypeList = new ArrayList<>();

            //1.连接
            System.out.println("开始获取数据库连接时间：" + TimeUtils.getNowTime());
            log.info("开始获取数据库连接时间：" + TimeUtils.getNowTime());
            Object conn = DatabaseUtil.getConn(type, srcurl, username, password, dbnames);

            //2.获取hbase namespace及表名
            System.out.println("开始获取所有NAMESPACE 表名时间：" + TimeUtils.getNowTime());
            log.info("开始获取所有NAMESPACE 表名时间：" + TimeUtils.getNowTime());

            //3.获取 NAMESPACE 信息
            Map<String, String> dbTabMap = DatabaseUtil.getAllDbTabMap(type, conn, dbnames, username, password, srcurl, ip, port);
            System.out.println("开始循环NAMESPACE 检测时间：" + TimeUtils.getNowTime());
            log.info("开始循环NAMESPACE 检测时间：" + TimeUtils.getNowTime());

            //3.1 循环NAMESPACE检测
            for (String dbname : dbTabMap.keySet()) {
                String[] tabnameArray = null;
                int sensitive = 0;
                String tabnames = dbTabMap.get(dbname);
                System.out.println("检测NAMESPACE : " + dbname);
                Outlineresult outlineresult = new Outlineresult();
                long startTime = System.currentTimeMillis();

                //向数据库添加数据  >>>sdd_outlineresult<<< 敏感数据发现概要结果表
                outlineresult.setTaskname(taskname); //任务名
                outlineresult.setSubmituser(submituser); //提交人
                outlineresult.setStarttime(TimestampUtil.getNowTime());//开始时间
                outlineresult.setLocation(srcurl);//url
                outlineresult.setSparefield1(dataSourceId);//数据源id
                outlineresult.setDatasourcetype(Const.OUTLINERESULT_DATASOURCETYPE_BIGDATA);//大数据组件
                outlineresultService.create(outlineresult);


                if (tabnames != null && !"".equals(tabnames)) {
                    tabnameArray = tabnames.split(",");
                    System.out.println("开始循环表检测时间：" + TimeUtils.getNowTime());
                    log.info("开始循环表检测时间：" + TimeUtils.getNowTime());

                    //3.2 循环表检测
                    for (int i = 0; i < tabnameArray.length; i++) {
                        long tableStartTime = System.currentTimeMillis();
                        String tablename = tabnameArray[i];

                        //获取列名
                        String[] valueCloneQualifier = null;
                        List<String> tabFieldList = new ArrayList<>();

                        //获取值
                        String[] valueCloneValue = null;
                        List<String[]> tabDataList = new ArrayList<>();

                        System.out.println("开始获取表数据时间：" + TimeUtils.getNowTime());
                        log.info("开始获取表数据时间：" + TimeUtils.getNowTime());

                        //3.2 读取表信息并处理
                        if (tablename != null && !"".equals(tablename)) {
                            Configuration configuration = HBaseConfiguration.create();
                            configuration.set("hbase.zookeeper.quorum", srcurl);
                            HTable hTable = new HTable(configuration, tablename);

                            //3.2.1 获取 tabDataList ,表内信息
                            Scan scan = new Scan();
                            //使用HTable得到resultcanner实现类的对象
                            ResultScanner resultScanner = hTable.getScanner(scan);

                            for (Result result : resultScanner) {
                                ArrayList<String> values = new ArrayList<>();
                                ArrayList<String> keys = new ArrayList<>();
                                //Cell：封装了Column的所有的信息：Rowkey、qualifier、value、时间戳
                                Cell[] cells = result.rawCells();

                                for (Cell cell : cells) {
                                    System.out.println("行键: " + Bytes.toString(CellUtil.cloneRow(cell)));
                                    System.out.println("列簇: " + Bytes.toString(CellUtil.cloneFamily(cell)));
                                    System.out.println("列: " + Bytes.toString(CellUtil.cloneQualifier(cell)));  //列
                                    System.out.println("值: " + Bytes.toString(CellUtil.cloneValue(cell)));  //值

                                    //拿到所有的列,无备注
                                    String cloneQualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
                                    keys.add(cloneQualifier);
                                    valueCloneQualifier = keys.toArray(new String[keys.size()]);

                                    //拿到所有的值
                                    String cloneValue = Bytes.toString(CellUtil.cloneValue(cell));
                                    values.add(cloneValue);
                                    valueCloneValue = values.toArray(new String[values.size()]);

                                    // tabDataList =  ["lisi","88","55","100"], tabDataList =["zhangsan","77","69","86"];
                                    // tabFieldList=  ["Row Key", "score.computer","score.english","score.math"]
                                }
                                Collections.addAll(tabDataList, valueCloneValue);
                                Collections.addAll(tabFieldList, valueCloneQualifier);
                            }

                            // 4.根据字段数据规则进行检测
                            System.out.println("开始根据字段数据规则检测时间：" + TimeUtils.getNowTime());
                            log.info("开始根据字段数据规则检测时间：" + TimeUtils.getNowTime());
                            Map<String, Map<String, Object>> fieldTypedataExampleMap =
                                    getFieldTypeDataExampleMapHBase(
                                            type, conn, dbnames, dbnames, ruleList, tabFieldList, tabDataList, username, password, srcurl, ip, port
                                    );
                            Map<String, Object> fieldTypeMap = fieldTypedataExampleMap.get("fieldType");
                            Map<String, Object> dataExampleMap = fieldTypedataExampleMap.get("dataExample");
                            Map<String, Object> dataTypeRuleMap = fieldTypedataExampleMap.get("dataTypeRule");
                            System.out.println("开始插入结果详情时间：" + TimeUtils.getNowTime());
                            log.info("开始插入结果详情时间：" + TimeUtils.getNowTime());


                            //  识别并插入库表 sdd_detailresult
                            System.out.println("开始插入结果详情时间：" + TimeUtils.getNowTime());
                            log.info("开始插入结果详情时间：" + TimeUtils.getNowTime());

                            //4.1 检测结果集中数据是否敏感
                            Map<String, Map<String, String>> hbaseExampleMap = getHbaseTypeMap(ruleList, tabDataList);
//                            Map<String, Object> fieldTypeMap = hbaseExampleMap.get("fileDataType");
                            Map<String, String> dataCount = hbaseExampleMap.get("dataCount");
                            Integer count = Integer.valueOf(dataCount.get("count"));

                            //4.1 插入结果详情表  sdd_detailresult
                            if (fieldTypeMap.size() > 0) {
                                sensitive++;
                                //存在敏感数据
                                for (String field : fieldTypeMap.keySet()) {
                                    Detailresult detailresult = new Detailresult();
                                    if (!dataTypeList.contains(fieldTypeMap.get(field))) {
                                        dataTypeList.add((String) fieldTypeMap.get(field));
                                    }
                                    detailresult.setSparefield1(Integer.valueOf(String.valueOf(dataSourceId)));//数据源ID
                                    detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_BIGDATA);//大数据组件
                                    detailresult.setTaskname(taskname);//任务名
                                    detailresult.setDborpath(dbnames);//库名或目录

                                    detailresult.setTableorfile(tablename); //表名或文件名
                                    detailresult.setDatatype(field); //敏感数据
//                    detailresult.setSparefield2((String) fieldTypeMap.get(field)); //备用字段2
//                    detailresult.setDatacount(String.valueOf(sensitive));
                                    detailresult.setDatacount(count); //统计敏感条数
                                    detailresult.setExample((String) dataExampleMap.get(fieldTypeMap.get(field))); //样例
                                    detailresultService.create(detailresult);    //sdd_detailresult
                                }
                            }

                            System.out.println("开始插入元数据时间：" + TimeUtils.getNowTime());
                            log.info("开始插入元结果时间：" + TimeUtils.getNowTime());

                            System.out.println("开始插入元数据时间：" + TimeUtils.getNowTime());
                            log.info("开始插入元结果时间：" + TimeUtils.getNowTime());

                            getSaveMeatadata(type, dbnames, tablename, conn, taskname, tablename, dataSourceId, submituser, tabDataList,
                                    tabFieldList, fieldTypeMap, dataExampleMap, dataTypeRuleMap, baseFieldAllMap, srcurl, tasknumber,
                                    username, password, ip, port);


                            System.out.println("结束插入元数据时间：" + TimeUtils.getNowTime());
                            log.info("结束插入元结果时间：" + TimeUtils.getNowTime());
                            long tableEndTime = System.currentTimeMillis();
                            long tableTotalTime = tableEndTime - tableStartTime;
                            log.info("表： " + dbnames + " 用时：" + tableTotalTime);
                            System.out.println("表： " + dbnames + " 用时：" + tableTotalTime);

                        }

                        outlineresult.setEndtime(TimestampUtil.getNowTime());
                        // 用时计算
                        long endTime = System.currentTimeMillis();
                        outlineresult.setUsetime(String.valueOf(Math.round(endTime - startTime) / 1000));
                        outlineresult.setTypenum(String.valueOf(dataTypeList.size()));
                        outlineresult.setSparefield2(String.valueOf(tabnameArray.length));
                        outlineresult.setSparefield3(String.valueOf(sensitive));
                        outlineresultService.update(outlineresult);
                    }
                }

                //插入表 sdd_statisticstask_outline 记录扫描元数据的信息
                StatisticstaskOutline statisticstaskOutline = new StatisticstaskOutline();
                statisticstaskOutline.setTaskname(taskname);
                statisticstaskOutline.setSrcname(srcname);
                statisticstaskOutline.setDbname(dbname);
                statisticstaskOutline.setSparefield1(type);
                statisticstaskOutline.setSparefield2(srcurl);
                statisticstaskOutline.setTablecount(String.valueOf(tabnameArray.length));//扫描表的数量
                statisticstaskOutline.setCreateuser(submituser);
                statisticstaskOutline.setCreatetime(outlineresult.getStarttime());

                statisticstaskOutlineService.create(statisticstaskOutline);
            }

//                        //4.根据字段数据规则进行检测,需改为对值进行扫描
//                        System.out.println("开始根据表数据规则检测时间：" + TimeUtils.getNowTime());
//                        log.info("开始根据表数据规则检测时间：" + TimeUtils.getNowTime());
//
//                        Map<String, Map<String, Object>> fieldTypedataExampleMap = getFieldTypeDataExampleMapHBase(type, conn, dbnames, dbnames, ruleList, tabFieldList, tabDataList);
//                        Map<String, Object> dataExampleMap = fieldTypedataExampleMap.get("dataExample");
//                        Map<String, Object> dataTypeRuleMap = fieldTypedataExampleMap.get("dataTypeRule");

            HBaseUtil.close();
            return true;
        } catch (Exception e) {
            log.error("检测" + srcurl + "数据源出现异常！", e);
            e.printStackTrace();
            return false;
        }
    }


    public static Map<String, Map<String, String>> getHbaseTypeMap(List<RuleDto> ruleDtoList, List<String[]> tabDataList) {
        Map<String, Map<String, String>> fileDataTypeExampleMap = new HashMap<>();
        // 存储敏感数据和数量 <敏感数据类型,数量>
        Map<String, String> fileDataTypeMap = new HashMap<>();
        // 存储数据样例Map<敏感数据类型类型,样例>
        Map<String, String> dataExampleMap = new TreeMap<>();
        int count = 0;
        try {
            if (tabDataList != null && tabDataList.size() > 0) {

                for (String[] strData : tabDataList) { // 遍历检测数据
                    List<String> checkinfo = new ArrayList<>();
                    //字符截取处理
                    String message = Arrays.toString(strData);
                    String messagesData = message.substring(1, message.length() - 1);
                    checkinfo.addAll(Arrays.asList(messagesData.split("。|，|,| |；|;|：|\t|\n|-|=|:")));
                    for (String data : checkinfo) {
                        //对拆分后的数据依次遍历
                        for (RuleDto ruleDto : ruleDtoList) {
                            //遍历list集合中数组,拆分为字符串
                            boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
                            if (checkSensResult) {
                                String dataType = ruleDto.getApplytypecname();//记录敏感数据类型
                                if (fileDataTypeMap.containsKey(dataType)) {
                                    fileDataTypeMap.put(dataType, String.valueOf(Integer.parseInt(fileDataTypeMap.get(dataType)) + 1));
                                } else {
                                    fileDataTypeMap.put(dataType, String.valueOf(1));
                                }
                                if (!dataExampleMap.containsKey(dataType)) {
                                    dataExampleMap.put(dataType, StringUtils.join(data, ","));//记录敏感数据样例，没样数据只存一个样例
                                }
                                count++;
                                break;//已经发现该数据是敏感数据，停止改数据的检测
                            }
                        }
                    }
                }
            }
            fileDataTypeExampleMap.put("fileDataType", fileDataTypeMap);
            fileDataTypeExampleMap.put("dataExample", dataExampleMap);

            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("count", String.valueOf(count));
            fileDataTypeExampleMap.put("dataCount", hashMap);
        } catch (Exception e) {
            System.out.println("获取文件敏感数据类型出现错误");
            e.printStackTrace();
        }
        return fileDataTypeExampleMap;
    }

    /**
     * HBase自动敏感数据任务
     *
     * @param type         类型
     * @param ruleDtoList  规则列表
     * @param tabFieldList 标签字段列表
     * @param tabDataList  标签数据列表
     * @return {@link Map}<{@link String}, {@link Map}<{@link String}, {@link Object}>>
     */
    public Map<String, Map<String, Object>> getFieldTypeDataExampleMapHBase(
            String type, Object conn, String dbname, String tablename, List<RuleDto> ruleDtoList, List<String> tabFieldList, List<String[]> tabDataList
            , String username, String password, String srcurl, String ip, String port
    ) {
        Map<String, Map<String, Object>> fieldTypedataExampleMap = new HashMap<>();
        // 存储最终字段类型 <字段名,类型>
        Map<String, Object> fieldTypeMap = new TreeMap<>();
        // 存储数据样例Map<类型,样例>
        Map<String, Object> dataExampleMap = new TreeMap<>();
        // 存储类型规则Map<类型,规则信息>
        Map<String, Object> dataTypeRuleMap = new TreeMap<>();
        // 总条数，用于计算类型百分比
        float totalNumber = tabDataList.size();  //
        try {
            Map<Integer, Object> fieldIsNotMap = new HashMap<>(); //字段非空map
            List<TreeMap<String, Integer>> fieldTypeList = new ArrayList<>();
            for (int i = 0; i < tabFieldList.size(); i++) {
                fieldTypeList.add(new TreeMap<>());
            }
            for (int j = 0; j < tabDataList.size(); j++) {// 行
                String[] dataArray = tabDataList.get(j);
                for (int i = 0; i < dataArray.length; i++) {// 列
                    String data = dataArray[i];//当前行列
                    String dataType = null;//当前行列类型
                    TreeMap<String, Integer> tmpMap = fieldTypeList.get(i);//获取当前列类型，用于记录当前列是什么类型数据和数量

                    // 将列数据存储到fieldIsNotMap,防止获取的部分数据中存在空的情况
                    String field = tabFieldList.get(i);
                    if (fieldIsNotMap.containsKey(i)) {
                        Object dataExample = fieldIsNotMap.get(tabFieldList.get(i));
                        if (dataExample == null && "".equals(dataExample)) {
                            fieldIsNotMap.put(i, dataExample);
                        }
                    } else {
                        fieldIsNotMap.put(i, data);
                    }

                    if (data != null && !"".equals(data)) {
                        for (RuleDto ruleDto : ruleDtoList) {//检测当前行列
                            boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
                            if (checkSensResult) {//发现列中存在敏感数据
                                dataType = ruleDto.getApplytypecname();//记录敏感数据类型
                                if (!dataExampleMap.containsKey(dataType)) {
                                    dataExampleMap.put(dataType, data);//记录敏感数据样例，没样数据只存一个样例
                                }
                                if (!dataTypeRuleMap.containsKey(dataType)) {
                                    dataTypeRuleMap.put(dataType, ruleDto);//记录敏感数据样例，没样数据只存一个样例
                                }
                                break;//已经发现该行列是敏感数据，停止改数据的检测
                            }
                        }
                    }
                    if (dataType != null) {//说明当前行列是敏感数据
                        if (tmpMap.containsKey(dataType)) {
                            tmpMap.put(dataType, tmpMap.get(dataType) + 1);//类型和数量
                        } else {
                            tmpMap.put(dataType, 1);
                        }
                    }
                    fieldTypeList.set(i, tmpMap);//记录当前列的类型和对应的数量
                }
            }

            // 数据遍历完后，判断取出的字段数据中是否存在空的情况
            for (Integer fieldIndex : fieldIsNotMap.keySet()) {
                Object value = fieldIsNotMap.get(fieldIndex);
                if (value == null || "".equals(value)) {
                    //查询字段中非空的数据
                    List<String[]> fieldDataList = DatabaseUtil.getFieldDataList(type, conn, dbname, tablename, tabFieldList.get(fieldIndex), 10, username, password, srcurl, ip, port);
                    for (int j = 0; j < fieldDataList.size(); j++) {
                        String[] dataArray = fieldDataList.get(j);
                        for (int i = 0; i < dataArray.length; i++) {// 列
                            String data = dataArray[i];//当前行列
                            String dataType = null;//当前行列类型

                            TreeMap<String, Integer> tmpMap = fieldTypeList.get(fieldIndex);//获取当前列类型，用于记录当前列是什么类型数据和数量

                            if (data != null && !"".equals(data)) {
                                for (RuleDto ruleDto : ruleDtoList) {//检测当前行列
                                    boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
                                    if (checkSensResult) {//发现列中存在敏感数据
                                        dataType = ruleDto.getApplytypecname();//记录敏感数据类型
                                        if (!dataExampleMap.containsKey(dataType)) {
                                            dataExampleMap.put(dataType, data);//记录敏感数据样例，没样数据只存一个样例
                                        }
                                        if (!dataTypeRuleMap.containsKey(dataType)) {
                                            dataTypeRuleMap.put(dataType, ruleDto);//记录敏感数据样例，没样数据只存一个样例
                                        }
                                        break;//已经发现该行列是敏感数据，停止改数据的检测
                                    }
                                }
                            }
                            if (dataType != null) {//说明当前行列是敏感数据
                                if (tmpMap.containsKey(dataType)) {
                                    tmpMap.put(dataType, tmpMap.get(dataType) + 1);//类型和数量
                                } else {
                                    tmpMap.put(dataType, 1);
                                }
                            }
                            fieldTypeList.set(fieldIndex, tmpMap);//记录当前列的类型和对应的数量
                        }
                    }
                }
            }
            for (int i = 0; i < fieldTypeList.size(); i++) {
                // 1：把map转换成entryset，再转换成保存Entry对象的list。
                List<Entry<String, Integer>> entryList = new ArrayList<>(fieldTypeList.get(i).entrySet());
                // 2：调用Collections.sort(list,comparator)方法把Entry-list排序
                Collections.sort(entryList, new Comparator<Entry<String, Integer>>() {
                    @Override
                    public int compare(Entry<String, Integer> o1, Entry<String, Integer> o2) {
                        return o1.getValue().compareTo(o2.getValue());
                    }
                });

                // 3：遍历排好序的Entry-list，可得到按顺序输出的结果
                for (Entry<String, Integer> entry : entryList) {
                    float typeNumber = entry.getValue();
                    float result = typeNumber / totalNumber;
                    if (result >= 0.1) {
                        fieldTypeMap.put(tabFieldList.get(i), entry.getKey());
                    }
                }
            }
            fieldTypedataExampleMap.put("fieldType", fieldTypeMap);
            fieldTypedataExampleMap.put("dataExample", dataExampleMap);
            fieldTypedataExampleMap.put("dataTypeRule", dataTypeRuleMap);

        } catch (Exception e) {
            System.out.println("获取字段类型出现错误");
            e.printStackTrace();
        }
        return fieldTypedataExampleMap;
    }


    /**
     * @param ruleDtoList:规则集合
     * @param tabFieldList:表字段集合
     * @param tabDataList:数据集合，每行中用;;拼接列
     * @Description:执行自动识别敏感数据类型任务
     * <AUTHOR>
     * @date 2020-4-22
     */
    public Map<String, Map<String, Object>> getFieldTypeDataExampleMap(
            String type, Object conn, String dbname, String tablename, List<RuleDto> ruleDtoList, List<String> tabFieldList, List<String[]> tabDataList
            , String username, String password, String srcurl, String ip, String port,List<String> tabCFieldList
    ) {
        Map<String, Map<String, Object>> fieldTypedataExampleMap = new HashMap<>();
        // 存储最终字段类型 <字段名,类型>
        Map<String, Object> fieldTypeMap = new TreeMap<>();
        // 存储数据样例Map<类型,样例>
        Map<String, Object> dataExampleMap = new TreeMap<>();
        // 存储类型规则Map<类型,规则信息>
        Map<String, Object> dataTypeRuleMap = new TreeMap<>();
        // 总条数，用于计算类型百分比
        float totalNumber = tabDataList.size();
        try {
            Map<Integer, Object> fieldIsNotMap = new HashMap<>(); //字段非空map
            List<TreeMap<String, Integer>> fieldTypeList = new ArrayList<>();
            for (int i = 0; i < tabFieldList.size(); i++) {
                fieldTypeList.add(new TreeMap<>());
            }
            for (int j = 0; j < tabDataList.size(); j++) {// 行
                String[] dataArray = tabDataList.get(j);
                for (int i = 0; i < dataArray.length; i++) {// 列
                    String data = dataArray[i];//当前行列
                    String dataType = null;//当前行列类型
                    TreeMap<String, Integer> tmpMap = fieldTypeList.get(i);//获取当前列类型，用于记录当前列是什么类型数据和数量

                    // 将列数据存储到fieldIsNotMap,防止获取的部分数据中存在空的情况
                    String fieldEName = tabFieldList.get(i);
                    String fieldCName = tabCFieldList.get(i);
                    Map<String, Object> fieldMap = new HashMap<>();
                    fieldMap.put("fieldEName",fieldEName);
                    fieldMap.put("fieldCName",fieldCName);

                    if (fieldIsNotMap.containsKey(i)) {
                        Object dataExample = fieldIsNotMap.get(tabFieldList.get(i));
                        if (dataExample == null && "".equals(dataExample)) {
                            fieldIsNotMap.put(i, dataExample);
                        }
                    } else {
                        fieldIsNotMap.put(i, data);
                    }

                    if (data != null && !"".equals(data)) {
                        for (RuleDto ruleDto : ruleDtoList) {//检测当前行列
                            //姓名、地址、手机号白名单过滤
                            if (Const.DATA_TYPE_NAME.equals(ruleDto.getApplytypecname()) && Const.nameWhiteList != null && Const.nameWhiteList.contains(data)) {
                                continue;
                            }
                            if (Const.DATA_TYPE_ADDRESS.equals(ruleDto.getApplytypecname()) || Const.DATA_TYPE_DETAIL_ADDRESS.equals(ruleDto.getApplytypecname()) && Const.addreWhiteList != null && Const.addreWhiteList.contains(data)) {
                                continue;
                            }
                            if (Const.DATA_TYPE_PHONE_NUMBER.equals(ruleDto.getApplytypecname()) && Const.phoneWhiteList != null && Const.phoneWhiteList.contains(data)) {
                                continue;
                            }
                            //分词（姓名、地址、地名）
                            Map<String, String> participleMap = dictDetailService.getDictDetailMap(Const.PARTICIPLE);
                            fieldMap.put("participleMap",participleMap);
                            //数据字典-字段中文名
                            Map<String, String> dataDictionaryFieldChineseNameMap = dictDetailService.getDictDetailMap(Const.DATA_DICTIONARY_FIELD_CHINESE_NAME);
                            fieldMap.put("dataDictionaryFieldChineseNameMap",dataDictionaryFieldChineseNameMap);
                            //数据字典-字段英文名
                            Map<String, String> dataDictionaryFieldEnglishNameMap = dictDetailService.getDictDetailMap(Const.DATA_DICTIONARY_FIELD_ENGLISH_NAME);
                            fieldMap.put("dataDictionaryFieldEnglishNameMap",dataDictionaryFieldEnglishNameMap);
                            //数据字典-字段内容
                            Map<String, String> dataDictionaryFieldContentMap = dictDetailService.getDictDetailMap(Const.DATA_DICTIONARY_FIELD_CONTENT);
                            fieldMap.put("dataDictionaryFieldContentMap",dataDictionaryFieldContentMap);

                            //数据字典
                            boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,fieldMap);
                            if (checkSensResult) {//发现列中存在敏感数据
                                dataType = ruleDto.getApplytypecname();//记录敏感数据类型
                                if (!dataExampleMap.containsKey(dataType)) {
                                    dataExampleMap.put(dataType, data);//记录敏感数据样例，没样数据只存一个样例
                                }
                                if (!dataTypeRuleMap.containsKey(dataType)) {
                                    List<RuleDto> ruleDtos = new ArrayList<>();
                                    ruleDtos.add(ruleDto);
                                    dataTypeRuleMap.put(dataType, ruleDtos);//记录敏感数据样例，没样数据只存一个样例
                                }else {
                                    List<RuleDto> ruleDtos = dataTypeRuleMap.get(dataType) != null ? (List<RuleDto>) dataTypeRuleMap.get(dataType) : new ArrayList<>();
                                    ruleDtos.add(ruleDto);
                                    dataTypeRuleMap.put(dataType,ruleDtos);
                                }
                                break;//已经发现该行列是敏感数据，停止改数据的检测
                            }
                        }
                    }
                    if (dataType != null) {//说明当前行列是敏感数据
                        if (tmpMap.containsKey(dataType)) {
                            tmpMap.put(dataType, tmpMap.get(dataType) + 1);//类型和数量
                        } else {
                            tmpMap.put(dataType, 1);
                        }
                    }
                    fieldTypeList.set(i, tmpMap);//记录当前列的类型和对应的数量
                }
            }

            // 数据遍历完后，判断取出的字段数据中是否存在空的情况
            for (Integer fieldIndex : fieldIsNotMap.keySet()) {
                Object value = fieldIsNotMap.get(fieldIndex);
                if (value == null || "".equals(value)) {
                    //查询字段中非空的数据
                    List<String[]> fieldDataList = DatabaseUtil.getFieldDataList(type, conn, dbname, tablename, tabFieldList.get(fieldIndex), 10, username, password, srcurl, ip, port);
                    for (int i = 0; i < fieldDataList.size(); i++) {
                        String[] dataArray = fieldDataList.get(i);
                        //String data = dataArray[fieldIndex];//指定列数据
                        String data = dataArray[0];
                        String dataType = null;//当前行列类型
                        TreeMap<String, Integer> tmpMap = fieldTypeList.get(fieldIndex);//获取当前列类型，用于记录当前列是什么类型数据和数量
                        if (data != null && !"".equals(data)) {
                            for (RuleDto ruleDto : ruleDtoList) {//检测当前行列
                                //姓名、地址、手机号白名单过滤
                                if (Const.DATA_TYPE_NAME.equals(ruleDto.getApplytypecname()) && Const.nameWhiteList != null && Const.nameWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_ADDRESS.equals(ruleDto.getApplytypecname()) || Const.DATA_TYPE_DETAIL_ADDRESS.equals(ruleDto.getApplytypecname()) && Const.addreWhiteList != null && Const.addreWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_PHONE_NUMBER.equals(ruleDto.getApplytypecname()) && Const.phoneWhiteList != null && Const.phoneWhiteList.contains(data)) {
                                    continue;
                                }
                                boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
                                if (checkSensResult) {//发现列中存在敏感数据
                                    dataType = ruleDto.getApplytypecname();//记录敏感数据类型
                                    if (!dataExampleMap.containsKey(dataType)) {
                                        dataExampleMap.put(dataType, data);//记录敏感数据样例，没样数据只存一个样例
                                    }
                                    if (!dataTypeRuleMap.containsKey(dataType)) {
                                        dataTypeRuleMap.put(dataType, ruleDto);//记录敏感数据样例，没样数据只存一个样例
                                    }
                                    break;//已经发现该行列是敏感数据，停止改数据的检测
                                }
                            }
                        }
                        if (dataType != null) {//说明当前行列是敏感数据
                            if (tmpMap.containsKey(dataType)) {
                                tmpMap.put(dataType, tmpMap.get(dataType) + 1);//类型和数量
                            } else {
                                tmpMap.put(dataType, 1);
                            }
                        }
                        fieldTypeList.set(fieldIndex, tmpMap);//记录当前列的类型和对应的数量

                        /*for (int i = 0; i < dataArray.length; i++) {// 列
                            String data = dataArray[i];//当前行列
                            String dataType = null;//当前行列类型

                            TreeMap<String, Integer> tmpMap = fieldTypeList.get(fieldIndex);//获取当前列类型，用于记录当前列是什么类型数据和数量

                            if (data != null && !"".equals(data)) {
                                for (RuleDto ruleDto : ruleDtoList) {//检测当前行列
                                    boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto);
                                    if (checkSensResult) {//发现列中存在敏感数据
                                        dataType = ruleDto.getApplytypecname();//记录敏感数据类型
                                        if (!dataExampleMap.containsKey(dataType)) {
                                            dataExampleMap.put(dataType, data);//记录敏感数据样例，没样数据只存一个样例
                                        }
                                        if (!dataTypeRuleMap.containsKey(dataType)) {
                                            dataTypeRuleMap.put(dataType, ruleDto);//记录敏感数据样例，没样数据只存一个样例
                                        }
                                        break;//已经发现该行列是敏感数据，停止改数据的检测
                                    }
                                }
                            }
                            if (dataType != null) {//说明当前行列是敏感数据
                                if (tmpMap.containsKey(dataType)) {
                                    tmpMap.put(dataType, tmpMap.get(dataType) + 1);//类型和数量
                                } else {
                                    tmpMap.put(dataType, 1);
                                }
                            }
                            fieldTypeList.set(fieldIndex, tmpMap);//记录当前列的类型和对应的数量
                        }*/
                    }
                }
            }
            for (int i = 0; i < fieldTypeList.size(); i++) {
                // 1：把map转换成entryset，再转换成保存Entry对象的list。
                List<Entry<String, Integer>> entryList = new ArrayList<>(fieldTypeList.get(i).entrySet());
                // 2：调用Collections.sort(list,comparator)方法把Entry-list排序
                Collections.sort(entryList, new Comparator<Entry<String, Integer>>() {
                    @Override
                    public int compare(Entry<String, Integer> o1, Entry<String, Integer> o2) {
                        return o1.getValue().compareTo(o2.getValue());
                    }
                });

                // 3：遍历排好序的Entry-list，可得到按顺序输出的结果
                for (Entry<String, Integer> entry : entryList) {
                    float typeNumber = entry.getValue();
                    float result = typeNumber / totalNumber;
                    if (result >= 0.1) {
                        fieldTypeMap.put(tabFieldList.get(i), entry.getKey());
                    }
                }

                // 用于统计一个字段出现的所有类型
                /*
                 * String type=""; for(Entry<String, Integer> entry:entryList){
                 * if("".equals(type)){ type=entry.getKey(); }else{
                 * type+=","+entry.getKey(); } } if(!"".equals(type)){
                 * fieldTypeMap.put(tabFieldList.get(i),type); }
                 */
            }
            fieldTypedataExampleMap.put("fieldType", fieldTypeMap);
            fieldTypedataExampleMap.put("dataExample", dataExampleMap);
            fieldTypedataExampleMap.put("dataTypeRule", dataTypeRuleMap);


        } catch (Exception e) {
            System.out.println("获取字段类型出现错误");
            e.printStackTrace();
        }
        return fieldTypedataExampleMap;
    }


    /**
     * @param type:数据库类型
     * @param dbname:数据库名
     * @param tablename:数据库表
     * @param conn:数据库连接
     * @param taskname:任务号
     * @param srcname:数据源名称
     * @param dataSourceId:数据源id
     * @param submituser:提交人
     * @param tabDataList:表数据
     * @param tabFieldList:表字段
     * @param fieldTypeMap:字段类型MAP(识别出的)
     * @param dataExampleMap:字段样例MAP(识别出的)
     * @param dataTypeRuleMap:
     * @param baseFieldAllMap:
     * @Description:获取插入元数据，核心(第一版董帅)，整理(张坤祥)
     * <AUTHOR>
     * @date 2020年12月16日11:35:14
     */
    private Map<String, Object> getSaveMeatadata(String type, String dbname, String tablename, Object conn, String taskname, String srcname,
                                                 Long dataSourceId, String submituser, List<String[]> tabDataList, List<String> tabFieldList,
                                                 Map<String, Object> fieldTypeMap, Map<String, Object> dataExampleMap, Map<String, Object> dataTypeRuleMap,
                                                 Map<String, Basefield> baseFieldAllMap, String srcurl, String tasknumber, String username, String password,
                                                 String ip, String port) throws Exception {

        List<TasksynrecordDetail> tasksynrecordDetailList = new ArrayList<>();
        int insertcount = 0;
        int updatecount = 0;

        System.out.println("开始插入元数据时间：" + TimeUtils.getNowTime());
        log.info("开始插入元结果时间：" + TimeUtils.getNowTime());

        //插入元数据表
        Map<String, String> tableInfoMap = DatabaseUtil.getTableInfoBySchema(type, dbname, tablename, conn, username, password, srcurl, ip, port);
        if (ObjectUtil.isEmpty(tableInfoMap)) {
            return new HashMap<>();
        }
        List<Map<String, String>> tableFieldInfoList = new ArrayList<>();

        if (type.equals("DM")) {
            tableFieldInfoList = DatabaseUtil.getTableFieldInfoBySchema(type, dbname, tablename, conn, srcurl, tabFieldList, username, password, ip, port);
        } else {
            tableFieldInfoList = DatabaseUtil.getTableFieldInfoBySchema(type, dbname, tablename, conn, srcurl, username, password, ip, port);
        }

        //同步主表    sdd_meta_table
        MetaTable metaTable = new MetaTable();
        Long mtid = null;
        String tableName = tableInfoMap.get("tableName");
        String tableCName = tableInfoMap.get("tableCName");

        //TODO 视图数据获取的表行数、数据大小为null
        String tableRows = tableInfoMap.get("tableRows");
        String dataSize = tableInfoMap.get("dataSize");


        metaTable.setTaskname(taskname); //任务名
        metaTable.setDbname(dbname); //库名
        metaTable.setDbstatus("0");//库状态(0默认可用, 1为废弃)
        metaTable.setTablename(tableName);//表名
        metaTable.setTablecname(tableCName);//表中文名
        metaTable.setTablestatus("0");//表状态(0默认可用, 1为废弃)
        metaTable.setSourcetype(type);//数据源类型
        metaTable.setSourcename(srcname);//数据源名称
        metaTable.setSourceid(dataSourceId);//数据源ID
//                          metaTable.setCategory();
//                          metaTable.setLevel();
        metaTable.setGetway("0");//获取方式(0系统同步,1自动获取,2Excel导入,3手工录入)
        metaTable.setEvaluationstatus("0");//评估状态(0未评估，1待评估，2待审核，3.重评估，4.已评估)
        metaTable.setSparefield1(Const.META_TABLE_SHOW);//是否隐藏(0显示,1隐藏)
//                          metaTable.setEvaluationmethod("");//(0未评估，1,简单评估，2,评估流程)


        //相同数据源相同表查询
        MetaTable dataSourceScan = metaTableService.getMetaTableByDataSourceScan(dbname, tableName, type, dataSourceId);

        if (dataSourceScan != null) {
            log.info("已存在表: {} ,对元数据表ID进行更新", tableName);
            metaTable.setId(dataSourceScan.getId());
            metaTable.setUpdateuser(submituser);
            metaTableService.update(metaTable);
            mtid = dataSourceScan.getId();
        } else {
            log.info("新增表写入 ddms_meta_table ,表名为: {}", tableName);
            metaTable.setCreateuser(submituser);
            metaTableService.create(metaTable);
            mtid = metaTable.getId();
        }

        //判断字段是否被原表删除，如果原表已经删除，那么元数据也跟着删除这个字段
        List<MetaField> metaFieldList = metaFieldService.findFieldNameByMtid(String.valueOf(mtid));
        if (ObjectUtil.isNotEmpty(metaFieldList)) {
            // 1. 提取需要保留的字段名（快速查找）
            Set<String> existingFieldNames = tableFieldInfoList.stream()
                    .map(map -> map.get("fieldName"))
                    .filter(Objects::nonNull)  // 过滤null值
                    .collect(Collectors.toSet());

            // 2. 获取需要删除的元数据字段
            List<Long> fieldsToDelete = metaFieldList.stream()
                    .filter(field -> !existingFieldNames.contains(field.getFieldname()))
                    .map(MetaField::getId)
                    .collect(Collectors.toList());

            // 3. 批量删除
            if (!fieldsToDelete.isEmpty()) {
                metaFieldService.deleteAllById(fieldsToDelete);
            }
        }

        List<MetaField> updateMetaField = new ArrayList<>();
        List<MetaField> createMetaField = new ArrayList<>();

        for (Map<String, String> tableFieldInfoMap : tableFieldInfoList) {
            String fieldName = tableFieldInfoMap.get("fieldName");
            String fieldCName = tableFieldInfoMap.get("fieldCName");
            String fieldType = tableFieldInfoMap.get("fieldType");
            String applytypecname = (String) fieldTypeMap.get(fieldName);
            //将识别规则的适用中文名，同步为标准化字段中文名
            String fieldSCName = applytypecname;
            String fieldSName = null;
            String category = null;
            String level = null;
            String example = null;
            if (applytypecname != null) {//敏感样例
                example = (String) dataExampleMap.get(applytypecname);
            } else {//非敏感样例
                if (tabDataList.size() > 0) {
                    String[] tabRow = tabDataList.get(0);
                    try {
                        example = tabRow[tabFieldList.indexOf(fieldName)];
                    } catch (Exception e) {
                        example = tabRow[0];
                    }
                }
            }
            //基础字段分类分级自动标注
            if (baseFieldAllMap.containsKey(fieldName)) {
                Basefield basefield = baseFieldAllMap.get(fieldName);
                category = basefield.getCategory();
                level = basefield.getSenLevel();
            } else {
                //自动化匹配规则的敏感级别及类别
                if (fieldTypeMap.containsKey(fieldName)) {
                    String datatype = (String) fieldTypeMap.get(fieldName);
                    if (dataTypeRuleMap.containsKey(datatype)) {
                        RuleDto ruleDto = (RuleDto) dataTypeRuleMap.get(datatype);
                        level = ruleDto.getSparefield1();
                        category = ruleDto.getSparefield2();
                    }
                }
            }

            //同步从表   sdd_meta_field
            MetaField metaField = new MetaField();
            metaField.setMtid(mtid);
            metaField.setFieldname(fieldName);
            metaField.setFieldcname(fieldCName);
            metaField.setFieldsname(fieldSName);
            metaField.setFieldscname(fieldSCName);
            metaField.setFieldstatus("0");//(0默认可用, 1为废弃)
            metaField.setExample(example);
            metaField.setCategory(category);
            metaField.setSenlevel(level);
            metaField.setSparefield1(Const.META_TABLE_SHOW);
            metaField.setSparefield3(fieldType);

            MetaField fieldByMtid = metaFieldService.updateMetaFieldByMtid(fieldName, metaField.getMtid());
            if (fieldByMtid != null) {
                log.info("已存在该字段: {} ,对字段表进行更新", fieldName);
                metaField.copy(fieldByMtid);
                metaField.setMtid(mtid);
                metaField.setFieldname(fieldName);
                metaField.setFieldcname(fieldCName);
                metaField.setFieldsname(fieldSName);
                metaField.setFieldscname(fieldByMtid.getFieldscname());
                metaField.setFieldstatus("0");//(0默认可用, 1为废弃)
                metaField.setExample(example);
                metaField.setCategory(category);
                metaField.setSenlevel(level);
                metaField.setSparefield1(Const.META_TABLE_SHOW);
                metaField.setSparefield3(fieldType);
                metaField.setId(fieldByMtid.getId());
                metaField.setUpdateuser(submituser);
                updateMetaField.add(metaField);

                TasksynrecordDetail tasksynrecordDetail = getTaskSynRecordDetail(taskname, dbname, tablename, tableCName, fieldName, fieldCName,
                        Const.FIELD_UPDATE_EXPLAIN, tableRows, tasknumber, submituser, null, null);
                tasksynrecordDetailList.add(tasksynrecordDetail);
            } else {
                log.info("新增表写入 ddms_meta_field ,字段名: {}", fieldName);
                //不同数据源相同库字段写库(新增)
                metaField.setCreateuser(submituser);
                createMetaField.add(metaField);

                TasksynrecordDetail tasksynrecordDetail = getTaskSynRecordDetail(taskname, dbname, tablename, tableCName, fieldName, fieldCName,
                        Const.FIELD_ADD_EXPLAIN, tableRows, tasknumber, submituser, null, null);
                tasksynrecordDetailList.add(tasksynrecordDetail);
            }
        }


        if (updateMetaField.size() > 0) {
            updatecount += updateMetaField.size();
            metaFieldRepository.saveAll(updateMetaField);
        }
        if (createMetaField.size() > 0) {
            insertcount += createMetaField.size();
            metaFieldRepository.saveAll(createMetaField);
        }

        StatisticstaskDetail statisticstaskDetail = getStatisticsTaskDetail(taskname, srcname, dbname, tableName, tableRows, dataSize,
                tasknumber, submituser, tableFieldInfoList.size());

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("insertcount", insertcount);
        dataMap.put("updatecount", updatecount);
        dataMap.put("statisticstaskDetail", statisticstaskDetail);
        dataMap.put("tasksynrecordDetailList", tasksynrecordDetailList);
        return dataMap;
    }

    /**
     * 求b关于a的相对补集
     *
     * @param characterList  集合
     * @param characterArray 集合
     * @return {@link List}<{@link String}>
     */
    public static List<String> relativeComplementRespect(List<String> characterList, List<String> characterArray) {
        Collection differenceList = new ArrayList(characterList);
        differenceList.retainAll(characterArray);
        characterList.removeAll(differenceList);
        return characterList;
    }


    public StatisticstaskDetail getStatisticsTaskDetail(String taskName, String srcName, String dbname, String tableName,
                                                        String tableRows, String dataSize, String taskNumber, String submitUser,
                                                        Integer fieldCount) {
        StatisticstaskDetail statisticstaskDetail = new StatisticstaskDetail();
        statisticstaskDetail.setTaskname(taskName);
        statisticstaskDetail.setSrcname(srcName);
        statisticstaskDetail.setSparefield1(dbname);
        statisticstaskDetail.setTablename(tableName);
        statisticstaskDetail.setFieldcount(String.valueOf(fieldCount));//字段的数量
        statisticstaskDetail.setDatacount(tableRows);//数据量（行）
        if (dataSize != null) {
            statisticstaskDetail.setSparefield2(dataSize + "MB");//数据量（MB）
        }
        statisticstaskDetail.setSparefield3(taskNumber);//任务批次号
        statisticstaskDetail.setCreateuser(submitUser);
        statisticstaskDetail.setCreatetime(Timestamp.valueOf(TimeUtils.getNowTime()));
        return statisticstaskDetail;
    }

    public TasksynrecordDetail getTaskSynRecordDetail(String taskName, String dbname, String tableName, String tableCName,
                                                      String fieldName, String fieldCName, String sysDetail,
                                                      String tableRows, String taskNumber, String submitUser,
                                                      Integer fieldCount, Integer fileTypeCount) {
        TasksynrecordDetail tasksynrecordDetail = new TasksynrecordDetail();
        tasksynrecordDetail.setFieldname(fieldName);
        tasksynrecordDetail.setFieldcname(fieldCName);
        tasksynrecordDetail.setTaskname(taskName);
        tasksynrecordDetail.setTasknumber(taskNumber);
        tasksynrecordDetail.setDbname(dbname);
        tasksynrecordDetail.setTablename(tableName);
        tasksynrecordDetail.setTablecname(tableCName);
        tasksynrecordDetail.setSyndetail(sysDetail);
        tasksynrecordDetail.setSparefield1(String.valueOf(fieldCount));//字段数量
        tasksynrecordDetail.setSparefield2(String.valueOf(fileTypeCount));//敏感字段数量
        if (StringUtils.isNotEmpty(tableRows)) {
            tasksynrecordDetail.setSparefield3(tableRows);//数据行数
        } else {
            tasksynrecordDetail.setSparefield3(String.valueOf(0));//数据行数
        }
        tasksynrecordDetail.setCreateuser(submitUser);
        tasksynrecordDetail.setCreatetime(Timestamp.valueOf(DateUtil.now()));
        return tasksynrecordDetail;
    }

    @Async
    public void asyncSaveStatisticsTaskDetail(List<StatisticstaskDetail> statisticstaskDetailList) {
        statisticstaskDetailRepository.saveAll(statisticstaskDetailList);
    }

    @Async
    public void asyncSaveTaskSynRecordDetail(List<TasksynrecordDetail> taskSynreCordDetailList) {
        tasksynrecordDetailRepository.saveAll(taskSynreCordDetailList);
    }

    /**
     * 自动识别敏感数据类型
     *
     * @return
     */
    public boolean doNODBSensitiveDataDiscovery(
            String taskname, Long dataSourceId, String submituser,
            String srcname, String type, String srcurl, String username,
            String password, String dbnames, List<RuleDto> ruleDtoList,
            List<CategoryDto> categoryList, Map<String, Basefield> baseFieldAllMap,
            String tasknumber, String taskType, String srcip, String srcport
    ) {
        try {
            int count = 0;
            int sensitive = 0;
            //保存敏感数据发现概要结果表
            Outlineresult outlineresult = new Outlineresult();
            long startTime = System.currentTimeMillis();
            outlineresult.setTaskname(taskname);
            outlineresult.setSubmituser(submituser);
            outlineresult.setStarttime(TimestampUtil.getNowTime());
            outlineresult.setLocation(srcip);
            outlineresult.setSparefield1(dataSourceId);
            outlineresult.setDatasourcetype(Const.OUTLINERESULT_DATASOURCETYPE_DB);//库表
            outlineresultService.create(outlineresult);
            List<String> dataTypeList = new ArrayList<>();
            int sum = 0;
            //任务敏感数据规则检测信息
            Map<String, Map<String, String>> fileDataTypeExampleMap = new HashMap<>();
//            String[] dbnameList = dbnames.split(",");
//            for (String dbname : dbnameList) {
//                Jedis jedis = RedisUtil.queryRedisConnect(srcip, srcport, password);
//                jedis.select(Integer.valueOf(dbname));
//                //获取所有表名
//                Set<String> keys = jedis.keys("*");
//                sum += keys.size();
//                for (String key : keys) {
//                    int fileLineNum = 0;
//                    //获取所有值
//                    String value = jedis.get(key);
//                    //读取数据返回拆分后的String集合
//                    List<String> fileDataList = RedisUtil.getValueData(value);
//                    log.info("检测数据中是否敏感时间：" + TimeUtils.getNowTime());
//                    //检测数据是否敏感
//                    fileDataTypeExampleMap = CheckFileManager.getFileDataTypeMap(ruleDtoList, fileDataList);
//                    log.info("====================对数据分析结束====================");
//                    Map<String, String> dataTypeMap = fileDataTypeExampleMap.get("fileDataType");
//                    Map<String, String> dataExampleMap = fileDataTypeExampleMap.get("dataExample");
//                    // 统计敏感数据数量、样本，记录详情结果
//                    if (dataTypeMap.size() > 0) {
//                        sensitive++;
//                        //存在敏感数据
//                        for (String dataType : dataTypeMap.keySet()) {
//                            if (!dataTypeList.contains(dataType)) {
//                                dataTypeList.add(dataType);
//                            }
//                            //敏感数据发现详情结果表
//                            Detailresult detailresult = new Detailresult();
//                            detailresult.setSparefield1(dataSourceId);
//                            detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_DB);//库表
//                            detailresult.setTaskname(taskname);
//                            detailresult.setDborpath(dbname);
//                            detailresult.setTableorfile(key);
//                            detailresult.setDatatype(dataType);
//                            if (fileLineNum == 0) {
//                                if (fileDataList == null) {
//                                    detailresult.setDatacount(fileLineNum);
//                                } else {
//                                    detailresult.setDatacount(Integer.valueOf(dataTypeMap.get(dataType)));
//                                }
//                            } else {
//                                detailresult.setDatacount(fileLineNum);
//                            }
//                            detailresult.setExample(dataExampleMap.get(dataType));
//                            detailresultService.create(detailresult);
//                        }
//                    } else {
//                        //没有敏感数据
//                        //敏感数据发现详情结果表
//                        Detailresult detailresult = new Detailresult();
//                        detailresult.setSparefield1(dataSourceId);
//                        detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_DB);//库表
//                        detailresult.setTaskname(taskname);
//                        detailresult.setDborpath(dbname);
//                        detailresult.setTableorfile(key);
//                        if (fileLineNum == 0) {
//                            if (fileDataList == null) {
//                                detailresult.setDatacount(fileLineNum);
//                            } else {
//                                detailresult.setDatacount(fileDataList.size());
//                            }
//                        } else {
//                            detailresult.setDatacount(fileLineNum);
//                        }
//                        detailresultService.create(detailresult);
//                    }
//
//                    //同步元数据
//                    MetaTable metaTable = new MetaTable();
//                    Long mtid = null;
//                    metaTable.setTaskname(taskname); //任务名
//                    metaTable.setDbname(dbname); //库名
//                    metaTable.setDbstatus("0");//库状态(0默认可用, 1为废弃)
//                    metaTable.setTablename(key);//表名
//                    metaTable.setTablecname(key);//表中文名
//                    metaTable.setTablestatus("0");//表状态(0默认可用, 1为废弃)
//                    metaTable.setSourcetype(type);//数据源类型
//                    metaTable.setSourcename(srcname);//数据源名称
//                    metaTable.setSourceid(dataSourceId);//数据源ID
//                    metaTable.setGetway("0");//获取方式(0系统同步,1自动获取,2Excel导入,3手工录入)
//                    metaTable.setEvaluationstatus("0");//评估状态(0未评估，1待评估，2待审核，3.重评估，4.已评估)
//                    metaTable.setSparefield1(Const.META_TABLE_SHOW);//是否隐藏(0显示,1隐藏)
//                    MetaTable dataSourceScan = metaTableService.getMetaTableByDataSourceScan(dbname, key, type, dataSourceId);
//                    boolean isUpdate = false;
//                    if (dataSourceScan != null) {
//                        log.info("已存在表: {} ,对元数据表ID进行更新", key);
//                        metaTable.setId(dataSourceScan.getId());
//                        metaTable.setUpdateuser(submituser);
//                        metaTableService.update(metaTable);
//                        mtid = dataSourceScan.getId();
//                        isUpdate = true;
//                    } else {
//                        log.info("新增表写入 ddms_meta_table ,表名为: {}", key);
//                        metaTable.setCreateuser(submituser);
//                        metaTableService.create(metaTable);
//                        mtid = metaTable.getId();
//                    }
//                }
//                jedis.close();
//            }


            String[] redisUrlList = srcurl.split(";");
            // 创建一个包含集群节点的 Set
            Set<HostAndPort> jedisClusterNodes = new HashSet<>();
            for (String redisUrl : redisUrlList) {
                String[] url = redisUrl.split(":");
                if (url.length > 4 || url.length < 3) {
                    throw new RuntimeException("数据源url配置有误");
                }
                String redisIp = url[0];
                int redisPort = Integer.valueOf(url[1]);
                jedisClusterNodes.add(new HostAndPort(redisIp, redisPort));
            }

            for (String redisUrl : redisUrlList) {
                String[] url = redisUrl.split(":");
                if (url.length > 4 || url.length < 3) {
                    throw new RuntimeException("数据源url配置有误");
                }
                String redisIp = url[0];
                String redisPort = url[1];
                String dbNames = url[2];
                String redisPassword = "";
                if (url.length == 4) {
                    redisPassword = url[3];
                }
                String[] dbNameList = dbNames.split(",");
                for (String dbname : dbNameList) {
                    Jedis jedis = RedisUtil.queryRedisConnect(redisIp, redisPort, redisPassword);
                    jedis.select(Integer.valueOf(dbname));
                    //获取所有表名
                    Set<String> keys = jedis.keys("*");
                    sum += keys.size();
                    for (String key : keys) {
                        int fileLineNum = 0;
                        //获取所有值
//                        String value = jedis.get(key);
                        JedisCluster jedisCluster = new JedisCluster(jedisClusterNodes);
                        String value = jedisCluster.get(key);
                        jedisCluster.close();

                        //读取数据返回拆分后的String集合
                        List<String> fileDataList = RedisUtil.getValueData(value);
                        log.info("检测数据中是否敏感时间：" + TimeUtils.getNowTime());
                        //检测数据是否敏感
                        fileDataTypeExampleMap = CheckFileManager.getFileDataTypeMap(ruleDtoList, fileDataList);
                        log.info("====================对数据分析结束====================");
                        Map<String, String> dataTypeMap = fileDataTypeExampleMap.get("fileDataType");
                        Map<String, String> dataExampleMap = fileDataTypeExampleMap.get("dataExample");
                        // 统计敏感数据数量、样本，记录详情结果
                        if (dataTypeMap.size() > 0) {
                            sensitive++;
                            //存在敏感数据
                            for (String dataType : dataTypeMap.keySet()) {
                                if (!dataTypeList.contains(dataType)) {
                                    dataTypeList.add(dataType);
                                }
                                //敏感数据发现详情结果表
                                Detailresult detailresult = new Detailresult();
                                detailresult.setSparefield1(Integer.valueOf(String.valueOf(dataSourceId)));
                                detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_DB);//库表
                                detailresult.setTaskname(taskname);
                                detailresult.setDborpath(dbname);
                                detailresult.setTableorfile(key);
                                detailresult.setDatatype(dataType);
                                if (fileLineNum == 0) {
                                    if (fileDataList == null) {
                                        detailresult.setDatacount(fileLineNum);
                                    } else {
                                        detailresult.setDatacount(Integer.valueOf(dataTypeMap.get(dataType)));
                                    }
                                } else {
                                    detailresult.setDatacount(fileLineNum);
                                }
                                detailresult.setExample(dataExampleMap.get(dataType));
                                detailresultService.create(detailresult);
                            }
                        } else {
                            //没有敏感数据
                            //敏感数据发现详情结果表
                            Detailresult detailresult = new Detailresult();
                            detailresult.setSparefield1(Integer.valueOf(String.valueOf(dataSourceId)));
                            detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_DB);//库表
                            detailresult.setTaskname(taskname);
                            detailresult.setDborpath(dbname);
                            detailresult.setTableorfile(key);
                            if (fileLineNum == 0) {
                                if (fileDataList == null) {
                                    detailresult.setDatacount(fileLineNum);
                                } else {
                                    detailresult.setDatacount(fileDataList.size());
                                }
                            } else {
                                detailresult.setDatacount(fileLineNum);
                            }
                            detailresultService.create(detailresult);
                        }

                        //同步元数据
                        MetaTable metaTable = new MetaTable();
                        Long mtid = null;
                        metaTable.setTaskname(taskname); //任务名
                        metaTable.setDbname(dbname); //库名
                        metaTable.setDbstatus("0");//库状态(0默认可用, 1为废弃)
                        metaTable.setTablename(key);//表名
                        metaTable.setTablecname(key);//表中文名
                        metaTable.setTablestatus("0");//表状态(0默认可用, 1为废弃)
                        metaTable.setSourcetype(type);//数据源类型
                        metaTable.setSourcename(srcname);//数据源名称
                        metaTable.setSourceid(dataSourceId);//数据源ID
                        metaTable.setGetway("0");//获取方式(0系统同步,1自动获取,2Excel导入,3手工录入)
                        metaTable.setEvaluationstatus("0");//评估状态(0未评估，1待评估，2待审核，3.重评估，4.已评估)
                        metaTable.setSparefield1(Const.META_TABLE_SHOW);//是否隐藏(0显示,1隐藏)
                        MetaTable dataSourceScan = metaTableService.getMetaTableByDataSourceScan(dbname, key, type, dataSourceId);
                        boolean isUpdate = false;
                        if (dataSourceScan != null) {
                            log.info("已存在表: {} ,对元数据表ID进行更新", key);
                            metaTable.setId(dataSourceScan.getId());
                            metaTable.setUpdateuser(submituser);
                            metaTableService.update(metaTable);
                            mtid = dataSourceScan.getId();
                            isUpdate = true;
                        } else {
                            log.info("新增表写入 ddms_meta_table ,表名为: {}", key);
                            metaTable.setCreateuser(submituser);
                            metaTableService.create(metaTable);
                            mtid = metaTable.getId();
                        }
                    }
                    jedis.close();
                }
            }


            outlineresult.setEndtime(TimestampUtil.getNowTime());
            //用时计算
            long endTime = System.currentTimeMillis();
            outlineresult.setUsetime(String.valueOf(Math.round(endTime - startTime) / 1000.000));
            outlineresult.setTypenum(String.valueOf(dataTypeList.size()));
            outlineresult.setSparefield2(String.valueOf(sum));
            outlineresult.setSparefield3(String.valueOf(sensitive));
            outlineresultService.update(outlineresult);
            return true;
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return false;
        }
    }

}
