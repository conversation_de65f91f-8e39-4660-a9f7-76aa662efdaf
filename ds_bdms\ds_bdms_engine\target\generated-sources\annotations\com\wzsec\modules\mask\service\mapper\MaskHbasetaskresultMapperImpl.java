package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskHbasetaskresult;
import com.wzsec.modules.mask.service.dto.MaskHbasetaskresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskHbasetaskresultMapperImpl implements MaskHbasetaskresultMapper {

    @Override
    public MaskHbasetaskresultDto toDto(MaskHbasetaskresult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskHbasetaskresultDto maskHbasetaskresultDto = new MaskHbasetaskresultDto();

        maskHbasetaskresultDto.setBatchnumber( entity.getBatchnumber() );
        maskHbasetaskresultDto.setCount( entity.getCount() );
        maskHbasetaskresultDto.setCreatetime( entity.getCreatetime() );
        maskHbasetaskresultDto.setCreateuserid( entity.getCreateuserid() );
        maskHbasetaskresultDto.setDataoutputdir( entity.getDataoutputdir() );
        maskHbasetaskresultDto.setDatasplit( entity.getDatasplit() );
        maskHbasetaskresultDto.setDbname( entity.getDbname() );
        maskHbasetaskresultDto.setId( entity.getId() );
        maskHbasetaskresultDto.setJobendtime( entity.getJobendtime() );
        maskHbasetaskresultDto.setJobstarttime( entity.getJobstarttime() );
        maskHbasetaskresultDto.setJobstatus( entity.getJobstatus() );
        maskHbasetaskresultDto.setJobtotaltime( entity.getJobtotaltime() );
        maskHbasetaskresultDto.setOutputtablename( entity.getOutputtablename() );
        maskHbasetaskresultDto.setOutputtype( entity.getOutputtype() );
        maskHbasetaskresultDto.setRemark( entity.getRemark() );
        maskHbasetaskresultDto.setSparefield1( entity.getSparefield1() );
        maskHbasetaskresultDto.setSparefield2( entity.getSparefield2() );
        maskHbasetaskresultDto.setSparefield3( entity.getSparefield3() );
        maskHbasetaskresultDto.setSparefield4( entity.getSparefield4() );
        maskHbasetaskresultDto.setStrategyname( entity.getStrategyname() );
        maskHbasetaskresultDto.setTabname( entity.getTabname() );
        maskHbasetaskresultDto.setUpdatetime( entity.getUpdatetime() );
        maskHbasetaskresultDto.setUpdateuserid( entity.getUpdateuserid() );
        maskHbasetaskresultDto.setWorksheet( entity.getWorksheet() );

        return maskHbasetaskresultDto;
    }

    @Override
    public List<MaskHbasetaskresultDto> toDto(List<MaskHbasetaskresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskHbasetaskresultDto> list = new ArrayList<MaskHbasetaskresultDto>( entityList.size() );
        for ( MaskHbasetaskresult maskHbasetaskresult : entityList ) {
            list.add( toDto( maskHbasetaskresult ) );
        }

        return list;
    }

    @Override
    public MaskHbasetaskresult toEntity(MaskHbasetaskresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskHbasetaskresult maskHbasetaskresult = new MaskHbasetaskresult();

        maskHbasetaskresult.setBatchnumber( dto.getBatchnumber() );
        maskHbasetaskresult.setCount( dto.getCount() );
        maskHbasetaskresult.setCreatetime( dto.getCreatetime() );
        maskHbasetaskresult.setCreateuserid( dto.getCreateuserid() );
        maskHbasetaskresult.setDataoutputdir( dto.getDataoutputdir() );
        maskHbasetaskresult.setDatasplit( dto.getDatasplit() );
        maskHbasetaskresult.setDbname( dto.getDbname() );
        maskHbasetaskresult.setId( dto.getId() );
        maskHbasetaskresult.setJobendtime( dto.getJobendtime() );
        maskHbasetaskresult.setJobstarttime( dto.getJobstarttime() );
        maskHbasetaskresult.setJobstatus( dto.getJobstatus() );
        maskHbasetaskresult.setJobtotaltime( dto.getJobtotaltime() );
        maskHbasetaskresult.setOutputtablename( dto.getOutputtablename() );
        maskHbasetaskresult.setOutputtype( dto.getOutputtype() );
        maskHbasetaskresult.setRemark( dto.getRemark() );
        maskHbasetaskresult.setSparefield1( dto.getSparefield1() );
        maskHbasetaskresult.setSparefield2( dto.getSparefield2() );
        maskHbasetaskresult.setSparefield3( dto.getSparefield3() );
        maskHbasetaskresult.setSparefield4( dto.getSparefield4() );
        maskHbasetaskresult.setStrategyname( dto.getStrategyname() );
        maskHbasetaskresult.setTabname( dto.getTabname() );
        maskHbasetaskresult.setUpdatetime( dto.getUpdatetime() );
        maskHbasetaskresult.setUpdateuserid( dto.getUpdateuserid() );
        maskHbasetaskresult.setWorksheet( dto.getWorksheet() );

        return maskHbasetaskresult;
    }

    @Override
    public List<MaskHbasetaskresult> toEntity(List<MaskHbasetaskresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskHbasetaskresult> list = new ArrayList<MaskHbasetaskresult>( dtoList.size() );
        for ( MaskHbasetaskresultDto maskHbasetaskresultDto : dtoList ) {
            list.add( toEntity( maskHbasetaskresultDto ) );
        }

        return list;
    }
}
