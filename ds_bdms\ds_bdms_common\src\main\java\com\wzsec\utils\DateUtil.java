package com.wzsec.utils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2020-4-22
 */
public class DateUtil {

    /**
     * 格式化日期
     *
     * @param date
     * @return
     */
    public static String formatDate(String format, Date date) {
        SimpleDateFormat sf = new SimpleDateFormat(format);
        return sf.format(date);
    }

    /**
     * 获取当前时间
     */
    public static String getNowTime() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(new Date());
    }

    public static String formatDate(Date date) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sf.format(date);
    }

    /**
     * @Description:获取两个时间之差的秒数
     * <AUTHOR> by xiongpf
     * @date 2018-01-10
     */
    public static int getTimeSecondsByBothDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startDate = null;
        Date endDate = null;
        long startDateMilliSeconds = 0;
        long endDateMilliSeconds = 0;
        try {
            startDate = sdf.parse(startTime);
            endDate = sdf.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        // 得到两个日期对象的总毫秒数
        if (null != startDate) {
            startDateMilliSeconds = startDate.getTime();
        }
        if (null != endDate) {
            endDateMilliSeconds = endDate.getTime();
        }

        long totalMilliSeconds = endDateMilliSeconds - startDateMilliSeconds;
        return (int) (totalMilliSeconds / 1000);
    }

    public static String getNowTimeString(String dateFormat) {
        Date now = new Date();
        SimpleDateFormat dateFormatVal = new SimpleDateFormat(dateFormat);
        String nowtime = dateFormatVal.format(now);
        return nowtime;
    }

    public static Timestamp str2Timestamp(String format, String dateStr) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        Date date = null;
        Timestamp timestamp = null;
        try {
            date = dateFormat.parse(dateStr);
            timestamp = new Timestamp(date.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timestamp;
    }

    public static Timestamp long2Timestamp(long timestamp) {
        return new Timestamp(timestamp);
    }

    /**
     * 将毫秒转成 *天*小时*分*秒 的格式
     * @param ms
     * @return
     */
    public static String formatTime(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        // Long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;

        StringBuffer sb = new StringBuffer();
        if(day > 0) {
            sb.append(day+"天");
        }
        if(hour > 0) {
            sb.append(hour+"小时");
        }
        if(minute > 0) {
            sb.append(minute+"分");
        }
        if(second > 0) {
            sb.append(second+"秒");
        }
        // if(milliSecond > 0) {
        //     sb.append(milliSecond+"毫秒");
        // }
        return sb.toString();
    }

    /**
     * 将UTC时间转北京时间
     * @param time
     * @param format
     * @return
     */
    public static String convertUTC2local(String time,String format){

        Date date = null;
        Calendar calendar =null;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            date = simpleDateFormat.parse(time);
            calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return TimeUtils.DateToStr2(calendar.getTime());
    }

    /**
     * 获取上周末时间
     */
    public static String getLastSunday() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        int dayOfWeek = calendar1.get(Calendar.DAY_OF_WEEK) - 1;
        int offset2 = 7 - dayOfWeek;
        calendar2.add(Calendar.DATE, offset2 - 7);
        return sdf.format(calendar2.getTime());
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) {//同一年
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
                {
                    timeDistance += 366;
                } else    //不是闰年
                {
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2 - day1);
        } else {// 不同年
            return day2 - day1;
        }
    }

    /**
     * 获取日差(在今天之前输出负天数,在今天之后输出正天数)
     *
     * @param dateStr 日期str
     * @return int
     */
    public static int getDayDifference(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        LocalDate date = LocalDate.parse(dateStr, formatter);

        long daysDifference = ChronoUnit.DAYS.between(today, date);
        return (int) daysDifference;
    }

}
