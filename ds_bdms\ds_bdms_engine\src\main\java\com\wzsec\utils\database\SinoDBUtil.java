package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

/**
 * SinoDB表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2024年10月17日 上午8:50:10
 */
@Slf4j
public class SinoDBUtil extends DatabaseUtil{

    private static String JDBC_DRIVER = "com.sinodbms.jdbc.IfxDriver";


    /**
     * 获取表信息
     * @param dbName    库名
     * @param tableName 表名
     * @param conn
     * @return 数据库表信息
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
//            stmt = conn.createStatement();// 执行创建表
//            rs = stmt.executeQuery("SELECT TABLE_NAME,TABLE_COMMENT,TABLE_ROWS,ROUND(((data_length + index_length) / 1024 / 1024), 2) AS Size_MB FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
//            if (rs != null && rs.next()) {
//                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
//                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
//                tableInfoMap.put("tableRows", rs.getString("TABLE_ROWS"));
//                tableInfoMap.put("dataSize", rs.getString("Size_MB"));
//            }
//            DatabaseMetaData metaData = conn.getMetaData();
//            rs = metaData.getColumns(null, null, tableName, null);
            tableInfoMap.put("tableName",tableName);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }

    /**
     * 获取数据库表字段信息
     * @param dbName    库名
     * @param tableName 表名
     * @param conn
     * @return
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            //获取数据库元数据
            DatabaseMetaData metaData = conn.getMetaData();
            rs = metaData.getColumns(null, null, tableName, null);
            while (rs.next()) {
                Map<String, String> fieldInfoMap = new HashMap<>();
                String columnName = rs.getString("column_name");
                String columnSize = rs.getString("column_size");
                String typeName = rs.getString("type_name");
                fieldInfoMap.put("fieldName",columnName);
//                fieldInfoMap.put("fieldCName", rs.getString("COLUMN_COMMENT"));
                fieldInfoMap.put("fieldType", typeName+"("+columnSize+")");
                fieldInfoList.add(fieldInfoMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * 获取数据库某个字段非空数据
     * @param conn
     * @param dbname    库名
     * @param tabname   表名
     * @param field
     * @param lineNum
     * @return
     */
    public static List<String[]> getFieldDataList(Connection conn, String dbname, String tabname, String field, Integer lineNum) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select " + field + " from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " where " + field + " is not null and " + field + " !=''  LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception e) {
            String[] passNull = {""};
            tabDataList.add(passNull);
            //System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        }finally {
            closeCon(rs,stmt,null);
        }
        return tabDataList;
    }

    /**
     * 获取数据库中所有的库名表名
     * @param conn
     * @param dbnames
     * @return
     * @throws SQLException
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        Map<String, String> dbTabMap = new TreeMap<String, String>();
        try {
            // 获取所有数据库的名字
            DatabaseMetaData meta = conn.getMetaData();
            ResultSet catalogs = meta.getCatalogs();
            System.out.println("数据库列表:");
            while (catalogs.next()) {
                System.out.println(catalogs.getString(1));
            }

            for (String catalog : getCatalogs(conn, meta)) {
                conn.setCatalog(catalog);
                ResultSet tables = meta.getTables(catalog, null, "%", new String[] {"TABLE"});
                System.out.println("数据库: " + catalog);
                while (tables.next()) {
                    System.out.println("表名: " + tables.getString("TABLE_NAME"));
                    if (dbTabMap.containsKey(catalog)) {
                        dbTabMap.put(catalog, dbTabMap.get(catalog) + "," + tables.getString("TABLE_NAME"));
                    } else {
                        dbTabMap.put(catalog, tables.getString("TABLE_NAME"));
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return dbTabMap;
    }

    /**
     * 获取数据库表数据
     * @param conn
     * @param dbname
     * @param tabname
     * @param lineNum
     * @return
     * @throws SQLException
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        Statement statement = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0){
                strSQL += " LIMIT " + lineNum;
            }
            // 创建Statement对象
            statement = conn.createStatement();
            //执行查询并获取结果集
            rs = statement.executeQuery(strSQL);
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        }finally {
            // 7. 关闭资源
            try {
                if (rs != null) {
                    rs.close();
                }
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return tabDataList;
    }

    /**
     * 获取数据库表中所有字段名
     * @param conn
     * @param dbname
     * @param tabname
     * @return
     * @throws SQLException
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        DatabaseMetaData metaData = null;
        List<String> list = new ArrayList<String>();
        try {
            //获取数据库的元数据
            metaData = conn.getMetaData();
            rs = metaData.getColumns(null, null, tabname, null);
            while (rs.next()) {
                // 获取列名
                String columnName = rs.getString("COLUMN_NAME");
                list.add(columnName);
            }
        } catch (Exception ex) {
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
            throw ex;
        } finally {
            closeCon(rs,stmt,null);
        }
        return list;
    }

    /**
     * 获取数据库表中前100条数据
     * @param conn
     * @param dbname
     * @param tabname
     * @return
     * @throws SQLException
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return count;
    }

    /**
     * 获取数据库字段信息
     * @param dbName    库名
     * @param tableName 表名
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @return
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, tableName, null);

            while (columns.next()) {
                Map<String, String> fieldInfoMap = new HashMap<>();
                fieldInfoMap.put("FieldEName", columns.getString("COLUMN_NAME"));//字段名
                fieldInfoMap.put("FieldCName",  columns.getString("REMARKS") != null ? columns.getString("REMARKS") : "");//注释
                String columnSize = rs.getString("column_size");
                fieldInfoMap.put("FieldType", rs.getString("type_name")+"("+columnSize+")");//格式：varchar(255)
                fieldInfoList.add(fieldInfoMap);
            }
            columns.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
        closeCon(rs, stmt, conn);
        return fieldInfoList;
    }

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * @param objList   Map数据库集合
     * @param dbName    库名
     * @param tableName 表名
     * @return
     */
    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbName, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * @param start     批量插入开始位
     * @param end       批量插入结束位
     * @param objList   Map数据库集合
     * @param dbname    库名
     * @param tableName 表名
     * @return
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            String sinodbSql = "";
            for (int i = start; i < end; i++) {
                sb.append("INSERT INTO ").append(tableName).append(" (");
                for (String column : fields) {
                    sb.append(column).append(", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");

                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(");");
            }
            sinodbSql = sb.toString();
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sinodbSql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * @param start      批量插入开始位
     * @param end        批量插入结束位
     * @param objList    Map数据库集合
     * @param dbname     dbname
     * @param tableName  表名
     * @param fieldnames 字段名
     * @return
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 通过Map生成创建表SQL语句，自动检测字段名及类型
     * @param fieldInfoList 字段信息列表
     * @param obj           Map对象
     * @param tableName     表名
     * @param maskfields    脱敏字段
     * @return
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("FieldEName")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("FieldCName") != null && !"".equals(fieldInfo.get("FieldCName"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("FieldCName")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r);\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * 通过Map生成创建表SQL语句，自动检测字段名及类型
     * @param fieldInfoList 字段信息列表
     * @param obj           Map对象
     * @param tableName     表名
     * @param maskfields    脱敏字段
     * @param dbname        库名
     * @param watermarkField
     * @return
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");

            for (Map<String, String> fieldInfo : fieldInfoList) {
                String fieldEName = fieldInfo.get("FieldEName");
                String fieldType = fieldInfo.get("FieldType");
                if (!obj.keySet().contains(fieldEName)) {// 跳过没有抽取的列
                    continue;
                }
                sb.append(fieldEName);// 字段名

                if (watermarkField != null && watermarkField.equals(fieldEName)) {
                    sb.append(" longtext");// 选择注入水印的字段长度改为
                } else if (maskfields != null && maskfields.contains(fieldEName)) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    //字符型数据转换
                    if (StringUtils.isEmpty(fieldType)){
                        sb.append(" varchar(255)");
                    }else if (fieldType.toLowerCase().contains("nvarchar") || fieldType.toLowerCase().contains("char2")) {
                        sb.append(" varchar(255)");
                        //数字型数据转换
                    } else if (fieldType.toLowerCase().contains("number")) {
                        /*String dataLength = "11";//默认给个11，大都够用
                        if (fieldType.contains("(")){
                            int startIndex = fieldType.indexOf("(") + 1;
                            int endIndex = fieldType.indexOf(")");
                            dataLength = fieldType.substring(startIndex,endIndex);
                        }*/
                        sb.append(" long");

                        //大文本类型转换
                    } else if (fieldType.toLowerCase().contains("text") || fieldType.toLowerCase().contains("clob")) {
                        sb.append(" longtext");

                        //日期类型转换
                    } else if (fieldType.toLowerCase().contains("decimal") || fieldType.toLowerCase().contains("date")) {
                        sb.append(" datetime");
                    } else {
                        sb.append(" ").append(fieldType);
                    }
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    }
//                    else {
//                        sb.append(" DEFAULT NULL");
//                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("FieldCName") != null && !"".equals(fieldInfo.get("FieldCName"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("FieldCName")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r);";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * 获取存储过程Sql
     * @param dbUrl    数据库连接url
     * @param username 用户名
     * @param password 密码
     * @param dbName   库名
     * @return
     */
    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT DISTINCT CONCAT('`', routine_name, '`') AS procedure_name " +
                "FROM information_schema.routines " +
                "WHERE routine_type = 'PROCEDURE' AND routine_schema = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,dbName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String procedureName = rs.getString("procedure_name");
                // 对于每个存储过程名称，使用 SHOW CREATE PROCEDURE 获取创建语句
                PreparedStatement stCreateProcedure = conn.prepareStatement("SHOW CREATE PROCEDURE " + procedureName);
                ResultSet rsCreateProcedure = stCreateProcedure.executeQuery();
                if (rsCreateProcedure != null && rsCreateProcedure.next()) {
                    String createProcedureSql = rsCreateProcedure.getString("Create Procedure");
                    sqlList.add(createProcedureSql);
                }
                rsCreateProcedure.close();
                stCreateProcedure.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    /**
     * 获取函数sql
     * @param dbUrl    数据库连接url
     * @param username 用户名
     * @param password 密码
     * @param dbName   库名
     * @return
     */
    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT DISTINCT CONCAT('`', routine_name, '`') AS procedure_name " +
                "FROM information_schema.routines " +
                "WHERE routine_type = 'FUNCTION' AND routine_schema = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,dbName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String procedureName = rs.getString("procedure_name");
                // 对于每个存储过程名称，使用 SHOW CREATE PROCEDURE 获取创建语句
                PreparedStatement stCreateProcedure = conn.prepareStatement("SHOW CREATE FUNCTION " + procedureName);
                ResultSet rsCreateProcedure = stCreateProcedure.executeQuery();
                if (rsCreateProcedure != null && rsCreateProcedure.next()) {
                    String createProcedureSql = rsCreateProcedure.getString("Create Function");
                    sqlList.add(createProcedureSql);
                }
                rsCreateProcedure.close();
                stCreateProcedure.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    /**
     * 获取触发器sql
     * @param dbUrl    数据库连接url
     * @param username 用户名
     * @param password 密码
     * @param dbName   库名
     * @return
     */
    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT TRIGGER_NAME AS 'Trigger_Name', EVENT_MANIPULATION AS 'Event', " +
                "EVENT_OBJECT_TABLE AS 'Table_Name', ACTION_STATEMENT AS 'Action_Statement', " +
                " ACTION_TIMING AS 'Timing', TRIGGER_SCHEMA AS 'Database_Name' " +
                "FROM information_schema.TRIGGERS WHERE TRIGGER_SCHEMA = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String triggerSqlStr = "CREATE TRIGGER %s %s %s ON %s FOR EACH ROW %s;";
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,dbName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String triggerName = rs.getString("Trigger_Name");
                String timing = rs.getString("Timing");
                String event = rs.getString("Event");
                String tableName = rs.getString("Table_Name");
                String actionStatement = rs.getString("Action_Statement");
                String triggerSql = String.format(triggerSqlStr, triggerName, timing, event,tableName,actionStatement);
                sqlList.add(triggerSql);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    /**
     * 获取视图sql
     * @param dbUrl     数据库连接url
     * @param username  用户名
     * @param password  密码
     * @param inDBName  输入库名
     * @param outDBName 输出库名
     * @return
     */
    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT TABLE_NAME, VIEW_DEFINITION FROM information_schema.VIEWS " +
                "WHERE TABLE_SCHEMA = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,inDBName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String tableName = rs.getString("TABLE_NAME");
                // 对于每个存储过程名称，使用 SHOW CREATE PROCEDURE 获取创建语句
                PreparedStatement stCreate = conn.prepareStatement("SHOW CREATE VIEW `" + tableName + "`");
                ResultSet rsCreate = stCreate.executeQuery();
                if (rsCreate != null && rsCreate.next()) {
                    String createSql = rsCreate.getString(2);
                    createSql = createSql.replace("`"+inDBName+"`","`"+outDBName+"`");//库名替换
                    sqlList.add(createSql);
                }
                rsCreate.close();
                stCreate.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    /**
     * 获取序列sql
     * @param dbUrl    数据库连接url
     * @param username 用户名
     * @param password 密码
     * @param dbName   库名
     * @return
     */
    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    /**
     * 获取索引sql
     * @param dbUrl     数据库连接url
     * @param username  用户名
     * @param password  密码
     * @param inDBName
     * @param outDBName 输出库名
     * @return
     */
    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        // 索引建表时会自带，也可额外设置
        List<String> sqlList = new ArrayList<>();
        // 用于拼接列名的临时 Map
        Map<String, List<String>> indexColumnMap = new HashMap<>();
        String sql = "SELECT TABLE_SCHEMA, TABLE_NAME, INDEX_NAME, COLUMN_NAME, SEQ_IN_INDEX " +
                "FROM information_schema.STATISTICS " +
                "WHERE TABLE_SCHEMA = ? " +
                "ORDER BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, inDBName);
            rs = stmt.executeQuery();
            while (rs.next()) {
                String tableSchema = rs.getString("TABLE_SCHEMA");
                String tableName = rs.getString("TABLE_NAME");
                String indexName = rs.getString("INDEX_NAME");
                String columnName = rs.getString("COLUMN_NAME");

                // 跳过主键索引，因为主键应该使用 ALTER TABLE 或在创建表时指定
                if ("PRIMARY".equalsIgnoreCase(indexName)) {
                    continue;
                }

                // 使用表模式、表名和索引名作为键，索引列名列表作为值
                String key = tableSchema + "." + tableName + "." + indexName;
                indexColumnMap.computeIfAbsent(key, k -> new ArrayList<>()).add("`" + columnName + "`");
            }

            // 拼接创建索引的 SQL 语句
            for (Map.Entry<String, List<String>> entry : indexColumnMap.entrySet()) {
                String[] parts = entry.getKey().split("\\.");
                String tableSchema = parts[0];
                String tableName = parts[1];
                String indexName = parts[2];
                List<String> columns = entry.getValue();

                String createIndexSql = "CREATE INDEX `" + indexName + "` ON `" + outDBName + "`.`" + tableName + "`" +
                        " (" + String.join(", ", columns) + ");";
                sqlList.add(createIndexSql);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return sqlList;
    }

    public static void main(String[] args) {
        // 数据库URL，用户名和密码（根据你的数据库配置进行更改）
        String jdbcUrl ="jdbc:sinodbms-sqli://150.223.33.230:8035/pro:SINODBMSSERVER=lo_sinodb1210_1";
        String username = "sinodbms";
        String password = "sinodbms";
        String dbname = "pro";
        String tableName = "t_userinfo_10";

        Connection connection = null;
        try {
            // 1. 获取数据库连接
            connection = DriverManager.getConnection(jdbcUrl, username, password);

            // 2. 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();

            // 3. 获取指定表的字段信息
            // 注意：某些数据库可能不支持null作为catalogName和schemaPattern的参数，
            // 根据实际情况调整这些参数
            ResultSet rs = metaData.getColumns(null, null, tableName, null);
            // 4. 处理结果集
            while (rs.next()) {
                String columnName = rs.getString("column_name");
                String columnSize = rs.getString("column_size");
                String typeName = rs.getString("type_name");
                System.out.println(columnName);
                System.out.println(columnSize);
                System.out.println(typeName);
            }
            rs.close();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            // 5. 关闭连接
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println("执行完成");
    }


    /**
     * 获取所有非系统本身的数据库
     * @param conn
     * @param meta
     * @return
     * @throws SQLException
     */
    private static String[] getCatalogs(Connection conn, DatabaseMetaData meta) throws SQLException {
        ResultSet catalogs = meta.getCatalogs();
        List<String> catalogList = new ArrayList<>();
        while (catalogs.next()) {
            String databaseName = catalogs.getString(1);
            //不添加系统自带的库
            if (!(databaseName.equals("sysadmin") || databaseName.equals("sysmaster") || databaseName.equals("sysuser") ||databaseName.equals("sysutils") || databaseName.equals("testdb"))){
                catalogList.add(databaseName);
            }
        }
        return catalogList.toArray(new String[0]);
    }

    // 获取表备注的方法（根据SinoDB的实际情况，可能需要调整SQL查询）
    private static String getTableRemark(Connection connection, String tableName) throws SQLException {
        String remark = null;
        String sql = "SELECT REMARKS FROM SYS_TABLES WHERE TABLE_NAME = ?"; // 假设SYS_TABLES是存储表信息的系统表，REMARKS是备注字段
        PreparedStatement stmt = connection.prepareStatement(sql);
        stmt.setString(1, tableName);
        ResultSet rs = stmt.executeQuery();
        if (rs.next()) {
            remark = rs.getString("REMARKS");
        }
        rs.close();
        stmt.close();
        return remark;
    }

    // 获取表行数的方法
    private static long getTableRowCount(Connection connection, String tableName) throws SQLException {
        long rowCount = 0;
        String sql = "SELECT COUNT(*) FROM " + tableName;
        PreparedStatement stmt = connection.prepareStatement(sql);
        ResultSet rs = stmt.executeQuery();
        if (rs.next()) {
            rowCount = rs.getLong(1);
        }
        rs.close();
        stmt.close();
        return rowCount;
    }

    // 获取表数据大小的方法（这里提供一个基本的示例，可能需要根据SinoDB的实际情况进行调整）
    // 注意：这个方法可能不是直接获取表的数据大小，而是通过统计行数和数据类型来估算
    private static double getTableDataSizeMB(Connection connection, String tableName) throws SQLException {
        // 这里只是一个估算，实际实现可能需要查询SinoDB的系统表或使用数据库特定的函数来获取准确的数据大小
        // 假设每行数据大小约为1KB（这只是一个假设，实际情况会根据字段类型和数据量有所不同）
        long rowCount = getTableRowCount(connection, tableName);
        double dataSizeKB = rowCount * 1; // 每行1KB
        double dataSizeMB = dataSizeKB / 1024; // 转换为MB
        return dataSizeMB;
    }

    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 15:31
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        // 1. 从输入参数中获取数据库连接信息
        String driverProgram = map.get("driverprogram");
        String srcurl = map.get("srcurl");
        String username = map.get("username");
        String password = map.get("password");
        // 解密密码（如果需要）
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }

        String dbname = map.get("dbname");
        String srcPort = map.get("srcport");
        String srcIp = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 注册 JDBC 驱动
            Class.forName(driverProgram);

            // 2. 打开连接到 SinoDB 数据库
            // 注意：SinoDB 的 JDBC URL 格式可能因版本和配置而异，这里只是一个示例
            // 假设 SinoDB 的 JDBC URL 是这样的：*******************************************************************************,8859-1,819;

            String[] split = srcurl.split(":SINODBMSSERVER=");
            String strPath = split[1];
            String jdbcUrl = "jdbc:sinodbms-sqli://" + srcIp + ":" + srcPort + "/sysadmin"+":SINODBMSSERVER="+strPath; // 假设连接到 default 数据库以检查新数据库是否存在
            connection = DriverManager.getConnection(jdbcUrl, username, password);

            // 3. 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            // 假设 SinoDB 支持 getCatalogs 方法或者我们有其他方式来检查数据库是否存在
            // 如果不支持 getCatalogs，我们可能需要执行一个特定的查询来检查数据库
            resultSet = metaData.getCatalogs(); // 这个可能需要根据 SinoDB 的实际情况进行调整
            boolean databaseExists = false;
            while (resultSet.next()) {
                String databaseName = resultSet.getString(1);
                if (dbname.equals(databaseName)) {
                    databaseExists = true;
                    break;
                }
            }

            // 4. 如果数据库不存在，则创建它
            // 注意：在 SinoDB 中创建数据库的语句可能与 MySQL 不同
            if (!databaseExists) {
                // 假设 SinoDB 的创建数据库语句与标准 SQL 相似
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement = connection.createStatement();
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "JDBC驱动未找到：" + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
        return msgMap;
    }
}
