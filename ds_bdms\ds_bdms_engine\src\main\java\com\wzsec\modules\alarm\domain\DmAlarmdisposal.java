package com.wzsec.modules.alarm.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2023-04-07
*/
@Entity
@Data
@Table(name="t_mask_alarmdisposal")
public class DmAlarmdisposal implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 事件详情 */
    @Column(name = "circumstantiality")
    private String circumstantiality;


    /** 检测时间 */
    @Column(name = "checktime")
    private String checktime;

    /** 处理状态 */
    @Column(name = "treatmentstate")
    private String treatmentstate;

    /** 备注 */
    @Column(name = "note")
    private String note;

    /** 备用字段1 */
    @Column(name = "reservefield1")
    private String reservefield1;

    /** 产生事件的设备类型 */
    @Column(name = "reservefield2")
    private String reservefield2;

    /** 备用字段3 */
    @Column(name = "reservefield3")
    private String reservefield3;

    /** 备用字段4 */
    @Column(name = "reservefield4")
    private String reservefield4;


    /** 事件处置人员 */
    @Column(name = "incidenthandler")
    private String incidenthandler;

    /** 事件处置时间 */
    @Column(name = "eventhandlingtime")
    private String eventhandlingtime;

    /** 源IP */
    @Column(name = "sourceip")
    private String sourceip;

    /** 源端口 */
    @Column(name = "sourceport")
    private String sourceport;

    /** 目标IP */
    @Column(name = "destinationip")
    private String destinationip;

    /** 目标端口 */
    @Column(name = "destinationport")
    private String destinationport;

    /** 事件相关用户名 */
    @Column(name = "account")
    private String account;

    /** 规则 */
    @Column(name = "eventrule")
    private String eventrule;

    /** 推送次数 */
    @Column(name = "pushnumber")
    private String pushnumber;


    public void copy(DmAlarmdisposal source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}