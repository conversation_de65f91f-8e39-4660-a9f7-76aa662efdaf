package com.wzsec.dotask.mask.service.excute.audit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.dotask.sdd.service.excute.common.RuleManager;
import com.wzsec.modules.mask.domain.MaskAuditLogResult;
import com.wzsec.modules.mask.domain.MaskAuditLogResultdetail;
import com.wzsec.modules.mask.service.MaskAuditLogResultService;
import com.wzsec.modules.mask.service.MaskAuditLogResultdetailService;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1Dto;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.ConstEngine;
import com.wzsec.utils.StringUtils;
import com.wzsec.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;

/**
 * Title: MaskLogAudit
 * Decription:
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@Slf4j
public class MaskLogAudit {

    private static Map<String,Integer> resultTypeCountMap = new HashMap<>();
    private static Map<String,Integer> resultTotalCountMap = new HashMap<>();
    private static Map<String,Integer> resultTypeLinesMap = new HashMap<>();
    private static Map<String,Integer> totalLinesMap = new HashMap<>();
    private static Map<String,Map<String,Integer>> sensitivedataMap = new HashMap<>();
    private static Map<String,MaskAuditLogResultdetail> resultdetailMap = new HashMap<>();
    private static Map<String, MaskAuditLogResult> resultMap = new HashMap<>();
    private final DatasourceService datasourceService;
    private final MaskAuditLogResultdetailService maskAuditLogResultdetailService;
    private final MaskAuditLogResultService maskAuditLogResultService;

    public MaskLogAudit(DatasourceService datasourceService, MaskAuditLogResultdetailService maskAuditLogResultdetailService, MaskAuditLogResultService maskAuditLogResultService) {
        this.datasourceService = datasourceService;
        this.maskAuditLogResultdetailService = maskAuditLogResultdetailService;
        this.maskAuditLogResultService = maskAuditLogResultService;
    }

    /**
     * 审计接口日志
     * @param maskAuditTaskDto
     * @param ruleList
     * @param checkTime
     * @param submituser
     */
    public boolean maskAuditForLog(MaskAuditTaskV1Dto maskAuditTaskDto, List<RuleDto> ruleList, String checkTime, String submituser) {
        log.info("开始执行接口日志审计任务");
        boolean resultStatus = false;
        String cleanBeforePath = "";
        String taskName = maskAuditTaskDto.getTaskname();
        try {
            String intervalDay = ConstEngine.sddEngineConfs.get("maskaudit.log.interval.day").trim();
            String checkDate = ConstEngine.sddEngineConfs.get("maskaudit.log.date").trim();
            String cleanAfterPath = ConstEngine.sddEngineConfs.get("maskaudit.log.clean.path").trim();
            DatasourceDto datasource = datasourceService.findById(maskAuditTaskDto.getOutsourceid().longValue());
            String type = datasource.getType();
            if (Const.FILE_LOCAL.equals(type)){
                // 本地磁盘检测
                String filePath = maskAuditTaskDto.getOutfilepath();
                String filetype = maskAuditTaskDto.getFiletype();
                String[] fileDirArr = filePath.split(Matcher.quoteReplacement(File.separator));
                String dateDir = fileDirArr[fileDirArr.length - 1];

                if (checkTimeFormat(dateDir)){
                    cleanBeforePath = filePath;
                    if (cleanAfterPath.endsWith(File.separator)){
                        cleanAfterPath = cleanAfterPath + dateDir;
                    }else {
                        cleanAfterPath = cleanAfterPath + File.separator + dateDir;
                    }
                }else {
                    if (StringUtils.isNotEmpty(checkDate)){
                        if (filePath.endsWith(File.separator)){
                            cleanBeforePath = filePath + checkDate;
                        }else {
                            cleanBeforePath = filePath + File.separator + checkDate;
                        }

                        if (cleanAfterPath.endsWith(File.separator)){
                            cleanAfterPath = cleanAfterPath + checkDate;
                        }else {
                            cleanAfterPath = cleanAfterPath + File.separator + checkDate;
                        }
                    }else {
                        String yesterDayDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", Integer.parseInt(intervalDay));
                        if (filePath.endsWith(File.separator)){
                            cleanBeforePath = filePath + yesterDayDate;
                        }else {
                            cleanBeforePath = filePath + File.separator + yesterDayDate;
                        }
                        if (cleanAfterPath.endsWith(File.separator)){
                            cleanAfterPath = filePath + yesterDayDate;
                        }else {
                            cleanAfterPath = filePath + File.separator + yesterDayDate;
                        }
                    }
                }

                log.info("清洗前文件路径为："+cleanBeforePath);
                log.info("清洗后文件路径为："+cleanAfterPath);
                Map<String,String> fileNameMap = new HashMap<>();
                getAllFileName(cleanBeforePath,fileNameMap,filetype);

                // 清洗日志
                fileClean(fileNameMap,cleanAfterPath);

                // 检测清洗后日志
                dealDirData(cleanAfterPath,ruleList);

                // 结果统计
                recordCheckResult(checkTime,taskName);

                resultStatus = true;
            }
            log.info("接口日志审计成功");
        }catch (Exception e){
            resultStatus = false;
            e.printStackTrace();
            log.info("接口日志审计失败");
        }finally {
            resultdetailMap = new HashMap<>();
            resultMap = new HashMap<>();
            resultTotalCountMap = new HashMap<>();
            resultTypeCountMap =new HashMap<>();
            sensitivedataMap = new HashMap<>();
        }


        return resultStatus;
    }

    private void recordCheckResult(String checkTime,String taskName) {
        // 详情结果
        for (String key : resultdetailMap.keySet()) {
            MaskAuditLogResultdetail resultdetail = resultdetailMap.get(key);
            Integer count = 0;
            Integer totalCount = 0;
            String[] keys = key.split(Const.AUDIT_SPLIT_JOIN);
            String strKey = keys[0] + Const.AUDIT_SPLIT_JOIN +
                    keys[1] + Const.AUDIT_SPLIT_JOIN +
                    keys[2] + Const.AUDIT_SPLIT_JOIN +
                    keys[3] + Const.AUDIT_SPLIT_JOIN +
                    keys[4] + Const.AUDIT_SPLIT_JOIN +
                    keys[5];
            // 统计次数
            if (resultTypeCountMap.containsKey(key)){
                count = resultTypeCountMap.get(key);
                resultdetail.setCheckcount(count);
            }
            // 检测总次数
            if (resultTotalCountMap.containsKey(strKey)){
                totalCount = resultTotalCountMap.get(strKey);
                resultdetail.setTotalcount(totalCount);
            }
            // 比例
            if (count != 0 && totalCount != 0){
                double rate = 100 * ((double) count / totalCount);
                BigDecimal bDec = new BigDecimal(rate);
                double ratio = bDec.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                resultdetail.setRatio(String.valueOf(ratio));
            }
            // 敏感数据
            if (sensitivedataMap.containsKey(key)){
                Map<String, Integer> map = sensitivedataMap.get(key);
                resultdetail.setSensitivedata(map.toString().replace("=",":"));
            }
            // 检测总行数
            if (totalLinesMap.containsKey(strKey)){
                Integer linesTotalCount = totalLinesMap.get(strKey);
                resultdetail.setTotallinescount(linesTotalCount);
            }
            // 统计行数
            if (resultTypeLinesMap.containsKey(key)){
                Integer linesCount = resultTypeLinesMap.get(key);
                resultdetail.setChecklinescount(linesCount);
            }
            resultdetail.setTaskname(taskName);
            resultdetail.setChecktime(checkTime);
            maskAuditLogResultdetailService.create(resultdetail);
        }
        log.info("详情结果统计完成");

        for (String key : resultMap.keySet()) {
            MaskAuditLogResult result = resultMap.get(key);
            if (resultTypeCountMap.containsKey(key)){
                Integer count = resultTypeCountMap.get(key);
                result.setCheckcount(count);
            }
            result.setTaskname(taskName);
            result.setChecktime(checkTime);
            maskAuditLogResultService.create(result);
        }
        log.info("概要结果统计完成");
    }


    /**
     * 处理清洗过后的单行数据
     * @param sline
     */
    private static void dealCleanedSingleLine(String sline,List<RuleDto> ruleList) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(sline);
        String custName = jsonObject.getString("custname");
        String custSimplename = jsonObject.getString("cust_simplename");
        String userId = jsonObject.getString("userid");
        String appId = jsonObject.getString("appid");
        String appName = jsonObject.getString("appname");
        String logSign = jsonObject.getString("logsign");
        String apiCode = jsonObject.getString("apicode");
        String apiName = jsonObject.getString("apiname");
        String apiMethod = jsonObject.getString("apimethod");
        String apiDes = jsonObject.getString("apides");
        String apiType = jsonObject.getString("apitype");
        String url = jsonObject.getString("url");
        String inputParams = jsonObject.getString("inputparams");
        String inparamMean = jsonObject.getString("inparam_mean");
        String inparamFormat = jsonObject.getString("inparam_format");
        String inparamNote = jsonObject.getString("inparam_note");
        String outputParams = jsonObject.getString("outputparams");
        String outparamMean = jsonObject.getString("outparam_mean");
        String outparamFormat = jsonObject.getString("outparam_format");
        String outparamNote = jsonObject.getString("outparam_note");
        String beginDate = jsonObject.getString("begindate");
        String endDate = jsonObject.getString("enddate");
        String cleanInputParams = jsonObject.getString("cleaninputparams");
        String cleanOutputParams = jsonObject.getString("cleanoutputparams");

        // 输出参数
        String checkparam = "value";

        String key = custSimplename + Const.AUDIT_SPLIT_JOIN +
                userId + Const.AUDIT_SPLIT_JOIN +
                appId + Const.AUDIT_SPLIT_JOIN +
                logSign + Const.AUDIT_SPLIT_JOIN +
                url + Const.AUDIT_SPLIT_JOIN +
                checkparam;
        // 统计接口检测总行数
        setTotalLines(key);
        // 统计输出参数
        String[] outputParamData = JSON.parseObject(cleanOutputParams, String[].class);
        setTotalCount(key,outputParamData.length);
        String example = "";
        for (String param : outputParamData) {
            String risk = Const.MASKAUDIT_RESULT_LOW; // 低
            String resultType = Const.MASKAUDIT_RESULTTYPE_NORMAL;
            for (RuleDto ruleDto : ruleList) {
                boolean isSensitive = RuleManager.checkDataByRuleDto(param, ruleDto,null);
                if (isSensitive){
                    resultType = ruleDto.getApplytypecname();
                    if ("".equals(example)){
                        example = sline;
                    }
                    risk = Const.MASKAUDIT_RESULT_HIGH; // 高
                    String detailkey = key + Const.AUDIT_SPLIT_JOIN + risk + Const.AUDIT_SPLIT_JOIN + resultType;
                    setsensitivedataCount(detailkey,param);
                    continue;
                }
            }

            String resKey = key + Const.AUDIT_SPLIT_JOIN + risk;
            String detailkey = key + Const.AUDIT_SPLIT_JOIN + risk + Const.AUDIT_SPLIT_JOIN + resultType;
            setResultTypeCount(resKey);
            setResultTypeCount(detailkey);
            setResultTypeLines(detailkey);
            if (!resultdetailMap.containsKey(key)){
                MaskAuditLogResultdetail resultdetail = new MaskAuditLogResultdetail();
                resultdetail.setCustname(custName);
                resultdetail.setCustSimplename(custSimplename);
                resultdetail.setUserid(userId);
                resultdetail.setAppid(appId);
                resultdetail.setAppname(appName);
                resultdetail.setLogsign(logSign);
                resultdetail.setApicode(apiCode);
                resultdetail.setApiname(apiName);
                resultdetail.setApimethod(apiMethod);
                resultdetail.setApitype(apiType);
                resultdetail.setUrl(url);
                resultdetail.setCheckparam(checkparam);
                resultdetail.setParamMean(outparamMean);
                resultdetail.setParamNote(outparamNote);
                if (Const.MASKAUDIT_RESULT_HIGH.equals(risk)){
                    resultdetail.setExample(example);
                }
                resultdetail.setRisk(risk);
                resultdetail.setResulttype(resultType);
                resultdetailMap.put(detailkey,resultdetail);
            }
            if (!resultMap.containsKey(resKey)){
                MaskAuditLogResult result = new MaskAuditLogResult();
                result.setCustname(custName);
                result.setCustSimplename(custSimplename);
                result.setUserid(userId);
                result.setAppid(appId);
                result.setAppname(appName);
                result.setLogsign(logSign);
                result.setApicode(apiCode);
                result.setApiname(apiName);
                result.setApimethod(apiMethod);
                result.setApitype(apiType);
                result.setUrl(url);
                result.setRisk(risk);
                resultMap.put(resKey,result);
            }
        }


    }

    /**
     * 结果类型统计行数
     * @param key
     */
    private static void setResultTypeLines(String key) {
        if (resultTypeLinesMap.containsKey(key)){
            Integer count = resultTypeLinesMap.get(key);
            resultTypeLinesMap.put(key,count+1);
        }else {
            resultTypeLinesMap.put(key,1);
        }
    }

    /**
     * 统计总行数
     * @param key
     */
    private static void setTotalLines(String key) {
        if (totalLinesMap.containsKey(key)){
            Integer count = totalLinesMap.get(key);
            totalLinesMap.put(key,count+1);
        }else {
            totalLinesMap.put(key,1);
        }
    }

    /**
     * 统计结果类型统计次数
     * @param key
     */
    private static void setResultTypeCount(String key) {
        if (resultTypeCountMap.containsKey(key)){
            Integer count = resultTypeCountMap.get(key);
            resultTypeCountMap.put(key,count+1);
        }else {
            resultTypeCountMap.put(key,1);
        }
    }

    /**
     * 统计结果类型统计次数
     * @param key
     */
    private static void setTotalCount(String key,Integer dataLength) {
        if (resultTotalCountMap.containsKey(key)){
            Integer count = resultTotalCountMap.get(key);
            resultTotalCountMap.put(key,count+dataLength);
        }else {
            resultTotalCountMap.put(key,dataLength);
        }
    }

    /**
     * 统计敏感数据出现的次数
     * @param key
     */
    private static void setsensitivedataCount(String key,String param) {
        if (sensitivedataMap.containsKey(key)){
            Map<String, Integer> map = sensitivedataMap.get(key);
            if (map.containsKey(param)){
                Integer count = map.get(param);
                map.put(param,count+1);
            }else {
                map.put(param,1);
            }
        }else {
            Map<String, Integer> map = new HashMap<>();
            map.put(param,1);
            sensitivedataMap.put(key,map);
        }
    }

    /**
     * 文件清洗
     * @param fileNameMap
     * @param cleanAfterPath
     */
    private void fileClean(Map<String,String> fileNameMap,String cleanAfterPath) {
        try {
            for (String key : fileNameMap.keySet()) {
                cleanAfterPath = cleanAfterPath + File.separator + "clean_" +fileNameMap.get(key);
                FileInputStream inputStream = new FileInputStream(key);
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                String str = null;
                while ((str = bufferedReader.readLine()) != null) {
                    cleanLog(str,cleanAfterPath);
                }
                log.info("清洗完成,文件为："+key);
            }
        }catch (Exception e){
            log.info("文件检测出现异常："+e.getMessage());
            e.printStackTrace();
        }

    }

    /**
     * 清洗日志
     * @param str
     */
    private void cleanLog(String str,String cleanAfterPath) {
        JSONObject jsonObject = JSONObject.parseObject(str);
        JSONObject cleanJson = new JSONObject(true);
        String custName = jsonObject.getString("custname");
        String custSimplename = jsonObject.getString("cust_simplename");
        String userId = jsonObject.getString("userid");
        String appId = jsonObject.getString("appid");
        String appName = jsonObject.getString("appname");
        String logSign = jsonObject.getString("logsign");
        String apiCode = jsonObject.getString("apicode");
        String apiName = jsonObject.getString("apiname");
        String apiMethod = jsonObject.getString("apimethod");
        String apiDes = jsonObject.getString("apides");
        String apiType = jsonObject.getString("apitype");
        String url = jsonObject.getString("url");
        String inputParams = jsonObject.getString("inputparams");
        String inparamMean = jsonObject.getString("inparam_mean");
        String inparamFormat = jsonObject.getString("inparam_format");
        String inparamNote = jsonObject.getString("inparam_note");
        String outputParams = jsonObject.getString("outputparams");
        String outparamMean = jsonObject.getString("outparam_mean");
        String outparamFormat = jsonObject.getString("outparam_format");
        String outparamNote = jsonObject.getString("outparam_note");
        String beginDate = jsonObject.getString("begindate");
        String endDate = jsonObject.getString("enddate");

        cleanJson.put("custname",custName);
        cleanJson.put("cust_simplename",custSimplename);
        cleanJson.put("userid",userId);
        cleanJson.put("appid",appId);
        cleanJson.put("appname",appName);
        cleanJson.put("logsign",logSign);
        cleanJson.put("apicode",apiCode);
        cleanJson.put("apiname",apiName);
        cleanJson.put("apimethod",apiMethod);
        cleanJson.put("apides",apiDes);
        cleanJson.put("apitype",apiType);
        cleanJson.put("url",url);
        cleanJson.put("inputparams",inputParams);
        cleanJson.put("inparam_mean",inparamMean);
        cleanJson.put("inparam_format",inparamFormat);
        cleanJson.put("inparam_note",inparamNote);
        cleanJson.put("outputparams",outputParams);
        cleanJson.put("outparam_mean",outparamMean);
        cleanJson.put("outparam_format",outparamFormat);
        cleanJson.put("outparam_note",outparamNote);
        cleanJson.put("begindate",beginDate);
        cleanJson.put("enddate",endDate);
        cleanJson.put("cleaninputparams", JSON.toJSONString(clearParams(inputParams)));
        cleanJson.put("cleanoutputparams",JSON.toJSONString(clearParams(outputParams)));

        // 保存到清洗后文件下
        append2File(cleanJson.toJSONString(),cleanAfterPath);
    }

    public static void main(String[] args) {
        String strr="{\"value\":{\"id\":\"1\",\"username\":\"蔡育\",\"age\":\"20\",\"address\":\"北京市朝阳区\",\"idcard\":\"******************\"},\"email\":\"<EMAIL>\",\"imsi\":\"***************\"}";
        List<String> strings = clearParams(strr);
        System.out.println(strings);
    }

    /**
     * 对参数进行清洗切分
     * @param str
     * @return
     */
    public static List<String> clearParams(String str){
        String[] datas = str.split(",|:|\"|\\{|}|\\[| |]");
        List<String> dataString = new ArrayList<String>();
        for (String data : datas) {
            if (!data.equals("")&&!data.equals(" ")) {
                dataString.add(data);
            }
        }
        return dataString;
    }

    /*
     *@Decription 追加日志到文件
     *<AUTHOR>
     *@date 2020/7/2
     */
    public static void append2File(String strLine, String path){
        // 判断文件夹是否存在
        String[] paths = path.split(Matcher.quoteReplacement(File.separator));
        StringBuffer fullPath = new StringBuffer();
        for (int i = 0; i < paths.length; i++) {
            fullPath.append(paths[i]).append(File.separator);
            File file = new File(fullPath.toString());
            if (paths.length - 1 != i) {
                if (!file.exists()) {
                    file.mkdir();
                }
            }
        }
        FileOutputStream fos;
        try {
            fos = new FileOutputStream(path, true);
            fos.write((strLine + "\n").getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.info("清洗数据保存:"+path+"出现异常!");
        }
    }


    /**
     * 验证YYYYMMDD格式字符串
     * @param str
     * @return
     */
    public boolean checkTimeFormat(String str){
        String pattern = "((\\d{3}[1-9]|\\d{2}[1-9]\\d|\\d[1-9]\\d{2}|[1-9]\\d{3})(((0[13578]|1[02])(0[1-9]|[12]\\d|3[01]))|((0[469]|11)(0[1-9]|[12]\\d|30))|(02(0[1-9]|[1]\\d|2[0-8]))))|(((\\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229)";
        boolean matches = str.matches(pattern);
        if(matches){
            return true;
        }else {
            return false;
        }
    }



    /**
     * 获取一个文件夹下的所有文件全路径和文件名
     *
     * @param path
     * @param fileNameMap
     * <AUTHOR> by wangqi
     * @date 2020-11-02
     */
    public static void getAllFileName(String path, Map<String, String> fileNameMap,String fileType) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    if (Const.MASKAUDIT_FILETYPE_TXT.equals(fileType)){
                        if (f.getAbsolutePath().endsWith("txt")) {
                            fileNameMap.put(f.getAbsolutePath(), f.getName());
                        }
                    }else if (Const.MASKAUDIT_FILETYPE_EXCEL.equals(fileType)){
                        if (f.getAbsolutePath().endsWith("xls") || f.getAbsolutePath().endsWith("xlsx")) {
                            fileNameMap.put(f.getAbsolutePath(), f.getName());
                        }
                    }else if (Const.MASKAUDIT_FILETYPE_CSV.equals(fileType)){
                        if (f.getAbsolutePath().endsWith("csv")) {
                            fileNameMap.put(f.getAbsolutePath(), f.getName());
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理数据
     * @param strInput
     */
    private void dealDirData(String strInput,List<RuleDto> ruleList) {
        try {
            File inDir = new File(strInput);
            File[] tempList = inDir.listFiles();
            for(int i = 0; i < tempList.length; i++) {
                dealSingleFile(tempList[i].toString(),ruleList);
            }
        }
        catch(Exception ex){
            ex.printStackTrace();
        }
    }


    /**
     *@Description:处理单个文件
     *<AUTHOR> by wangqi
     *@date 2021-01-19
     */
    public static void dealSingleFile(String strInput,List<RuleDto> ruleList) {
        BufferedReader br = null;
        try {
            String f_name = "";
            File f_Source = new File(strInput);
            f_name = f_Source.getName();
            br = new BufferedReader(new FileReader(f_Source));

            String sline = null;
            long startTime = System.currentTimeMillis();
            while((sline=br.readLine()) != null) {
                System.out.println("处理行："+sline);
                dealCleanedSingleLine(sline,ruleList);
            }

            long endTime = System.currentTimeMillis();
            long useTime = endTime - startTime;
            System.out.println("处理文件'"+f_name+"'用时:" + useTime +"ms");
        }
        catch(Exception ex) {
            ex.printStackTrace();
        }
        finally {
            try {
                br.close();
            }
            catch(Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}
