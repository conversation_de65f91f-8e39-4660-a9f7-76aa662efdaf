package com.wzsec.dotask.mask.service.impl;

import com.wzsec.dotask.mask.service.DoMaskAuditTaskService;
import com.wzsec.dotask.mask.service.excute.audit.*;
import com.wzsec.modules.mask.domain.DBTaskConfig;
import com.wzsec.modules.mask.domain.FileTaskConfig;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.*;
import com.wzsec.modules.mask.service.mapper.MaskAuditTaskV1Mapper;
import com.wzsec.modules.sdd.metadata.domain.MetaField;
import com.wzsec.modules.sdd.metadata.service.MetaFieldService;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.strategy.service.StrategyService;
import com.wzsec.modules.sdd.strategy.service.dto.StrategyDto;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


// 默认不使用缓存
//import org.springframework.cache.annotation.CacheConfig;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR>
 * @date 2021-01-14
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "maksAuditTask")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoMaskAuditTaskServiceImpl_v1 implements DoMaskAuditTaskService {


    private final MaskAuditTaskV1Service maskAuditTaskService;
    private final MaskAuditTaskV1Mapper maskAuditTaskV1Mapper;
    private final DatasourceService datasourceService;
    private final StrategyService strategyService;
    private final RuleService ruleService;
    private final MaskAuditResultdetailV1Service maskAuditResultdetailV1Service;
    private final MaskAuditResultV1Service maskAuditResultV1Service;
    private final MaskAuditLogResultdetailService maskAuditLogResultdetailService;
    private final MaskAuditLogResultService maskAuditLogResultService;
    private final MaskAuditJarLogResultService maskAuditJarLogResultService;
    private final DBTaskConfigService dbTaskConfigService;
    private final DbBatchTaskConfigService dbBatchTaskConfigService;
    private final FileTaskConfigService fileTaskConfigService;
    private final MaskStrategyTableService maskStrategyTableService;
    private final MaskStrategyFieldService maskStrategyFieldService;
    private MetaFieldService metaFieldService;
    private final MaskStrategyFileMainService maskStrategyFileMainService;
    private final MaskStrategyFileFormatSubService maskStrategyFileFormatSubService;


    public DoMaskAuditTaskServiceImpl_v1(MaskAuditTaskV1Service maskAuditTaskService, MaskAuditTaskV1Mapper maskAuditTaskV1Mapper,
                                         DatasourceService datasourceService, StrategyService strategyService, RuleService ruleService,
                                         MaskAuditResultV1Service maskAuditResultV1Service, MaskAuditResultdetailV1Service maskAuditResultdetailV1Service,
                                         MaskAuditLogResultdetailService maskAuditLogResultdetailService, MaskAuditLogResultService maskAuditLogResultService,
                                         MaskAuditJarLogResultService maskAuditJarLogResultService, DBTaskConfigService dbTaskConfigService,
                                         DbBatchTaskConfigService dbBatchTaskConfigService,FileTaskConfigService fileTaskConfigService,
                                         MaskStrategyTableService maskStrategyTableService, MaskStrategyFieldService maskStrategyFieldService,
                                         MaskStrategyFileMainService maskStrategyFileMainService, MaskStrategyFileFormatSubService maskStrategyFileFormatSubService) {
        this.maskAuditTaskService = maskAuditTaskService;
        this.maskAuditTaskV1Mapper = maskAuditTaskV1Mapper;
        this.datasourceService = datasourceService;
        this.strategyService = strategyService;
        this.ruleService = ruleService;
        this.maskAuditResultV1Service = maskAuditResultV1Service;
        this.maskAuditResultdetailV1Service = maskAuditResultdetailV1Service;
        this.maskAuditLogResultdetailService = maskAuditLogResultdetailService;
        this.maskAuditLogResultService = maskAuditLogResultService;
        this.maskAuditJarLogResultService = maskAuditJarLogResultService;
        this.dbTaskConfigService = dbTaskConfigService;
        this.dbBatchTaskConfigService = dbBatchTaskConfigService;
        this.fileTaskConfigService = fileTaskConfigService;
        this.maskStrategyTableService = maskStrategyTableService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.metaFieldService = metaFieldService;
        this.maskStrategyFileMainService = maskStrategyFileMainService;
        this.maskStrategyFileFormatSubService = maskStrategyFileFormatSubService;
    }

    @Async//异步执行
    @Override
    public void execution(Integer id, String submituser) {
        boolean result = false;
        MaskAuditTaskV1Dto maskAuditTaskDto = maskAuditTaskService.findById(id);
        log.info("脱敏审计开始执行");
        log.info("开始执行脱敏审计任务，ID：" + id);
        // 检测时间
        String checkTime = TimeUtils.getNowTime();
        // 获取敏感数据策略
        List<StrategyDto> strategyDtoList = strategyService.findAllByIdsStr(maskAuditTaskDto.getDisstrategy());//未考虑规则禁用启用，只要任务中选了就可以使用
        // 从策略中获取规则id并去重
        Set<Long> ruleidsSet = new HashSet<>();//用于去重
        for (StrategyDto strategyDto : strategyDtoList) {
            String[] ruleids = strategyDto.getRuleids().split(",");
            ruleidsSet.addAll(Arrays.asList(ArrayUtil.stringToLong(ruleids)));
        }
        String ruleidsStr = StringUtils.join(ruleidsSet, ",");
        // 查询敏感数据规则
        List<RuleDto> ruleList = ruleService.findAllByIdsStr(ruleidsStr);//未考虑规则禁用启用，只要策略中选了就可以使用

        String maskTaskNumber = maskAuditTaskDto.getMasktask();
        //key为表名或文件名；value为字段或列对应的算法信息
        Map<String, Map<String, String>> tableAlgorithmInfo = getTableAlgorithmInfo(maskTaskNumber);

        try {
            String checktype = maskAuditTaskDto.getChecktype();
            if (Const.MASKAUDIT_CHECKTYPE_DB.equals(checktype)) {
                String auditTableName = maskAuditTaskDto.getTablename().toLowerCase();
                Map<String, String> lineAlgorithmInfo = null;
                for (Map.Entry<String, Map<String, String>> entry : tableAlgorithmInfo.entrySet()) {
                    //脱敏后的表可能表名不变，也可能添加_mask_时间后缀，都比对一下
                    String tableName = entry.getKey().toLowerCase();
                    String maskTableName = entry.getKey().toLowerCase() + "_mask_";
                    if (auditTableName.equals(tableName) || auditTableName.startsWith(maskTableName)){
                        lineAlgorithmInfo = entry.getValue();
                        break;
                    }
                }
                // 库表
                MaskDatabaseAudit maskDatabaseAudit = new MaskDatabaseAudit(datasourceService, maskAuditResultdetailV1Service, maskAuditResultV1Service);
                result = maskDatabaseAudit.maskAuditForDB(maskAuditTaskDto, ruleList, checkTime, submituser, lineAlgorithmInfo);
            } else if (Const.MASKAUDIT_CHECKTYPE_FILE.equals(checktype)) {
                Map<String, String> lineAlgorithmInfo = new LinkedHashMap<>();
                Integer index = 0;
                for (Map.Entry<String, Map<String, String>> entry : tableAlgorithmInfo.entrySet()) {
                    for (Map.Entry<String, String> stringEntry : entry.getValue().entrySet()) {
                        index++;
                        lineAlgorithmInfo.put(index.toString(), stringEntry.getValue());
                    }
                    break;
                }
                // 文件
                MaskFileAudit maskFileAudit = new MaskFileAudit(datasourceService, maskAuditResultdetailV1Service, maskAuditResultV1Service);
                result = maskFileAudit.maskAuditForFile(maskAuditTaskDto, ruleList, checkTime, submituser, lineAlgorithmInfo);
            } else if (Const.MASKAUDIT_CHECKTYPE_LOG.equals(checktype)) {
                // 接口日志
                MaskLogAudit maskLogAudit = new MaskLogAudit(datasourceService, maskAuditLogResultdetailService, maskAuditLogResultService);
                result = maskLogAudit.maskAuditForLog(maskAuditTaskDto, ruleList, checkTime, submituser);
            } else if (Const.MASKAUDIT_CHECKTYPE_JARLOG.equals(checktype)) {
                // 脱敏Jar包  hive控制台方式调用算法包 日志格式
                MaskJarLogAudit maskJarLogAudit = new MaskJarLogAudit(datasourceService, maskAuditJarLogResultService);
                result = maskJarLogAudit.maskAuditForJarLog(maskAuditTaskDto, checkTime, submituser);
            } else if (Const.MASKAUDIT_CHECKTYPE_PRO_JARLOG.equals(checktype)) {
                // 脱敏程序Jar包 TODO 页面字典未配置此类型:5,与脱敏Jar包输出的结果表相同
                MaskProJarLogAudit maskProJarLogAudit = new MaskProJarLogAudit(datasourceService, maskAuditJarLogResultService);
                result = maskProJarLogAudit.maskAuditForJarLog(maskAuditTaskDto, checkTime, submituser);
            }
            // 更新任务
            if (result) {
                maskAuditTaskDto.setExecutionstate(Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_SUCCESS);
                maskAuditTaskService.update(maskAuditTaskV1Mapper.toEntity(maskAuditTaskDto));
            } else {
                maskAuditTaskDto.setExecutionstate(Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_FAIL);
                maskAuditTaskService.update(maskAuditTaskV1Mapper.toEntity(maskAuditTaskDto));
            }

            log.info("更新任务执行状态成功");
            log.info("脱敏审计任务执行完成");
        } catch (Exception e) {
            log.info("错误信息：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 通过任务号，获取表算法信息
     * @param maskTaskNumber
     * @return
     */
    public Map<String,Map<String, String>> getTableAlgorithmInfo(String maskTaskNumber){
        //key为表名或文件名；value为字段或列对应的算法信息
        Map<String,Map<String, String>> tableAlgorithmInfo = new LinkedHashMap<>();
        //key为字段名或当前列数，value为该字段或当前列数对应的算法名
        Map<String, String> LineAlgorithmInfo = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(maskTaskNumber)){
            if (maskTaskNumber.startsWith(Const.MASK_DB_TASKNUM_PREFIX)){ //数据库单表脱敏
                DBTaskConfig dbTaskConfig = dbTaskConfigService.findByTaskName(maskTaskNumber);//查询单表脱敏任务配置
                if (dbTaskConfig != null) {
                    String strategyId = dbTaskConfig.getStrategyid();
                    getAlgorithmByTableStrategyType(dbTaskConfig.getTablename(),
                            Long.valueOf(dbTaskConfig.getInputdatasourceid()), strategyId, LineAlgorithmInfo, tableAlgorithmInfo);
                }
            } else if (maskTaskNumber.startsWith(Const.MASK_DB_BATCH_TASKNUM_PREFIX)){ //数据库多表脱敏
                List<Map<String, Object>> dbBatchTaskConfig = dbBatchTaskConfigService.findByTaskName(maskTaskNumber);//查询多表脱敏任务配置
                if (dbBatchTaskConfig != null) {
                    for (Map<String, Object> map : dbBatchTaskConfig) {
                        String strategyId = map.get("strategyid").toString();
                        String tableName = map.get("tablename").toString();
                        Long sourceId = Long.valueOf(map.get("sourceid").toString());
                        getAlgorithmByTableStrategyType(tableName,sourceId, strategyId, LineAlgorithmInfo, tableAlgorithmInfo);
                    }
                }

            } else if (maskTaskNumber.startsWith(Const.MASK_FILE_TASKNUM_PREFIX)){ //文件脱敏
                FileTaskConfig fileTaskConfig = fileTaskConfigService.findByTaskName(maskTaskNumber);//查询文件脱敏任务配置
                if (fileTaskConfig != null) {
                    Integer strategyId = fileTaskConfig.getStrategyid();
                    MaskStrategyFileMainDto strategyMainDto = maskStrategyFileMainService.findById(strategyId);
                    if (Const.FILETASKTYPE_FORMATTING.equals(strategyMainDto.getStrategytype())){
                        List<Map<String, Object>> algoInfo = maskStrategyFileFormatSubService.getAllStrategyAlgoInfoByStrategyId(strategyId);
                        for (Map<String, Object> map : algoInfo) {
                            String algorithmName = map.get("algorithmname")!=null?map.get("algorithmname").toString():"";
                            String columnDesc = map.get("columndesc").toString();
                            LineAlgorithmInfo.put(columnDesc, algorithmName);
                        }
                        tableAlgorithmInfo.put(StringUtils.isNotEmpty(fileTaskConfig.getTablename()) ?
                                fileTaskConfig.getTablename() : fileTaskConfig.getInputpath(), LineAlgorithmInfo);
                    } else {
                        log.info("非格式化文件任务无法获取每列脱敏算法。");
                    }
                }
            }
        }

        return tableAlgorithmInfo;
    }

    /**
     * 通过表策略类型(单表策略、全局策略)，获取算法信息
     * @param tableName
     * @param DataSourceId
     * @param strategyId
     * @param LineAlgorithmInfo
     * @param tableAlgorithmInfo
     */
    public void getAlgorithmByTableStrategyType(String tableName, Long DataSourceId, String strategyId,
                                       Map<String, String> LineAlgorithmInfo, Map<String,Map<String, String>> tableAlgorithmInfo){
        MaskStrategyTableDto tableStrategy = maskStrategyTableService.findById(Integer.valueOf(strategyId));
        if (Const.STRATEGY_TYPE_SINGLETABLE.equals(tableStrategy.getStrategytype())){
            //通过策略id查询单表策略对应的算法信息
            List<Map<String, Object>> maskAlgoInfoByStrategyId = maskStrategyFieldService.getMaskAlgoInfoByStrategyId(strategyId);
            for (Map<String, Object> map : maskAlgoInfoByStrategyId) {
                String algorithmName = map.get("algorithmname")!=null?map.get("algorithmname").toString():"";
                String fieldEName = map.get("fieldename").toString();
                LineAlgorithmInfo.put(fieldEName, algorithmName);
            }
            tableAlgorithmInfo.put(tableName, LineAlgorithmInfo);
        } else {
            //查询元数据对应的标准化中文名、全局策略配置的算法信息
            List<MetaField> metaFieldsList = metaFieldService.findInfoFieldInfoByTabNameSourceId(tableName, DataSourceId);
            List<Map<String, Object>> algoInfo = maskStrategyTableService.getAllStrategyAlgoInfoByStrategyId(strategyId);
            //标准化中文名与全局策略的数据信息能对上，说明该字段使用了全局策略里的算法
            for (MetaField metaField : metaFieldsList) {
                String fieldName = metaField.getFieldname();
                String algoName = null;
                String fieldSCname = metaField.getFieldscname();
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(fieldSCname)) {
                    for (Map<String, Object> map : algoInfo) {
                        String dataName = map.get("dataname").toString();
                        if (dataName.equals(fieldSCname)) {
                            algoName = map.get("algenglishname") == null ? null : map.get("algenglishname").toString();
                            break;
                        }
                    }
                }
                LineAlgorithmInfo.put(fieldName, algoName);
            }
            tableAlgorithmInfo.put(tableName, LineAlgorithmInfo);
        }
    }

}
