package com.wzsec.modules.api;

import com.wzsec.modules.api.server.HttpProxyServer;

/**
 * @Description 普通代理模式，暂未使用  张坤祥 2021年6月23日19:06:36
 * <AUTHOR>
 * @Date 2021/2/8 17:30
 */
public class HttpProxyServerApp {
    public static void main(String[] args) {
        System.out.println("APIMask-Engine server start");
        int port = 9999;
        if (args.length > 0) {
            port = Integer.valueOf(args[0]);
        }
        new HttpProxyServer().start(port);

    }
}
