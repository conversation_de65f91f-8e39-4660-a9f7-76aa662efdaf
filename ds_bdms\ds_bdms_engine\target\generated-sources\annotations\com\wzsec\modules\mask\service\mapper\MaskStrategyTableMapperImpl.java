package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyTable;
import com.wzsec.modules.mask.service.dto.MaskStrategyTableDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskStrategyTableMapperImpl implements MaskStrategyTableMapper {

    @Override
    public MaskStrategyTableDto toDto(MaskStrategyTable entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyTableDto maskStrategyTableDto = new MaskStrategyTableDto();

        maskStrategyTableDto.setCreatetime( entity.getCreatetime() );
        maskStrategyTableDto.setCreateuser( entity.getCreateuser() );
        maskStrategyTableDto.setDbname( entity.getDbname() );
        maskStrategyTableDto.setEnabled( entity.getEnabled() );
        maskStrategyTableDto.setId( entity.getId() );
        maskStrategyTableDto.setLevelVal( entity.getLevelVal() );
        maskStrategyTableDto.setMemo( entity.getMemo() );
        maskStrategyTableDto.setSourcetype( entity.getSourcetype() );
        maskStrategyTableDto.setSparefield1( entity.getSparefield1() );
        maskStrategyTableDto.setSparefield2( entity.getSparefield2() );
        maskStrategyTableDto.setSparefield3( entity.getSparefield3() );
        maskStrategyTableDto.setSparefield4( entity.getSparefield4() );
        maskStrategyTableDto.setSparefield5( entity.getSparefield5() );
        maskStrategyTableDto.setStatus( entity.getStatus() );
        maskStrategyTableDto.setStrategydesc( entity.getStrategydesc() );
        maskStrategyTableDto.setStrategyname( entity.getStrategyname() );
        maskStrategyTableDto.setStrategytype( entity.getStrategytype() );
        maskStrategyTableDto.setTabcname( entity.getTabcname() );
        maskStrategyTableDto.setTabename( entity.getTabename() );
        maskStrategyTableDto.setTabid( entity.getTabid() );
        maskStrategyTableDto.setUpdatetime( entity.getUpdatetime() );
        maskStrategyTableDto.setUpdateuser( entity.getUpdateuser() );

        return maskStrategyTableDto;
    }

    @Override
    public List<MaskStrategyTableDto> toDto(List<MaskStrategyTable> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyTableDto> list = new ArrayList<MaskStrategyTableDto>( entityList.size() );
        for ( MaskStrategyTable maskStrategyTable : entityList ) {
            list.add( toDto( maskStrategyTable ) );
        }

        return list;
    }

    @Override
    public MaskStrategyTable toEntity(MaskStrategyTableDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyTable maskStrategyTable = new MaskStrategyTable();

        maskStrategyTable.setCreatetime( dto.getCreatetime() );
        maskStrategyTable.setCreateuser( dto.getCreateuser() );
        maskStrategyTable.setDbname( dto.getDbname() );
        maskStrategyTable.setEnabled( dto.getEnabled() );
        maskStrategyTable.setId( dto.getId() );
        maskStrategyTable.setLevelVal( dto.getLevelVal() );
        maskStrategyTable.setMemo( dto.getMemo() );
        maskStrategyTable.setSourcetype( dto.getSourcetype() );
        maskStrategyTable.setSparefield1( dto.getSparefield1() );
        maskStrategyTable.setSparefield2( dto.getSparefield2() );
        maskStrategyTable.setSparefield3( dto.getSparefield3() );
        maskStrategyTable.setSparefield4( dto.getSparefield4() );
        maskStrategyTable.setSparefield5( dto.getSparefield5() );
        maskStrategyTable.setStatus( dto.getStatus() );
        maskStrategyTable.setStrategydesc( dto.getStrategydesc() );
        maskStrategyTable.setStrategyname( dto.getStrategyname() );
        maskStrategyTable.setStrategytype( dto.getStrategytype() );
        maskStrategyTable.setTabcname( dto.getTabcname() );
        maskStrategyTable.setTabename( dto.getTabename() );
        maskStrategyTable.setTabid( dto.getTabid() );
        maskStrategyTable.setUpdatetime( dto.getUpdatetime() );
        maskStrategyTable.setUpdateuser( dto.getUpdateuser() );

        return maskStrategyTable;
    }

    @Override
    public List<MaskStrategyTable> toEntity(List<MaskStrategyTableDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyTable> list = new ArrayList<MaskStrategyTable>( dtoList.size() );
        for ( MaskStrategyTableDto maskStrategyTableDto : dtoList ) {
            list.add( toEntity( maskStrategyTableDto ) );
        }

        return list;
    }
}
