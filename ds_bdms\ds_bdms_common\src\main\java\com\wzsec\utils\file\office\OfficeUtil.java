package com.wzsec.utils.file.office;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PushbackInputStream;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
//import org.apache.poi.POIOLE2TextExtractor;
//import org.apache.poi.POITextExtractor;
//import org.apache.poi.POIXMLDocument;
//import org.apache.poi.extractor.ExtractorFactory;
import org.apache.poi.hslf.extractor.PowerPointExtractor;
import org.apache.poi.hssf.record.crypto.Biff8EncryptionKey;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.poifs.crypt.Decryptor;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.extractor.ExcelExtractor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xslf.extractor.XSLFPowerPointExtractor;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

/**
 * Word and PPT and Excel 读取
 *
 * <AUTHOR>
 * @date 2019-06-17
 */
@Slf4j
public class OfficeUtil {

    /**
     * 是否是2007及以上版本
     *
     * @param filePath 文件地址
     * @return true 是 false 不是
     * @throws IOException
     * @description 通过检测文件头判断是2007及以上返回true 不是返回false
     * <AUTHOR>
     * @date 2019-06-17
     */
    public static boolean isOffice2007(String filePath) throws IOException {
        boolean b = false;
        InputStream fis = null;
        fis = new ByteArrayInputStream(FileUtils.readFileToByteArray(new File(filePath)));
        if (!fis.markSupported()) {
            fis = new PushbackInputStream(fis, 8);
        }
        // if (POIFSFileSystem.hasPOIFSHeader(fis)) {//2003及以下版本
        // return false;
        // }
//        if (POIXMLDocument.hasOOXMLHeader(fis)) {// 2007及以上版本
//            b = true;
//        }
        return b;
    }

    /**
     * 读取PPT文本内容
     *
     * @param filePath 文件地址
     * @return
     * @description 处理2003与2007两种版本PPT
     * <AUTHOR>
     * @date 2019-06-17
     */
    public static String getPPTStr(String filePath) {
        StringBuilder result = new StringBuilder();
        InputStream fis = null;
        try {
            fis = new ByteArrayInputStream(FileUtils.readFileToByteArray(new File(filePath)));
            if (isOffice2007(filePath)) {
                // System.out.println("检测后，ppt版本为2007及以上");
                XMLSlideShow slide = null;
                slide = new XMLSlideShow(fis);
                XSLFPowerPointExtractor extractor = new XSLFPowerPointExtractor(slide);
                result.append(extractor.getText());
            } else {
                // System.out.println("检测后，ppt版本为2003及以下");
                PowerPointExtractor ex = null;
                ex = new PowerPointExtractor(fis);
                result.append(ex.getText());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }

    /**
     * 读取word文本内容
     *
     * @param filePath:文件地址
     * @return
     * @description 处理2003与2007两种版本word
     * <AUTHOR>
     * @date 2019-06-17
     */
    public static String getWordStr(String filePath) {
        String result = null;
        InputStream in = null;
        StringBuilder str = new StringBuilder();
        boolean isDocx = filePath.toLowerCase().endsWith(".docx");
        boolean isDoc = filePath.toLowerCase().endsWith(".doc");
        try {
            in = new FileInputStream(filePath);

            if (isDocx) {// 2007及以上版本
                // System.out.println("检测后，word版本为2007及以上");
                XWPFDocument document = new XWPFDocument(in);
                XWPFWordExtractor extractor = new XWPFWordExtractor(document);
                result = extractor.getText();
                //List<XWPFParagraph> paragraphList = document.getParagraphs();
                //XWPFParagraph paragraph = paragraphList.get(7);
                //XmlObject object = paragraph.getCTP().getRArray(7);
                //XmlCursor cursor = object.newCursor();
                //System.out.println(cursor.getTextValue());
            } else { // 2003及以下版本
                // System.out.println("检测后，word版本为2003及以下");
                HWPFDocument document = new HWPFDocument(in);
                WordExtractor extractor = new WordExtractor(document);
                //String res = getWordStrNest(filePath);
                str.append(extractor.getText());
                result = str.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 支持读取word2003版嵌套03或07版word、03或07版excel、03或07版ppt内容
     *
     * @param filePath 文件地址
     * <AUTHOR>
     * @date 2020-02-18
     */
    /*private static String getWordStrNest(String filePath) {
        String result = null;
        try {
            InputStream fis = new FileInputStream(filePath);

            POIFSFileSystem fileSystem = new POIFSFileSystem(fis);
            // 首先，获取工作簿的提取器
            POIOLE2TextExtractor oleTextExtractor = ExtractorFactory.createExtractor(fileSystem);
            // 然后是任何嵌入式Excel，Word，PowerPoint的提取器列表
            POITextExtractor[] embeddedExtractors = ExtractorFactory.getEmbededDocsTextExtractors(oleTextExtractor);
            System.out.println(embeddedExtractors.length);
            for (POITextExtractor textExtractor : embeddedExtractors) {
                //判断是03版本或者07版本
                if (textExtractor instanceof ExcelExtractor || textExtractor instanceof WordExtractor || textExtractor instanceof PowerPointExtractor) {
                    // 如果嵌入式对象是03版Excel电子表格。
                    if (textExtractor instanceof ExcelExtractor) {
                        ExcelExtractor excelExtractor = (ExcelExtractor) textExtractor;
                        result = excelExtractor.getText();
                        System.out.println("excel文档内容" + excelExtractor.getText());
                    }
                    // 03版Word文档
                    else if (textExtractor instanceof WordExtractor) {
                        WordExtractor wordExtractor = (WordExtractor) textExtractor;
                        String[] paragraphText = wordExtractor.getParagraphText();
                        for (String paragraph : paragraphText) {
                            result = paragraph;
                            System.out.println(paragraph);
                        }
                    }
                    // 03版PowerPoint演示文稿。
                    else if (textExtractor instanceof PowerPointExtractor) {
                        PowerPointExtractor powerPointExtractor = (PowerPointExtractor) textExtractor;
                        result = powerPointExtractor.getText();
                    }
                } else {
                    //07版本嵌套word
                    if (textExtractor instanceof XWPFWordExtractor) {
                        XWPFWordExtractor wordExtractor = (XWPFWordExtractor) textExtractor;
                        String paragraphText = wordExtractor.getText();
                        result = paragraphText;
                    }//07版本嵌套ppt
                    else if (textExtractor instanceof XSLFPowerPointExtractor) {
                        XSLFPowerPointExtractor powerPointExtractor = (XSLFPowerPointExtractor) textExtractor;
                        result = powerPointExtractor.getText();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }*/

    /**
     * 读入excel文件，解析后返回
     *
     * @param filePath
     * @param pwd
     * @throws Exception
     */
    public static List<String> getExcelStr(String filePath, String pwd) throws Exception {
        //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
        List<String> list = new ArrayList<>();

        File file = new File(filePath);
        if (file.length() > 5242880) {//超大excel读取
            return com.wzsec.utils.file.office.ExcelReader.readerExcel(filePath);
        }
        //检查文件
        //checkFile(file);
        //获得Workbook工作薄对象
        Workbook workbook = getWorkBook(file, pwd);
        if (workbook != null) {
            for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
                //获得当前sheet工作表
                Sheet sheet = workbook.getSheetAt(sheetNum);
                if (sheet == null) {
                    continue;
                }
                //获得当前sheet的开始行
                int firstRowNum = sheet.getFirstRowNum();
                //获得当前sheet的结束行
                int lastRowNum = sheet.getLastRowNum();
                //循环所有行
                for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
                    //获得当前行
                    Row row = sheet.getRow(rowNum);
                    if (row == null) {
                        continue;
                    }
                    //获得当前行的开始列
                    int firstCellNum = row.getFirstCellNum();
                    //获得当前行的最后一列
                    int LastCellNum = row.getLastCellNum();
                    //循环当前行
                    for (int cellNum = firstCellNum; cellNum < LastCellNum; cellNum++) {
                        Cell cell = row.getCell(cellNum);
                        if (cell != null) {
                            String str = getCellValue(cell);
                            list.add(str);
                        }
                    }
                }
            }
            try {
                workbook.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return list;
    }

    /**
     * 创建Workbook工作薄对象
     *
     * @param file
     * @throws Exception
     */
    private static Workbook getWorkBook(File file, String pwd) throws Exception {
        // 创建Workbook工作薄对象，表示整个excel
        Workbook workbook = null;
        InputStream is = null;
        // 获取excel文件的io流
        try {
            is = new FileInputStream(file);
            // 1.不加密
            // 根据程序判断excel是2007以上版本还是2003版本获得不同的Workbook实现类对象
            if (isOffice2007(file.getAbsolutePath())) {
                // 2007
                workbook = new XSSFWorkbook(is);
            } else {
                // 2003
                workbook = new HSSFWorkbook(is);
            }
            is.close();
        } catch (Exception e) {
            // 2.加密
            POIFSFileSystem pfs = null;
            try {
                is = new FileInputStream(file);
                // 2007
                pfs = new POIFSFileSystem(is);
                EncryptionInfo encInfo = new EncryptionInfo(pfs);
                Decryptor decryptor = Decryptor.getInstance(encInfo);
                if (decryptor.verifyPassword(pwd)) {
                    log.info(file + "为2007版本，Excel解密成功");
                    workbook = new XSSFWorkbook(decryptor.getDataStream(pfs));
                } else {
                    throw new RuntimeException("解析xlsx文件失败,解密密码错误");
                }
                is.close();
            } catch (RuntimeException e1) {
                throw new RuntimeException(e1.getMessage());
            } catch (Exception e1) {
                try {
                    is = new FileInputStream(file);
                    // 2003
                    pfs = new POIFSFileSystem(is);
                    Biff8EncryptionKey.setCurrentUserPassword(pwd);
                    workbook = new HSSFWorkbook(pfs);
                    log.info(file + "为2003版本，Excel解密成功");
                    is.close();
                } catch (Exception e2) {
                    log.info(file + "为2003版本，Excel解密失败");
                    throw new RuntimeException("解析xls文件失败,不支持2003版本加密的Excel文件");
                }
            }
        } finally {
            try {
                if (is != null)
                    is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return workbook;
    }

    /**
     * 获取单元格数据类型
     *
     * @param cell 单元格
     */
    private static String getCellValue(Cell cell) {
        String cellValue = "";
        if (cell == null) {
            return cellValue;
        }
        //把数字当成String来读，避免出现1读成1.0的情况
        if (cell.getCellType() == CellType.NUMERIC) {
            cell.setCellType(CellType.STRING);
            //CellType.STRING
        }
        //判断数据的类型
        switch (cell.getCellType()) {
            case NUMERIC: //数字
                cellValue = String.valueOf(cell.getNumericCellValue());
                break;
            case STRING: //字符串
                cellValue = String.valueOf(cell.getStringCellValue());
                break;
            case BOOLEAN: //Boolean
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA: //公式
                cellValue = String.valueOf(cell.getCellFormula());
                break;
            case BLANK: //空值
                cellValue = "";
                break;
            case ERROR: //故障
                cellValue = "非法字符";
                break;
            default:
                cellValue = "未知类型";
                break;
        }
        return cellValue;
    }



}
