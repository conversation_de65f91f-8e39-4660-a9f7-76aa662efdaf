package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @date 2022-04-18
 */
public interface MaskPictaskconfigRepository extends JpaRepository<MaskPictaskconfig, Integer>, JpaSpecificationExecutor<MaskPictaskconfig> {
    @Query(value = "select MAX(`taskname`) from sdd_mask_pictaskconfig where taskname like concat('%',?1,'%')", nativeQuery = true)
    String findMAXTasknoByPrefix(String prefix);
}