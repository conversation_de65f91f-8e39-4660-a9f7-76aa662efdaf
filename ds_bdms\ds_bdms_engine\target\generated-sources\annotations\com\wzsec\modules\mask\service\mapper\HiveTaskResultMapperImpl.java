package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HiveTaskResult;
import com.wzsec.modules.mask.service.dto.HiveTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class HiveTaskResultMapperImpl implements HiveTaskResultMapper {

    @Override
    public HiveTaskResultDto toDto(HiveTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        HiveTaskResultDto hiveTaskResultDto = new HiveTaskResultDto();

        hiveTaskResultDto.setCreatetime( entity.getCreatetime() );
        hiveTaskResultDto.setDatafileinputpath( entity.getDatafileinputpath() );
        hiveTaskResultDto.setDatafileoutputpath( entity.getDatafileoutputpath() );
        hiveTaskResultDto.setDatainputpath( entity.getDatainputpath() );
        hiveTaskResultDto.setDataoutputpath( entity.getDataoutputpath() );
        hiveTaskResultDto.setDatarows( entity.getDatarows() );
        hiveTaskResultDto.setDatasplit( entity.getDatasplit() );
        hiveTaskResultDto.setDbname( entity.getDbname() );
        hiveTaskResultDto.setFileformat( entity.getFileformat() );
        hiveTaskResultDto.setId( entity.getId() );
        hiveTaskResultDto.setJobendtime( entity.getJobendtime() );
        hiveTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        hiveTaskResultDto.setJobstatus( entity.getJobstatus() );
        hiveTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        hiveTaskResultDto.setMaskstrategystr( entity.getMaskstrategystr() );
        hiveTaskResultDto.setPartitioninfo( entity.getPartitioninfo() );
        hiveTaskResultDto.setPlatform( entity.getPlatform() );
        hiveTaskResultDto.setQueuename( entity.getQueuename() );
        hiveTaskResultDto.setRemark( entity.getRemark() );
        hiveTaskResultDto.setSparefield1( entity.getSparefield1() );
        hiveTaskResultDto.setSparefield2( entity.getSparefield2() );
        hiveTaskResultDto.setSparefield3( entity.getSparefield3() );
        hiveTaskResultDto.setSparefield4( entity.getSparefield4() );
        hiveTaskResultDto.setSparefield5( entity.getSparefield5() );
        hiveTaskResultDto.setTablename( entity.getTablename() );
        hiveTaskResultDto.setTaskname( entity.getTaskname() );
        hiveTaskResultDto.setUpdatetime( entity.getUpdatetime() );
        hiveTaskResultDto.setUserid( entity.getUserid() );
        hiveTaskResultDto.setUsername( entity.getUsername() );

        return hiveTaskResultDto;
    }

    @Override
    public List<HiveTaskResultDto> toDto(List<HiveTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HiveTaskResultDto> list = new ArrayList<HiveTaskResultDto>( entityList.size() );
        for ( HiveTaskResult hiveTaskResult : entityList ) {
            list.add( toDto( hiveTaskResult ) );
        }

        return list;
    }

    @Override
    public HiveTaskResult toEntity(HiveTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        HiveTaskResult hiveTaskResult = new HiveTaskResult();

        hiveTaskResult.setCreatetime( dto.getCreatetime() );
        hiveTaskResult.setDatafileinputpath( dto.getDatafileinputpath() );
        hiveTaskResult.setDatafileoutputpath( dto.getDatafileoutputpath() );
        hiveTaskResult.setDatainputpath( dto.getDatainputpath() );
        hiveTaskResult.setDataoutputpath( dto.getDataoutputpath() );
        hiveTaskResult.setDatarows( dto.getDatarows() );
        hiveTaskResult.setDatasplit( dto.getDatasplit() );
        hiveTaskResult.setDbname( dto.getDbname() );
        hiveTaskResult.setFileformat( dto.getFileformat() );
        hiveTaskResult.setId( dto.getId() );
        hiveTaskResult.setJobendtime( dto.getJobendtime() );
        hiveTaskResult.setJobstarttime( dto.getJobstarttime() );
        hiveTaskResult.setJobstatus( dto.getJobstatus() );
        hiveTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        hiveTaskResult.setMaskstrategystr( dto.getMaskstrategystr() );
        hiveTaskResult.setPartitioninfo( dto.getPartitioninfo() );
        hiveTaskResult.setPlatform( dto.getPlatform() );
        hiveTaskResult.setQueuename( dto.getQueuename() );
        hiveTaskResult.setRemark( dto.getRemark() );
        hiveTaskResult.setSparefield1( dto.getSparefield1() );
        hiveTaskResult.setSparefield2( dto.getSparefield2() );
        hiveTaskResult.setSparefield3( dto.getSparefield3() );
        hiveTaskResult.setSparefield4( dto.getSparefield4() );
        hiveTaskResult.setSparefield5( dto.getSparefield5() );
        hiveTaskResult.setTablename( dto.getTablename() );
        hiveTaskResult.setTaskname( dto.getTaskname() );
        hiveTaskResult.setUpdatetime( dto.getUpdatetime() );
        hiveTaskResult.setUserid( dto.getUserid() );
        hiveTaskResult.setUsername( dto.getUsername() );

        return hiveTaskResult;
    }

    @Override
    public List<HiveTaskResult> toEntity(List<HiveTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HiveTaskResult> list = new ArrayList<HiveTaskResult>( dtoList.size() );
        for ( HiveTaskResultDto hiveTaskResultDto : dtoList ) {
            list.add( toEntity( hiveTaskResultDto ) );
        }

        return list;
    }
}
