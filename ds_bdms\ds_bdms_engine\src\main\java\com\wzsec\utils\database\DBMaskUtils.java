package com.wzsec.utils.database;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import com.alibaba.fastjson.JSON;
import com.mongodb.MongoClient;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.wzsec.modules.mask.domain.DBTaskResult;
import com.wzsec.utils.AlgorithmUtils;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import org.bson.Document;
import lombok.extern.slf4j.Slf4j;
import java.sql.*;
import java.util.*;


/**
 * 数据库脱敏工具类
 *
 * <AUTHOR>
 * @date 2020-11-12
 */
@Slf4j
public class DBMaskUtils {

    /**
     * @Description:获取字段脱敏算法信息根据策略名
     * <AUTHOR>
     * @date 2019-10-22
     */
    public static Map<String,Object> getMaskAlgoInfo(List<Map<String,Object>> list, HashMap<String, cn.god.mask.common.Algorithm> algorithmMap){
        Map<String,Object> maskAlgoInfoMap=new LinkedHashMap<String, Object>();
        try{
            Map<String,Algorithm> algorithmIdMap = new HashMap<>();
            if (algorithmMap != null) {
                for (Map.Entry<String, Algorithm> entry : algorithmMap.entrySet()) {
                    Algorithm algorithm = entry.getValue();
                    algorithmIdMap.put(algorithm.getAlgorithmid().toString(), algorithm);
                }
            }
            for (Map<String, Object> map : list) {
                String strAlgoInfo = "";
                String isExtract = map.get("isextract") != null ? map.get("isextract").toString() : Const.ALG_CONFIG_WAY_ENTIRETY;
                // TODo 整体配置的规则，走之前逻辑
                if (Const.ALG_CONFIG_WAY_ENTIRETY.equals(isExtract)) {
                    String algenglishname = map.get("algenglishname") != null ? map.get("algenglishname").toString() : null;
                    String param = map.get("param") != null ? map.get("param").toString() : null;
                    String secretkey = map.get("secretkey") != null ? map.get("secretkey").toString() : null;
                    if (StringUtils.isNotEmpty(algenglishname)) {
                        StringBuilder fieldStrBuilder = new StringBuilder();
                        fieldStrBuilder.append(isExtract+"$");
                        fieldStrBuilder.append(algenglishname);
                        fieldStrBuilder.append("$");
                        if (StringUtils.isNotEmpty(param)) {
                            fieldStrBuilder.append(param); //参数
                            fieldStrBuilder.append(",");
                        }
                        if (StringUtils.isNotEmpty(secretkey)) {
                            fieldStrBuilder.append(secretkey); //密钥
                        }
                        strAlgoInfo = fieldStrBuilder.toString();
                        if (strAlgoInfo.endsWith(",")) {  //如果以","结尾，去除","
                            strAlgoInfo = strAlgoInfo.substring(0, strAlgoInfo.lastIndexOf(","));
                        }
                    }
                } else {
                    // TODO 分段配置的规则，在原始的基础上，将多段脱敏信息拼接到一处，用分号隔开
                    StringBuilder fieldStrBuilder = new StringBuilder();
                    String extractconfig = map.get("extractconfig") != null ? map.get("extractconfig").toString() : null;
                    String extractalgid = map.get("extractalgid") != null ? map.get("extractalgid").toString() : null;
                    if (StringUtils.isNotEmpty(extractconfig) && StringUtils.isNotEmpty(extractalgid)){
                        String[] extractConfigArr = extractconfig.split(";");
                        String[] extractAlgIdArr = extractalgid.split(";");
                        for (int i = 0; i < extractAlgIdArr.length; i++) {
                            fieldStrBuilder.append(isExtract+"$");
                            String algId = extractAlgIdArr[i];
                            if (algorithmIdMap.containsKey(algId)) {
                                Algorithm algorithm = algorithmIdMap.get(algId);
                                String algenglishname = algorithm.getAlgenglishname();
                                fieldStrBuilder.append(algenglishname+"$");
                                String par = Const.algorithmParameterSecretKeyMap().get(algenglishname) != null ?
                                        Const.algorithmParameterSecretKeyMap().get(algenglishname) : "";
                                fieldStrBuilder.append(par+"$");
                                fieldStrBuilder.append(extractConfigArr[i]);
                                fieldStrBuilder.append(";");
                            }
                        }

                        strAlgoInfo = fieldStrBuilder.toString();
                        if (strAlgoInfo.endsWith(";")) {  //如果以","结尾，去除","
                            strAlgoInfo = strAlgoInfo.substring(0, strAlgoInfo.lastIndexOf(";"));
                        }

                    }
                }

                //格式1  0$算法名$参数或秘钥
                //格式2  1$算法名$参数或秘钥$分段位数(格式：1-6);1$算法名$参数或秘钥$分段位数(格式：15-18);
                maskAlgoInfoMap.put((String) map.get("fieldename"), strAlgoInfo);
            }
        } catch(Exception ex){
            ex.printStackTrace();
            log.error("获取字段脱敏算法信息出现异常");
        }
        return maskAlgoInfoMap;
    }


    /**
     * @Description:获取抽取字段的数据根据字段名字符串和数据库配置
     * <AUTHOR>
     * @date 2019-10-22
     */
    public static List<Map<String, String>> getAllFieldDataByFieldNameStrAndDataSourceConfig(String fieldNameStr,
            String dbname, String tabname, String url, String username, String password ,String driver, String limitingcondition){

        Connection con = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        if(limitingcondition == null){
            limitingcondition = "";
        }
        List<Map<String, String>> fieldDataList= null;
        try {
            if (url.contains("oracle") || url.contains("ORACLE")) {
                Class.forName(driver);
                con = (Connection) DriverManager.getConnection(url, username, password);
                log.info("连接oracle数据库成功");
                // 查抽取字段数据
                String sql = "select " + fieldNameStr + " from " + tabname;
                if (null != limitingcondition && !"".equals(limitingcondition)) {
                    sql += " " + limitingcondition;
                }
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (url.contains("mysql") || url.contains("MYSQL")) {
                Class.forName(driver);
                con = DriverManager.getConnection(url, username, password);
                log.info("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "select " + fieldNameStr + " from " + tabname;
                if (null != limitingcondition && !"".equals(limitingcondition)) {
                    sql += " " + limitingcondition;
                }
                System.err.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (url.contains("gbase") || url.contains("GBASE")) {
                Class.forName(driver);
                con = (Connection) DriverManager.getConnection(url, username, password);
                log.info("连接gbase数据库成功");
                // 查抽取字段数据
                String sql = "select " + fieldNameStr + " from " + tabname;
                if (null != limitingcondition && !"".equals(limitingcondition)) {
                    sql += " " + limitingcondition;
                }
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (url.contains("sqlserver") || url.contains("SQLSERVER")) {
                Class.forName(driver);
                con = (Connection) DriverManager.getConnection(url, username, password);
                log.info("连接sqlserver数据库成功");
                // 查抽取字段数据
                String sql = "select " + fieldNameStr + " from " + tabname;
                if (null != limitingcondition && !"".equals(limitingcondition)) {
                    sql += " " + limitingcondition;
                }
                // System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (url.contains("db2") || url.contains("DB2")) {
                Class.forName(driver);
                con = (Connection) DriverManager.getConnection(url, username, password);
                log.info("连接db2数据库成功");
                // 查抽取字段数据
                String sql = "select " + fieldNameStr + " from " + tabname;
                if (null != limitingcondition && !"".equals(limitingcondition)) {
                    sql += " " + limitingcondition;
                }
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            }else if (url.contains("postgresql") || url.contains("POSTGRESQL")) {
                Class.forName(driver);
                con = (Connection) DriverManager.getConnection(url, username, password);
                log.info("连接postgresql数据库成功");
                // 查抽取字段数据
                String sql = "select " + fieldNameStr + " from " + tabname;
                if (null != limitingcondition && !"".equals(limitingcondition)) {
                    sql += " " + limitingcondition;
                }
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (url.contains("dm") || url.contains("DM")) {
                Class.forName(driver);
                con = (Connection) DriverManager.getConnection(url, username, password);
                log.info("连接dm数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String sql = "select " + fieldNameStr + " from " + tabname;
                if (null != limitingcondition && !"".equals(limitingcondition)) {
                    sql += " " + limitingcondition;
                }
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            }  else if (url.contains("mongdb") || url.contains("MONGDB")) {
                String[] ip_port_source = url.split(":");
                List<ServerAddress> adds = new ArrayList<>();
                // ServerAddress()两个参数分别为 服务器地址 和 端口
                ServerAddress serverAddress = new ServerAddress(ip_port_source[0], Integer.parseInt(ip_port_source[1]));
                adds.add(serverAddress);

                List<MongoCredential> credentials = new ArrayList<>();
                // MongoCredential.createScramSha1Credential()三个参数分别为 用户名 数据库名称
                // 密码
                MongoCredential mongoCredential = MongoCredential.createScramSha1Credential(username, ip_port_source[2],
                        password.toCharArray());
                credentials.add(mongoCredential);

                // 通过连接认证获取MongoDB连接
                MongoClient mongoClient = new MongoClient(adds, credentials);

                MongoDatabase mongoDatabase = mongoClient.getDatabase(dbname);
                // 获取集合
                MongoCollection<Document> collection = mongoDatabase.getCollection(tabname);
                // 查找集合中指定数量的文档
                FindIterable<Document> findIterable = collection.find();
                MongoCursor<Document> cursor = findIterable.iterator();
                fieldDataList = new ArrayList<Map<String, String>>();
                String[] fieldNameStrs = fieldNameStr.split(",");
                while (cursor.hasNext()) {
                    Document document = cursor.next();
                    String json = document.toJson();
                    Map<String, Object> documentMap = JSON.parseObject(json, new HashMap<>().getClass());
                    Map<String, String> rowMap = new LinkedHashMap<>();
                    for (String fieldName : fieldNameStrs) {
                        Object data = documentMap.get(fieldName);
                        if (data != null) {
                            rowMap.put(fieldName, (String)data);
                        } else {
                            rowMap.put(fieldName, null);
                        }
                    }

                    fieldDataList.add(rowMap);
                }
            }
            if (resultSet != null) {
                // 存放数据
                fieldDataList = new ArrayList<Map<String, String>>();
                String[] fieldNameStrs = fieldNameStr.split(",");
                // 遍历查询结果集
                while (resultSet.next()) {
                    Map<String, String> fieldDataMap = new LinkedHashMap<String, String>();
                    for (String fieldName : fieldNameStrs) {
                        fieldDataMap.put(fieldName, resultSet.getString(fieldName));
                    }
                    fieldDataList.add(fieldDataMap);
                }
            }
        } catch (Exception ex) {
            log.error("连接数据库失败或获取抽取字段的数据出现异常");
            ex.printStackTrace();
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return fieldDataList;
    }

    /**
     * @Description:对抽取字段中脱敏字段的数据进行脱敏并返回脱敏行数
     * <AUTHOR>
     * @date 2019-10-11
     */
    public static DBTaskResult getMaskDataList(List<Map<String, String>> fieldDataList, Map<String, String> maskAlgoInfoMap,HashMap<String, Algorithm> algorithmMap) {
        int totalcount = fieldDataList.size();
        int count = 0;
        int maskcount = 0;
        String beforemaskdata = "";
        String aftermaskdata = "";
        StringBuffer dataline = new StringBuffer();
        StringBuffer maskdataline = new StringBuffer();

        // 遍历抽取字段的数据
        for (Map<String, String> map : fieldDataList) {
            boolean flag = false;
            for (String filedName : map.keySet()) {
                String data = map.get(filedName);
                String result = data;
                // 该字段有数据才进行脱敏
                if (data != null && !"".equals(data)) {
                    // 获取字段的脱敏算法信息 MD5$123,412
                    String maskAlgoInfo = maskAlgoInfoMap.get(filedName);
                    // 该字段有脱敏算法才进行脱敏
                    if (maskAlgoInfo != null && !"".equals(maskAlgoInfo)) {
                        flag = true;
                        String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                        String algo = AlgoParamInfo[0];
                        String param = "";
                        if (AlgoParamInfo.length > 1) {
                            param = AlgoParamInfo[1];
                        }
                        try {
                            // 数据:data算法名:algo参数:param
                            //判断当前执行是否使用自定义算法
                            Algorithm algorithm = algorithmMap.get(algo);
                            String sparefield1 = algorithm.getSparefield1();
                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                            }else {
                                //自定义算法
                                String jarname = algorithm.getJarname();//jar包名称
                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                String methodname = algorithm.getMethodname();//方法名
                                Map<String, String> paramMap = new HashMap<>();
                                paramMap.put("jarname",jarname);
                                paramMap.put("funcnamepath",funcnamepath);
                                paramMap.put("methodname",methodname);
                                paramMap.put("maskData",data);
                                result = AlgorithmUtils.invokeJarMethod(paramMap);
                            }
                            //result = data;
                        } catch (Exception e) {
                            log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algo + "参数:" + param);
                            e.printStackTrace();
                        }
                        map.put(filedName, result);
                    }
                    if (flag) {
                        maskcount++;
                    }
                }
                if (count < 5) {
                    dataline.append(data + ",");
                    maskdataline.append(result + ",");
                }
            }
            if (count < 5) {
                beforemaskdata += dataline.toString().substring(0, dataline.toString().length() - 1) + "\n";
                aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
                dataline = new StringBuffer();
                maskdataline = new StringBuffer();
            }
            count++;
        }

        beforemaskdata = beforemaskdata.substring(0, beforemaskdata.toString().length() - 1);
        aftermaskdata = aftermaskdata.substring(0, aftermaskdata.toString().length() - 1);
        DBTaskResult dBTaskResult = new DBTaskResult();
        dBTaskResult.setBeforemaskdata(beforemaskdata);
        dBTaskResult.setAftermaskdata(aftermaskdata);
        dBTaskResult.setMasklines(count);
        return dBTaskResult;
    }


}
