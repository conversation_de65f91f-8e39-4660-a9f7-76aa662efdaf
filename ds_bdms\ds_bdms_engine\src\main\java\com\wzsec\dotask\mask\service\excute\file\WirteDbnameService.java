package com.wzsec.dotask.mask.service.excute.file;

import java.sql.*;
import java.util.*;

public class WirteDbnameService {

    /**
     * @Title: getMysqlCreateTableSql
     * @Description: 获取mysql创建表语句
     * <AUTHOR>
     * @date 2019年11月14日
     */
    public static String getMysqlCreateTableSql(String tablename, String fieldnames) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            String[] fieldnamearr = fieldnames.split(",");
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tablename).append("` (\r\n");
            // boolean firstId = true;
            for (String fieldname : fieldnamearr) {

                sb.append("`").append(fieldname).append("`");// 字段名
                sb.append(" varchar(255)");// 类型
                sb.append(" DEFAULT NULL");
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)ENGINE=InnoDB DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @Title: getOraclereateTableSql
     * @Description: 获取oracle创建表语句
     * <AUTHOR>
     * @date 2019年11月15日
     */
    public static String getOraclereateTableSql(String dbname, String tablename, String fieldnames) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            String[] fieldnamearr = fieldnames.split(",");
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE \"" + dbname + "\".\"").append(tablename).append("\" (\r\n");
            // boolean firstId = true;
            for (String fieldname : fieldnamearr) {
                sb.append(fieldname.toUpperCase());// 字段名
                sb.append(" varchar(255)");// 类型
                sb.append(",\n");
            }
            sql = sb.toString();

            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }


    /**
     * 获取DM创建表语句
     *
     * @param dbname     dbname
     * @param tableName  表
     * @param fieldnames 字段名
     * @return {@link String}
     */
    public static String getDMCreateTableSql(String dbname, String tableName, String fieldnames) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            String[] fieldnamearr = fieldnames.split(",");

            sb.append("CREATE TABLE \"" + dbname + "\".\"").append(tableName).append("\" (\r\n");
            for (String fieldname : fieldnamearr) {
                sb.append(fieldname.toUpperCase());// 字段名
                sb.append(" varchar(255)");// 类型
                sb.append(",\n");
            }
            sql = sb.toString();

            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
            //MAIN:数据库默认的表空间
            sql = sql + "STORAGE(ON \"" + "MAIN" + "\", CLUSTERBTR);";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }


    /**
     * 获取Informix创建表语句
     *
     * @param dbname     dbname
     * @param tableName  表
     * @param fieldnames 字段名
     * @return {@link String}
     */
    public static String getInformixCreateTableSql(String dbname, String tableName, String fieldnames) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            String[] fieldnamearr = fieldnames.split(",");
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            for (String fieldname : fieldnamearr) {
                sb.append(fieldname);// 字段名
                sb.append(" varchar(255)");// 类型
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r);";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @Title: createTable
     * @Description: 创建表
     * <AUTHOR>
     * @date 2019年11月14日
     */
    public static Boolean createTable(String createTableSql, String jdbc_driver, String srcurl, String username,
                                      String password) {
        Boolean result = false;
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getCon(jdbc_driver, srcurl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            if (0 == stmt.executeUpdate(createTableSql)) {
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        } finally {
            closeCon(null, stmt, conn);
        }
        return result;
    }

    /**
     * @Title: getMysqlInsert2TableSqlAndPatams
     * @Description: mysql生成批量插入语句, 批量插入参数
     * <AUTHOR>
     * @date 2019年11月14日
     */
    public static Map<String, Object> getMysqlInsert2TableSqlAndPatams(List<Map<String, String>> maskDataLists, String tablename) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = maskDataLists.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tablename).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, String> map : maskDataLists) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Title: getOracleInsert2TableSqlAndPatams
     * @Description: oracle生成批量插入语句, 批量插入参数
     * <AUTHOR>
     * @date 2019年11月15日
     */
    public static Map<String, Object> getOracleInsert2TableSqlAndPatams(List<Map<String, String>> maskDataLists,
                                                                        String tablename) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = maskDataLists.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            System.err.println();
            sb.append("INSERT ALL ");

            for (Map<String, String> map : maskDataLists) {
                sb.append(" INTO  " + aa).append(tablename).append(aa + " (");
                for (String column : fields) {
                    sb.append(column).append(", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();

                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);

                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Title: getSybaseIqInsert2TableSqlAndPatams
     * @Description: sybase生成批量插入语句, 批量插入参数
     * <AUTHOR>
     * @date 2019年12月17日
     */
    public static Map<String, Object> getSybaseIqInsert2TableSqlAndPatams(List<Map<String, String>> maskDataLists, String tablename) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = maskDataLists.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tablename).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, String> map : maskDataLists) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }


    /**
     * @Title: executeUpdate
     * @Description: 数据库增删改方法
     * <AUTHOR>
     * @date 2019年11月14日
     */
    public static Boolean executeUpdate(String insert2MysqlTableSql, Object[] insert2MysqlTableParams, String jdbc_driver,
                                        String srcurl, String username, String password) {
        Boolean result = false;
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = getCon(jdbc_driver, srcurl, username, password);// 打开连接
            pstmt = conn.prepareStatement(insert2MysqlTableSql);
            for (int i = 0; i < insert2MysqlTableParams.length; i++) {
                pstmt.setObject(i + 1, insert2MysqlTableParams[i]);
            }
            if (pstmt.executeUpdate() > 0) {
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        } finally {
            closeCon(null, pstmt, conn);
        }
        return result;
    }


    /**
     * @Title: createTable
     * @Description: 获取数据库连接
     * <AUTHOR>
     * @date 2019年11月14日
     */
    public static Connection getCon(String jdbcName, String url, String username, String password) {
        try {
            Class.forName(jdbcName);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        Connection con = null;
        try {
            con = DriverManager.getConnection(url, username, password);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return con;
    }

    /**
     * @Title: createTable
     * @Description: 释放连接
     * <AUTHOR>
     * @date 2019年11月14日
     */
    public static void closeCon(ResultSet rs, Statement st, Connection conn) {
        try {
            if (rs != null) {
                rs.close(); // 关闭结果集
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (st != null) {
                    st.close(); // 关闭Statement
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null) {
                        conn.close(); // 关闭连接
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
