package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2021-01-26
*/
@Entity
@Data
@Table(name="sdd_mask_hivetaskresult")
public class HiveTaskResult implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 作业平台 */
    @Column(name = "platform")
    private String platform;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 表名 */
    @Column(name = "tablename")
    private String tablename;

    /** 分区信息 */
    @Column(name = "partitioninfo")
    private String partitioninfo;

    /** 队列 */
    @Column(name = "queuename")
    private String queuename;

    /** 脱敏策略json串 */
    @Column(name = "maskstrategystr")
    private String maskstrategystr;

    /** 输出文件数据格式 */
    @Column(name = "fileformat")
    private String fileformat;

    /** 数据输入路径 */
    @Column(name = "datainputpath")
    private String datainputpath;

    /** 数据输出路径 */
    @Column(name = "dataoutputpath")
    private String dataoutputpath;

    /** 数据文件输入路径 */
    @Column(name = "datafileinputpath")
    private String datafileinputpath;

    /** 数据文件输出路径 */
    @Column(name = "datafileoutputpath")
    private String datafileoutputpath;

    /** 数据分隔符 */
    @Column(name = "datasplit")
    private String datasplit;

    /** 起始时间 */
    @Column(name = "jobstarttime")
    private Timestamp jobstarttime;

    /** 结束时间 */
    @Column(name = "jobendtime")
    private Timestamp jobendtime;

    @Column(name = "jobtotaltime")
    private String jobtotaltime;

    /** 作业状态（执行中、执行成功、执行失败） */
    @Column(name = "jobstatus")
    private String jobstatus;

    /** 数据行数 */
    @Column(name = "datarows")
    private String datarows;

    /** 创建时间 */
    @Column(name = "createtime")
    private String createtime;

    /** 更新时间 */
    @Column(name = "updatetime")
    private String updatetime;

    /** 用户ID */
    @Column(name = "userid")
    private String userid;

    /** 作业提交人姓名 */
    @Column(name = "username")
    private String username;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(HiveTaskResult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
