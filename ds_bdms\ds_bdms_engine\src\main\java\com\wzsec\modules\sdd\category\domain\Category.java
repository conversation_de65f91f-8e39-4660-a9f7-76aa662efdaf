package com.wzsec.modules.sdd.category.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-06-04
*/
@Entity
@Data
@Table(name="sdd_category")
public class Category implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 类别标识 */
    @Column(name = "category")
    //@NotBlank
    private String category;

    /** 类别名称 */
    @Column(name = "categoryname")
    //@NotBlank
    private String categoryname;

    /** 类别层级(1类别，2大类，3子类) */
    @Column(name = "type")
    //@NotBlank
    private String type;

    /** 上级类别ID(0为顶级类别) */
    @Column(name = "pid")
    private Long pid ;

    /** 规则ID */
    @Column(name = "ruleid")
    private Long ruleid ;

    /** 对应数据 */
    @Column(name = "data")
    private String data;

    /** 对应数据样例 */
    @Column(name = "example")
    private String example;

    /** 创建用户名 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户名 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(Category source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

}