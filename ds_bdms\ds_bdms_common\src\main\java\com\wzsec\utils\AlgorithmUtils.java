package com.wzsec.utils;

import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.Map;

/**
 * 自定义算法 工具类
 * <AUTHOR>
 * @date 2025/3/5 15:59
 */
public class AlgorithmUtils {

    /**
     * 调用 JAR 包中指定类的方法
     *
     * @param params 包含 JAR 包名称、方法路径、方法名和待处理内容的 Map
     * @return 方法调用的结果，如果调用失败返回 null
     */
    public static String invokeJarMethod(Map<String, String> params) {
        // 输入参数检查
        if (params == null) {
            System.err.println("输入的参数 Map 不能为 null");
            return null;
        }
        //jar包名称
        String jarName = params.get("jarname");
        //方法路径
        String funcNamePath = params.get("funcnamepath");
        //方法名
        String methodName = params.get("methodname");
        //数据内容
        String maskData = params.get("maskData");

        if (jarName == null || funcNamePath == null || methodName == null || maskData == null) {
            System.err.println("输入的参数中： jar包名称、方法路径、方法名 或 数据内容 不能为 null");
            return null;
        }

        URLClassLoader classLoader = null;
        try {
            File jarFile = new File(jarName);
            if (!jarFile.exists()) {
                System.err.println("指定的 JAR 文件不存在: " + jarName);
                return null;
            }
            URL jarUrl = jarFile.toURI().toURL();
            classLoader = new URLClassLoader(new URL[]{jarUrl});
            Class<?> targetClass = classLoader.loadClass(funcNamePath);

            // 获取构造函数并创建实例
            Constructor<?> constructor = targetClass.getDeclaredConstructor();
            Object instance = constructor.newInstance();

            // 获取方法并调用
            Method method = targetClass.getMethod(methodName, String.class);
            return (String) method.invoke(instance, maskData);
        } catch (ClassNotFoundException e) {
            System.err.println("未找到指定的类: " + funcNamePath);
        } catch (NoSuchMethodException e) {
            System.err.println("未找到指定的方法: " + methodName);
        } catch (Exception e) {
            System.err.println("调用方法时发生错误: " + e.getMessage());
        } finally {
            // 确保类加载器被关闭
            if (classLoader != null) {
                try {
                    classLoader.close();
                } catch (Exception e) {
                    System.err.println("关闭类加载器时发生错误: " + e.getMessage());
                }
            }
        }
        return null;
    }
}
