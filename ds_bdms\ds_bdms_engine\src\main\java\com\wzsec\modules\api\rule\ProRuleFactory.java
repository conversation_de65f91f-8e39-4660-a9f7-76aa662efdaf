package com.wzsec.modules.api.rule;

public class ProRuleFactory {

    public static boolean checkDataByProRule(String strData, String prule) {
        switch (prule.toUpperCase()) {
            case "P_PHONENUMBER":
                return ProRule.p_checkPhoneNumber(strData);
            case "P_MOBILENUMBER":
                return ProRule.p_checkMobileNumber(strData);
            case "P_UNICOMNUMBER":
                return ProRule.p_checkUnicomNumber(strData);
            case "P_TELECOMNUMBER":
                return ProRule.p_checkTelecomNumber(strData);
            case "P_FIXPHONE":
                return ProRule.p_checkFixphone(strData);
            case "P_IDCODE":
                return ProRule.p_checkIdcode(strData);
            case "P_ADDRESS":
                return ProRule.p_checkAddress(strData);
            case "P_NAME":
                return ProRule.p_checkName(strData);
            case "P_LONGITUDEANDLATITUDE":
                return ProRule.p_checkLongitudeAndLatitude(strData);
            case "P_EMAIL":
                return ProRule.p_checkEmail(strData);
            case "P_AES":
                return ProRule.p_checkAES(strData);
            case "P_3DES":
                return ProRule.p_check3DES(strData);
            case "P_MD5":
                return ProRule.p_checkMD5(strData);
            case "P_SHA256":
                return ProRule.p_checkSHA256(strData);
            case "P_CHECKIMEI":
                return ProRule.p_checkIMEI(strData);
            case "P_CHECKIMSI":
                return ProRule.p_checkIMSI(strData);
            case "P_CHECKMEID":
                return ProRule.p_checkMEID(strData);
            case "P_CHECKACCOUNTOPENINGPERMITNO":
                return ProRule.p_checkAccountOpeningPermitNo(strData);
            case "P_CHECKTAXATIONNO":
                return ProRule.p_checkTaxationNo(strData);
            case "P_CHECKHMPASSCHECK":
                return ProRule.p_checkHMPassCheck(strData);
            case "P_CHECKOFFICERCARD":
                return ProRule.p_checkOfficerCard(strData);
            case "P_CHECKPASSPORTCARD":
                return ProRule.p_checkPassPortCard(strData);
            case "P_CHECKUNIFIEDCREDITCODE":
                return ProRule.p_checkUnifiedCreditCode(strData);
            case "P_CHECKBUSINESSLICENSE":
                return ProRule.p_checkBusinesslicense(strData);
            case "P_CHECKBANKCARD":
                return ProRule.p_checkBankCard(strData);
            case "P_CHECKIP":
                return ProRule.p_checkIP(strData);
            case "P_CHECKCARNUMBERNO":
                return ProRule.p_checkCarnumberNO(strData);
            case "P_CHECKVALIDENTPCODE":
                return ProRule.p_checkValidEntpCode(strData);
            case "P_CHECKPROVINCES":
                return ProRule.p_checkProvinces(strData);
            case "P_CHECKNATIONAL":
                return ProRule.p_checkNational(strData);
            case "P_CHECKENTERPRISENAME":
                return ProRule.p_checkEnterpriseName(strData);
            case "P_CHECKQQ":
                return ProRule.p_checkQQ(strData);
            case "P_CHECKVIN":
                return ProRule.p_checkVIN(strData);
            case "P_CHECKIPV6":
                return ProRule.p_checkIPv6(strData);
            case "P_DATETIME":
                return ProRule.p_checkDate(strData);
            case "P_GENERAL":
                return ProRule.p_general(strData);
            default:
                return false;
        }
    }
}
