package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.mask.domain.MaskStrategyFileMain;
import com.wzsec.modules.mask.service.MaskStrategyFileFormatSubService;
import com.wzsec.modules.mask.service.MaskStrategyFileMainService;
import com.wzsec.modules.mask.service.MaskStrategyFileUnformatSubService;
import com.wzsec.modules.mask.service.dto.*;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021-06-20
 */
// @Api(tags = "文件策略管理")
@RestController
@RequestMapping("/api/maskStrategyFileMain")
public class MaskStrategyFileMainController {

    private final MaskStrategyFileMainService maskStrategyFileMainService;
    private final MaskStrategyFileFormatSubService maskStrategyFileFormatSubService;
    private final MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService;

    public MaskStrategyFileMainController(MaskStrategyFileMainService maskStrategyFileMainService, MaskStrategyFileFormatSubService maskStrategyFileFormatSubService,MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService) {
        this.maskStrategyFileMainService = maskStrategyFileMainService;
        this.maskStrategyFileFormatSubService = maskStrategyFileFormatSubService;
        this.maskStrategyFileUnformatSubService = maskStrategyFileUnformatSubService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskStrategyFileMain:list')")
    public void download(HttpServletResponse response, MaskStrategyFileMainQueryCriteria criteria) throws IOException {
        maskStrategyFileMainService.download(maskStrategyFileMainService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询文件策略")
    // @ApiOperation("查询文件策略")
    @PreAuthorize("@el.check('maskStrategyFileMain:list')")
    public ResponseEntity<Object> getMaskStrategyFileMains(MaskStrategyFileMainQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(maskStrategyFileMainService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增文件策略")
    // @ApiOperation("新增文件策略")
    @PreAuthorize("@el.check('maskStrategyFileMain:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskStrategyFileMain resources) {
        if ("0".equals(resources.getStrategytype())){
            verify(String.valueOf(resources.getTotalcolumn()), resources.getExtractcolum());
        }
        String strategyname = resources.getStrategyname();
        int strategycount = maskStrategyFileMainService.findStrategyCountByStrategyName(strategyname);
        if (strategycount > 0) {
            throw new BadRequestException("策略名称不能重复");
        }
        resources.setStatus("1");// 默认设置禁用
        return new ResponseEntity<>(maskStrategyFileMainService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改文件策略")
    // @ApiOperation("修改文件策略")
    @PreAuthorize("@el.check('maskStrategyFileMain:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskStrategyFileMain resources) {
        if ("0".equals(resources.getStrategytype())){
            verify(String.valueOf(resources.getTotalcolumn()), resources.getExtractcolum());
        }
        String strategyname = resources.getStrategyname();
        MaskStrategyFileMainDto byId = maskStrategyFileMainService.findById(resources.getId());
        if (!byId.getStrategyname().equals(strategyname)) {
            int strategycount = maskStrategyFileMainService.findStrategyCountByStrategyName(strategyname);
            if (strategycount > 0) {
                throw new BadRequestException("策略名称不能重复");
            }
        }
        maskStrategyFileMainService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除文件策略")
    // @ApiOperation("删除文件策略")
    @PreAuthorize("@el.check('maskStrategyFileMain:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskStrategyFileMainService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("返回所有文件脱敏策略")
    // @ApiOperation("返回文件脱敏策略")
    @GetMapping(value = "/getFileMaskStrategys")
    @PreAuthorize("@el.check('maskStrategyFileMain:list')")
    public ResponseEntity<Object> getFileMaskStrategys(String strategytype) {
        return new ResponseEntity<>(maskStrategyFileMainService.getFileMaskStrategys(strategytype), HttpStatus.OK);
    }

    //列数和抽取列的校验
    public void verify(String str, String str2) {
        String flag = "^[0-9]*$";
        boolean b = str.matches(flag);
        if (!b || Integer.valueOf(str) <= 0) {
            throw new BadRequestException("文件总列数只能输入数字且数字要大于0");
        }
        if (str2.length() == 1) {
            boolean b1 = str2.matches(flag);
            if (!b1) {
                throw new BadRequestException("抽取列格式有误,格式示例： 1,2,3");
            }
            if (Integer.valueOf(str2) <= 0 || Integer.valueOf(str2) > Integer.valueOf(str)) {
                throw new BadRequestException("抽取列要在文件总列数的范围内");
            }
        } else {
            String[] split = str2.split(",");
            for (String extractcolum : split) {
                if (!extractcolum.matches(flag)||"".equals(extractcolum)){
                    throw new BadRequestException("抽取列格式有误,格式示例： 1,2,3 ... 间隔逗号为小写");
                }
                if (Integer.valueOf(extractcolum) <= 0 || Integer.valueOf(extractcolum) > Integer.valueOf(str)) {
                    throw new BadRequestException("抽取列要在文件总列数的范围内");
                }
            }
        }
    }


    @GetMapping(value = "/static")
    @Log("获取静态脱敏文件脱敏策略")
    public ResponseEntity<Object> findStaticDesensitizationMaskStrategyFile(@RequestParam(value = "appKey") String appKey) {
        if (!appKey.equals("43aa4a05b55dafc2895d5fa354b1e818")) {
            throw new BadRequestException("接口拒绝访问");
        }
        return new ResponseEntity<>(maskStrategyFileMainService.findMaskStrategyFile(), HttpStatus.OK);
    }

}
