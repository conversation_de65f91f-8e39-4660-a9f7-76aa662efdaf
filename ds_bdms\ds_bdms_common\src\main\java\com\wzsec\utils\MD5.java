package com.wzsec.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.apache.log4j.Logger;
//import cn.ctyun.mask.common.Consts;

/**
 * MD5加密算法
 * 没盐值
 */
public class MD5 {
	
	private static Logger log = Logger.getLogger(MD5.class);
	
	/*static{
        log.info("调用脱敏算法包:"+ Consts.JarPackageName + Consts.LogSplit + "算法:"+ MD5.class.getName());
    }*/

    public static String md5(String data) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(data.getBytes());
        StringBuffer buf = new StringBuffer();
        byte[] bits = md.digest();

        for(int i = 0; i < bits.length; ++i) {
            int a = bits[i];
            if(a < 0) {
                a += 256;
            }
            if(a < 16) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(a));
        }

        return buf.toString();
    }

    public String encrypt(String paramString) throws Exception {
        String returnstr = "";
        //\\u3000是中文(全角)空格   \\u0020空格  *表示多个  \^以什么开始  \$以什么结尾
        if(paramString != null && !"".equals(paramString.trim()) && !paramString.matches("^[\\u0020\\u3000]*$")) {
            returnstr = md5(paramString);
            return returnstr;
        }
        else{
            return paramString;
        }
    }

    public static void main(String[] args) {
        MD5 md5 = new MD5();
        try {
        	//log.info("MD5脱敏:" + md5.encrypt("chinactyun"));
        	System.out.println(md5.encrypt("chinactyun"));
            System.out.println(md5.encrypt("tangfuqiang"));
            System.out.println(md5.encrypt("11010800000001"));
            System.out.println(md5.encrypt("11010800000001@"));
        }
        catch(Exception var3) {
            var3.printStackTrace();
        }
        
        MD5 md52 = new MD5();
        try {
        	//log.info("MD5脱敏:" + md5.encrypt("chinactyun"));
        	System.out.println(md52.encrypt("chinactyun"));
            System.out.println(md52.encrypt("tangfuqiang"));
            System.out.println(md52.encrypt("11010800000001"));
            System.out.println(md52.encrypt("11010800000001@"));
        }
        catch(Exception var3) {
            var3.printStackTrace();
        } 
    }
}
