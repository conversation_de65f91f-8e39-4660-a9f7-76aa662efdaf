package com.wzsec.dotask.mask.service.excute.audit;

import com.jcraft.jsch.*;
import com.wzsec.dotask.mask.service.excute.file.ReadFileService;
import com.wzsec.dotask.sdd.service.excute.common.CheckFileManager;
import com.wzsec.dotask.sdd.service.excute.common.RuleManager;
import com.wzsec.modules.mask.domain.MaskAuditResultV1;
import com.wzsec.modules.mask.domain.MaskAuditResultdetailV1;
import com.wzsec.modules.mask.service.MaskAuditResultV1Service;
import com.wzsec.modules.mask.service.MaskAuditResultdetailV1Service;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1Dto;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.*;
import java.lang.reflect.Field;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Title: MaskFileAudit
 * Decription:
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@Slf4j
public class MaskFileAudit {

    private final DatasourceService datasourceService;
    private final MaskAuditResultdetailV1Service maskAuditResultdetailV1Service;
    private final MaskAuditResultV1Service maskAuditResultV1Service;

    public MaskFileAudit(DatasourceService datasourceService, MaskAuditResultdetailV1Service maskAuditResultdetailV1Service, MaskAuditResultV1Service maskAuditResultV1Service) {
        this.datasourceService = datasourceService;
        this.maskAuditResultdetailV1Service = maskAuditResultdetailV1Service;
        this.maskAuditResultV1Service = maskAuditResultV1Service;
    }

    /**
     * 审计文件
     * @param maskAuditTaskDto
     * @param ruleList
     * @param checkTime
     * @param submituser
     * @return
     */
    public boolean maskAuditForFile(MaskAuditTaskV1Dto maskAuditTaskDto, List<RuleDto> ruleList, String checkTime, String submituser, Map<String, String> lineAlgorithmInfo) {
        log.info("开始执行文件脱敏审计任务");
        boolean resultStatus = false;
        Map<String,String> strategyMap = new HashMap<>();
        Map<String,String> resultMap = new HashMap<>();
        Map<String,Integer> riskCountMap = new HashMap<>();
        // 根据数据源id查询数据源
        DatasourceDto datasource = datasourceService.findById(maskAuditTaskDto.getOutsourceid().longValue());
        String type = datasource.getType();
        String username = datasource.getUsername();
        String password = null, ip = null, port = null;
        if (StringUtils.isNotEmpty(datasource.getPassword())) {
            try {
                String decrypt = AES.decrypt(datasource.getPassword(), Const.AES_SECRET_KEY);
                password = decrypt;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (StringUtils.isNotEmpty(datasource.getSrcurl())){
            String[] hostNameAndPort = datasource.getSrcurl().split(":|：");
            ip = hostNameAndPort[0];
            port = hostNameAndPort.length > 1 ? hostNameAndPort[1] : null;
        }
        log.info("数据源类型：" + type);
        try {
            String outPath = maskAuditTaskDto.getOutfilepath();
            String filetype = maskAuditTaskDto.getFiletype();
            String filespilt = maskAuditTaskDto.getFilespilt();
            String maskstrategy = maskAuditTaskDto.getMaskstrategy();
            String taskName = maskAuditTaskDto.getTaskname();

            String[] strategyArr = maskstrategy.split(",");
            for (String strategy : strategyArr) {
                String[] fieldAlg = strategy.split("\\|");
                if (fieldAlg.length>1){
                    strategyMap.put(fieldAlg[0],fieldAlg[1]);
                }else {
                    strategyMap.put(fieldAlg[0],"");
                }
            }

            if (Const.FILE_HDFS.equals(type)){
                Configuration conf=new Configuration();
                FileSystem fs= FileSystem.get(new URI(outPath),conf);
                Path path = new Path(outPath);
                FileStatus[] status = fs.listStatus(path);
                for (FileStatus file : status) {
                    log.info("文件路径：" + file.getPath().getName());
                    // 匹配类型
                    if (file.isFile()) {
                        String fieldName = file.getPath().getName();
                        if ((Const.MASKAUDIT_FILETYPE_TXT.equals(filetype) && fieldName.endsWith(".txt")) ||
                                (Const.MASKAUDIT_FILETYPE_EXCEL.equals(filetype) && (fieldName.endsWith(".xls") || fieldName.endsWith(".xlsx"))) ||
                                (Const.MASKAUDIT_FILETYPE_CSV.equals(filetype) && fieldName.endsWith(".csv"))
                        ) {
                            FSDataInputStream inputStream = fs.open(file.getPath());
                            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
                            fileCheck(ruleList, strategyMap, riskCountMap, resultMap, checkTime, submituser, maskAuditTaskDto, br, fieldName, filespilt, lineAlgorithmInfo);
                            br.close();
                            inputStream.close();
                        }
                    }
                }
            } else if (Const.FILE_LOCAL.equals(type) || Const.FILE_LINUX.equals(type)){
                List<File> fileList = new ArrayList<>();
                FileUtil.getFileList(outPath, fileList);//获取目录下所有文件，含子目录
                for (File file : fileList) {
                    String fieldName = file.getName();
                    log.info("文件路径：" + file.getPath());
                    // 匹配类型
                    if (file.isFile()) {
                        ArrayList<String> readFileList = null;
                        if (Const.MASKAUDIT_FILETYPE_TXT.equals(filetype) && fieldName.endsWith("txt")) {
                            readFileList = ReadFileService.readTxtFile(file.getPath(),null);
                        } else if (Const.MASKAUDIT_FILETYPE_CSV.equals(filetype) && fieldName.endsWith("csv")) {
                            readFileList = ReadFileService.readCsvFile(file.getPath(), null);
                        } else if (Const.MASKAUDIT_FILETYPE_EXCEL.equals(filetype) && fieldName.endsWith("xls")) {
                            readFileList = ReadFileService.readXlsFile(file.getPath(), filespilt, null);
                        } else if (Const.MASKAUDIT_FILETYPE_EXCEL.equals(filetype) && fieldName.endsWith("xlsx")) {
                            readFileList = ReadFileService.readXlsxFile(file.getPath(), filespilt, null);
                        }
                        if (readFileList != null) {
                            fileCheck(ruleList, strategyMap, riskCountMap, resultMap, checkTime, submituser, maskAuditTaskDto, readFileList, fieldName, filespilt, lineAlgorithmInfo);
                        }
                    }
                }
            } else if (Const.FILE_FTP.equals(type)){
                SSHFieldReader sshFieldReader = new SSHFieldReader(ip,port,username,password,outPath);
                List<String> fieldList = sshFieldReader.getFileList();
                Session session = sshFieldReader.getSession();
                for (String fieldName : fieldList) {
                    log.info("文件路径：" + fieldName);
                    if ((Const.MASKAUDIT_FILETYPE_TXT.equals(filetype) && fieldName.endsWith(".txt")) ||
                            (Const.MASKAUDIT_FILETYPE_EXCEL.equals(filetype) && (fieldName.endsWith(".xls") || fieldName.endsWith(".xlsx"))) ||
                            (Const.MASKAUDIT_FILETYPE_CSV.equals(filetype) && fieldName.endsWith(".csv"))
                    ) {
                        String command = "cat " + fieldName ; // 查看文件内容指令
                        Channel channel = sshFieldReader.getChannel(session, command);
                        InputStream stdout = channel.getInputStream();
                        BufferedReader reader = new BufferedReader(new InputStreamReader(stdout));
                        channel.connect();
                        fileCheck(ruleList, strategyMap, riskCountMap, resultMap, checkTime, submituser, maskAuditTaskDto, reader, fieldName, filespilt, lineAlgorithmInfo);
                        reader.close();
                        stdout.close();
                        session.disconnect();
                        channel.disconnect();
                    }
                }
            } else {
                throw new Exception("暂不支持该文件类型审计");
            }

            resultStatus = true;
            log.info("文件检测成功");
        }catch (Exception e){
            log.info("文件检测出现异常：" + e.getMessage());
            resultStatus = false;
            e.printStackTrace();
        }finally {
            // 清理数据
            riskCountMap = null;
            resultMap = null;
            strategyMap = null;
        }
        return resultStatus;
    }


    public void fileCheck(List<RuleDto> ruleList,Map<String,String> strategyMap,
                          Map<String,Integer> riskCountMap,Map<String,String> resultMap,
                          String checkTime, String submituser,MaskAuditTaskV1Dto maskAuditTaskDto,
                          BufferedReader br, String fileName,String filespilt,
                          Map<String, String> lineAlgorithmInfo) throws Exception {

        int linenumber = 0;
        String line = null;
        while (null != (line = br.readLine())){
            if (linenumber<10){
                linenumber++;
                String[] dataArr = line.split(filespilt);
                for (int j = 0; j < dataArr.length; j++) {
                    for (RuleDto ruleDto : ruleList) {
                        boolean result = RuleManager.checkDataByRuleDto(dataArr[j], ruleDto,null);
                        if (result){
                            String applytypecname = ruleDto.getApplytypecname();
                            if (strategyMap.containsKey(String.valueOf(j+1))){
                                // 敏感  field-->alg-是否符合策略-敏感级别-样例-文件名
                                //if (StringUtils.isNotEmpty(strategyMap.get(String.valueOf(j+1)))){
                                if (lineAlgorithmInfo!=null && lineAlgorithmInfo.containsKey(String.valueOf(j+1))){
                                    // 符合策略-敏感 中
                                    resultMap.put(String.valueOf(j+1),
                                            strategyMap.get(String.valueOf(j+1)) + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_STRATEGY_TRUE + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_RESULT_MIDDLE + Const.AUDIT_SPLIT_JOIN +
                                                    applytypecname + ":" + dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                    fileName);
                                }else {
                                    // 不符合策略-敏感 高
                                    resultMap.put(String.valueOf(j+1),
                                            "无" + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_STRATEGY_FALSE + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_RESULT_HIGH + Const.AUDIT_SPLIT_JOIN +
                                                    applytypecname + ":" + dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                    fileName);
                                }
                            }
                        }else {
                            // String applytypecname = Const.LEVEL_NO;
                            if (strategyMap.containsKey(String.valueOf(j+1))){
                                //if (StringUtils.isNotEmpty(strategyMap.get(String.valueOf(j+1)))){
                                if (lineAlgorithmInfo!= null && lineAlgorithmInfo.containsKey(String.valueOf(j+1))){
                                    // 符合策略-不敏感 无
                                    if (!resultMap.containsKey(String.valueOf(j+1))){
                                        resultMap.put(String.valueOf(j+1),
                                                strategyMap.get(String.valueOf(j+1)) + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_STRATEGY_TRUE + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_RESULT_NO + Const.AUDIT_SPLIT_JOIN +
                                                        dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                        fileName);
                                    }
                                }else {
                                    // 不符合策略-不敏感 低
                                    if (!resultMap.containsKey(String.valueOf(j+1))){
                                        resultMap.put(String.valueOf(j+1),
                                                "无" + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_STRATEGY_FALSE + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_RESULT_LOW + Const.AUDIT_SPLIT_JOIN +
                                                        dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                        fileName);
                                    }
                                }
                            }
                        }
                    }
                }
            }else {
                linenumber++;
            }
        }
        // 统计详情
        for (String field : resultMap.keySet()) {
            String value = resultMap.get(field);
            String[] values = value.split(Const.AUDIT_SPLIT_JOIN);
            MaskAuditResultdetailV1 detail = new MaskAuditResultdetailV1();
            detail.setTaskname(maskAuditTaskDto.getTaskname());
            detail.setLinenumber(linenumber);
            detail.setField("第"+field+"列");
            detail.setAlgorithm(values[0]);
            detail.setIssafesame(values[1]);
            detail.setRisk(values[2]);
            detail.setExample(values[3]);
            detail.setFilename(values[4]);
            detail.setChecktime(checkTime);
            detail.setSubmitter(submituser);
            if (riskCountMap.containsKey(detail.getRisk())){
                riskCountMap.put(detail.getRisk(),riskCountMap.get(detail.getRisk())+1);
            }else {
                riskCountMap.put(detail.getRisk(),1);
            }
            maskAuditResultdetailV1Service.create(detail);
        }
        log.info("详情结果统计完成");

        // 统计概要
        for (String key : riskCountMap.keySet()) {
            MaskAuditResultV1 result = new MaskAuditResultV1();
            result.setChecktime(checkTime);
            result.setChecktype(maskAuditTaskDto.getChecktype());
            result.setOutpath(maskAuditTaskDto.getOutfilepath());
            result.setCount(String.valueOf(riskCountMap.get(key)));
            result.setRisk(key);
            result.setSubmitter(submituser);
            result.setTaskname(maskAuditTaskDto.getTaskname());
            result.setSystemid(maskAuditTaskDto.getSystemid());
            maskAuditResultV1Service.create(result);
        }
        log.info("概要结果统计完成");
    }

    public void fileCheck(List<RuleDto> ruleList,Map<String,String> strategyMap,
                          Map<String,Integer> riskCountMap,Map<String,String> resultMap,
                          String checkTime, String submituser,MaskAuditTaskV1Dto maskAuditTaskDto,
                          List<String> fileData, String fileName,String filespilt,
                          Map<String, String> lineAlgorithmInfo) throws Exception {

        int linenumber = 0;
        for (String fileDatum : fileData) {
            if (linenumber<10){
                linenumber++;
                String[] dataArr = fileDatum.split(filespilt);
                for (int j = 0; j < dataArr.length; j++) {
                    for (RuleDto ruleDto : ruleList) {
                        String column = String.valueOf(j + 1);
                        boolean result = RuleManager.checkDataByRuleDto(dataArr[j], ruleDto,null);
                        if (result){
                            String applytypecname = ruleDto.getApplytypecname();
                            if (strategyMap.containsKey(column)){
                                // 敏感  field-->alg-是否符合策略-敏感级别-样例-文件名
                                //if (StringUtils.isNotEmpty(strategyMap.get(String.valueOf(j+1)))){
                                if (lineAlgorithmInfo != null && strategyMap.get(column).equals(lineAlgorithmInfo.get(column))){
                                    // 符合策略-敏感 中
                                    resultMap.put(column,
                                            strategyMap.get(String.valueOf(j+1)) + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_STRATEGY_TRUE + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_RESULT_MIDDLE + Const.AUDIT_SPLIT_JOIN +
                                                    applytypecname + ":" + dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                    fileName);
                                }else {
                                    // 不符合策略-敏感 高
                                    resultMap.put(column,
                                            "无" + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_STRATEGY_FALSE + Const.AUDIT_SPLIT_JOIN +
                                                    Const.MASKAUDIT_RESULT_HIGH + Const.AUDIT_SPLIT_JOIN +
                                                    applytypecname + ":" + dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                    fileName);
                                }
                            }
                        }else {
                            // String applytypecname = Const.LEVEL_NO;
                            if (strategyMap.containsKey(column)){
                                //if (StringUtils.isNotEmpty(strategyMap.get(String.valueOf(j+1)))){
                                if (lineAlgorithmInfo != null && strategyMap.get(column).equals(lineAlgorithmInfo.get(column))){
                                    // 符合策略-不敏感 无
                                    if (!resultMap.containsKey(column)){
                                        resultMap.put(column,
                                                strategyMap.get(column) + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_STRATEGY_TRUE + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_RESULT_NO + Const.AUDIT_SPLIT_JOIN +
                                                        dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                        fileName);
                                    }
                                }else {
                                    // 不符合策略-不敏感 低
                                    if (!resultMap.containsKey(column)){
                                        resultMap.put(column,
                                                "无" + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_STRATEGY_FALSE + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_RESULT_LOW + Const.AUDIT_SPLIT_JOIN +
                                                        dataArr[j] + Const.AUDIT_SPLIT_JOIN +
                                                        fileName);
                                    }
                                }
                            }
                        }
                    }
                }
            }else {
                linenumber++;
            }
        }

        // 统计详情
        for (String field : resultMap.keySet()) {
            String value = resultMap.get(field);
            String[] values = value.split(Const.AUDIT_SPLIT_JOIN);
            MaskAuditResultdetailV1 detail = new MaskAuditResultdetailV1();
            detail.setTaskname(maskAuditTaskDto.getTaskname());
            detail.setLinenumber(linenumber);
            detail.setField("第"+field+"列");
            detail.setAlgorithm(values[0]);
            detail.setIssafesame(values[1]);
            detail.setRisk(values[2]);
            detail.setExample(values[3]);
            detail.setFilename(values[4]);
            detail.setChecktime(checkTime);
            detail.setSubmitter(submituser);
            if (riskCountMap.containsKey(detail.getRisk())){
                riskCountMap.put(detail.getRisk(),riskCountMap.get(detail.getRisk())+1);
            }else {
                riskCountMap.put(detail.getRisk(),1);
            }
            maskAuditResultdetailV1Service.create(detail);
        }
        log.info("详情结果统计完成");

        // 统计概要
        for (String key : riskCountMap.keySet()) {
            MaskAuditResultV1 result = new MaskAuditResultV1();
            result.setChecktime(checkTime);
            result.setChecktype(maskAuditTaskDto.getChecktype());
            result.setOutpath(maskAuditTaskDto.getOutfilepath());
            result.setCount(String.valueOf(riskCountMap.get(key)));
            result.setRisk(key);
            result.setSubmitter(submituser);
            result.setTaskname(maskAuditTaskDto.getTaskname());
            result.setSystemid(maskAuditTaskDto.getSystemid());
            maskAuditResultV1Service.create(result);
        }
        log.info("概要结果统计完成");
    }
}
