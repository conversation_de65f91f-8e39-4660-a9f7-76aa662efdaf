package com.wzsec.modules.correlation.service.mapper;

import com.wzsec.modules.correlation.domain.KeyCorrelationMaster;
import com.wzsec.modules.correlation.service.dto.KeyCorrelationMasterDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:02+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KeyCorrelationMasterMapperImpl implements KeyCorrelationMasterMapper {

    @Override
    public KeyCorrelationMasterDto toDto(KeyCorrelationMaster entity) {
        if ( entity == null ) {
            return null;
        }

        KeyCorrelationMasterDto keyCorrelationMasterDto = new KeyCorrelationMasterDto();

        keyCorrelationMasterDto.setCreatetime( entity.getCreatetime() );
        keyCorrelationMasterDto.setCreateuser( entity.getCreateuser() );
        keyCorrelationMasterDto.setId( entity.getId() );
        keyCorrelationMasterDto.setMfid( entity.getMfid() );
        keyCorrelationMasterDto.setMtid( entity.getMtid() );
        keyCorrelationMasterDto.setSourceid( entity.getSourceid() );
        keyCorrelationMasterDto.setUpdatetime( entity.getUpdatetime() );
        keyCorrelationMasterDto.setUpdateuser( entity.getUpdateuser() );

        return keyCorrelationMasterDto;
    }

    @Override
    public List<KeyCorrelationMasterDto> toDto(List<KeyCorrelationMaster> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KeyCorrelationMasterDto> list = new ArrayList<KeyCorrelationMasterDto>( entityList.size() );
        for ( KeyCorrelationMaster keyCorrelationMaster : entityList ) {
            list.add( toDto( keyCorrelationMaster ) );
        }

        return list;
    }

    @Override
    public KeyCorrelationMaster toEntity(KeyCorrelationMasterDto dto) {
        if ( dto == null ) {
            return null;
        }

        KeyCorrelationMaster keyCorrelationMaster = new KeyCorrelationMaster();

        keyCorrelationMaster.setCreatetime( dto.getCreatetime() );
        keyCorrelationMaster.setCreateuser( dto.getCreateuser() );
        keyCorrelationMaster.setId( dto.getId() );
        keyCorrelationMaster.setMfid( dto.getMfid() );
        keyCorrelationMaster.setMtid( dto.getMtid() );
        keyCorrelationMaster.setSourceid( dto.getSourceid() );
        keyCorrelationMaster.setUpdatetime( dto.getUpdatetime() );
        keyCorrelationMaster.setUpdateuser( dto.getUpdateuser() );

        return keyCorrelationMaster;
    }

    @Override
    public List<KeyCorrelationMaster> toEntity(List<KeyCorrelationMasterDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KeyCorrelationMaster> list = new ArrayList<KeyCorrelationMaster>( dtoList.size() );
        for ( KeyCorrelationMasterDto keyCorrelationMasterDto : dtoList ) {
            list.add( toEntity( keyCorrelationMasterDto ) );
        }

        return list;
    }
}
