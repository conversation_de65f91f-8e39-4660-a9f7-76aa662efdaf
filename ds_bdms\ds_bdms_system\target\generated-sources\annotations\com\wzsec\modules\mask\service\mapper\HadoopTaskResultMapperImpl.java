package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HadoopTaskResult;
import com.wzsec.modules.mask.service.dto.HadoopTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:06+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class HadoopTaskResultMapperImpl implements HadoopTaskResultMapper {

    @Override
    public HadoopTaskResultDto toDto(HadoopTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        HadoopTaskResultDto hadoopTaskResultDto = new HadoopTaskResultDto();

        hadoopTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        hadoopTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        hadoopTaskResultDto.setConfigargs( entity.getConfigargs() );
        hadoopTaskResultDto.setCreatetime( entity.getCreatetime() );
        hadoopTaskResultDto.setDatafileinputpath( entity.getDatafileinputpath() );
        hadoopTaskResultDto.setDatafileoutputpath( entity.getDatafileoutputpath() );
        hadoopTaskResultDto.setDatainputpath( entity.getDatainputpath() );
        hadoopTaskResultDto.setDataoutputpath( entity.getDataoutputpath() );
        hadoopTaskResultDto.setDatarows( entity.getDatarows() );
        hadoopTaskResultDto.setDatasplit( entity.getDatasplit() );
        hadoopTaskResultDto.setFileformat( entity.getFileformat() );
        hadoopTaskResultDto.setId( entity.getId() );
        hadoopTaskResultDto.setJobendtime( entity.getJobendtime() );
        hadoopTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        hadoopTaskResultDto.setJobstatus( entity.getJobstatus() );
        hadoopTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        hadoopTaskResultDto.setPlatform( entity.getPlatform() );
        hadoopTaskResultDto.setRemark( entity.getRemark() );
        hadoopTaskResultDto.setSparefield1( entity.getSparefield1() );
        hadoopTaskResultDto.setSparefield2( entity.getSparefield2() );
        hadoopTaskResultDto.setSparefield3( entity.getSparefield3() );
        hadoopTaskResultDto.setSparefield4( entity.getSparefield4() );
        hadoopTaskResultDto.setTaskname( entity.getTaskname() );
        hadoopTaskResultDto.setUpdatetime( entity.getUpdatetime() );
        hadoopTaskResultDto.setUsername( entity.getUsername() );

        return hadoopTaskResultDto;
    }

    @Override
    public List<HadoopTaskResultDto> toDto(List<HadoopTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HadoopTaskResultDto> list = new ArrayList<HadoopTaskResultDto>( entityList.size() );
        for ( HadoopTaskResult hadoopTaskResult : entityList ) {
            list.add( toDto( hadoopTaskResult ) );
        }

        return list;
    }

    @Override
    public HadoopTaskResult toEntity(HadoopTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        HadoopTaskResult hadoopTaskResult = new HadoopTaskResult();

        hadoopTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        hadoopTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        hadoopTaskResult.setConfigargs( dto.getConfigargs() );
        hadoopTaskResult.setCreatetime( dto.getCreatetime() );
        hadoopTaskResult.setDatafileinputpath( dto.getDatafileinputpath() );
        hadoopTaskResult.setDatafileoutputpath( dto.getDatafileoutputpath() );
        hadoopTaskResult.setDatainputpath( dto.getDatainputpath() );
        hadoopTaskResult.setDataoutputpath( dto.getDataoutputpath() );
        hadoopTaskResult.setDatarows( dto.getDatarows() );
        hadoopTaskResult.setDatasplit( dto.getDatasplit() );
        hadoopTaskResult.setFileformat( dto.getFileformat() );
        hadoopTaskResult.setId( dto.getId() );
        hadoopTaskResult.setJobendtime( dto.getJobendtime() );
        hadoopTaskResult.setJobstarttime( dto.getJobstarttime() );
        hadoopTaskResult.setJobstatus( dto.getJobstatus() );
        hadoopTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        hadoopTaskResult.setPlatform( dto.getPlatform() );
        hadoopTaskResult.setRemark( dto.getRemark() );
        hadoopTaskResult.setSparefield1( dto.getSparefield1() );
        hadoopTaskResult.setSparefield2( dto.getSparefield2() );
        hadoopTaskResult.setSparefield3( dto.getSparefield3() );
        hadoopTaskResult.setSparefield4( dto.getSparefield4() );
        hadoopTaskResult.setTaskname( dto.getTaskname() );
        hadoopTaskResult.setUpdatetime( dto.getUpdatetime() );
        hadoopTaskResult.setUsername( dto.getUsername() );

        return hadoopTaskResult;
    }

    @Override
    public List<HadoopTaskResult> toEntity(List<HadoopTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HadoopTaskResult> list = new ArrayList<HadoopTaskResult>( dtoList.size() );
        for ( HadoopTaskResultDto hadoopTaskResultDto : dtoList ) {
            list.add( toEntity( hadoopTaskResultDto ) );
        }

        return list;
    }
}
