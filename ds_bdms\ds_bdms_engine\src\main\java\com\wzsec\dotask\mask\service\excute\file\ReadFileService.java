package com.wzsec.dotask.mask.service.excute.file;

import cn.hutool.core.lang.Console;
import com.alibaba.fastjson.JSON;
import com.wzsec.utils.ExcelUtil;
import com.wzsec.utils.FileEncode;
import com.wzsec.utils.StringUtils;
import org.apache.commons.io.FileUtils;
//import org.apache.poi.POIXMLDocument;
import org.apache.poi.hssf.record.crypto.Biff8EncryptionKey;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.crypt.Decryptor;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.NativeDetector;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

public class ReadFileService{

	/**
	 *@Title: readTxtFile
	 *@Description: 读取txt文件内容
	 *<AUTHOR>
	 *@date 2019年11月12日
	 */
	public static ArrayList<String> readTxtFile(String filePath, Integer limitLine) {
		 ArrayList<String> arrayList = new ArrayList<String>();
	        try {
	            File f = new File(filePath);
	            if(f.isFile()&&f.exists()){
	                // 获取文件编码格式
	                String code = FileEncode.getFileEncode(filePath);
	                // 根据编码格式解析文件
	                if("asci".equals(code)){
	                    // 这里采用GBK编码，而不用环境编码格式，因为环境默认编码不等于操作系统编码
	                    // code = System.getProperty("file.encoding");
	                    code = "GBK";
	                }

	                InputStreamReader read = new InputStreamReader(new FileInputStream(f),code);
	                BufferedReader reader=new BufferedReader(read);
	                String line;
					if (limitLine != null){
						Integer count = 0;
						while ((line = reader.readLine()) != null && (count <= limitLine)) {
							arrayList.add(line);
							count ++;
						}
					} else {
						while ((line = reader.readLine()) != null) {
							arrayList.add(line);
						}
					}
	                read.close();
	            }
	        } catch (Exception e) {
	        }
	        return arrayList;
	}

	/**
	 *@Title: readCsvFile
	 *@Description: 读取csv文件内容
	 *<AUTHOR>
	 *@date 2019年11月12日
	 */
	public static ArrayList<String> readCsvFile(String filePath, Integer limitLine) {
		InputStreamReader inStreamReader = null;
		BufferedReader bufReader = null;
		ArrayList<String> strDataList = null;
		try {
			File f = new File(filePath);
			if(f.isFile()&&f.exists()) {
				// 获取文件编码格式
				String code = FileEncode.getFileEncode(filePath);
				// 根据编码格式解析文件
				if ("asci".equals(code)) {
					// 这里采用GBK编码，而不用环境编码格式，因为环境默认编码不等于操作系统编码
					// code = System.getProperty("file.encoding");
					code = "GBK";
				}
				inStreamReader = new InputStreamReader(new FileInputStream(new File(filePath)), code);  //取决于csv文件编码格式
				bufReader = new BufferedReader(inStreamReader);
				strDataList = new ArrayList<String>();  //拆分后存放到List
				String strLine = "";  //每行数据
				if (limitLine != null){
					Integer count = 0;
					while ((strLine = bufReader.readLine()) != null && (count <= limitLine)) {
						strDataList.add(strLine);
						count ++;
					}
				} else {
					while ((strLine = bufReader.readLine()) != null) {
						strDataList.add(strLine);
					}
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			if (inStreamReader != null) {
				try {
					inStreamReader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (bufReader != null) {
				try {
					bufReader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return strDataList;
	}

	/**
	 *@Title: readXlsFile
	 *@Description: 读取excel文件(xls)
	 *<AUTHOR>
	 *@date 2020年2月12日
	 */
	public static ArrayList<String> readXlsFile(String absolutePath,String splitstr, Integer limitLine){
		ArrayList<String> list = new ArrayList<String>();
		 //检查文件
        //checkFile(file);
        //获得Workbook工作薄对象
        Workbook workbook;
		try {
			workbook = getWorkBook(new File(absolutePath),null);
			  //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
	        if(workbook != null){
	            for(int sheetNum = 0;sheetNum < workbook.getNumberOfSheets();sheetNum++){
	                //获得当前sheet工作表
	                Sheet sheet = workbook.getSheetAt(sheetNum);
	                if(sheet == null){
	                    continue;
	                }
	                //获得当前sheet的开始行
	                int firstRowNum  = sheet.getFirstRowNum();
	                //获得当前sheet的结束行
	                int lastRowNum = sheet.getLastRowNum();
					if (limitLine != null){
						lastRowNum = limitLine;
					}

					//循环所有行
					for(int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++){
						//获得当前行
						Row row = sheet.getRow(rowNum);
						if(row == null){
							continue;
						}
						//获得当前行的开始列
						int firstCellNum = row.getFirstCellNum();
						//获得当前行的最后一列
						int LastCellNum = row.getLastCellNum();
						//循环当前行
						StringBuffer sb = new StringBuffer();
						for(int cellNum = firstCellNum; cellNum < LastCellNum;cellNum++){
							Cell cell = row.getCell(cellNum);
							if(cell!=null){
								String str = getCellValue(cell);
								sb.append(str);
								sb.append(splitstr);
							}
						}
						list.add(sb.substring(0,sb.length()-1).toString());
					}

	            }
	            try {
					workbook.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
	        }
		} catch (Exception e1) {
			e1.printStackTrace();
		}
        return list;
	}

	/**
	 *@Title: readXlsxFile
	 *@Description: 读取excel文件(xlsx)
	 *<AUTHOR>
	 *@date 2020年2月12日
	 */
	public static ArrayList<String> readXlsxFile(String absolutePath,String splitstr, Integer limitLine) {
		ArrayList<String> list = new ArrayList<String>();
		 //检查文件
       //checkFile(file);
       //获得Workbook工作薄对象
       Workbook workbook;
		try {
			workbook = getWorkBook(new File(absolutePath),null);
			  //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
	        if(workbook != null){
	            for(int sheetNum = 0;sheetNum < workbook.getNumberOfSheets();sheetNum++){
	                //获得当前sheet工作表
	                Sheet sheet = workbook.getSheetAt(sheetNum);
	                if(sheet == null){
	                    continue;
	                }
	                //获得当前sheet的开始行
	                int firstRowNum  = sheet.getFirstRowNum();
	                //获得当前sheet的结束行
	                int lastRowNum = sheet.getLastRowNum();
					if (limitLine != null){
						lastRowNum = limitLine;
					}
	                //循环所有行
	                for(int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++){
	                    //获得当前行
	                    Row row = sheet.getRow(rowNum);
	                    if(row == null){
	                        continue;
	                    }
	                    //获得当前行的开始列
	                    int firstCellNum = row.getFirstCellNum();
	                    //获得当前行的最后一列
	                    int LastCellNum = row.getLastCellNum();
	                    //循环当前行
	                    StringBuffer sb = new StringBuffer();
	                    for(int cellNum = firstCellNum; cellNum < LastCellNum;cellNum++){
	                        Cell cell = row.getCell(cellNum);
	                        if(cell!=null){
	                        	String str = getCellValue(cell);
								sb.append(str);
								sb.append(splitstr);
							}
	                    }
	                    list.add(sb.substring(0,sb.length()-1).toString());
	                }
	            }
	            try {
					workbook.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
	        }
		} catch (Exception e1) {
			e1.printStackTrace();
		}
       return list;
	}


	/**
	 * 是否是2007及以上版本
	 *
	 * @description 通过检测文件头判断是2007及以上返回true 不是返回false
	 * @return true 是 false 不是
	 * @throws IOException
	 * <AUTHOR>
	 * @date 2019-06-17
	 */
	public static boolean isOffice2007(String filePath) {

		boolean b = false;
		try {
			InputStream fis = new ByteArrayInputStream(FileUtils.readFileToByteArray(new File(filePath)));
			if (!fis.markSupported()) {
				fis = new PushbackInputStream(fis, 8);
			}
			//能正常打开说明是2007及以上的文件
			OPCPackage pkg = OPCPackage.open(fis);
			b = true;
		} catch (Exception e) {
			//e.printStackTrace();
		}
		// if (POIFSFileSystem.hasPOIFSHeader(fis)) {//2003及以下版本
		// return false;
		// }
//		if (POIXMLDocument.hasOOXMLHeader(fis)) {// 2007及以上版本
//			b = true;
//		}
		return b;
	}



	/**
     * 创建Workbook工作薄对象
     * @param file
     * @throws Exception
     */
	public static Workbook getWorkBook(File file,String pwd) throws Exception {
		// 创建Workbook工作薄对象，表示整个excel
		Workbook workbook = null;
		InputStream is = null;
		// 获取excel文件的io流
		try {
			is = new FileInputStream(file);
			// 1.不加密
			// 根据程序判断excel是2007以上版本还是2003版本获得不同的Workbook实现类对象
			if (isOffice2007(file.getAbsolutePath())) {
				// 2007
				workbook = new XSSFWorkbook(is);
			} else {
				// 2003
				workbook = new HSSFWorkbook(is);
			}
			is.close();
		} catch (Exception e) {
			// 2.加密
			POIFSFileSystem pfs = null;
			try {
				is = new FileInputStream(file);
				// 2007
				pfs = new POIFSFileSystem(is);
				EncryptionInfo encInfo = new EncryptionInfo(pfs);
				Decryptor decryptor = Decryptor.getInstance(encInfo);
				if (decryptor.verifyPassword(pwd)) {
					//logger.info(file + "为2007版本，Excel解密成功");
					workbook = new XSSFWorkbook(decryptor.getDataStream(pfs));
				} else {
					throw new RuntimeException("解析xlsx文件失败,解密密码错误");
				}
				is.close();
			} catch (RuntimeException e1) {
				throw new RuntimeException(e1.getMessage());
			} catch (Exception e1) {
				try {
					is = new FileInputStream(file);
					// 2003
					pfs = new POIFSFileSystem(is);
					Biff8EncryptionKey.setCurrentUserPassword(pwd);
					workbook = new HSSFWorkbook(pfs);
					//logger.info(file + "为2003版本，Excel解密成功");
					is.close();
				} catch (Exception e2) {
					//logger.info(file + "为2003版本，Excel解密失败");
					throw new RuntimeException("解析xls文件失败,不支持2003版本加密的Excel文件");
				}
			}
		} finally {
			try {
				if (is != null)
					is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return workbook;
	}
    /**
     * 获取数据类型
     * @param cell
     */
	private static String getCellValue(Cell cell) {
		String cellValue = "";
		if (cell == null) {
			return cellValue;
		}
		//把数字当成String来读，避免出现1读成1.0的情况
		if (cell.getCellType() == CellType.NUMERIC) {
			cell.setCellType(CellType.STRING);
			//CellType.STRING
		}
		//判断数据的类型
		switch (cell.getCellType()) {
			case NUMERIC: //数字
				cellValue = String.valueOf(cell.getNumericCellValue());
				break;
			case STRING: //字符串
				cellValue = String.valueOf(cell.getStringCellValue());
				break;
			case BOOLEAN: //Boolean
				cellValue = String.valueOf(cell.getBooleanCellValue());
				break;
			case FORMULA: //公式
				cellValue = String.valueOf(cell.getCellFormula());
				break;
			case BLANK: //空值
				cellValue = "";
				break;
			case ERROR: //故障
				cellValue = "非法字符";
				break;
			default:
				cellValue = "未知类型";
				break;
		}
		return cellValue;
	}


	public static void Sheet5(List<String> sqlList){
		String path = "D:\\System\\Desktop\\管网分类分级202408\\数据架构管理-20240827.xlsx";
		String splitstr = ",";
		ArrayList<String> list = readXlsxFile_TEST5(path, splitstr);
		System.out.println("数据数量："+list.size());
		for (int i = 1; i < list.size(); i++) {
			String[] split = list.get(i).split(",");
			String sql = "INSERT INTO `sdd_category` VALUES (null, '"+split[0]+"', '"+split[1]+"', '5', 0, NULL, NULL, NULL, '管理员', '2024-08-29 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL);";
			sqlList.add(sql);
		}
	}

	public static void Sheet4(List<String> sqlList){
		String path = "D:\\System\\Desktop\\管网分类分级202408\\数据架构管理-20240827.xlsx";
		String splitstr = ",";
		ArrayList<String> list = readXlsxFile_TEST4(path, splitstr);
		System.out.println("数据数量："+list.size());
		for (int i = 1; i < list.size(); i++) {
			String[] split = list.get(i).split(",");
			String sql = "INSERT INTO `sdd_category` VALUES (null, '"+split[0]+"', '"+split[1]+"', '4', 0, NULL, NULL, NULL, '管理员', '2024-08-29 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL);";
			sqlList.add(sql);
		}
	}

	public static void Sheet3(List<String> sqlList){
		String path = "D:\\System\\Desktop\\管网分类分级202408\\数据架构管理-20240827.xlsx";
		String splitstr = ",";
		ArrayList<String> list = readXlsxFile_TEST3(path, splitstr);
		System.out.println("数据数量："+list.size());
		for (int i = 1; i < list.size(); i++) {
			String[] split = list.get(i).split(",");
			String sql = "INSERT INTO `sdd_category` VALUES (null, '"+split[0]+"', '"+split[1]+"', '3', 0, NULL, NULL, NULL, '管理员', '2024-08-29 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL);";
			sqlList.add(sql);
		}
	}

	public static void Sheet2(List<String> sqlList){
		String path = "D:\\System\\Desktop\\管网分类分级202408\\数据架构管理-20240827.xlsx";
		String splitstr = ",";
		ArrayList<String> list = readXlsxFile_TEST2(path, splitstr);
		System.out.println("数据数量："+list.size());
		for (int i = 1; i < list.size(); i++) {
			String[] split = list.get(i).split(",");
			String sql = "INSERT INTO `sdd_category` VALUES (null, '"+split[0]+"', '"+split[1]+"', '2', 0, NULL, NULL, NULL, '管理员', '2024-08-29 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL);";
			sqlList.add(sql);
		}
	}

	public static void Sheet1(List<String> sqlList){
		String path = "D:\\System\\Desktop\\管网分类分级202408\\数据架构管理-20240827.xlsx";
		String splitstr = ",";
		ArrayList<String> list = readXlsxFile_TEST1(path, splitstr);
		System.out.println("数据数量："+list.size());
		for (int i = 1; i < list.size(); i++) {
			String[] split = list.get(i).split(",");
			String sql = "INSERT INTO `sdd_category` VALUES (null, '"+split[0]+"', '"+split[1]+"', '1', 0, NULL, NULL, NULL, '管理员', '2024-08-29 00:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL);";
			sqlList.add(sql);
		}
	}

	public static ArrayList<String> readXlsxFile_TEST1(String absolutePath, String splitstr) {
		ArrayList<String> list = new ArrayList<String>();
		// 获得Workbook工作薄对象
		Workbook workbook;
		try {
			workbook = getWorkBook(new File(absolutePath), null);
			// 创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
			if (workbook != null) {
				int numberOfSheets = 1; // 假设我们只处理第一个工作表
				for (int sheetNum = 0; sheetNum < numberOfSheets; sheetNum++) {
					// 获得当前sheet工作表
					Sheet sheet = workbook.getSheetAt(sheetNum);
					if (sheet == null) {
						continue;
					}
					// 循环所有行
					for (int rowNum = sheet.getFirstRowNum(); rowNum <= sheet.getLastRowNum(); rowNum++) {
						// 获得当前行
						Row row = sheet.getRow(rowNum);
						if (row == null) {
							continue;
						}
						// 初始化StringBuffer来存储当前行的数据
						StringBuffer sb = new StringBuffer();
						// 只处理第三列和第五列（注意：列索引从0开始）
						int[] desiredColumns = {2, 3}; // 第三列和第五列的索引
						for (int columnIndex : desiredColumns) {
							Cell cell = row.getCell(columnIndex);
							if (cell != null) {
								String str = getCellValue(cell);
								sb.append(str);
								sb.append(splitstr);
							}
						}
						// 移除最后一个分隔符并添加到列表中
						if (sb.length() > 0) {
							list.add(sb.substring(0, sb.length() - splitstr.length()));
						}
					}
				}
				try {
					workbook.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return list;
	}

	public static ArrayList<String> readXlsxFile_TEST2(String absolutePath, String splitstr) {
		ArrayList<String> list = new ArrayList<String>();
		// 获得Workbook工作薄对象
		Workbook workbook;
		try {
			workbook = getWorkBook(new File(absolutePath), null);
			// 创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
			if (workbook != null) {
				// 获得当前sheet工作表
				Sheet sheet = workbook.getSheetAt(1);
				// 循环所有行
				for (int rowNum = sheet.getFirstRowNum(); rowNum <= sheet.getLastRowNum(); rowNum++) {
					// 获得当前行
					Row row = sheet.getRow(rowNum);
					if (row == null) {
						continue;
					}
					// 初始化StringBuffer来存储当前行的数据
					StringBuffer sb = new StringBuffer();
					// 只处理第三列和第五列（注意：列索引从0开始）
					int[] desiredColumns = {2, 3}; // 第三列和第五列的索引
					for (int columnIndex : desiredColumns) {
						Cell cell = row.getCell(columnIndex);
						if (cell != null) {
							String str = getCellValue(cell);
							sb.append(str);
							sb.append(splitstr);
						}
					}
					// 移除最后一个分隔符并添加到列表中
					if (sb.length() > 0) {
						list.add(sb.substring(0, sb.length() - splitstr.length()));
					}
				}
				try {
					workbook.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return list;
	}

	public static ArrayList<String> readXlsxFile_TEST3(String absolutePath, String splitstr) {
		ArrayList<String> list = new ArrayList<String>();
		// 获得Workbook工作薄对象
		Workbook workbook;
		try {
			workbook = getWorkBook(new File(absolutePath), null);
			// 创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
			if (workbook != null) {
				// 获得当前sheet工作表
				Sheet sheet = workbook.getSheetAt(2);
				// 循环所有行
				for (int rowNum = sheet.getFirstRowNum(); rowNum <= sheet.getLastRowNum(); rowNum++) {
					// 获得当前行
					Row row = sheet.getRow(rowNum);
					if (row == null) {
						continue;
					}
					// 初始化StringBuffer来存储当前行的数据
					StringBuffer sb = new StringBuffer();
					// 只处理第三列和第五列（注意：列索引从0开始）
					int[] desiredColumns = {3,4}; // 第三列和第五列的索引
					for (int columnIndex : desiredColumns) {
						Cell cell = row.getCell(columnIndex);
						if (cell != null) {
							String str = getCellValue(cell);
							sb.append(str);
							sb.append(splitstr);
						}
					}
					// 移除最后一个分隔符并添加到列表中
					if (sb.length() > 0) {
						list.add(sb.substring(0, sb.length() - splitstr.length()));
					}
				}
				try {
					workbook.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return list;
	}

	public static ArrayList<String> readXlsxFile_TEST4(String absolutePath, String splitstr) {
		ArrayList<String> list = new ArrayList<String>();
		// 获得Workbook工作薄对象
		Workbook workbook;
		try {
			workbook = getWorkBook(new File(absolutePath), null);
			// 创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
			if (workbook != null) {
				// 获得当前sheet工作表
				Sheet sheet = workbook.getSheetAt(3);
				// 循环所有行
				for (int rowNum = sheet.getFirstRowNum(); rowNum <= sheet.getLastRowNum(); rowNum++) {
					// 获得当前行
					Row row = sheet.getRow(rowNum);
					if (row == null) {
						continue;
					}
					// 初始化StringBuffer来存储当前行的数据
					StringBuffer sb = new StringBuffer();
					// 只处理第三列和第五列（注意：列索引从0开始）
					int[] desiredColumns = {4,5}; // 第三列和第五列的索引
					for (int columnIndex : desiredColumns) {
						Cell cell = row.getCell(columnIndex);
						if (cell != null) {
							String str = getCellValue(cell);
							sb.append(str);
							sb.append(splitstr);
						}
					}
					// 移除最后一个分隔符并添加到列表中
					if (sb.length() > 0) {
						list.add(sb.substring(0, sb.length() - splitstr.length()));
					}
				}
				try {
					workbook.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return list;
	}

	public static ArrayList<String> readXlsxFile_TEST5(String absolutePath, String splitstr) {
		ArrayList<String> list = new ArrayList<String>();
		// 获得Workbook工作薄对象
		Workbook workbook;
		try {
			workbook = getWorkBook(new File(absolutePath), null);
			// 创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
			if (workbook != null) {
				// 获得当前sheet工作表
				Sheet sheet = workbook.getSheetAt(4);
				// 循环所有行
				for (int rowNum = sheet.getFirstRowNum(); rowNum <= sheet.getLastRowNum(); rowNum++) {
					// 获得当前行
					Row row = sheet.getRow(rowNum);
					if (row == null) {
						continue;
					}
					// 初始化StringBuffer来存储当前行的数据
					StringBuffer sb = new StringBuffer();
					// 只处理第三列和第五列（注意：列索引从0开始）
					int[] desiredColumns = {5,6}; // 第三列和第五列的索引
					for (int columnIndex : desiredColumns) {
						Cell cell = row.getCell(columnIndex);
						if (cell != null) {
							String str = getCellValue(cell);
							sb.append(str);
							sb.append(splitstr);
						}
					}
					// 移除最后一个分隔符并添加到列表中
					if (sb.length() > 0) {
						list.add(sb.substring(0, sb.length() - splitstr.length()));
					}
				}
				try {
					workbook.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return list;
	}
}
