package com.wzsec.utils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import com.jcraft.jsch.SftpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

/**
 * FTP工具类
 *
 * <AUTHOR>
 * @date 2020-4-23
 */
@Slf4j
public class FTPUtil {

    private static String ftpseparator = "/";
    private static int connectTimeOut = 1000;

    /**
     * 登陆FTP服务器
     *
     * @param hostname 主机IP
     * @param port     端口
     * @param username 账号
     * @param password 密码
     * @return
     * <AUTHOR>
     * @date 2020-5-22
     */
    public static FTPClient login(String hostname, int port, String username, String password) throws Exception {
        FTPClient ftp = new FTPClient();
        try {
            ftp.setConnectTimeout(connectTimeOut);
            ftp.connect(hostname, port);
            //如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器  
            if (!ftp.login(username, password)) {
                //ftp = null;
                throw new Exception("账号或密码错误");
            }
        } catch (Exception e) {
            //ftp = null;
            throw new Exception(e.getMessage());
            //e.printStackTrace();
        }
        return ftp;//登录  
    }

    /**
     * 从FTP服务器指定目录下载指定文件
     *
     * @param hostname   主机IP
     * @param port       端口
     * @param username   账号
     * @param password   密码
     * @param remotePath ftp服务器文件路径
     * @param fileName   ftp服务器文件名
     * @param localPath  本地保存路径
     * @return
     * <AUTHOR>
     * @date 2020-4-27
     */
    public static boolean downloadFileByFilePath(String hostname, int port, String username, String password, String remotePath, String fileName, String localPath) throws Exception {
        if (!remotePath.endsWith(ftpseparator)) {
            remotePath += ftpseparator;
        }
        boolean result = false;
        FTPClient ftp = new FTPClient();
        FTPFile[] fs = null;
        try {
            int reply;
            ftp.setConnectTimeout(connectTimeOut);
            ftp.connect(hostname, port);
            //如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器  
            boolean islogin = ftp.login(username, password);//登录  
            if (!islogin) {
                log.info("ftp账号密码错误");
            }
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            ftp.setControlEncoding("UTF-8"); // 中文支持
            reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return result;
            }
            log.info("正在遍历目录：" + remotePath);
            String workingDirectory = ftp.printWorkingDirectory();
            String pathname = workingDirectory + remotePath;
            if (!ftp.changeWorkingDirectory(pathname)) {//转移到FTP服务器目录
                log.error("无法切换到目录: " + pathname);
                throw new Exception();
            }
            ftp.enterLocalPassiveMode();
            File localFileDir = new File(localPath);
            localFileDir.mkdirs();
            fs = ftp.listFiles();
            // 遍历，目录下的所有文件,用于过滤下载文件不存在的状态
            for (FTPFile ff : fs) {
                if (ff.isFile()) {
                    if (ff.getName().equals(fileName)) {
                        File localFile = new File(localPath + File.separator + fileName);
                        OutputStream is = new FileOutputStream(localFile);
                        result = ftp.retrieveFile(fileName, is);
                        is.close();
                        result = true;
                        break;
                    }
                }
            }
            if (result) {
                log.info(remotePath + fileName + ": 文件下载成功！");
            } else {
                log.info(remotePath + fileName + ": 文件下载失败！");
            }
            ftp.logout();
        } catch (Exception e) {
            log.info("下载失败，下载过程出现异常！");
            throw e;
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                    ioe.printStackTrace();
                }
            }
        }
        return result;
    }


    /**
     * 从FTP服务器指定目录下载文件(含子目录)
     *
     * @param hostname   主机IP
     * @param port       端口
     * @param username   账号
     * @param password   密码
     * @param remotePath ftp服务器文件路径
     * @param localPath  本地保存路径
     * @return
     * <AUTHOR>
     * @date 2020-4-23
     */
    public static boolean downloadFileByDirectory(String hostname, int port, String username, String password, String remotePath, String localPath) throws Exception {
        if (!remotePath.endsWith(ftpseparator)) {
            remotePath += ftpseparator;
        }
        boolean result = false;
        FTPClient ftp = new FTPClient();
        FTPFile[] fs = null;
        try {
            int reply;
            ftp.setConnectTimeout(connectTimeOut);
            ftp.connect(hostname, port);
            //如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器  
            boolean islogin = ftp.login(username, password); //登录  
            if (!islogin) {
                log.info("ftp账号密码错误");
            }
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            ftp.setControlEncoding("UTF-8"); // 中文支持
            reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return result;
            }
            log.info("正在遍历目录：" + remotePath);
            if (!ftp.changeWorkingDirectory(ftp.printWorkingDirectory() + remotePath)) {//转移到FTP服务器目录
                throw new Exception();
            }
            ftp.enterLocalPassiveMode();
            File localFileDir = new File(localPath);
            localFileDir.mkdirs();
            fs = ftp.listFiles();
            // 遍历，目录下的所有文件
            for (FTPFile ff : fs) {
                String ffName = ff.getName();
                if (ff.isFile()) {
                    log.info("正在下载文件：" + ffName);
                    File localFile = new File(localPath + File.separator + ffName);
                    OutputStream is = new FileOutputStream(localFile);
                    boolean retrieveFile = ftp.retrieveFile(ff.getName(), is);
                    if (!retrieveFile) {
                        log.info("ftp文件下载到本地出现错误");
                    }
                    is.close();
                } else if (ff.isDirectory()) {
                    downloadFileByDirectory(hostname, port, username, password, remotePath + ffName, localPath + File.separator + ffName);
                }
            }
            log.info(remotePath + ": 目录下文件已下载到本地" + localPath + "目录下成功！");
            ftp.logout();
            result = true;
        } catch (Exception e) {
            log.info("下载失败，下载过程出现异常！");
            throw e;
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                    ioe.printStackTrace();
                }
            }
        }
        return result;
    }


    /**
     * 从FTP服务器指定目录获取文件路径(含子目录)
     *
     * @param hostname     主机IP
     * @param port         端口
     * @param username     账号
     * @param password     密码
     * @param remotePath   ftp服务器文件路径
     * @param filePathList 文件路径集合
     * @return
     * <AUTHOR>
     * @date 2020-4-27
     */
    public static void getAllFilePathByDirectory(String hostname, int port, String username, String password, String remotePath, List<String> filePathList) throws Exception {
        if (!remotePath.endsWith(ftpseparator)) {
            remotePath += ftpseparator;
        }
        FTPClient ftp = new FTPClient();
        FTPFile[] fs = null;
        if (remotePath.endsWith(""))
            try {
                int reply;
                ftp.setConnectTimeout(connectTimeOut);
                ftp.connect(hostname, port);
                //如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器  
                boolean islogin = ftp.login(username, password); //登录  
                if (!islogin) {
                    log.info("ftp账号密码错误");
                    throw new Exception();
                }
                ftp.setFileType(FTP.BINARY_FILE_TYPE);
                ftp.setControlEncoding("UTF-8"); // 中文支持
                reply = ftp.getReplyCode();
                if (!FTPReply.isPositiveCompletion(reply)) {
                    ftp.disconnect();
                    return;
                }
                log.info("正在遍历目录：" + remotePath);
                if (!ftp.changeWorkingDirectory(ftp.printWorkingDirectory() + remotePath)) {//转移到FTP服务器目录
                    throw new Exception();
                }
                ftp.enterLocalPassiveMode();
                fs = ftp.listFiles();
                // 遍历，目录下的所有文件
                for (FTPFile ff : fs) {
                    String ffName = ff.getName();
                    if (!".".equals(ffName) && !"..".equals(ffName)) {
                        if (ff.isFile()) {
                            filePathList.add(remotePath + ffName);
                        } else if (ff.isDirectory()) {
                            getAllFilePathByDirectory(hostname, port, username, password, remotePath + ffName, filePathList);
                        }
                    }
                }
                ftp.logout();
            } catch (Exception e) {
                log.info("获取所有文件路径出现异常！");
                throw e;
            } finally {
                if (ftp.isConnected()) {
                    try {
                        ftp.disconnect();
                    } catch (IOException ioe) {
                        ioe.printStackTrace();
                    }
                }
            }
    }


    /**
     * 从FTP服务器指定目录下载指定文件
     *
     * @param hostname   主机IP
     * @param port       端口
     * @param username   账号
     * @param password   密码
     * @param remotePath ftp服务器文件路径
     * @param localPath  本地保存路径
     * @return
     * <AUTHOR>
     * @date 2020-4-27
     */
    public static boolean downloadFileByFilePath(String hostname, int port, String username, String password, String remotePath, String localPath) throws Exception {
        FTPClient ftpClient = new FTPClient();
        Boolean flag = false;
        try {
            ftpClient.connect(hostname, port);
            ftpClient.login(username, password);
            ftpClient.enterLocalPassiveMode();
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);

            //尝试切换切换目录，看下是否切换成功
            if (ftpClient.changeWorkingDirectory(remotePath)) {
                // 路径是一个目录,下载该目录内所有文件
                FTPFile[] files = ftpClient.listFiles();
                for (FTPFile file : files) {
                    if (!file.isDirectory()) {
                        // 是文件，进行下载操作
                        String remoteFilePath = remotePath + Const.SEPARATOR_LINUX + file.getName();
                        downloadFile(ftpClient, remoteFilePath, localPath);
                    }
                }
            } else {
                // 路径是一个文件,直接下载该文件
                downloadFile(ftpClient, remotePath, localPath);
            }

            ftpClient.logout();
            ftpClient.disconnect();
            flag = true;
        } catch (IOException ex) {
            ex.printStackTrace();
            flag = false;
        } finally {
            return flag;
        }
    }

    private static void downloadFile(FTPClient ftpClient, String remoteFilePath, String localDirectoryPath) throws IOException {
        File localFile = new File(localDirectoryPath + "/" + new File(remoteFilePath).getName());
        OutputStream outputStream = new FileOutputStream(localFile);

        boolean success = ftpClient.retrieveFile(remoteFilePath, outputStream);
        outputStream.close();
        if (success) {
            log.info("文件下载成功！ " + localFile.getAbsolutePath());
        } else {
            log.info("文件下载失败：" + remoteFilePath);
        }
    }

    /**
     * 上传文件
     *
     * @param host
     * @param port
     * @param username
     * @param password
     * @param localPath
     * @param uploadingPath
     * @return
     */
    public static boolean uploadingFile(String host, int port, String username, String password, String localPath, String uploadingPath) {
        FTPClient ftpClient = new FTPClient();
        boolean flag = false;
        try {
            // 连接到FTP服务器
            ftpClient.connect(host, port);
            ftpClient.login(username, password);
            ftpClient.enterLocalPassiveMode();
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);

            // 创建远程目录（如果不存在）
            String[] split = uploadingPath.split(Const.SEPARATOR_LINUX);
            String path = "";
            for (String s : split) {
                path += s + Const.SEPARATOR_LINUX;
                //尝试切换远程目录，目录不存在就创建
                if (ftpClient.changeWorkingDirectory(path)) {
                    log.info("目录：" + path + " 已存在，无需创建...");
                } else {
                    log.info("目录：" + path + " 不存在，开始创建...");
                    ftpClient.makeDirectory(path);
                }
            }

            // 上传所有本地文件到FTP服务器
            File localDir = new File(localPath);
            if (localDir.isFile()) {
                String remoteFilePath = uploadingPath + Const.SEPARATOR_LINUX + localDir.getName();
                uploadFile(ftpClient, localDir, remoteFilePath);
            } else {
                File[] files = localDir.listFiles();
                for (File file : files) {
                    if (file.isFile()) {
                        String remoteFilePath = uploadingPath + Const.SEPARATOR_LINUX + file.getName();
                        uploadFile(ftpClient, file, remoteFilePath);
                    }
                }
            }


            ftpClient.logout();
            ftpClient.disconnect();
            flag = true;
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            return flag;
        }

    }

    private static void uploadFile(FTPClient ftpClient, File localFile, String remoteFilePath) throws IOException {
        try (FileInputStream inputStream = new FileInputStream(localFile)) {
            boolean isUploaded = ftpClient.storeFile(remoteFilePath, inputStream);
            if (isUploaded) {
                log.info("文件 " + localFile.getName() + " 已上传到 " + remoteFilePath);
            } else {
                log.info("文件 " + localFile.getName() + " 上传失败");
            }
        }
    }

    public static void main(String[] args) {
        String host = "*************";
        int port = 21;
        String username = "ftpuser";
        String password = "<EMAIL>";
        String remotePath = "/data/mcbtest/file/out/010520231018002";
        String localPath = "D:\\filemask\\fileDownload\\CSV.csv";
        boolean file = uploadingFile(host, port, username, password, localPath, remotePath);
        System.out.println(file);
        /*try {
            //        boolean downFile = downloadFileByDirectory("*************", 3721, "zhangkx", "123456", "/test", "D:/download");
            List<String> filePathList = new ArrayList<>();
            getAllFilePathByDirectory("*************", 21, "wz001", "wz001", "/zkx/", filePathList);

            for (String file : filePathList) {
                System.out.println("开始下载：" + file);
                String remotePath = file.substring(0, file.lastIndexOf(Const.SEPARATOR_LINUX));
                String remoteFileName = file.substring(file.lastIndexOf(Const.SEPARATOR_LINUX) + 1);
                downloadFileByFilePath("*************", 21, "wz001", "wz001", remotePath, remoteFileName, "D:/CtyunWork/download/ftp");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }*/
    }
}

