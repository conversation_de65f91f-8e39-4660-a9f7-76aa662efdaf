package com.wzsec.utils.database;

import cn.hutool.core.util.StrUtil;
import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

/**
 * 达梦数据库工具类(DM8)
 */
@Slf4j
public class DMUtil extends DatabaseUtil {

    private static String JDBC_DRIVER = "dm.jdbc.driver.DmDriver";

    /**
     * 获取库表(Map<库名,(表1,表2)>)
     *
     * @param conn    连接
     * @param dbnames dbname
     * @return {@link Map}<{@link String}, {@link String}>
     * @throws SQLException sqlexception异常
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) {
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = new HashMap<String, String>();
        try {
            String strSQL = "select owner,TABLE_NAME from all_tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where owner in (" + "'" + dbnames + "'" + ") ";
            }
            conn.setAutoCommit(false);
            stmt = conn.createStatement();

            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String dbName = rs.getString("owner");// 库名
                String tabname = rs.getString("TABLE_NAME");// 表名
                if (!tabname.contains("##") && !tabname.contains("$")) {  //排除系统表
                    if (dbTabMap.containsKey(dbName)) {
                        dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + tabname);
                    } else {
                        dbTabMap.put(dbName, tabname);
                    }
                }
            }
            conn.commit();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020年09月27日
     */
    public static Map<String, String> getAllDbAndTabMap(String dburl, String username, String password, String dbnames) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = new HashMap<String, String>();
        try {
            String strSQL = "select owner,TABLE_NAME from all_tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where owner in (" + dbnames + ") ";
            }
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String dbName = rs.getString("owner");// 库名
                String tabname = rs.getString("TABLE_NAME");// 表名
                if (dbTabMap.containsKey(dbName)) {
                    dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + tabname);
                } else {
                    dbTabMap.put(dbName, tabname);
                }
            }
            conn.commit();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return dbTabMap;
    }

    /**
     * 获取字段名称列表
     *
     * @param conn    连接
     * @param dbname  dbname
     * @param tabname tabname
     * @return {@link List}<{@link String}>
     * @throws SQLException sqlexception异常
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + "\"" + dbname + "\"" + "." + "\"" + tabname + "\"";
            if (lineNum != null && lineNum != 0)
                strSQL += " where rownum <= " + lineNum + " order by rownum";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println(ex);
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020年09月27日
     */
    public static int getTabDataCount(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            conn = getConn(JDBC_DRIVER, dburl + "/" + dbname, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }


    /**
     * 获取字段名称列表
     *
     * @param conn      连接
     * @param dbname    dbname
     * @param tablename 表名
     * @return {@link List}<{@link String}>
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tablename) {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> fieldList = new ArrayList<String>();
        try {
            String sql = "select COLUMN_NAME from all_tab_columns  where table_name= '" + tablename + "' and owner ='"
                    + dbname + "' ";
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            conn.commit();
            while (rs.next()) {
                fieldList.add(rs.getString(1));
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldList;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020年09月27日
     */
    public static List<String> getFieldNameList(String url, String username, String password, String dbname,
                                                String tablename) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> fieldList = new ArrayList<String>();
        try {
            String sql = "select COLUMN_NAME from all_tab_columns  where table_name= '" + tablename + "' and owner ='"
                    + dbname + "' ";
            conn = getConn(JDBC_DRIVER, url, username, password);
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            conn.commit();
            while (rs.next()) {
                fieldList.add(rs.getString(1));
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return fieldList;
    }


    /**
     * 获取模式表字段信息
     *
     * @param dbName    库名
     * @param tableName 表名
     * @param conn      连接
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            //String sql = "select ut.COLUMN_NAME, uc.comments from user_tab_columns ut inner JOIN user_col_comments uc on ut.TABLE_NAME  = uc.table_name and ut.COLUMN_NAME = uc.column_name where ut.Table_Name='" + tableName + "' order by ut.column_name";
            String sql = "SELECT acc.COLUMN_NAME, acc.COMMENTS,atc.DATA_TYPE FROM  ALL_TAB_COLUMNS atc " +
                    "LEFT JOIN ALL_COL_COMMENTS acc ON atc.OWNER = acc.OWNER AND atc.TABLE_NAME = acc.TABLE_NAME AND atc.COLUMN_NAME = acc.COLUMN_NAME " +
                    "WHERE atc.OWNER = '"+dbName+"' AND atc.TABLE_NAME = '"+tableName+"' " +
                    "ORDER BY atc.COLUMN_ID;";
            rs = stmt.executeQuery(sql);
            //select ut.COLUMN_NAME, uc.comments from user_tab_columns ut inner JOIN user_col_comments uc on ut.TABLE_NAME  = uc.table_name and ut.COLUMN_NAME = uc.column_name where ut.Table_Name='T_USER' order by ut.column_name
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COMMENTS"));
                    fieldInfoMap.put("fieldType", rs.getString("DATA_TYPE"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * 获取模式表字段信息
     *
     * @param dbName       库名
     * @param tableName    表名
     * @param conn         连接
     * @param tabFieldList 标签字段列表
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn, List<String> tabFieldList) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();

        for (String fieldName : tabFieldList) {
            Statement stmt = null;
            ResultSet rs = null;
            try {
                stmt = conn.createStatement();// 执行创建表
                //String sql = "select ut.COLUMN_NAME, uc.comments from all_tab_columns ut inner JOIN all_col_comments uc on ut.TABLE_NAME  = uc.table_name and ut.COLUMN_NAME = uc.column_name where ut.Table_Name= '" + tableName + "' AND  ut.COLUMN_NAME= '" + fieldName + "' order by ut.column_name";
                String sql = "SELECT acc.COLUMN_NAME, acc.COMMENTS,atc.DATA_TYPE FROM  ALL_TAB_COLUMNS atc " +
                        "LEFT JOIN ALL_COL_COMMENTS acc ON atc.OWNER = acc.OWNER AND atc.TABLE_NAME = acc.TABLE_NAME AND atc.COLUMN_NAME = acc.COLUMN_NAME " +
                        "WHERE atc.OWNER = '"+dbName+"' AND atc.TABLE_NAME = '"+tableName+"' AND atc.COLUMN_NAME = '"+fieldName+"'" +
                        "ORDER BY atc.COLUMN_ID;";
                rs = stmt.executeQuery(sql);
                if (rs != null) {
                    while (rs.next()) {
                        Map<String, String> fieldInfoMap = new HashMap<>();
                        fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                        fieldInfoMap.put("fieldCName", rs.getString("COMMENTS"));
                        fieldInfoMap.put("fieldType", rs.getString("DATA_TYPE"));
                        fieldInfoList.add(fieldInfoMap);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                closeCon(rs, stmt, null);
            }
        }
        return fieldInfoList;
    }

    /**
     * 获取表信息
     *
     * @param dbName    数据库名字
     * @param tableName 表名
     * @param conn      连接
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
//            String sql = " select tvname AS tabname, comment$ from SYSTABLECOMMENTS " +
//                    "where schname = '" + dbName + "' AND tvname = '" + tableName + "' order by tvname ";

            String sql = "SELECT t1.name AS TABLE_NAME,t2.rows AS TABLE_ROWS,ROUND(t2.mb / 1024, 2) AS Size_MB FROM SYSOBJECT t1 " +
                    "JOIN(SELECT obj.objname AS name,sum(obj.dbsize) AS mb,sum(obj.rows) AS rows FROM SYSDBOBJECT obj " +
                    "WHERE obj.objtype = 'TABLE' AND obj.owner = '"+dbName+"' AND obj.objname = '"+tableName+"' GROUP BY obj.objname) t2 " +
                    "ON t1.name = t2.name";

            rs = stmt.executeQuery(sql);
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName","");
                tableInfoMap.put("tableRows", rs.getString("TABLE_ROWS"));
                tableInfoMap.put("dataSize", rs.getString("Size_MB"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }


    /**
     * 获取表字段信息
     *
     * @param dbName    库名
     * @param tableName 表名
     * @param dburl     数据库url
     * @param username  用户名
     * @param password  密码
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username, String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            String template = "select COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,COLUMN_ID from all_tab_columns  where table_name='{}' and owner='{}' ";
            String sql = StrUtil.format(template, tableName, dbName);
            rs = stmt.executeQuery(sql);
            // ResultSet primaryKey = stmt.executeQuery("select COLUMN_NAME from
            // user_cons_columns cu, user_constraints au where
            // cu.constraint_name = au.constraint_name and au.constraint_type =
            // 'P' and au.table_name ='" + tableName + "'");
            // System.err.println(primaryKey);
            if (rs != null) {
                fieldInfoList = new ArrayList<>();

                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("COLUMN_NAME", rs.getString("COLUMN_NAME"));// 字段名
                    fieldInfoMap.put("DATA_TYPE", rs.getString("DATA_TYPE"));// 字段类型
                    fieldInfoMap.put("DATA_LENGTH", rs.getString("DATA_LENGTH"));// 数据长度
                    if (rs.getString("DATA_PRECISION") == null) {
                        fieldInfoMap.put("DATA_PRECISION", "");
                    } else {
                        fieldInfoMap.put("DATA_PRECISION", rs.getString("DATA_PRECISION"));// 数据长度(和上方不同)
                    }
                    if (rs.getString("DATA_SCALE") == null) {
                        fieldInfoMap.put("DATA_SCALE", "");
                    } else {
                        fieldInfoMap.put("DATA_SCALE", rs.getString("DATA_SCALE"));// 获取数据精度
                    }
                    fieldInfoMap.put("NULLABLE", rs.getString("NULLABLE"));// 获取是否为空
                    fieldInfoMap.put("COLUMN_ID", rs.getString("COLUMN_ID"));// 字段序号
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return fieldInfoList;
    }

    /**
     * 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     *
     * @param objList   Map数据库集合
     * @param dbName    库名
     * @param tableName 表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbName, String tableName) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        try {
            List<Object> params = new ArrayList<>();
            //Set<String> fields = objList.get(0).keySet();

            List<String> fields = new ArrayList<>();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT ALL ");
            for (int i = 0; i < objList.size(); i++) {
                Map<String, Object> map = objList.get(i);
                sb.append(" INTO  " + aa).append(tableName).append(aa + " (");
                for (String column : fields) {
                    sb.append("" + aa).append(column.toUpperCase()).append(aa + ", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();
                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList,
                                       Map<String, Object> obj, String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE \"").append(tableName).append("\" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("COLUMN_NAME"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("\"").append(fieldInfo.get("COLUMN_NAME")).append("\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("COLUMN_NAME"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("DATA_TYPE"));// 类型
                    if (fieldInfo.get("DATA_TYPE").contains("CHAR") || fieldInfo.get("DATA_TYPE").contains("char")) {
                        sb.append(" ").append("(" + fieldInfo.get("DATA_LENGTH") + ")");// 长度
                    }
                }
                sb.append(",\n");
            }
            sql = sb.toString();

            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
            //MAIN:数据库默认的表空间
            sql = sql + "STORAGE(ON \"" + "MAIN" + "\", CLUSTERBTR);";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }


    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        String[] fieldnamearr = fieldnames.split(",");
        try {
            List<Object> params = new ArrayList<>();
            //Set<String> fields = objList.get(0).keySet();

            List<String> fields = new ArrayList<>();
            for (String s : fieldnamearr) {
                fields.add(s);
            }

            StringBuilder sb = new StringBuilder();
            sb.append("INSERT ALL ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append(" INTO  " + "\"" + dbname + "\"" + "." + aa).append(tableName).append(aa + " (");
                for (String column : fields) {
                    sb.append("" + aa).append(column.toUpperCase()).append(aa + ", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();
                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2020年09月27日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT ALL ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append(" INTO  " + "\"" + dbname + "\"" + "." + aa).append(tableName).append(aa + " (");
                for (String column : fields) {
                    sb.append("" + aa).append(column).append(aa + ", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();
                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2020年09月27日
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE \"" + dbname + "\".\"").append(tableName).append("\" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("COLUMN_NAME"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("\"").append(fieldInfo.get("COLUMN_NAME")).append("\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("COLUMN_NAME"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("DATA_TYPE"));// 类型
                    if (fieldInfo.get("DATA_TYPE").contains("CHAR") || fieldInfo.get("DATA_TYPE").contains("char")) {
                        sb.append(" ").append("(" + fieldInfo.get("DATA_LENGTH") + ")");// 长度
                    }
                }

                /*
                 * if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                 * sb.append(" NOT NULL"); } if
                 * ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra")))
                 * {// 判断非空 sb.append(" AUTO_INCREMENT");// 自增 } else { if
                 * (fieldInfo.get("Default") != null) {
                 * sb.append(" DEFAULT '").append(fieldInfo.get("Default")).
                 * append("'");// 默认值 } else { sb.append(" DEFAULT NULL"); } }
                 */
                /*
                 * if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                 * sb.append(" PRIMARY KEY");// 主键 }
                 */
                /*
                 * if (fieldInfo.get("Comment") != null &&
                 * !"".equals(fieldInfo.get("Comment"))) {
                 * sb.append(" COMMENT '").append(fieldInfo.get("Comment")).
                 * append("'"); }
                 */
                sb.append(",\n");
            }
            sql = sb.toString();

            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
            //MAIN:数据库默认的表空间
            sql = sql + "STORAGE(ON \"" + "MAIN" + "\", CLUSTERBTR);";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }


    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 17:13
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();
        String driverProgram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");
        // 如果密码是加密的，进行解密
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }
        String dbname = map.get("dbname");
        String srcport = map.get("srcport");
        String srcip = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            Class.forName(driverProgram);
            String jdbcUrl = "jdbc:dm://" + srcip + ":" + srcport + "/SYS";
            connection = DriverManager.getConnection(jdbcUrl, username, password);
            statement = connection.createStatement();
            String sql = "SELECT COUNT(*) FROM sys.dba_users WHERE LOWER(username) = LOWER('"+dbname+"\')";
            // 检查数据库是否存在
            resultSet = statement.executeQuery(sql);
            if (resultSet.next() && resultSet.getInt(1) == 0) {
                // 数据库不存在，可以创建
                String createDatabaseSQL = "CREATE USER \""+dbname+"\" IDENTIFIED BY \"<EMAIL>\"";
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库 " + dbname + " 已创建！");
                msgMap.put("code", "DATABASE_CREATED");
                msgMap.put("msg", "数据库 " + dbname + " 已创建");
            } else {
                System.out.println("数据库 " + dbname + " 已存在，跳过创建步骤！");
                msgMap.put("code", "DATABASE_EXISTS");
                msgMap.put("msg", "数据库 " + dbname + " 已存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return msgMap;
    }

}
