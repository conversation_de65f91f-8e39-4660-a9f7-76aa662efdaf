package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-12
*/
@Entity
@Data
@Table(name="sdd_mask_filetaskresult")
public class FileTaskResult implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 输入路径 */
    @Column(name = "inputpath")
    private String inputpath;

    /** 输入文件格式 */
    @Column(name = "inputfiletype")
    private String inputfiletype;

    /** 分隔符 */
    @Column(name = "splitstr")
    private String splitstr;

    /** 策略名 */
    @Column(name = "strategyname")
    private String strategyname;

    /** 输出类型（1库表，2文件） */
    @Column(name = "outputtype")
    private String outputtype;

    /** 目标源库名或目录 */
    @Column(name = "outputpath")
    private String outputpath;

    /** 目标源表名或输出文件类型 */
    @Column(name = "outputname")
    private String outputname;

    /** 数据总行数 */
    @Column(name = "totallines")
    private Integer totallines;

    /** 脱敏前数据（前5条） */
    @Column(name = "beforemaskdata")
    private String beforemaskdata;

    /** 脱敏后数据（前5条） */
    @Column(name = "aftermaskdata")
    private String aftermaskdata;

    /** 任务结果状态（提交中、执行成功 ,执行失败） */
    @Column(name = "taskstatus")
    private String taskstatus;

    /** 作业提交人ID */
    @Column(name = "userid")
    private String userid;

    /** 作业提交人姓名 */
    @Column(name = "username")
    private String username;

    /** 起始时间 */
    @Column(name = "jobstarttime")
    private String jobstarttime;

    /** 结束时间 */
    @Column(name = "jobendtime")
    private String jobendtime;

    /** 用时 */
    @Column(name = "jobtotaltime")
    private String jobtotaltime;

    /** 创建时间 */
    @Column(name = "createtime")
    private String createtime;

    /** 更新时间 */
    @Column(name = "updatetime")
    private String updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    /** 文件名 */
    @Column(name = "filenames")
    private String filenames;

    /** minio脱敏文件信息 */
    @Column(name = "miniodata")
    private String miniodata;

    public void copy(FileTaskResult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
