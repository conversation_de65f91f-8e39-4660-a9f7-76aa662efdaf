package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskHbasetaskconfig;
import com.wzsec.modules.mask.service.dto.MaskHbasetaskconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskHbasetaskconfigMapperImpl implements MaskHbasetaskconfigMapper {

    @Override
    public MaskHbasetaskconfigDto toDto(MaskHbasetaskconfig entity) {
        if ( entity == null ) {
            return null;
        }

        MaskHbasetaskconfigDto maskHbasetaskconfigDto = new MaskHbasetaskconfigDto();

        maskHbasetaskconfigDto.setBatchnumber( entity.getBatchnumber() );
        maskHbasetaskconfigDto.setCount( entity.getCount() );
        maskHbasetaskconfigDto.setCreatetime( entity.getCreatetime() );
        maskHbasetaskconfigDto.setCreateuserid( entity.getCreateuserid() );
        maskHbasetaskconfigDto.setDataoutputdir( entity.getDataoutputdir() );
        maskHbasetaskconfigDto.setDatasplit( entity.getDatasplit() );
        maskHbasetaskconfigDto.setDbname( entity.getDbname() );
        maskHbasetaskconfigDto.setId( entity.getId() );
        maskHbasetaskconfigDto.setOutputtablename( entity.getOutputtablename() );
        maskHbasetaskconfigDto.setOutputtype( entity.getOutputtype() );
        maskHbasetaskconfigDto.setQueuename( entity.getQueuename() );
        maskHbasetaskconfigDto.setRemark( entity.getRemark() );
        maskHbasetaskconfigDto.setSparefield1( entity.getSparefield1() );
        maskHbasetaskconfigDto.setSparefield2( entity.getSparefield2() );
        maskHbasetaskconfigDto.setSparefield3( entity.getSparefield3() );
        maskHbasetaskconfigDto.setSparefield4( entity.getSparefield4() );
        maskHbasetaskconfigDto.setStatus( entity.getStatus() );
        maskHbasetaskconfigDto.setStrategyname( entity.getStrategyname() );
        maskHbasetaskconfigDto.setStrategystr( entity.getStrategystr() );
        maskHbasetaskconfigDto.setTabname( entity.getTabname() );
        maskHbasetaskconfigDto.setUpdatetime( entity.getUpdatetime() );
        maskHbasetaskconfigDto.setUpdateuserid( entity.getUpdateuserid() );
        maskHbasetaskconfigDto.setUsername( entity.getUsername() );
        maskHbasetaskconfigDto.setWorksheet( entity.getWorksheet() );

        return maskHbasetaskconfigDto;
    }

    @Override
    public List<MaskHbasetaskconfigDto> toDto(List<MaskHbasetaskconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskHbasetaskconfigDto> list = new ArrayList<MaskHbasetaskconfigDto>( entityList.size() );
        for ( MaskHbasetaskconfig maskHbasetaskconfig : entityList ) {
            list.add( toDto( maskHbasetaskconfig ) );
        }

        return list;
    }

    @Override
    public MaskHbasetaskconfig toEntity(MaskHbasetaskconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskHbasetaskconfig maskHbasetaskconfig = new MaskHbasetaskconfig();

        maskHbasetaskconfig.setBatchnumber( dto.getBatchnumber() );
        maskHbasetaskconfig.setCount( dto.getCount() );
        maskHbasetaskconfig.setCreatetime( dto.getCreatetime() );
        maskHbasetaskconfig.setCreateuserid( dto.getCreateuserid() );
        maskHbasetaskconfig.setDataoutputdir( dto.getDataoutputdir() );
        maskHbasetaskconfig.setDatasplit( dto.getDatasplit() );
        maskHbasetaskconfig.setDbname( dto.getDbname() );
        maskHbasetaskconfig.setId( dto.getId() );
        maskHbasetaskconfig.setOutputtablename( dto.getOutputtablename() );
        maskHbasetaskconfig.setOutputtype( dto.getOutputtype() );
        maskHbasetaskconfig.setQueuename( dto.getQueuename() );
        maskHbasetaskconfig.setRemark( dto.getRemark() );
        maskHbasetaskconfig.setSparefield1( dto.getSparefield1() );
        maskHbasetaskconfig.setSparefield2( dto.getSparefield2() );
        maskHbasetaskconfig.setSparefield3( dto.getSparefield3() );
        maskHbasetaskconfig.setSparefield4( dto.getSparefield4() );
        maskHbasetaskconfig.setStatus( dto.getStatus() );
        maskHbasetaskconfig.setStrategyname( dto.getStrategyname() );
        maskHbasetaskconfig.setStrategystr( dto.getStrategystr() );
        maskHbasetaskconfig.setTabname( dto.getTabname() );
        maskHbasetaskconfig.setUpdatetime( dto.getUpdatetime() );
        maskHbasetaskconfig.setUpdateuserid( dto.getUpdateuserid() );
        maskHbasetaskconfig.setUsername( dto.getUsername() );
        maskHbasetaskconfig.setWorksheet( dto.getWorksheet() );

        return maskHbasetaskconfig;
    }

    @Override
    public List<MaskHbasetaskconfig> toEntity(List<MaskHbasetaskconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskHbasetaskconfig> list = new ArrayList<MaskHbasetaskconfig>( dtoList.size() );
        for ( MaskHbasetaskconfigDto maskHbasetaskconfigDto : dtoList ) {
            list.add( toEntity( maskHbasetaskconfigDto ) );
        }

        return list;
    }
}
