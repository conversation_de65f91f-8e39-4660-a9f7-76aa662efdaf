package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskKanonymizationresult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
* <AUTHOR>
* @date 2024-10-14
*/
public interface MaskKanonymizationresultRepository extends JpaRepository<MaskKanonymizationresult, Integer>, JpaSpecificationExecutor<MaskKanonymizationresult> {
}