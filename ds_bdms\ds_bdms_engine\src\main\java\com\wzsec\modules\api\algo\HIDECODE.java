/**
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 * <p>
 * [2015] - [2020] China Telecom Corporation Limited,
 * All Rights Reserved.
 * <p>
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 *
 * @Title: HIDECODE.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年1月8日
 */

/**
 *@Title: HIDECODE.java
 *@Description: TODO
 *<AUTHOR>
 *@date 2019年1月8日
 */
package com.wzsec.modules.api.algo;

/**
 * @ClassName: NHIDECODE
 * @Description: 改写结果脱敏算法_通用掩码
 * <AUTHOR>
 * @date 2020年2月24日
 */
public class HIDECODE {

    /**
     * 通用掩码（参数：掩码字符，首保留位，尾保留位）
     *
     * @param hideCode      掩码字符
     * @param strHideLength 首保留位，尾保留位
     * <AUTHOR>
     * @date 2020年2月24日
     */
    public static String getFinalHideCodeStr(String hideCode, String strHideLength) {
        int intHideLength = Integer.parseInt(strHideLength);
        if (hideCode != null && hideCode.length() == 1) {
            StringBuilder sbHideCode = new StringBuilder();
            for (int j = 1; j <= intHideLength; j++) {
                sbHideCode.append(hideCode);
            }
            return sbHideCode.toString();
        } else {
            return "HideCodeErrorForAlgorithm";
        }
    }

    /**
     * @Description: 对数据进行掩码替换保留指定位置
     * @param srcData(数据)
     * @param hideCode(掩码符号)
     * @param strStart(显示前M位)
     * @param strEnd(显示后N位)
     * <AUTHOR>
     * @date 2020年2月24日
     */
    public static String encrypt(String srcData, String hideCode, String strStart, String strEnd) {
        if (srcData != null && !srcData.trim().equals("")) {
            if (hideCode != null && hideCode.length() == 1) {
                int intStart = Integer.parseInt(strStart);
                int intEnd = Integer.parseInt(strEnd);
                if (0 <= intStart) {
                    if (srcData.length() >= (intStart + intEnd)) {
                        String strHideLength = Integer.toString(srcData.length() - (intStart + intEnd));
                        String strFinalHideCode = getFinalHideCodeStr(hideCode, strHideLength);
                        String strFinalStart = srcData.substring(0, intStart);
                        String strFinalEnd = srcData.substring(srcData.length() - intEnd, srcData.length());
                        return strFinalStart + strFinalHideCode + strFinalEnd;
                    } else {
                        return "ParamConfigErrorForAlgorithm";
                    }
                } else {
                    return "ParamConfigErrorForAlgorithm";
                }
            } else {
                return "HideCodeErrorForAlgorithm";
            }
        } else {
            return srcData;
        }
    }

    /**
     * @Description:
     * <AUTHOR>
     * @date 2020年2月24日
     */
    public static void main(String[] args) {
        String srcData = "6217211107001880725";
        System.out.println(encrypt(srcData, "*", "6", "4"));
        System.out.println(encrypt(srcData, "&", "3", "2"));
        System.out.println(encrypt(srcData, "&", "2", "4"));
    }

}
