package com.wzsec.dotask.mask.service.excute.batch;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.client.MongoClient;
import com.sequoiadb.base.CollectionSpace;
import com.sequoiadb.base.DBCollection;
import com.sequoiadb.base.DBCursor;
import com.sequoiadb.base.Sequoiadb;
import com.wzsec.config.Log;
import com.wzsec.modules.alarm.config.MonitorRiskAlarmData;
import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.mask.domain.BatchTaskTabConfig;
import com.wzsec.modules.mask.domain.DbBatchTaskConfig;
import com.wzsec.modules.mask.domain.DbBatchTaskResult;
import com.wzsec.modules.mask.domain.Maskrule;
import com.wzsec.modules.mask.repository.AlgorithmRepository;
import com.wzsec.modules.mask.repository.MaskruleRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import com.wzsec.modules.mask.service.dto.DbBatchTaskConfigDto;
import com.wzsec.modules.mask.service.dto.DbBatchTaskResultDto;
import com.wzsec.modules.mask.service.dto.MaskStrategyTableDto;
import com.wzsec.modules.mask.service.mapper.DbBatchTaskConfigMapper;
import com.wzsec.modules.mask.service.mapper.DbBatchTaskResultMapper;
import com.wzsec.modules.mask.service.mapper.MaskruleMapper;
import com.wzsec.modules.sdd.metadata.domain.MetaField;
import com.wzsec.modules.sdd.metadata.service.MetaFieldService;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.proxy.common.rule.ProRuleFactory;
import com.wzsec.proxy.common.utils.SpringUtil;
import com.wzsec.utils.*;
import com.wzsec.utils.database.*;
import com.wzsec.utils.rule.Rule4AlgorithmUtil;
import com.wzsec.watermark.alg.WaterMarkAlgFactory;
import org.apache.commons.lang.StringUtils;
import org.apache.hive.jdbc.HiveStatement;
import org.bson.BSONObject;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;

import static com.wzsec.utils.database.SequoiaDBUtil.tableNameStr;

/**
 * <AUTHOR>
 * @Description 执行批量脱敏，设置获取分批获取数据版本(MYSQL-OK)，会创建临时表导致数据库服务器临时空间占满，影响业务使用
 * @date 2021年2月4日 下午2:21:31
 */
public class DoBatchDBTaskJob {

    private final static Logger log = LoggerFactory.getLogger(DoBatchDBTaskJob.class);

    private static DmAlarmdisposalService maskAlarmdisposalService = SpringUtil.getApplicationContext().getBean(DmAlarmdisposalService.class);

    private DbBatchTaskConfigService dbBatchTaskConfigService;

    private DbBatchTaskResultService dbBatchTaskResultService;

    private DatasourceService dataSourceService;

    private MaskStrategyFieldService maskStrategyFieldService;

    private MaskStrategyTableService maskStrategyTableService;

    private AlgorithmService algorithmService;

    private DbBatchTaskConfigMapper dbBatchTaskConfigMapper;

    private DbBatchTaskResultMapper dbBatchTaskResultMapper;

    private SystemLogService logService;

    private MetaFieldService metaFieldService;

    private Integer taskid;
    private String submituser;

    private MaskTaskresultrecordsService maskTaskresultrecordsService;

    private SdkOperationrecordRepository sdkOperationrecordRepository;

    Long pageSize = Long.parseLong(ConstEngine.sddEngineConfs.get("dbtask.readdatacount"));// 一次读取1万条数据再写入到数据库
    Integer writeDataCount = Integer.parseInt(ConstEngine.sddEngineConfs.get("dbtask.writedatacount"));

    String startTime = null;
    String endTime = null;
    String totalTime = null;

    String taskname = null;
    String out_type = null;

    String in_dbtype = null;
    String in_fieldNameStr = null;
    String in_dbname = null;
    String in_tabname = null;
    String in_drivername = null;
    String in_url = null;
    String in_username = null;
    String in_password = null;
    String in_srcip = null;
    String in_srcport = null;
    String in_limitingcondition = null;
    String strategyid = null;

    String out_dbtype = null;
    String out_dbname = null;
    String out_tabname = null;
    String out_drivername = null;
    String out_url = null;
    String out_username = null;
    String out_password = null;
    String out_srcip = null;
    String out_srcport = null;
    String out_path = null;
    String out_ip = null;

    Integer resultid = null;
    Long totalLineCount = 0l;
    Long maskLineCount = 0l;
    Long saveLineCount = 0l;
    int exampleLineCount = 5;
    String beforemaskdata = "";
    String aftermaskdata = "";

    boolean flag = false;
    Map<String, Object> maskAlgoInfoMap = null;
    List<String> fieldNameStrList = new ArrayList<>();
    DbBatchTaskConfig dbBatchTaskConfig = null;
    DbBatchTaskResult dbBatchTaskResult = null;
    BatchTaskTabConfig batchTaskTabConfig = null;
    AlgorithmRepository algorithmRepository = null;
    MaskruleRepository maskruleRepository = null;
    MaskruleMapper maskruleMapper = null;
    HashMap<String, cn.god.mask.common.Algorithm> algorithmMap = null;

    String maskWay = null;

    Boolean isWatermark = null;
    String watermarkField = null;
    String waterMarkInfo = null;

    Integer lastMaskLines = 0;

    MongoClient mongoClient = null;
    MongoClient outMongoClient = null;


    Map<String, String> abnormalHandleMap = new HashMap<>(); //异常数据处理

    Map<String, String> abnormalHandlingAlarmMap = new HashMap<>(); //异常数据处理告警


    public DoBatchDBTaskJob(DbBatchTaskConfigService dbBatchTaskConfigService, DatasourceService datasourceService,
                            MaskStrategyFieldService maskStrategyFieldService, DbBatchTaskResultService dbBatchTaskResultService,
                            AlgorithmService algorithmService, DbBatchTaskConfigMapper dbBatchTaskConfigMapper, DbBatchTaskResultMapper dbBatchTaskResultMapper,
                            SystemLogService logService, Integer id, String submituser, String in_tabname, String strategyid, MaskTaskresultrecordsService maskTaskresultrecordsService,
                            MetaFieldService metaFieldService, MaskStrategyTableService maskStrategyTableService, BatchTaskTabConfig batchTaskTabConfig,AlgorithmRepository algorithmRepository,
                            MaskruleMapper maskruleMapper,MaskruleRepository maskruleRepository) {
        this.dbBatchTaskConfigService = dbBatchTaskConfigService;
        this.dataSourceService = datasourceService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.dbBatchTaskResultService = dbBatchTaskResultService;
        this.algorithmService = algorithmService;
        this.dbBatchTaskConfigMapper = dbBatchTaskConfigMapper;
        this.dbBatchTaskResultMapper = dbBatchTaskResultMapper;
        this.taskid = id;
        this.submituser = submituser;
        this.in_tabname = in_tabname;
        this.strategyid = strategyid;
        this.logService = logService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.metaFieldService = metaFieldService;
        this.maskStrategyTableService = maskStrategyTableService;
        this.batchTaskTabConfig = batchTaskTabConfig;
        this.algorithmRepository = algorithmRepository;
        this.maskruleMapper = maskruleMapper;
        this.maskruleRepository = maskruleRepository;
    }

    public boolean exec() {
        // 任务开始时间
        startTime = DateUtil.getNowTime();
        long startTimeLong = System.currentTimeMillis();
        try {
            // 获取脱敏JAR包中规则
            algorithmMap = algorithmService.getAlgorithmByEName();

            initParam();

            initResult();

            // 更新任务表
            dbBatchTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTING);
            dbBatchTaskConfigService.update(dbBatchTaskConfig);

            // 获取表中数据条数
            totalLineCount = getDataCount();

            // 获取脱敏JAR包中规则
            //algorithmMap = algorithmService.getAlgorithmByEName();

            //历史脱敏数量>=0 且 本次数据总量和历史脱敏量相等，无需脱敏
            if (lastMaskLines.longValue() >= 0 && totalLineCount == lastMaskLines.longValue()) {
                log.info("库名为：" + in_dbname + ",表名为：" + in_tabname + " 没有新增数据，无需脱敏");
                createNoneDataTable();
                flag = true;
            } else {
                //历史脱敏数量 > 本次数据总数量 ，说明该表数据变更过，这种程序处理不了，提示脱敏失败
                if (lastMaskLines > totalLineCount) {
                    throw new Exception("库名：" + in_dbname + ",表名：" + in_tabname + "里，本次脱敏该表数据总量小于历史脱敏数据总量，表数据有变更，需清理mask表、历史脱敏结果后重新脱敏");
                }
                Long s = totalLineCount - lastMaskLines;
                log.info("库名为：" + in_dbname + ",表名为：" + in_tabname + "上次脱敏至" + out_tabname + "表的数据数量为：" + lastMaskLines +
                        ",本次需脱敏数据数量：" + s);
                if (Const.DB_MONGODB.equalsIgnoreCase(in_dbtype)) {   // MongoDB
                    doMaskDataByMongoDB(maskruleRepository);
                } else if (Const.DB_SEQUOIADB.equalsIgnoreCase(in_dbtype)) {//巨杉
                    doMaskDataBySequoiaDB(maskruleRepository);
                } else if (Const.DB_REDIS.equalsIgnoreCase(in_dbtype)) {//redis
                    doMaskDataByRedis(maskruleRepository);
                } else {
                    // 脱敏执行流程
                    doMaskData(maskruleRepository);
                }
            }


            // 结束时间
            endTime = DateUtil.getNowTime();
            // 执行总时间
            totalTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(startTime, endTime));
            // 更新时间
            dbBatchTaskResult.setJobendtime(endTime);
            dbBatchTaskResult.setJobtotaltime(totalTime);
            dbBatchTaskResult.setOutputipaddress(out_ip);
            dbBatchTaskResult.setOutputpath(out_path);
            dbBatchTaskResult.setOutputname(out_tabname);
            dbBatchTaskResult.setTotallines(totalLineCount.intValue());
            dbBatchTaskResult.setMasklines(maskLineCount.intValue());
            dbBatchTaskResult.setLastmasklines(lastMaskLines);
            dbBatchTaskResult.setIsremove(Const.IS_REMOVE_NO);
            dbBatchTaskResult.setBeforemaskdata(beforemaskdata);
            dbBatchTaskResult.setAftermaskdata(aftermaskdata);
            if (flag) {
                log.info("任务ID【" + taskid + "】,保存到" + ("1".equals(out_type) ? "库表" : "文件") + "成功");
                // 更新结果
                dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_SUCCESS);
                dbBatchTaskResultService.update(dbBatchTaskResult);

                //更新执行条进度
                dbBatchTaskConfigService.alterTaskExecutionAnticipate("100", taskid);

                // 更新任务表
                //dbBatchTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS);
                //dbBatchTaskConfigService.update(dbBatchTaskConfig);
                //插入任务结果记录表
                MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
                maskTaskresultrecords.setTaskname(dbBatchTaskResult.getTaskname());
                maskTaskresultrecords.setTasktype(Const.MASK_TASK_DB);
                maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE);
                maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
                maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
                maskTaskresultrecordsService.create(maskTaskresultrecords);
            } else {
                log.info("任务ID【" + taskid + "】,保存到" + ("1".equals(out_type) ? "库表" : "文件") + "失败");
                // 更新结果
                dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_FAIL);
                dbBatchTaskResultService.update(dbBatchTaskResult);

                Random random = new Random();
                int randomNumber = random.nextInt((50 - 30) + 1) + 30;
                dbBatchTaskConfigService.alterTaskExecutionAnticipate(String.valueOf(randomNumber), taskid);

                // 更新任务表
                //dbBatchTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
                //dbBatchTaskConfigService.update(dbBatchTaskConfig);
                //插入任务结果记录表
                MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
                maskTaskresultrecords.setTaskname(dbBatchTaskResult.getTaskname());
                maskTaskresultrecords.setTasktype(Const.MASK_TASK_DB);
                maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE);
                maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
                maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
                maskTaskresultrecordsService.create(maskTaskresultrecords);
            }

            // TODO 脱敏字段异常处置告警,写入告警记录表
            if (!abnormalHandlingAlarmMap.isEmpty()) {
                desensitizationAbnormalAlarm();
            }

        } catch (Exception e) {
            e.printStackTrace();

            // 更新结果
            dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_FAIL);
            dbBatchTaskResultService.update(dbBatchTaskResult);

            Random random = new Random();
            int randomNumber = random.nextInt((50 - 30) + 1) + 30;
            dbBatchTaskConfigService.alterTaskExecutionAnticipate(String.valueOf(randomNumber), taskid);

            // 更新任务表
            //dbBatchTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
            //dbBatchTaskConfigService.update(dbBatchTaskConfig);
            //插入任务结果记录表
            MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
            maskTaskresultrecords.setTaskname(dbBatchTaskResult.getTaskname());
            maskTaskresultrecords.setTasktype(Const.MASK_TASK_DB);
            maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE);
            maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
            maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
            maskTaskresultrecordsService.create(maskTaskresultrecords);
            log.error("批量脱敏任务，表" + in_tabname + "执行失败");

            //TODO 写入系统监控-异常日志 且首页告警统计
            Log log = new Log();
            String description = "任务号:" + taskname + ",表名：" + in_tabname + ",执行数据库多表脱敏任务出现异常";
            log.setDescription(description);
            StackTraceElement[] stackTrace = e.getStackTrace();
            String method = stackTrace[0].getClassName() + "." + stackTrace[0].getMethodName();
            log.setMethod(method);
            String parameters = "{taskname:" + taskname + "}";
            log.setParams(parameters);
            long endTime = System.currentTimeMillis();
            int time = (int) ((endTime - startTimeLong) / 1000);
            log.setTime((long) time);
            log.setCreateTime(new Timestamp(System.currentTimeMillis()));

            //将打印到控制台的内容，转为字符串,存放到日志里
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            String exInfo = sw.toString();

            //前面需拼接com.wzsec.exception.BadRequestException:  格式，以免影响之前逻辑
            log.setExceptionDetail("com.wzsec.exception.BadRequestException: " + e.getMessage() + "\n\t" +
                    "at" + exInfo);
            logService.saveLogs(log);
        } finally {
            log.info("批量脱敏任务，表" + in_tabname + "执行完毕");
            return flag;
        }
    }

    /**
     * 执行redis脱敏任务
     */
    private void doMaskDataByRedis(MaskruleRepository maskruleRepository) {
        long initialPosition = 1L;
        if (totalLineCount > pageSize) { // 查询总数据量大于设定每页读取行数,即进行分页处理
            initialPosition = totalLineCount % pageSize.intValue() == 0 ?
                    totalLineCount / pageSize.intValue() : totalLineCount / pageSize.intValue() + 1;
        }
        try {
            //获取输入输出连接
            Jedis inJedis = RedisUtil.queryRedisConnect(in_srcip, in_srcport, in_password);
            Jedis outJedis = RedisUtil.queryRedisConnect(out_srcip, out_srcport, out_password);
            //策略字段
            String[] fieldNameStrs = in_fieldNameStr.split(",");
            // 任务开始时间
            startTime = DateUtil.getNowTime();
            boolean isCreateTable = true;
            for (int i = 0; i < initialPosition; i++) {
                // 读取redis结果集
                String getResultSetStartTime = DateUtil.getNowTime();
                int skip = i * pageSize.intValue();
                int limit = pageSize.intValue();
                String getResultSetEndTime = DateUtil.getNowTime();
                String getResultSetTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(getResultSetStartTime, getResultSetEndTime));
                if (pageSize > totalLineCount) {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, totalLineCount, getResultSetTime);
                } else {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, pageSize + i * pageSize, getResultSetTime);
                }
                List<Map<String, Object>> fieldDataList = new ArrayList<>();
                inJedis.select(Integer.valueOf(in_dbname));
                String inStaticMask = inJedis.get(in_tabname);
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(inStaticMask);
                for (JsonNode arrayNode : jsonNode) {
                    Iterator<String> fieldNames = arrayNode.fieldNames();
                    Map<String, Object> fieldDataMap = new LinkedHashMap<>();
                    while (fieldNames.hasNext()) {
                        String fieldName = fieldNames.next();
                        JsonNode node = arrayNode.get(fieldName);
                        fieldDataMap.put(fieldName,node == null?"":node.toString().replaceAll("\"",""));
                    }
                    fieldDataMap = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap,new ArrayList<>(),maskruleRepository);
                    fieldDataList.add(fieldDataMap);
                }

                boolean isUpdateExample = false;
                //TODO 因传参原因，单独处理 "保持数值均值特征脱敏" NUMKEEPAVERAGE
                for (Map.Entry<String, Object> entry : maskAlgoInfoMap.entrySet()) {
                    if ("NUMKEEPAVERAGE$".equals(entry.getValue())) {
                        String fieldName = entry.getKey();
                        List<Object> numDataList = new ArrayList<>();
                        for (Map<String, Object> stringMap : fieldDataList) {
                            String data = stringMap.get(fieldName) == null ? "" : stringMap.get(fieldName).toString();
                            numDataList.add(data);
                        }
                        List<Object> averageData = Rule4AlgorithmUtil.p_average(numDataList);
                        for (int j = 0; j < fieldDataList.size(); j++) {
                            fieldDataList.get(j).put(fieldName, averageData.get(j).toString());
                        }
                        isUpdateExample = true;
                    }
                }

                // TODO 数据样例更新
                if (isUpdateExample) {
                    StringBuffer maskdataline = new StringBuffer();
                    aftermaskdata = "";
                    for (int z = 0; z < fieldDataList.size(); z++) {
                        Map<String, Object> stringStringMap = fieldDataList.get(z);
                        for (Map.Entry<String, Object> maskDataEntey : stringStringMap.entrySet()) {
                            maskdataline.append(maskDataEntey.getValue() + ",");
                        }
                        aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
                        maskdataline.setLength(0); //拼接完一行后，清空数据
                        //只取采5条数据样例
                        if ((z + 1) == 5) {
                            break;
                        }
                    }
                }

                if (fieldDataList.size() == pageSize) {// 写出结果
                        /*saveencdecRowData(databaseUtil, driversByType, isCreateTable, fieldDataList, fieldNameStrs,
                                i * pageSize, i * pageSize + pageSize, writeDataCount);// 批量保存数据，里面设置有批量写入*/
                    saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入

                    if (pageSize > totalLineCount) {
                        log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                    } else {
                        log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                    }
                    fieldDataList.clear();
                    isCreateTable = false;
                } else if (fieldDataList.size() != pageSize) {// 写出结果
                    saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入


                    if (pageSize > totalLineCount) {
                        log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                    } else {
                        log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                    }

                    fieldDataList.clear();
                    isCreateTable = false;
                }

            }
            inJedis.close();
            outJedis.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    /**
     * 脱敏字段异常处置告警,写入告警记录表(非主干代码记录日志异常仅输出警告)
     */
    private void desensitizationAbnormalAlarm() {
        try {
            // 拼接字段处置方式
            StringBuilder result = new StringBuilder();
            for (Map.Entry<String, String> entry : abnormalHandlingAlarmMap.entrySet()) {
                result.append("字段:").append(entry.getKey()).append(", 异常数据处置方式:").append(entry.getValue()).append("; ");
            }

            if (result.length() > 0) {
                result.setLength(result.length() - 2);
            }

            String template = "库名:{}, 表名:{}, {} ";
            String alarmDetails = StrUtil.format(template, in_dbname, in_tabname, result);
            String url = dbBatchTaskResult.getIpaddress();

            String in_ip = "";
            String in_port = "";
            if (url.contains("//")) {
                String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringBetween(url, "//", "/");
                String[] ips = hostNameAndPort1.split(":");
                in_ip = ips[0];
                in_port = ips[1];
            } else if (url.contains("@")) {
                String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringAfter(url, "@");
                String hostNameAndPort2 = com.wzsec.utils.StringUtils.substringBeforeLast(hostNameAndPort1, ":");
                String[] ips = hostNameAndPort2.split(":");
                in_ip = ips[0];
                in_port = ips[1];
            } else {
                String[] ips = url.split(":");
                in_ip = ips[0];
                in_port = ips[1];
            }

            String out_ip = dbBatchTaskResult.getOutputipaddress();
            DmAlarmdisposal maskAlarmdisposal = new DmAlarmdisposal();
            maskAlarmdisposal.setCircumstantiality(alarmDetails);
            maskAlarmdisposal.setChecktime(DateUtil.getNowTime());
            maskAlarmdisposal.setReservefield2(dbBatchTaskResult.getOutputtype()); //事件类型-库表
            maskAlarmdisposal.setReservefield3(Const.RISK_MIDDLE); //事件等级-中
            maskAlarmdisposal.setSourceip(in_ip);
            maskAlarmdisposal.setSourceport(in_port);
            maskAlarmdisposal.setDestinationip(out_ip);
            maskAlarmdisposal.setDestinationport("");
            maskAlarmdisposal.setAccount(dbBatchTaskResult.getUsername());
            maskAlarmdisposal.setEventrule(Const.DICT_DESENSITIZATION_ABNORMAL_DATA_HANDLING); //事件名称
            maskAlarmdisposalService.create(maskAlarmdisposal);
            //告警推送syslog
            MonitorRiskAlarmData.sendDmExample(maskAlarmdisposal);
        } catch (Exception e) {
            log.warn("写入统计查询-告警记录或推送syslog异常");
        }
    }


    /**
     * 执行静态脱敏任务(根据总行数及配置的读取条数进行分页遍历结果集)
     */
    private void doMaskData(MaskruleRepository maskruleRepository) throws Exception {

        long initialPosition = 0L;
        initialPosition = (totalLineCount / pageSize);

        long tmpPage = 0;
        tmpPage = totalLineCount % pageSize == 0 ? 0 : 1;
        initialPosition = initialPosition + tmpPage;   //获取分页数量

        Connection con = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;

        try {

            boolean isCreateTable = false;
            if (Const.DB_TASK_OUTPUTTYPE_DB.equals(out_type)) {

                //如果目标源有表，则直接向该表新增数据，反之先创建表再新增
                String out_tabname = this.out_dbname + "." + this.out_tabname;
                if (Const.DB_MYSQL.equalsIgnoreCase(out_dbtype) || Const.DB_UNIDB.equalsIgnoreCase(out_dbtype) || Const.DB_STARROCKS.equalsIgnoreCase(out_dbtype)) {
                    out_tabname = "`" + this.out_dbname + "`.`" + this.out_tabname + "`";
                }
                if (Const.DB_TERADATA.equalsIgnoreCase(out_dbtype)) {
                    out_tabname = this.out_tabname + "." + this.out_tabname.replaceAll(" ","");
                }
                if (Const.DB_SINODB.equalsIgnoreCase(out_dbtype)) {
                    out_tabname = this.out_tabname ;
                }
                if (Const.DB_ORACLE.equalsIgnoreCase(out_dbtype)) {
                    out_tabname = "\"" + this.out_dbname + "\".\"" + this.out_tabname + "\"";
                }
                if (Const.DB_OSCAR.equalsIgnoreCase(out_dbtype)) {
                    out_tabname = "\"" + this.out_tabname + "\"";
                }
                String sql = "select count(*) from " + out_tabname;
                if (null == DatabaseUtil.prepareStatement(sql, null, out_drivername, out_url, out_username, out_password)) {
                    isCreateTable = true;
                }

            }

            //TODO blob longblob类型 => mysql
            List<Map<String, String>> fieldInfoList = new ArrayList<>();
            if (in_dbtype.equalsIgnoreCase(Const.DB_MYSQL)) {
                fieldInfoList = MysqlUtil.queryFieldInfoList(in_dbname, in_tabname, in_url, in_username, in_password);
                fieldInfoList = fieldInfoList.stream().filter(t-> t.get("FieldType").contains("blob")).collect(Collectors.toList());
            }
            for (int i = 0; i < initialPosition; i++) {
                // TODO 获取分页获取结果集
                resultSet = getResultSet(con, preparedStatement, i * pageSize + lastMaskLines, pageSize);

                String[] fieldNameStrs = in_fieldNameStr.split(",");
                List<Map<String, Object>> fieldDataList = new ArrayList<>();

                if (resultSet != null) {
                    // 遍历查询结果集
                    while (resultSet.next()) {
                        Map<String, Object> fieldDataMap = new LinkedHashMap<String, Object>();
                        for (String fieldName : fieldNameStrs) {
                            boolean flag = true;
                            if (ObjectUtil.isNotEmpty(fieldInfoList) && in_dbtype.equalsIgnoreCase(Const.DB_MYSQL)){
                                List<Map<String, String>> collect = fieldInfoList.stream().filter(t -> t.get("FieldEName").equals(fieldName)).collect(Collectors.toList());
                                if (ObjectUtil.isNotEmpty(collect)){
                                    flag = false;
                                }
                            }
                            if (flag) {
                                String fieldNameReplace = fieldName.replaceAll("`", "");
                                fieldDataMap.put(fieldNameReplace, resultSet.getString(fieldNameReplace));
                            }else {
                                //blob 或 longblob 类型数据
                                String fieldNameReplace = fieldName.replaceAll("`", "");
                                Blob blob = resultSet.getBlob(fieldNameReplace);
                                if (blob != null){
                                    fieldDataMap.put(fieldNameReplace, blob);
                                }else {
                                    fieldDataMap.put(fieldNameReplace, null);
                                }
                            }
                        }

                        // 行数据写出
                        fieldDataMap = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap,fieldInfoList, maskruleRepository);
                        fieldDataList.add(fieldDataMap);
                        if (fieldDataList.size() == pageSize) {// 写出结果
                            saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入
                            fieldDataList.clear();
                            isCreateTable = false;
                        }
                    }

                    boolean isUpdateExample = false;
                    //TODO 因传参原因，单独处理 "保持数值均值特征脱敏" NUMKEEPAVERAGE
                    for (Map.Entry<String, Object> entry : maskAlgoInfoMap.entrySet()) {
                        if ("NUMKEEPAVERAGE$".equals(entry.getValue())) {
                            String fieldName = entry.getKey();
                            List<Object> numDataList = new ArrayList<>();
                            for (Map<String, Object> stringMap : fieldDataList) {
                                String data = stringMap.get(fieldName) == null? "":stringMap.get(fieldName).toString();
                                numDataList.add(data);
                            }
                            try {
                                List<Object> averageData = Rule4AlgorithmUtil.p_average(numDataList);
                                for (int j = 0; j < fieldDataList.size(); j++) {
                                    fieldDataList.get(j).put(fieldName, averageData.get(j).toString());
                                }
                            } catch (Exception ex) {
                                log.info("保持数值均值特征脱敏算法出现错误：" + ex.getMessage());
                            }
                            isUpdateExample = true;
                        }
                    }

                    // TODO 数据样例更新
                    if (isUpdateExample) {
                        StringBuffer maskdataline = new StringBuffer();
                        aftermaskdata = "";
                        for (int z = 0; z < fieldDataList.size(); z++) {
                            Map<String, Object> stringStringMap = fieldDataList.get(z);
                            for (Map.Entry<String, Object> maskDataEntey : stringStringMap.entrySet()) {
                                maskdataline.append(maskDataEntey.getValue() + ",");
                            }
                            aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
                            maskdataline.setLength(0); //拼接完一行后，清空数据
                            //只取采5条数据样例
                            if ((z + 1) == 5) {
                                break;
                            }
                        }
                    }


                    if (fieldDataList != null && fieldDataList.size() > 0) {// 写出结果
                        saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入
                        fieldDataList.clear();
                        isCreateTable = false;
                    }

                }
                if (!flag) {
                    throw new Exception("本批数据脱敏失败");
                }
                if (totalLineCount - (i * pageSize + pageSize) >= 0) {
                    Console.log("==> 完成脱敏 {} 数据落盘 {} 行 ,剩余 {} 行", out_tabname, i * pageSize + pageSize, totalLineCount - (i * pageSize + pageSize));
                }
            }

        } catch (Exception ex) {
            log.error("连接数据库失败或获取抽取字段的数据出现异常");
            throw ex;
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 巨杉脱敏任务
     */
    private void doMaskDataBySequoiaDB(MaskruleRepository maskruleRepository) {
        long initialPosition = 1L;
        if (totalLineCount > pageSize) { // 查询总数据量大于设定每页读取行数,即进行分页处理
            initialPosition = totalLineCount % pageSize.intValue() == 0 ?
                    totalLineCount / pageSize.intValue() : totalLineCount / pageSize.intValue() + 1;
        }

        try {
            //获取输入输出连接
            Sequoiadb inSequoiadb = SequoiaDBUtil.queryConnect(in_url, in_username, in_password);
            Sequoiadb outSequoiadb = SequoiaDBUtil.queryConnect(out_url, out_username, out_password);

            //策略字段
            String[] fieldNameStrs = in_fieldNameStr.split(",");

            // 任务开始时间
            startTime = DateUtil.getNowTime();

            boolean isCreateTable = true;

            for (int i = 0; i < initialPosition; i++) {

                // 读取SequoiaDB结果集
                String getResultSetStartTime = DateUtil.getNowTime();

                int skip = i * pageSize.intValue();
                int limit = pageSize.intValue();
//                List<Document> documentList = MongoDBUtil.batchReadDocument(in_dbname, in_tabname, mongoClient, skip, limit);

                String getResultSetEndTime = DateUtil.getNowTime();
                String getResultSetTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(getResultSetStartTime, getResultSetEndTime));

                if (pageSize > totalLineCount) {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, totalLineCount, getResultSetTime);
                } else {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, pageSize + i * pageSize, getResultSetTime);
                }

                List<Map<String, Object>> fieldDataList = new ArrayList<>();
                CollectionSpace inCs = inSequoiadb.getCollectionSpace(in_dbname);
                List<String> inClNameList = inCs.getCollectionNames();
                for (String coll : inClNameList) {
                    DBCollection collection = inCs.getCollection(tableNameStr(coll));
                    DBCursor cursor = collection.query();
                    while (cursor.hasNext()) {
                        Map<String, Object> fieldDataMap = new LinkedHashMap<>();
                        BSONObject next = cursor.getNext();
                        Set<String> keySet = next.keySet();
                        for (String key : keySet) {
                            Object obj = next.get(key);
                            fieldDataMap.put(key, obj == null ? "" : obj.toString());
                        }
                        fieldDataMap = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap,new ArrayList<>(),maskruleRepository);
                        fieldDataList.add(fieldDataMap);
                    }
                }

                boolean isUpdateExample = false;
                //TODO 因传参原因，单独处理 "保持数值均值特征脱敏" NUMKEEPAVERAGE
                for (Map.Entry<String, Object> entry : maskAlgoInfoMap.entrySet()) {
                    if ("NUMKEEPAVERAGE$".equals(entry.getValue())) {
                        String fieldName = entry.getKey();
                        List<Object> numDataList = new ArrayList<>();
                        for (Map<String, Object> stringMap : fieldDataList) {
                            String data = stringMap.get(fieldName) == null?"":stringMap.get(fieldName).toString();
                            numDataList.add(data);
                        }
                        List<Object> averageData = Rule4AlgorithmUtil.p_average(numDataList);
                        for (int j = 0; j < fieldDataList.size(); j++) {
                            fieldDataList.get(j).put(fieldName, averageData.get(j).toString());
                        }
                        isUpdateExample = true;
                    }
                }

                // TODO 数据样例更新
                if (isUpdateExample) {
                    StringBuffer maskdataline = new StringBuffer();
                    aftermaskdata = "";
                    for (int z = 0; z < fieldDataList.size(); z++) {
                        Map<String, Object> stringStringMap = fieldDataList.get(z);
                        for (Map.Entry<String, Object> maskDataEntey : stringStringMap.entrySet()) {
                            maskdataline.append(maskDataEntey.getValue() + ",");
                        }
                        aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
                        maskdataline.setLength(0); //拼接完一行后，清空数据
                        //只取采5条数据样例
                        if ((z + 1) == 5) {
                            break;
                        }
                    }
                }

                if (fieldDataList.size() == pageSize) {// 写出结果
                        /*saveencdecRowData(databaseUtil, driversByType, isCreateTable, fieldDataList, fieldNameStrs,
                                i * pageSize, i * pageSize + pageSize, writeDataCount);// 批量保存数据，里面设置有批量写入*/
                    saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入

                    if (pageSize > totalLineCount) {
                        log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                    } else {
                        log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                    }
                    fieldDataList.clear();
                    isCreateTable = false;
                } else if (fieldDataList.size() != pageSize) {// 写出结果
                    saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入


                    if (pageSize > totalLineCount) {
                        log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                    } else {
                        log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                    }

                    fieldDataList.clear();
                    isCreateTable = false;
                }

            }
            inSequoiadb.disconnect();
            outSequoiadb.disconnect();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 执行MongoDB脱敏任务
     */
    private void doMaskDataByMongoDB(MaskruleRepository maskruleRepository) {

        long initialPosition = 1L;
        if (totalLineCount > pageSize) { // 查询总数据量大于设定每页读取行数,即进行分页处理
            initialPosition = totalLineCount % pageSize.intValue() == 0 ?
                    totalLineCount / pageSize.intValue() : totalLineCount / pageSize.intValue() + 1;
        }

        try {
            //获取输入输出连接
            mongoClient = MongoDBUtil.getConnect(in_url, in_username, in_password, in_dbname);
            outMongoClient = MongoDBUtil.getConnect(out_url, out_username, out_password, out_dbname);
            //策略字段
            String[] fieldNameStrs = in_fieldNameStr.split(",");

            // 任务开始时间
            startTime = DateUtil.getNowTime();

            boolean isCreateTable = true;

            for (int i = 0; i < initialPosition; i++) {

                // 读取MongoDB结果集
                String getResultSetStartTime = DateUtil.getNowTime();

                int skip = i * pageSize.intValue();
                int limit = pageSize.intValue();
                List<Document> documentList = MongoDBUtil.batchReadDocument(in_dbname, in_tabname, mongoClient, skip, limit);

                String getResultSetEndTime = DateUtil.getNowTime();
                String getResultSetTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(getResultSetStartTime, getResultSetEndTime));

                if (pageSize > totalLineCount) {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, totalLineCount, getResultSetTime);
                } else {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, pageSize + i * pageSize, getResultSetTime);
                }

                List<Map<String, Object>> fieldDataList = new ArrayList<>();

                if (documentList.size() > 0) {
                    // 遍历查询结果集
                    for (Document document : documentList) {

                        JSONObject jsonObject = JSONUtil.parseObj(document.toJson());

                        Map<String, Object> fieldDataMap = new LinkedHashMap<>();
                        for (String fieldName : fieldNameStrs) {
                            String fieldValue = jsonObject.getStr(fieldName);
                            if (fieldValue.contains("$numberLong")) {
                                fieldValue = JSONUtil.parseObj(fieldValue).getStr("$numberLong");
                            }
                            fieldDataMap.put(fieldName, fieldValue);
                        }

                        fieldDataMap = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap,new ArrayList<>(),maskruleRepository);
                        fieldDataList.add(fieldDataMap);
                    }

                    boolean isUpdateExample = false;
                    //TODO 因传参原因，单独处理 "保持数值均值特征脱敏" NUMKEEPAVERAGE
                    for (Map.Entry<String, Object> entry : maskAlgoInfoMap.entrySet()) {
                        if ("NUMKEEPAVERAGE$".equals(entry.getValue())) {
                            String fieldName = entry.getKey();
                            List<Object> numDataList = new ArrayList<>();
                            for (Map<String, Object> stringMap : fieldDataList) {
                                String data = stringMap.get(fieldName) == null?"":stringMap.get(fieldName).toString();
                                numDataList.add(data);
                            }
                            List<Object> averageData = Rule4AlgorithmUtil.p_average(numDataList);
                            for (int j = 0; j < fieldDataList.size(); j++) {
                                fieldDataList.get(j).put(fieldName, averageData.get(j).toString());
                            }
                            isUpdateExample = true;
                        }
                    }

                    // TODO 数据样例更新
                    if (isUpdateExample) {
                        StringBuffer maskdataline = new StringBuffer();
                        aftermaskdata = "";
                        for (int z = 0; z < fieldDataList.size(); z++) {
                            Map<String, Object> stringStringMap = fieldDataList.get(z);
                            for (Map.Entry<String, Object> maskDataEntey : stringStringMap.entrySet()) {
                                maskdataline.append(maskDataEntey.getValue() + ",");
                            }
                            aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
                            maskdataline.setLength(0); //拼接完一行后，清空数据
                            //只取采5条数据样例
                            if ((z + 1) == 5) {
                                break;
                            }
                        }
                    }

                    if (fieldDataList.size() == pageSize) {// 写出结果
                        /*saveencdecRowData(databaseUtil, driversByType, isCreateTable, fieldDataList, fieldNameStrs,
                                i * pageSize, i * pageSize + pageSize, writeDataCount);// 批量保存数据，里面设置有批量写入*/
                        saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入

                        if (pageSize > totalLineCount) {
                            log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                        } else {
                            log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                        }
                        fieldDataList.clear();
                        isCreateTable = false;
                    } else if (fieldDataList.size() != pageSize) {// 写出结果
                        saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入


                        if (pageSize > totalLineCount) {
                            log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                        } else {
                            log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                        }

                        fieldDataList.clear();
                        isCreateTable = false;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            mongoClient.close();
            outMongoClient.close();
        }
    }

    private Map<String, Object> getMaskRowData(Map<String, Object> maskAlgoInfoMap,
                                               HashMap<String, cn.god.mask.common.Algorithm> algorithmMap,
                                               Map<String, Object> fieldDataMap,
                                               List<Map<String, String>> fieldInfoList,
                                               MaskruleRepository maskruleRepository) {
        // 行数据脱敏
        StringBuffer dataline = new StringBuffer();
        StringBuffer maskdataline = new StringBuffer();
        for (String filedName : fieldDataMap.keySet()) {
            Object obj = fieldDataMap.get(filedName);
            String data = "";
            if (obj != null){
                List<Map<String, String>> collect = fieldInfoList.stream().filter(t -> t.get("FieldEName").equals(filedName)).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(collect)){
                    if (obj instanceof Blob) {
                        Maskrule maskrule = maskruleRepository.findRuleName("通用掩码规则");
                        //算法id
                        Integer algorithmid = maskrule.getAlgorithmid();
                        AlgorithmDto algorithmDto = algorithmService.findById(algorithmid);
                        Blob maskedBlob = BlobMaskUtil.getMaskedBlob((Blob) obj, null, maskrule, algorithmDto);
                        fieldDataMap.put(filedName, maskedBlob);
                    }
                    dataline.append("(BLOB),");
                    maskdataline.append("(BLOB),");
                    continue;
                }else {
                    data = fieldDataMap.get(filedName).toString();
                }
            }
            String result = data;
            // 该字段有数据才进行脱敏
            if (data != null && !"".equals(data)) {
                // 获取字段的脱敏算法信息 MD5$123,412
                String maskAlgoInfo = maskAlgoInfoMap.get(filedName) == null?"":maskAlgoInfoMap.get(filedName).toString();
                // 该字段有脱敏算法才进行脱敏
                if (maskAlgoInfo != null && !"".equals(maskAlgoInfo) && !"NUMKEEPAVERAGE$".equals(maskAlgoInfo)) {
                    // TODO 原始逻辑
                    /*String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                    String algo = AlgoParamInfo[0];
                    String param = "";
                    if (AlgoParamInfo.length > 1) {
                        param = AlgoParamInfo[1];
                    }
                    try {
                        // 数据:data算法名:algo参数:param
                        result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                    } catch (Exception e) {
                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algo + "参数:" + param);
                    } catch (Error e) {
                        log.error("执行脱敏出现错误,数据:" + data + "算法名:" + algo + "参数:" + param);
                    }
                    fieldDataMap.put(filedName, result);*/

                    // TODO 脱敏规则新增分段配置后逻辑
                    String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                    String isExtract = AlgoParamInfo[0];
                    if (Const.ALG_CONFIG_WAY_ENTIRETY.equals(isExtract)) {
                        String algo = AlgoParamInfo[1];
                        String param = "";
                        if (AlgoParamInfo.length > 2) {
                            param = AlgoParamInfo[2];
                        }
                        try {
                            // 联系方式复合类型脱敏 (CONTACT_INFORMATION_COMBINATION)
                            // "HIDECODE"  "*,3,4" (手机号通用)  "HIDECODE"  "*,3,3" (固话通用)
                            if (algo.equalsIgnoreCase("CONTACT_INFORMATION_COMBINATION")) {
                                if (ProRuleFactory.checkDataByProRule("P_PHONENUMBER", data)) {
                                    result = MaskAlgFactory.getMaskData(data, "HIDECODE", "*,3,4", null);
                                } else if (ProRuleFactory.checkDataByProRule("P_FIXPHONE", data)) {
                                    result = MaskAlgFactory.getMaskData(data, "HIDECODE", "*,3,3", null);
                                } else {
                                    result = MaskAlgFactory.getMaskData(data, "HIDECODE", "*,3,0", null);
                                }
                            } else {
                                // 数据:data算法名:algo参数:param
                                Algorithm algorithm = algorithmMap.get(algo);
                                String sparefield1 = algorithm.getSparefield1();
                                if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                    result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                                }else {
                                    //自定义算法
                                    String jarname = algorithm.getJarname();//jar包名称
                                    String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                    String methodname = algorithm.getMethodname();//方法名
                                    Map<String, String> paramMap = new HashMap<>();
                                    paramMap.put("jarname",jarname);
                                    paramMap.put("funcnamepath",funcnamepath);
                                    paramMap.put("methodname",methodname);
                                    paramMap.put("maskData",data);
                                    result = AlgorithmUtils.invokeJarMethod(paramMap);
                                }
                            }
                        } catch (Exception e) {

                            log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algo + "参数:" + param);
                            // TODO 异常数据处置
                            String abnormalDataHandling = abnormalHandleMap.get(filedName);
                            log.warn("异常数据处置算法名为: {}", abnormalDataHandling);
                            result = abnormalDataHandling(filedName, result, data, abnormalDataHandling);

                        }
                        fieldDataMap.put(filedName, result);
                    } else {
                        String tmpData = data;
                        String maskData = "";
                        String algo = "";
                        String param = "";
                        try {
                            String[] split = maskAlgoInfo.split(";");
                            for (String configPar : split) {
                                AlgoParamInfo = configPar.split("\\$");
                                algo = AlgoParamInfo[1];
                                param = "";
                                if (AlgoParamInfo.length > 2) {
                                    param = AlgoParamInfo[2];
                                }
                                String extract = AlgoParamInfo[3];
                                String[] extractArr = extract.split("-");
                                Integer startIndex = Integer.valueOf(extractArr[0]) - 1;
                                Integer endIndex = Integer.valueOf(extractArr[1]);
                                maskData = tmpData.substring(startIndex, endIndex);

                                // 数据:data算法名:algo参数:param
                                Algorithm algorithm = algorithmMap.get(algo);
                                String sparefield1 = algorithm.getSparefield1();
                                String middleData = "";
                                if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                    middleData = MaskAlgFactory.getMaskData(maskData, algo, param, algorithmMap);
                                }else {
                                    //自定义算法
                                    String jarname = algorithm.getJarname();//jar包名称
                                    String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                    String methodname = algorithm.getMethodname();//方法名
                                    Map<String, String> paramMap = new HashMap<>();
                                    paramMap.put("jarname",jarname);
                                    paramMap.put("funcnamepath",funcnamepath);
                                    paramMap.put("methodname",methodname);
                                    paramMap.put("maskData",maskData);
                                    middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                                }

                                String startData = tmpData.substring(0, startIndex);

                                String endData = tmpData.substring(endIndex, data.length());

                                tmpData = startData + middleData + endData;
                            }
                        } catch (Exception e) {
                            log.error("执行脱敏出现异常,原始数据：" + data + "，拆分后脱敏数据:" + maskData + "算法名:" + algo + "参数:" + param);
                        } finally {
                            //拆分后数据脱敏有异常，结束循环，不要继续脱敏，原本数据返回即可
                            result = tmpData;
                        }
                        fieldDataMap.put(filedName, result);
                    }
                }
            }
            if (exampleLineCount > 0) {
                dataline.append(data + ",");
                maskdataline.append(result + ",");
            }
        }
        if (exampleLineCount > 0) {
            beforemaskdata += dataline.toString().substring(0, dataline.toString().length() - 1) + "\n";
            aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
            exampleLineCount--;
        }
        maskLineCount++;
        return fieldDataMap;
    }

    /**
     * 异常数据处理
     *
     * @param result 结果
     * @param data   数据
     * @return {@link String }
     */
    private String abnormalDataHandling(String filedName, String result, String data, String abnormalDataHandling) {
        // TODO ---------- 异常数据处理 ----------
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(abnormalDataHandling)) {
                if (!abnormalDataHandling.equalsIgnoreCase("IGNORE")) { //置空,替换
                    com.wzsec.modules.mask.domain.Algorithm algorithm = algorithmService.findByAlgorithmName(abnormalDataHandling);
                    String sparefield1 = algorithm.getSparefield1();
                    if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                        result = MaskAlgFactory.getMaskData(data, abnormalDataHandling, Const.AES_SECRET_KEY, null);
                    }else {
                        //自定义算法
                        String jarname = algorithm.getJarname();//jar包名称
                        String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                        String methodname = algorithm.getMethodname();//方法名
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("jarname",jarname);
                        paramMap.put("funcnamepath",funcnamepath);
                        paramMap.put("methodname",methodname);
                        paramMap.put("maskData",data);
                        result = AlgorithmUtils.invokeJarMethod(paramMap);
                    }

                    String cNameAlgorithm = abnormalDataHandling.equals(Const.ABNORMAL_DATA_HANDLING_EMPTY) ?
                            Const.ABNORMAL_DATA_HANDLING_EMPTY_CNAME : Const.ABNORMAL_DATA_HANDLING_SUBSTITUTION_CNAME;
                    abnormalHandlingAlarmMap.put(filedName, cNameAlgorithm);
                } else { // 忽略
                    abnormalHandlingAlarmMap.put(filedName, Const.ABNORMAL_DATA_HANDLING_IGNORE_CNAME);
                }
            }
        } catch (Exception ex) {
            int length = data.length();
            StringBuilder masked = new StringBuilder();
            for (int i = 0; i < length; i++) {
                masked.append('*');
            }
            result = masked.toString();
        }
        return result;
        // TODO ---------- 异常数据处理 ----------
    }


    public static void main(String[] args) {
        String data = "1234566789x";
        Integer startIndex = 1 - 1;
        Integer endIndex = 6;
        String maskData = data.substring(startIndex, endIndex);
        // 数据:data算法名:algo参数:param
        String middleData = "";
        for (int i = 0; i < maskData.length(); i++) {
            middleData += "*";
        }
        String startData = data.substring(0, startIndex);

        String endData = data.substring(endIndex + 1, data.length());
        System.out.println(startData + middleData + endData);
    }

    /**
     * 批量保存数据
     *
     * @param isCreateTable 创建表
     * @param fieldDataList 字段数据列表
     * @param fieldnamaearr fieldnamaearr
     */
    private void saveMaskRowData(boolean isCreateTable, List<Map<String, Object>> fieldDataList, String[] fieldnamaearr, long startLine, long endLine) throws Exception {
        //TODO 原设置水印逻辑，数据最后一位拼接不可见字符
        //如果水印下标不为空，将对应下标位的数据设置水印
        if (isWatermark) {
            Integer waterLineSpacing = Integer.valueOf(ConstEngine.sddEngineConfs.get("waterLineSpacing"));
            //数据行数大于设置的水印行间距，就按照间隔数注入水印
            if (fieldDataList.size() >= waterLineSpacing) {
                for (int i = 0; i < fieldDataList.size(); i++) {
                    Integer index = i + 1;
                    if (index % waterLineSpacing == 0) {//该行等于水印行间距的倍数(例如100、200、300等)
                        String fieldName = fieldDataList.get(i).get(watermarkField) == null?"":fieldDataList.get(i).get(watermarkField).toString();
                        String data = fieldName + waterMarkInfo;
                        fieldDataList.get(i).put(watermarkField, data);
                    }
                }
            } else {
                // 小于水印行间隔，随机抽取一行注入水印
                Random random = new Random();
                int randomNumber = random.nextInt(fieldDataList.size());
                String fieldName = fieldDataList.get(randomNumber).get(watermarkField) == null?"":fieldDataList.get(randomNumber).get(watermarkField).toString();
                String data = fieldName + waterMarkInfo;
                fieldDataList.get(randomNumber).put(watermarkField, data);
            }
        }

        // 6.保存脱敏后的数据到的位置
        if (Const.DB_TASK_OUTPUTTYPE_DB.equals(out_type)) {
            if (Const.DB_MONGODB.equalsIgnoreCase(in_dbtype)) {
                flag = DatabaseUtil.mongoDBDataNewTable(isCreateTable, out_dbname, out_tabname, writeDataCount, outMongoClient, fieldDataList);
            } else if (Const.DB_SEQUOIADB.equalsIgnoreCase(in_dbtype)) {
                flag = DatabaseUtil.sequoiaDBDataNewTable(isCreateTable, out_dbname, out_tabname, writeDataCount, out_url, fieldDataList, out_username, out_password);
            } else if (Const.DB_REDIS.equalsIgnoreCase(in_dbtype)) {
                flag = DatabaseUtil.redisDataNewTable(isCreateTable, out_dbname, out_tabname, writeDataCount, fieldDataList, out_srcip,out_srcport,out_password);
            } else {
                // TODO 脱敏结果写入新表
                flag = DatabaseUtil.insertData2NewTable(isCreateTable, in_dbname, in_tabname, in_url, in_username, in_password, in_dbtype,
                        fieldDataList, out_tabname, out_url, out_username, out_password, fieldNameStrList, out_dbname, out_dbtype, watermarkField);
            }
        } else {// 此处不能再写到excel，写到txt或者csv
            // 保存到文件
            // flag = CreateExcel.writedata(out_path, out_tabname, in_tabname,
            // fieldDataList);
            try {
                String line = System.getProperty("line.separator");// 平台换行!
                if (!out_path.endsWith(File.separator)) {
                    out_path = out_path + File.separator;
                }

                if (Const.DB_TASK_OUTPUTTYPE_EXCEL.equals(out_type)) {
                    List<List<?>> rowList = new ArrayList<>();
                    //转换数据
                    for (Map<String, Object> m : fieldDataList) {
                        List<Object> rowDataList = new ArrayList<>();
                        for (String k : m.keySet()) {
                            rowDataList.add(m.get(k));
                        }
                        rowList.add(rowDataList);
                    }

                    if (fieldDataList.size() > 0) {
                        if (fieldDataList.size() < endLine) {
                            endLine = startLine + fieldDataList.size();
                        }
                        String path = out_path + taskname + File.separator + out_tabname + "(" + startLine + "-" + endLine + ")" + ".xlsx";
                        BigExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getBigWriter(path);
                        writer.write(rowList);
                        writer.close();
                        flag = true;
                    }
                } else {
                    if (fieldDataList.size() > 0) {
                        if (fieldDataList.size() < endLine) {
                            endLine = startLine + fieldDataList.size();
                        }
                        String path = out_path + taskname + File.separator + out_tabname + "(" + startLine + "-" + endLine + ")";
                        if (Const.DB_TASK_OUTPUTTYPE_TXT.equals(out_type)) {
                            path = path + ".txt";
                            out_tabname = out_tabname + "(" + startLine + "-" + endLine + ")" +".txt";
                        } else {
                            path = path + ".csv";
                            out_tabname = out_tabname + "(" + startLine + "-" + endLine + ")" +".csv";
                        }
                        File file = new File(path);
                        // 如果没有文件就创建
                        if (!file.isFile()) {
                            // 获取父目录
                            File fileParent = file.getParentFile();
                            // 判断是否存在
                            if (!fileParent.exists()) {
                                // 创建父目录文件
                                fileParent.mkdirs();
                            }
                            if (!file.exists()) {
                                file.createNewFile();
                            }
                        }
                        FileWriter write = new FileWriter(file, true);
                        // 将数据写入文件中
                        for (Map<String, Object> map : fieldDataList) {
                            write.write(StringUtils.join(map.values(), ",") + line);
                        }
                        write.close();

                        //库表到文件 推送到SFTP\FTP
                        String outputPath = dbBatchTaskConfig.getOutputdirectory();
                        Integer outputdatasourceid = dbBatchTaskConfig.getOutputdatasourceid();
                        if (outputdatasourceid != null){
                            DatasourceDto outPutDatasource = dataSourceService.findById(Long.valueOf(outputdatasourceid));
                            String outPutDataSourceType = outPutDatasource.getType();
                            if (Const.FILE_SFTP.equals(outPutDataSourceType) || Const.FILE_FTP.equals(outPutDataSourceType)) {
                                String[] hostNameAndPort = outPutDatasource.getSrcurl().split(":|：");
                                String outPutDatasourceIp = hostNameAndPort[0];//数据源ip
                                int outPutDatasourcePort = hostNameAndPort.length > 1 ? Integer.valueOf(hostNameAndPort[1]) : Const.DEFAULT_PORT;//数据源端口
                                String outPutDatasourceAccount = outPutDatasource.getUsername();
                                String outPutDatasourcePassword = outPutDatasource.getPassword();//数据源密码
                                if (com.wzsec.utils.StringUtils.isNotEmpty(outPutDatasourcePassword)) {
                                    try {
                                        String decrypt = AES.decrypt(outPutDatasourcePassword, Const.AES_SECRET_KEY);
                                        outPutDatasourcePassword = decrypt;
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                                String uploadingPath = outputPath + Const.SEPARATOR_LINUX + dbBatchTaskConfig.getTaskname();
                                if (Const.FILE_SFTP.equals(outPutDataSourceType)) {
                                    //sftp
                                    SFTPUtil.uploadingFile(outPutDatasourceIp, outPutDatasourcePort, outPutDatasourceAccount, outPutDatasourcePassword, path, uploadingPath);
                                } else if (Const.FILE_FTP.equals(outPutDataSourceType)) {
                                    //ftp
                                    FTPUtil.uploadingFile(outPutDatasourceIp, outPutDatasourcePort, outPutDatasourceAccount, outPutDatasourcePassword, path, uploadingPath);
                                }
                            }
                        }
                    }
                    flag = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
                flag = false;
            }
        }
    }

    private void initParam() {
        DbBatchTaskConfigDto dbBatchTaskConfigById = dbBatchTaskConfigService.findById(taskid);
        dbBatchTaskConfig = dbBatchTaskConfigMapper.toEntity(dbBatchTaskConfigById);

        taskname = dbBatchTaskConfig.getTaskname();
        out_type = dbBatchTaskConfig.getOutputtype();
        out_path = dbBatchTaskConfig.getOutputdirectory();
        maskWay = dbBatchTaskConfig.getMaskway();

        in_tabname = batchTaskTabConfig.getTabename();
        in_limitingcondition = batchTaskTabConfig.getConlimit();

        Integer out_dataSourceId = dbBatchTaskConfig.getOutputdatasourceid();
        // 输入数据源信息
        Integer in_dataSourceId = dbBatchTaskConfig.getInputdatasourceid();
        DatasourceDto in_datasourceDto = dataSourceService.findById(Long.valueOf(in_dataSourceId));
        in_dbtype = in_datasourceDto.getType();
        in_dbname = in_datasourceDto.getDbname();
        in_drivername = in_datasourceDto.getDriverprogram();
        in_url = in_datasourceDto.getSrcurl();
        in_username = in_datasourceDto.getUsername();
        in_password = in_datasourceDto.getPassword();
        in_srcip = in_datasourceDto.getSrcip();
        in_srcport = in_datasourceDto.getSrcport();
        //AES解密
        if (StringUtils.isNotEmpty(in_password)) {
            in_password = AES.decrypt(in_password, Const.AES_SECRET_KEY);
        }

        MaskStrategyTableDto maskStrategyTableDto = maskStrategyTableService.findById(Integer.valueOf(strategyid));


        List<Map<String, Object>> list = null;
        if (Const.STRATEGY_TYPE_SINGLETABLE.equals(maskStrategyTableDto.getStrategytype())) {
            list = maskStrategyFieldService.getMaskAlgoInfoByStrategyId(strategyid);

            for (Map<String, Object> maskStrategyField : list) {
                String fieldename = (String) maskStrategyField.get("fieldename");
                String abnormalHandle = maskStrategyField.get("abnormalhandle") != null ?
                        maskStrategyField.get("abnormalhandle").toString() : "";
                abnormalHandleMap.put(fieldename, abnormalHandle);
            }

        } else {
            List<MetaField> metaFieldsList = metaFieldService.findInfoFieldInfoByTabNameSourceId(in_tabname, Long.valueOf(in_dataSourceId));
            List<Map<String, Object>> algoInfo = maskStrategyTableService.getAllStrategyAlgoInfoByStrategyId(strategyid);
            list = new ArrayList<>();
            for (MetaField metaField : metaFieldsList) {
                String fieldName = metaField.getFieldname();
                String algoName = null;
                String key = null;
                String param = null;
                String isextract = null;
                String extractconfig = null;
                String extractalgid = null;
                String abnormalHandle = null;
                String fieldSCname = metaField.getFieldscname();
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(fieldSCname)) {
                    for (Map<String, Object> map : algoInfo) {
                        String dataName = map.get("dataname").toString();
                        if (dataName.equals(fieldSCname)) {
                            algoName = map.get("algenglishname") == null ? null : map.get("algenglishname").toString();
                            key = map.get("secretkey") == null ? null : map.get("secretkey").toString();
                            param = map.get("param") == null ? null : map.get("param").toString();
                            isextract = map.get("isextract") == null ? null : map.get("isextract").toString();
                            extractconfig = map.get("extractconfig") == null ? null : map.get("extractconfig").toString();
                            extractalgid = map.get("extractalgid") == null ? null : map.get("extractalgid").toString();
                            abnormalHandle = map.get("abnormalhandle") == null ? null : map.get("abnormalhandle").toString();
                            abnormalHandleMap.put(fieldName, abnormalHandle);
                            break;
                        }
                    }
                }
                Map<String, Object> map = new HashMap<>();
                map.put("fieldename", fieldName);
                map.put("algenglishname", algoName);
                map.put("secretkey", key);
                map.put("param", param);
                map.put("isextract", isextract);
                map.put("extractconfig", extractconfig);
                map.put("extractalgid", extractalgid);
                list.add(map);
            }
        }

        maskAlgoInfoMap = DBMaskUtils.getMaskAlgoInfo(list, algorithmMap);
        in_fieldNameStr = "";
        for (String fieldName : maskAlgoInfoMap.keySet()) {
            /*if (Const.MYSQL_SIGN.equalsIgnoreCase(in_dbtype)) {
                in_fieldNameStr += "`" + fieldName + "`,";
            } else {
                in_fieldNameStr += fieldName + ",";
            }*/
            in_fieldNameStr += fieldName + ",";
            String algo = maskAlgoInfoMap.get(fieldName) == null?"":maskAlgoInfoMap.get(fieldName).toString();
            if (!"".equals(algo)) {
                fieldNameStrList.add(fieldName);
            }
        }
        if (in_fieldNameStr.endsWith(",")) { // 如果以","结尾，去除","
            in_fieldNameStr = in_fieldNameStr.substring(0, in_fieldNameStr.lastIndexOf(","));
        }

        DatasourceDto out_datasourceDto = dataSourceService.findById(Long.valueOf(out_dataSourceId));

        // 输出数据源信息
        if (Const.SOURCE_DB.equals(out_type)) {
            out_dbtype = out_datasourceDto.getType();
            out_dbname = out_datasourceDto.getDbname();
            out_drivername = out_datasourceDto.getDriverprogram();
            out_url = out_datasourceDto.getSrcurl();
            //if (!out_url.contains("?") && !out_url.contains("characterEncoding")) {
            //    out_url += "?useCursorFetch=true&characterEncoding=utf8";
            //}
            out_username = out_datasourceDto.getUsername();
            out_password = out_datasourceDto.getPassword();
            out_srcip = out_datasourceDto.getSrcip();
            out_srcport = out_datasourceDto.getSrcport();
            //AES解密
            if (StringUtils.isNotEmpty(out_password)) {
                out_password = AES.decrypt(out_password, Const.AES_SECRET_KEY);
            }
        }

        if (Const.BATCH_MASK_TABLENAME_MASKTIMESTAMP.equals(dbBatchTaskConfig.getMasktablename())) {
            // 文件名或表名
            out_tabname = in_tabname + "_MASK_" + DateUtil.formatDate(new Date());
            if (Const.DB_DM.equalsIgnoreCase(out_dbtype)) {
                out_tabname = in_tabname + "_MASK_" + DateUtil.formatDate(new Date());
            } else if (Const.DB_ORACLE.equalsIgnoreCase(out_dbtype)) {
                //TODO oracle 12.2版本之前，表名长度有限制，大约30字符左右，截取一下
                out_tabname = in_tabname + "_MASK_" + DateUtil.formatDate(new Date());
                if (out_tabname.length() >= 30) {
                    String substring = in_tabname.substring(0, 9);
                    out_tabname = substring + "_MASK_" + DateUtil.formatDate(new Date());
                }
            }
        } else {
            // 文件名或表名
            out_tabname = in_tabname;
        }

        //初始化所有算法
        algorithmMap = algorithmService.getAlgorithmByEName();

        //如果没有输出源ip，就从url里截取
        out_ip = out_datasourceDto.getSrcip();
        if (StringUtils.isEmpty(out_path)) {
            out_path = out_datasourceDto.getDbname();
        }
        if (out_ip == null) {
            setoutputIpAddress();
        }

        //是否设置水印
        isWatermark = Const.ISWATERMARK_YES.equals(batchTaskTabConfig.getIswatermark());
        if (isWatermark) {
            waterMarkInfo = WaterMarkAlgFactory.getWaterMarkInfo(batchTaskTabConfig.getDataprovider(), batchTaskTabConfig.getDatause(), true);

            //输出类型是库表，且抽取列不为空，将抽取列作为水印加注列
            if (Const.DB_TASK_OUTTYPE_DB.equals(out_type) && org.apache.commons.lang3.StringUtils.isNotEmpty(batchTaskTabConfig.getWatermarkcol())) {
                watermarkField = batchTaskTabConfig.getWatermarkcol();
            } else {
                //设置水印，默认在最后一位添加；如若最后一位使用了可逆算法，改为倒数第二位，以此类推
                for (int i = list.size() - 1; i >= 0; i--) {
                    //脱敏算法
                    String algoName = list.get(i).get("algenglishname") == null ? null : list.get(i).get("algenglishname").toString();
                    String fieldName = list.get(i).get("fieldename") == null ? null : list.get(i).get("fieldename").toString();
                    Boolean isWatermarkFlag;
                    if (com.wzsec.utils.StringUtils.isEmpty(algoName)) {
                        isWatermarkFlag = true;
                    } else {
                        com.wzsec.modules.mask.domain.Algorithm algorithm = algorithmService.findByAlgorithmName(algoName);
                        isWatermarkFlag = Const.ALGORITHM_IS_REVERSIBLE_NO.equals(algorithm.getIsreversible());
                    }

                    //如果这一列没有选择脱敏算法，或所选的算法不可逆，选择这一列数据写入水印
                    if (isWatermarkFlag) {
                        watermarkField = fieldName;
                        break;
                    }
                }
            }
        }

        //是否增量脱敏
        if (Const.MASK_WARY_INCREMENT.equals(maskWay)) {
            DbBatchTaskResult lastResult = dbBatchTaskResultService.getLastResult(in_tabname, out_tabname, in_dataSourceId, out_dataSourceId);
            lastMaskLines = lastResult != null ? lastResult.getLastmasklines() + lastResult.getMasklines() : 0;
        }

//        if (dbBatchTaskConfigById.get)
//        DBBatchTaskResult lastResult = dBBatchTaskResultService.getLastResult(
//                new DBBatchTaskResult(this.in_tabname, dBBatchTaskConfig.getDbname(), out_tabame, in_dataSourceId, out_dataSourceId));
//        lastMaskLines = lastResult!=null?lastResult.getLastlines()+lastResult.getThislines():0L;
    }

    private void setoutputIpAddress() {
        if (Const.DB_TASK_OUTPUTTYPE_DB.equals(out_type)) {
            if (in_dbtype.toLowerCase().contains("mysql") || out_url.contains("MYSQL") || in_dbtype.equalsIgnoreCase(Const.DB_STARROCKS) || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
                String temp1 = out_url.substring(out_url.indexOf("3306/") + 5);
                // outputPath = temp1.substring(0,temp.indexOf("?"));
                if (temp1.contains("?")) {
                    out_path = temp1.substring(0, temp1.indexOf("?"));
                } else {
                    out_path = temp1;
                }
            }else if (in_dbtype.toLowerCase().contains("teradata") || out_url.contains(Const.DB_TERADATA)) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
                String temp1 = out_url.substring(out_url.indexOf("3306/") + 5);
                // outputPath = temp1.substring(0,temp.indexOf("?"));
                if (temp1.contains("?")) {
                    out_path = temp1.substring(0, temp1.indexOf("?"));
                } else {
                    out_path = temp1;
                }
            }else if (in_dbtype.toLowerCase().contains("sinodb") || out_url.contains(Const.DB_SINODB)) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
                String temp1 = out_url.substring(out_url.indexOf("3306/") + 5);
                // outputPath = temp1.substring(0,temp.indexOf("?"));
                if (temp1.contains("?")) {
                    out_path = temp1.substring(0, temp1.indexOf("?"));
                } else {
                    out_path = temp1;
                }
            } else if (in_dbtype.toLowerCase().contains("oracle")) {
                String temp = out_url.substring(out_url.indexOf("@") + 1);
                out_ip = temp.substring(0, temp.indexOf(":"));
                out_path = out_url.substring(out_url.indexOf(":1521:") + 4, out_url.lastIndexOf(":") - 1);
            } else if (in_dbtype.toLowerCase().equals("gbase")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("db2")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("hive2")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {

            } else if (in_dbtype.toLowerCase().equals("gbase8s")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().equals("informix")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().equals("mariadb")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            }
        } else {
            out_ip = "127.0.0.1";
        }
    }

    /**
     * 添加数据库脱敏任务结果
     */
    private void initResult() {
        DbBatchTaskResult dbBatchTaskResult = new DbBatchTaskResult();
        dbBatchTaskResult.setTaskname(taskname);
        dbBatchTaskResult.setTabname(in_tabname);
        dbBatchTaskResult.setDbname(in_dbname);
        dbBatchTaskResult.setIpaddress(in_url);// 数据源主机或Ip地址
        dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTING);
        dbBatchTaskResult.setOutputtype(out_type);
        dbBatchTaskResult.setUsername(submituser);
        dbBatchTaskResult.setJobstarttime(startTime);
        dbBatchTaskResult.setJobstarttime(startTime);
        DbBatchTaskResultDto dbBatchTaskResultDto = dbBatchTaskResultService.create(dbBatchTaskResult);
        this.dbBatchTaskResult = dbBatchTaskResultMapper.toEntity(dbBatchTaskResultDto);
        resultid = dbBatchTaskResult.getId();
    }


    /**
     * 分页获取结果集
     *
     * @param con             连接
     * @param stmt            发送
     * @param initialPosition 初始位置
     * @param recordCount     记录数
     * @return {@link ResultSet}
     */
    private ResultSet getResultSet(Connection con, Statement stmt, long initialPosition, long recordCount) {

        ResultSet resultSet = null;
        try {
            String fieldNameOne = "";
            String in_fieldNameStr = this.in_fieldNameStr;
            if (in_dbtype.equalsIgnoreCase(Const.DB_MYSQL) || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB) || in_dbtype.equalsIgnoreCase(Const.DB_TIDB) || in_dbtype.equalsIgnoreCase(Const.DB_STARROCKS)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = this.in_fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("`" + field + "`,");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            } else if (in_dbtype.equalsIgnoreCase(Const.DB_TERADATA)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = this.in_fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("\"" + field + "\",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
                fieldNameOne = split[0];
            } else if (in_dbtype.equalsIgnoreCase(Const.DB_SINODB)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = this.in_fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("`" + field + "`,");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            }else if (in_dbtype.equalsIgnoreCase(Const.DB_PSOTGRESQL) ||
                    in_dbtype.equalsIgnoreCase(Const.DB_ORACLE) ||
                    in_dbtype.equalsIgnoreCase(Const.DB_HIGHGO) ||
                    in_dbtype.equalsIgnoreCase(Const.DB_KINGBASE)
            ) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = this.in_fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("\"" + field + "\",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            }else if (in_dbtype.equalsIgnoreCase(Const.DB_OSCAR)){
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = this.in_fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append(field + ",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            }


            // TODO oracle
            if (in_dbtype.toLowerCase().contains("oracle")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //log.info("连接oracle数据库成功");
                // 查抽取字段数据(分页通过子查询实现)
                String sql = null;
                if (org.apache.commons.lang3.StringUtils.isNotBlank(in_limitingcondition) && in_limitingcondition.contains("WHERE")) { //仅支持WHERE,无LIMIT
                    String template = "select {} from (select rownum r,t.* from (select * from {}.\"{}\" {}) t where rownum<={}) where r>{} ";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, recordCount + initialPosition, initialPosition);
                } else {
                    String template = "select {} from (select rownum r,t.* from {}.\"{}\" t where rownum<={}) where r>{} ";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, recordCount + initialPosition, initialPosition);
                }
                Console.log("oracle分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO MySQL
            } else if (in_dbtype.toLowerCase().contains("mysql") || in_dbtype.equalsIgnoreCase(Const.DB_STARROCKS) || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {// 2021年2月24日18:24:46已测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                //String template = "select {} from {}.{} limit {},{}";
                String template = "select {} from `{}`.`{}`";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    template += " " + in_limitingcondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname);
                } else {
                    template += " limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.equalsIgnoreCase(Const.DB_TIDB)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                //String template = "select {} from {}.{} limit {},{}";
                String template = "select {} from `{}`.`{}`";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    template += " " + in_limitingcondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname);
                } else {
                    template += " limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("teradata")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                String template = "select "+in_fieldNameStr+"from"+in_tabname;
                if (StringUtils.isNotBlank(in_limitingcondition)){
                    template += " " + in_limitingcondition;
                }
                String sql = "";
                if (initialPosition >= 0 && recordCount > 0){
                    //分页
                    sql = "SELECT "+ in_fieldNameStr + " from (" +
                            "select "+ in_fieldNameStr + ",ROW_NUMBER() OVER (ORDER BY "+fieldNameOne+") as rn  FROM "+in_dbname+"."+in_tabname+" ) t " +
                            "WHERE rn BETWEEN "+initialPosition+" AND "+recordCount;
                    if (StringUtils.isNotBlank(in_limitingcondition)){
                        sql += " " + in_limitingcondition;
                    }
                }else {
                    sql = template;
                    if (StringUtils.isNotBlank(in_limitingcondition)){
                        sql += " " + in_limitingcondition;
                    }
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("sinodb")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                //String template = "select {} from {}.{} limit {},{}";
                String template = "select {} from {}";
                if (StringUtils.isNotEmpty(in_limitingcondition)) {
                    template += " " + in_limitingcondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, this.in_fieldNameStr, in_tabname);
                } else {
                    template += " limit {}";
                    sql = StrUtil.format(template, this.in_fieldNameStr, in_tabname, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("kingbase")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接kingbase数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from \"" + in_tabname+"\"";
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.equalsIgnoreCase(Const.DB_OSCAR)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接oscar数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from \"" + in_tabname+"\"";
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //log.info("连接dm数据库成功");
                String template = "select {} from {}.{} limit {},{}";
                String sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                //Console.log("dm分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO informix
            } else if (in_dbtype.toLowerCase().equals("informix")) {
                Class.forName(in_drivername);
                //jdbc:informix-sqli://192.168.1.137:9003/mask:informixserver=tramsserver;
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接informix数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = in_fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String template = "select skip {} first {} {} from {}";
                String sql = StrUtil.format(template, initialPosition, recordCount, in_fieldNameStr, in_tabname);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO MariaDB
            } else if (in_dbtype.toLowerCase().contains("mariadb")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String template = "select {} from {}.{} limit {},{}";
                String sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                Console.log("MariaDB分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().equals("gbase")) {// 2021年2月24日18:24:46已测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(
                        in_url + "?useCursorFetch=true&defaultFetchSize=" + pageSize, in_username, in_password);
                log.info("连接gbase数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                Console.log("gbase分页查询语句: {}", sql);
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {// 2021年2月24日18:24:46未测试
//				sqlserver表有两种权限，分为dbo和guest权限,dbo权限的下的表可以用表名直接查询,guest权限的下的表必须使用用户.表名进行查询,
//				dbo
//				database owner
//				数据库的创建者,创建该对象的用户
//				DBO是每个数据库的默认用户，具有所有者权限，即DbOwner ，通过用DBO作为所有者来定义对象，能够使数据库中的任何用户引用而不必提供所有者名称。
//				比如：你以User1登录进去并建表Table，而未指定DBO， 当用户User2登进去想访问Table时就得知道这个Table是你User1建立的，要写上User1.Table，如果他不知道是你建的，则访问会有问题。
//				如果你建表时把所有者指给了Dbo，则别的用户进来时写上Dbo.Table就行了，不必知道User1。
//				不光表是如此，视图等等数据库对象建立时也要如此才算是好。
//				guest
//				顾客   能够访问数据库中对象的数据,要求dbo分配权限给guest,一般给他查看的权限select
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接sqlserver数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname + " order by uid offset " + initialPosition + " rows fetch next " + recordCount + " rows only";
                Console.log("sqlserver分页查询语句: {}", sql);

                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("db2")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接db2数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("hive2")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                Connection conn = null;
                HiveStatement hstmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", in_username, in_password, in_url);
                    hstmt = HiveUtil.getStmt(conn);
                    if (null != in_dbname && !in_dbname.equals("")) {
                        String strUseBase = "use " + in_dbname;
                        hstmt.execute(strUseBase);
                    }
                    String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                    if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                        sql += " " + in_limitingcondition;
                    }
                    resultSet = hstmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(hstmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接postgresql数据库成功");
                String template = "select {} from \"{}\"";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    template += " " + in_limitingcondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, in_tabname);
                } else {
                    template += " OFFSET {} LIMIT {}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains(Const.DB_GAUSS)) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接Gauss数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {// 2021年2月24日18:24:46不支持

            } else if (in_dbtype.toLowerCase().equals("gbase8s")) {
                Class.forName(in_drivername);
                // jdbc:gbasedbt-sqli://192.168.1.71:9088/jdbcdb:GBASEDBTSERVER=gbase01;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=30
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase8s数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = in_fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("bytehouse")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);

            } else if (in_dbtype.toLowerCase().contains("clickhouse")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);

            } else if (in_dbtype.toLowerCase().contains("highgo")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接highgo数据库成功");
                String template = "select {} from \"{}\"";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    template += " " + in_limitingcondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, in_tabname);
                } else {
                    template += " OFFSET {} LIMIT {}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("goldendb")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                // 查抽取字段数据
                String sql = "";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);

            }
            resultSet.setFetchSize(pageSize.intValue());// oracle支持，mysql和gbase不支持(已通过改写url支持)。
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultSet;
    }

    private Long getDataCount() {
        Connection con = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        long count = 0;
        String sql = "";
        try {
            String in_dbname = this.in_dbname;
            String in_tabname = this.in_tabname;
            if (in_dbtype.equalsIgnoreCase(Const.DB_MYSQL) || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB) || in_dbtype.equalsIgnoreCase(Const.DB_STARROCKS)) {
                in_dbname = "`" + in_dbname + "`";
                in_tabname = "`" + in_tabname + "`";
            }
            if (in_dbtype.equalsIgnoreCase(Const.DB_PSOTGRESQL)) {
                in_dbname = "";//PG查询时不要添加库名，需要 库名.模式名.表名 才能查询生效，目前暂无模式名配置
                in_tabname = "\"" + in_tabname + "\"";
            }

            //如果有携带条件，先条件查询出数据数量，再count统计
            //直接select count(*) from a limit 10 ，会导致limit无效
            if (StringUtils.isNotBlank(in_limitingcondition)) {
                sql = "select count(*) from (select * from " + in_dbname + "." + in_tabname + " " + in_limitingcondition + ") a";
            } else if (in_dbtype.equalsIgnoreCase(Const.DB_KINGBASE) || in_dbtype.equalsIgnoreCase(Const.DB_OSCAR)){
                sql = "select count(*) from " + in_tabname;
            } else {
                sql = "select count(*) from " + in_dbname + "." + in_tabname;
            }
            if (in_dbtype.toLowerCase().contains("oracle")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接oracle数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("mysql") || in_dbtype.equalsIgnoreCase(Const.DB_STARROCKS) || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接mysql数据库成功");
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.equalsIgnoreCase(Const.DB_TIDB)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接TiDB数据库成功");
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("teradata")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接teradata数据库成功");
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("sinodb")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接sinodb数据库成功");
                sql = "select count(*) from " + in_tabname;
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("mariadb")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接MariaDB数据库成功");
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().equals("gbase")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接sqlserver数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("db2")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接db2数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("hive2")) {
                Class.forName(in_drivername);
                Connection conn = null;
                HiveStatement stmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", in_username, in_password, in_url);
                    stmt = HiveUtil.getStmt(conn);
                    if (null != in_dbname && !in_dbname.equals("")) {
                        String strUseBase = "use " + in_dbname;
                        stmt.execute(strUseBase);
                    }
                    resultSet = stmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(stmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接postgresql数据库成功");
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    sql = "select count(*) from (select * from " + in_tabname + " " + in_limitingcondition + ") a";
                } else {
                    sql = "select count(*) from " + in_tabname;
                }
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("kingbase")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接kingbase数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.equalsIgnoreCase(Const.DB_OSCAR)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接Oscar数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains(Const.DB_GAUSS)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接Gauss数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接dm数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {
                MongoClient mongoClient = MongoDBUtil.getConnect(in_url, in_username, in_password, in_dbname);
                count = MongoDBUtil.countDocuments(in_dbname, in_tabname, mongoClient);
                mongoClient.close();
            } else if (in_dbtype.equals(Const.DB_SEQUOIADB)) {
                Sequoiadb sdb = SequoiaDBUtil.queryConnect(in_url, in_username, in_password);
                CollectionSpace cs = sdb.getCollectionSpace(in_dbname);
                List<String> collectionNames = cs.getCollectionNames();
                String collName = collectionNames.get(0);
                DBCollection cl = cs.getCollection(tableNameStr(collName));
                DBCursor cursor = cl.query();
                int row = 0;
                while (cursor.hasNext()) {
                    BSONObject next = cursor.getNext();
                    row++;
                    System.out.println(next);
                }
                sdb.disconnect();
                count = row;
            } else if (in_dbtype.equals(Const.DB_REDIS)) {
                Jedis jedis = RedisUtil.queryRedisConnect(in_srcip, in_srcport, in_password);
                jedis.select(Integer.valueOf(in_dbname));
                String instaticmask = jedis.get(in_tabname);
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(instaticmask);
                int row = 0;
                for (JsonNode arrayNode : jsonNode) {
                    row++;
                }
                count = row;
            } else if (in_dbtype.toLowerCase().equals("gbase8s")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase8s数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().equals("informix")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接informix数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().equals("bytehouse")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接bytehouse数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().equals("clickhouse")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接Clickhouse数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("highgo")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接highgo数据库成功");
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    sql = "select count(*) from (select * from " + in_tabname + " " + in_limitingcondition + ") a";
                } else {
                    sql = "select count(*) from " + in_tabname;
                }
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().equals("goldendb")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接GoldenDB数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            }
            if (ObjectUtil.isNotEmpty(resultSet)) {
                while (resultSet.next()) {
                    count = resultSet.getLong(1);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    /**
     * 创建没有脱敏数据的表
     *
     * @throws Exception
     * <AUTHOR>
     * @date 2022/5/30
     */
    private void createNoneDataTable() {
        String sql = "select count(*) from " + out_tabname;
        String out_tabname = this.out_dbname + "." + this.out_tabname;
        if (out_dbtype.equalsIgnoreCase(Const.DB_MYSQL) || out_dbtype.equalsIgnoreCase(Const.DB_UNIDB) || out_dbtype.equalsIgnoreCase(Const.DB_STARROCKS)) {
            out_tabname = "`" + this.out_dbname + "`.`" + this.out_tabname + "`";
        }
        if (Const.DB_ORACLE.equalsIgnoreCase(out_dbtype)) {
            out_tabname = "\"" + this.out_dbname + "\".\"" + this.out_tabname + "\"";
        }
        if (Const.DB_SEQUOIADB.equalsIgnoreCase(out_dbtype)) {
            //TODO 巨杉数据库
            return;
        }
        if (Const.DB_REDIS.equalsIgnoreCase(out_dbtype)) {
            //TODO redis
            return;
        }
        //如若该表没有被创建，创建该表
        if (null == DatabaseUtil.prepareStatement(sql, null, in_drivername, out_url, out_username, out_password)) {
            Map<String, Object> fieldDataMap = new LinkedHashMap<String, Object>();
            String fieldNameStr = in_fieldNameStr;
            String[] fieldNameStrs = fieldNameStr.split(",");
            for (String fieldName : fieldNameStrs) {
                if (fieldName.startsWith("`")) {
                    fieldName = fieldName.substring(1, fieldName.length() - 1);
                }
                fieldDataMap.put(fieldName, "");
            }
            // 行数据写出
            //fieldDataMap = getMaskRowData(fieldDataMap);
            List<Map<String, Object>> fieldDataList = new LinkedList<>();
            fieldDataList.add(fieldDataMap);
            boolean tableSql = DatabaseUtil.createTableSql(
                    in_url, in_tabname,
                    in_dbtype, in_username, in_password,
                    fieldDataList, this.out_tabname, out_dbname,
                    out_url, out_username, out_password,
                    fieldNameStrList);
            if (!tableSql) {
                new Exception("表名为" + out_tabname + "的表暂无数据，创建空表失败");
            }
            log.info("表名为" + out_tabname + "的表暂无数据，已创建空表");
        } else {
            log.info("目标源已有名为：" + out_tabname + "的表，无需创建");
        }
    }
}
