package com.wzsec.dotask.mask.service.impl;

import com.wzsec.dotask.mask.service.DoHdfsTaskService;
import com.wzsec.dotask.mask.service.excute.hdfs.DataMaskingOnHadoop;
import com.wzsec.modules.mask.domain.*;
import com.wzsec.modules.mask.repository.EngineServerRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import com.wzsec.modules.mask.service.dto.HadoopTaskConfigDto;
import com.wzsec.modules.mask.service.dto.HadoopTaskResultDto;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileMainDto;
import com.wzsec.modules.mask.service.mapper.HadoopTaskConfigMapper;
import com.wzsec.modules.mask.service.mapper.HadoopTaskResultMapper;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.utils.Const;
import com.wzsec.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
//import cn.ctyun.mask.utils.RunJobThread;


// 默认不使用缓存
//import org.springframework.cache.annotation.CacheConfig;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR>
 * @date 2020-11-16
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "fileTask")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoHdfsTaskServiceImpl implements DoHdfsTaskService {

    private final HadoopTaskConfigService hadoopTaskConfigService;

    private final HadoopTaskResultService hadoopTaskResultService;

    private final HadoopTaskConfigMapper hadoopTaskConfigMapper;

    private final HadoopTaskResultMapper hadoopTaskResultMapper;

    private final MaskStrategyFileMainService maskStrategyFileMainService;

    private final RuleService ruleService;

    private final MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService;

    private final AlgorithmService algorithmService;

    private final MaskTaskresultrecordsService maskTaskresultrecordsService;

    private final EngineServerRepository engineServerRepository;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    @Value("${spring.profiles.active}")
    private String active;

    public DoHdfsTaskServiceImpl(HadoopTaskConfigService hadoopTaskConfigService, HadoopTaskResultService hadoopTaskResultService
            ,HadoopTaskConfigMapper hadoopTaskConfigMapper,HadoopTaskResultMapper hadoopTaskResultMapper,MaskStrategyFileMainService maskStrategyFileMainService,
                                 RuleService ruleService,MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService,MaskTaskresultrecordsService maskTaskresultrecordsService,
                                 AlgorithmService algorithmService, EngineServerRepository engineServerRepository,SdkOperationrecordRepository sdkOperationrecordRepository,SdkApplyconfigRepository sdkApplyconfigRepository) {
        this.hadoopTaskConfigService = hadoopTaskConfigService;
        this.hadoopTaskResultService = hadoopTaskResultService;
        this.hadoopTaskConfigMapper = hadoopTaskConfigMapper;
        this.hadoopTaskResultMapper = hadoopTaskResultMapper;
        this.maskStrategyFileMainService = maskStrategyFileMainService;
        this.ruleService = ruleService;
        this.maskStrategyFileUnformatSubService = maskStrategyFileUnformatSubService;
        this.algorithmService = algorithmService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.engineServerRepository = engineServerRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }

    @Async//异步执行
    @Override
    public void execution(Integer taskconfigid, String submituser) {

        log.info("开始执行HDFS脱敏任务");
        // 加载白名单 非格式化文件脱敏会用到
        Const.nameWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.NAME_SIGN, Const.whiteList_SIGN);
        Const.addreWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.ADDRESS_SIGN, Const.whiteList_SIGN);
        Const.phoneWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.PHONE_SIGN, Const.whiteList_SIGN);

        boolean isSuccess = false;

        String taskname = "";
        String fileformat = "";
        String datasplit = "";
        String fieldPostionAlgoConfig = "";
        String datainputpath = "";
        String dataoutputpath = "";
        String queuename = "";
        String startTime = "";
        String endTime = "";
        String totalTime = "";
        String status = null;
        String taskstatus= null;
        String taskMessage = null;

        HadoopTaskConfig hadoopTaskConfig = null;
        HadoopTaskResult hadoopTaskResult = null;

        List<Map<String,Object>> ruleList = new ArrayList<>();

        HadoopTaskResultModel hadoopTaskResultModel = new HadoopTaskResultModel();

        String engine = hadoopTaskConfig.getSparefield2();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String eip = ipPort[0];
        String eport = ipPort[1];

        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(eip+":"+eport);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        try {

            //TODO 将所使用的引擎，任务负载数+1
            if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
                engineServerRepository.addKingbaseCountByIpPort(eip, eport);
            }else{
                engineServerRepository.addCountByIpPort(eip, eport);
            }

            startTime = DateUtil.getNowTime();

            HadoopTaskConfigDto hadoopTaskConfigDto = hadoopTaskConfigService.findById(taskconfigid);
            hadoopTaskConfig = hadoopTaskConfigMapper.toEntity(hadoopTaskConfigDto);
            // job作业提交插入信息
            HadoopTaskResult hadoopTaskResultDB = new HadoopTaskResult();
            hadoopTaskResultDB.setTaskname(hadoopTaskConfigDto.getTaskname());
            hadoopTaskResultDB.setUsername(submituser);
            hadoopTaskResultDB.setPlatform(Const.PLATFORM_HADOOP);
            hadoopTaskResultDB.setFileformat(hadoopTaskConfigDto.getFileformat());
            hadoopTaskResultDB.setDatasplit(hadoopTaskConfigDto.getDatasplit());
            hadoopTaskResultDB.setDatainputpath(hadoopTaskConfigDto.getDatainputdir());
            hadoopTaskResultDB.setDataoutputpath(hadoopTaskConfigDto.getDataoutputdir());
            hadoopTaskResultDB.setConfigargs(hadoopTaskConfigDto.getFieldpostionalgoconfig());
            hadoopTaskResultDB.setJobstarttime(startTime);
            hadoopTaskResultDB.setRemark(hadoopTaskConfigDto.getRemark());
            hadoopTaskResultDB.setJobstatus(Const.TASK_RESULT_EXECUTING);
            hadoopTaskResultDB.setBeforemaskdata("");
            hadoopTaskResultDB.setAftermaskdata("");
            hadoopTaskResultDB.setSparefield1(String.valueOf(hadoopTaskConfigDto.getStrategyid()));
            HadoopTaskResultDto hadoopTaskResultDto = hadoopTaskResultService.create(hadoopTaskResultDB);
            hadoopTaskResult = hadoopTaskResultMapper.toEntity(hadoopTaskResultDto);

            taskname = hadoopTaskResultDto.getTaskname();
            fileformat = hadoopTaskConfigDto.getFileformat();
            datasplit = hadoopTaskConfigDto.getDatasplit();
            fieldPostionAlgoConfig = hadoopTaskConfigDto.getFieldpostionalgoconfig();
            queuename = hadoopTaskConfigDto.getQueuename();
            datainputpath =  hadoopTaskConfigDto.getDatainputdir();
            dataoutputpath =  hadoopTaskConfigDto.getDataoutputdir();
            // 根据策略id查询HDFS文件脱敏策略
            MaskStrategyFileMainDto maskStrategyFileMainDto = maskStrategyFileMainService.findById(hadoopTaskConfigDto.getStrategyid());
            // job表中用户名、任务号、文件格式、分隔符、脱敏参数、数据输入路径、数据输出路径、队列
            String[] maskingConfigArgs = new String[9];

            maskingConfigArgs[0] = submituser;
            maskingConfigArgs[1] = taskname;
            maskingConfigArgs[2] = fileformat; // 文件格式
            maskingConfigArgs[3] = datasplit; // 数据分隔符
            maskingConfigArgs[4] = fieldPostionAlgoConfig; // 脱敏配置参数
            // 示例:2|MD5$9876543210123456;5|IDMASK$11,14
            // maskingConfigArgs[2] = "6|MD5;7|ADDRESS;17|MD5";
            // //脱敏配置参数
            // 示例:2|MD5$9876543210123456;5|IDMASK$11,14
            maskingConfigArgs[5] = datainputpath; // 数据输入路径
            maskingConfigArgs[6] = dataoutputpath; // 数据输出路径
            maskingConfigArgs[7] = queuename; // 队列
            maskingConfigArgs[8] = maskStrategyFileMainDto.getStrategytype(); // 策略类型(0:格式化文件,1非格式化文件)
            if ("1".equals(maskStrategyFileMainDto.getStrategytype())) {
                // 非格式化文件脱敏策略
                // 根据策略ID查询非格式化文件脱敏策略
                List<MaskStrategyFileUnformatSub> maskStrategySubList= maskStrategyFileUnformatSubService.findByStrategyid(maskStrategyFileMainDto.getId());

                for (MaskStrategyFileUnformatSub maskStrategyFileUnformatSub : maskStrategySubList) {
                    Map<String,Object> ruleMap = new HashMap<>();
                    RuleDto ruleDto = ruleService.findById(maskStrategyFileUnformatSub.getSenruleid().longValue());
                    AlgorithmDto algorithmDto = algorithmService.findById(maskStrategyFileUnformatSub.getAlgorithmid());
                    ruleMap.put("senRule",ruleDto);
                    ruleMap.put("algorithm",algorithmDto);
                    ruleMap.put("param",maskStrategyFileUnformatSub.getParam());
                    ruleMap.put("secretkey",maskStrategyFileUnformatSub.getSecretkey());
                    ruleList.add(ruleMap);
                }
            }

            isSuccess= DataMaskingOnHadoop.doDataMasking(maskingConfigArgs,hadoopTaskResultModel,ruleList);
//            isSuccess = doDataMasking(maskingConfigArgs);
            log.info(Const.STRMASKLOGFLAG + "Hadoop脱敏执行是否成功:" + isSuccess);

            if (!isSuccess) {
                // 结果状态
               status = Const.TASK_RESULT_EXECUTE_FAIL;
                // 任务状态
                taskstatus = Const.HDFS_TASK_EXECUTESTATE_EXECUTE_FAIL;
                taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE;

                sdkOperationrecord.setObjectname(hadoopTaskResult.getTaskname());
                sdkOperationrecord.setOperation("HDFS脱敏任务执行失败");
            }else{
                // 结果状态
                status = Const.TASK_RESULT_EXECUTE_SUCCESS;
                // 任务状态
                taskstatus = Const.HDFS_TASK_EXECUTESTATE_EXECUTE_SUCCESS;
                taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE;

                sdkOperationrecord.setObjectname(hadoopTaskResult.getTaskname());
                sdkOperationrecord.setOperation("HDFS脱敏任务执行成功");
            }


        } catch (Exception e) {
            // 结果状态
            status = Const.TASK_RESULT_EXECUTE_FAIL;
            // 任务状态
            taskstatus = Const.HDFS_TASK_EXECUTESTATE_EXECUTE_FAIL;

            sdkOperationrecord.setObjectname(hadoopTaskResult.getTaskname());
            sdkOperationrecord.setOperation("HDFS脱敏任务执行失败");

            e.printStackTrace();
        }finally {
            endTime = DateUtil.getNowTime();
            // 执行总时间
            totalTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(startTime, endTime));

            if(null != hadoopTaskResult){
                hadoopTaskResult.setJobendtime(endTime);
                hadoopTaskResult.setJobtotaltime(totalTime);
                hadoopTaskResult.setJobstatus(status);
            /*    hadoopTaskResult.setDatainputpath(hadoopTaskResultModel.getInputpath());
                hadoopTaskResult.setDataoutputpath(hadoopTaskResultModel.getOutputPath());
                hadoopTaskResult.setDatarows(String.valueOf(hadoopTaskResultModel.getCount()));*/
                // 更新结果
                hadoopTaskResultService.update(hadoopTaskResult);
                MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
                maskTaskresultrecords.setTaskname(hadoopTaskResult.getTaskname());
                maskTaskresultrecords.setTasktype(Const.MASK_TASK_HDFS);
                maskTaskresultrecords.setTaskstatus(taskMessage);
                maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
                maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
                maskTaskresultrecordsService.create(maskTaskresultrecords);

                //插入SDK操作记录
                sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
                sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
                sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
                sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
                sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_HDFS);
                sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
                sdkOperationrecordRepository.save(sdkOperationrecord);
            }

            if(null != hadoopTaskConfig){
                // 更新任务表
                hadoopTaskConfig.setStatus(taskstatus);
                hadoopTaskConfigService.update(hadoopTaskConfig);
            }

            //TODO 将所使用的引擎，任务负载数-1
            if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
                engineServerRepository.reduceKingbaseCountByIpPort(eip, eport);
            }else {
                engineServerRepository.reduceCountByIpPort(eip, eport);
            }

            log.info("任务ID【" + taskconfigid + "】,执行脱敏任务结束");
            log.info("任务ID【" + taskconfigid + "】,执行完毕");
        }
    }

    /**
     *@Description:执行数据脱敏
     *<AUTHOR> by xiongpf
     *@date 2017-01-12
     */
    public boolean doDataMasking(String[] args) {
        boolean isSuccess = false;
        if(args == null || args.length!=8) {
            System.out.println("There is no correct arguments!");
            return isSuccess;
        }
        try{
            //调用Hadoop脱敏程序jar包中RunJobThread
            //isSuccess = DataMaskingForLinuxJobV2.run(args);
           // RunJobThread runJobThread = new RunJobThread(args);
            //启用一个线程做Hadoop脱敏任务
          //  Thread thread = new Thread(runJobThread);
          //  thread.start();
            isSuccess = true;
        }
        catch(Exception ex) {
            ex.printStackTrace();
        }
        return isSuccess;
    }
}
