package com.wzsec.modules.api.server.auth;

import java.util.Base64;

public abstract class BasicHttpProxyAuthenticationProvider implements HttpProxyAuthenticationProvider {

    public static final String AUTH_TYPE_BASIC = "Basic";
    public static final String AUTH_REALM_BASIC = "Access to the staging site";

    @Override
    public String authType() {
        return AUTH_TYPE_BASIC;
    }

    @Override
    public String authRealm() {
        return AUTH_REALM_BASIC;
    }

    protected abstract boolean authenticate(String usr, String pwd);

    @Override
    public boolean authenticate(String authorization) {
        String usr = "";
        String pwd = "";
        if (authorization != null && authorization.length() > 0) {
            String token = authorization.substring(AUTH_TYPE_BASIC.length() + 1);
            String decode = new String(Base64.getDecoder().decode(token));
            String[] arr = decode.split(":");
            usr = arr[0];
            if (arr.length >= 2) {
                pwd = arr[1];
            }
        }
        return authenticate(usr, pwd);
    }

}
