package com.wzsec.dotask.mask.service.excute.file;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import com.wzsec.dotask.sdd.service.excute.common.RuleManager;
import com.wzsec.modules.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.mask.domain.MaskstrategyIdentifymaskstrategiesdetail;
import com.wzsec.modules.mask.service.AlgorithmService;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.utils.AlgorithmUtils;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 识别脱敏策略操作
 *
 * <AUTHOR>
 * @date 2024年10月22日 上午10:15:10
 */
@Slf4j
@Component
public class MaskReadService {

    /**
     * 异常数据处理
     *
     * @param data 数据
     * @return {@link String }
     */
    private static String abnormalDataHandling(String filedName, String data, String abnormalDataHandling,
                                               Map<String, String> abnormalHandlingAlarmMap,AlgorithmService algorithmService) {
        // TODO ---------- 异常数据处理 ----------
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(abnormalDataHandling)) {
                if (!abnormalDataHandling.equalsIgnoreCase("IGNORE")) { //置空,替换
                    //TODO ? MaskAlgFactory.getMaskData
                    data = MaskAlgFactory.getMaskData(data, abnormalDataHandling, Const.AES_SECRET_KEY, null);

                    com.wzsec.modules.mask.domain.Algorithm algorithm = algorithmService.findByAlgorithmName(abnormalDataHandling);
                    String sparefield1 = algorithm.getSparefield1();
                    if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                        data = MaskAlgFactory.getMaskData(data, abnormalDataHandling, Const.AES_SECRET_KEY, null);
                    }else {
                        //自定义算法
                        String jarname = algorithm.getJarname();//jar包名称
                        String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                        String methodname = algorithm.getMethodname();//方法名
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("jarname",jarname);
                        paramMap.put("funcnamepath",funcnamepath);
                        paramMap.put("methodname",methodname);
                        paramMap.put("maskData",data);
                        data = AlgorithmUtils.invokeJarMethod(paramMap);
                    }

                    String cNameAlgorithm = abnormalDataHandling.equals(Const.ABNORMAL_DATA_HANDLING_EMPTY) ?
                            Const.ABNORMAL_DATA_HANDLING_EMPTY_CNAME : Const.ABNORMAL_DATA_HANDLING_SUBSTITUTION_CNAME;
                    abnormalHandlingAlarmMap.put(filedName, cNameAlgorithm);
                } else { // 忽略
                    abnormalHandlingAlarmMap.put(filedName, Const.ABNORMAL_DATA_HANDLING_IGNORE_CNAME);
                }
            }
        } catch (Exception ex) {
            int length = data.length();
            StringBuilder masked = new StringBuilder();
            for (int i = 0; i < length; i++) {
                masked.append('*');
            }
            data = masked.toString();
        }
        return data;
        // TODO ---------- 异常数据处理 ----------
    }

    /**
     * 获取非格式化文件的脱敏数据
     * @param lineDataList
     * @param ruleList
     * @param splitstr
     * @param algorithmMap
     * @param maskFieldIndex
     * @param nonFormattedDisposalFilesMap
     * @param abnormalHandlingAlarmMap
     * @return
     */
    public static List<String> getMaskDataListIdentifyDetail(
            List<String> lineDataList, List<Map<String, Object>> ruleList,
            String splitstr, HashMap<String, Algorithm> algorithmMap,
            List<Integer> maskFieldIndex,
            Map<Integer, MaskstrategyIdentifymaskstrategiesdetail> nonFormattedDisposalFilesMap,
            Map<String, String> abnormalHandlingAlarmMap,
            AlgorithmService algorithmService
    ) {
        try {
            List<String> maskDataList = new ArrayList<String>();
            for (String lineData : lineDataList) {
                StringBuilder maskDataSBuilder = new StringBuilder();
                String newLine = MaskingHelper.replaceWhile(lineData, splitstr);
                String[] arrData = StringUtils.split(newLine, splitstr);
                String maskingData = "";
                String maskLineData = "";
                if (arrData.length > 0) {
                    for (int i = 0; i < arrData.length; i++) {
                        String data = arrData[i];
                        boolean isStr = false;
                        if (maskFieldIndex != null && StringUtils.isNotEmpty(data) && data.length() > 2) {
                            //最前面一位与最后一位如果是单引号，需要去掉，还原数据
                            String startStr = data.substring(0, 1);
                            String endStr = data.substring(data.length() - 1, data.length());
                            if ("'".equals(startStr) && "'".equals(endStr)) {
                                data = data.substring(1, data.length() - 1);
                                isStr = true;
                            }
                        }
                        String tmpMaskData = "";
                        boolean flag = false;
                        if (ruleList != null) {

                            for (Map<String, Object> ruleMap : ruleList) {

                                RuleDto ruleDto = (RuleDto) ruleMap.get("senRule");
                                //姓名、地址、手机号白名单过滤
                                if (Const.DATA_TYPE_NAME.equals(ruleDto.getApplytypecname()) && Const.nameWhiteList != null && Const.nameWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_ADDRESS.equals(ruleDto.getApplytypecname()) || Const.DATA_TYPE_DETAIL_ADDRESS.equals(ruleDto.getApplytypecname()) && Const.addreWhiteList != null && Const.addreWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_PHONE_NUMBER.equals(ruleDto.getApplytypecname()) && Const.phoneWhiteList != null && Const.phoneWhiteList.contains(data)) {
                                    continue;
                                }
                                boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);

                                // TODO 命中识别规则
                                if (checkSensResult) {
                                    String maskData = data;
                                    String algoName = "";
                                    String algoPara = "";
                                    try {
                                        Object maskStrategyStr = ruleMap.get("maskstrategystr");
                                        if (maskStrategyStr != null) {
                                            //TODO 脱敏策略示例： HIDECODE$*,0,0$1-6|RANDOMMAPPING$$15-18HIDECODE$*,0,0$1-6RANDOMMAPPING$$15-18
                                            String[] strategyStr = maskStrategyStr.toString().split("\\|");
                                            //下标从1开始，下标0数据为空
                                            for (int j = 1; j < strategyStr.length; j++) {
                                                String configStr = strategyStr[j];
                                                String[] config = configStr.split("\\$");
                                                algoName = config[0];
                                                algoPara = config[1];
                                                String[] indexArr = config[2].split("-");
                                                Integer startIndex = Integer.valueOf(indexArr[0]) - 1;
                                                Integer endIndex = Integer.valueOf(indexArr[1]);
                                                String substring = maskData.substring(startIndex, endIndex);
                                                String middleData = "";
                                                //判断当前执行是否使用自定义算法
                                                Algorithm algorithm = algorithmMap.get(algoName);
                                                String sparefield1 = algorithm.getSparefield1();
                                                if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                                    middleData = MaskAlgFactory.getMaskData(substring, algoName, algoPara, algorithmMap);
                                                }else {
                                                    //自定义算法
                                                    String jarname = algorithm.getJarname();//jar包名称
                                                    String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                                    String methodname = algorithm.getMethodname();//方法名
                                                    Map<String, String> paramMap = new HashMap<>();
                                                    paramMap.put("jarname",jarname);
                                                    paramMap.put("funcnamepath",funcnamepath);
                                                    paramMap.put("methodname",methodname);
                                                    paramMap.put("maskData",maskData);
                                                    middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                                                }

                                                String startData = maskData.substring(0, startIndex);
                                                String endData = maskData.substring(endIndex, data.length());
                                                maskData = startData + middleData + endData;
                                            }
                                            tmpMaskData = maskData;
                                        } else {
                                            // 该数据为敏感数据
                                            // 脱敏算法
                                            AlgorithmDto algorithmDto = (AlgorithmDto) ruleMap.get("algorithm");
                                            algoName = algorithmDto.getAlgenglishname();
                                            StringBuilder parStringBuilder = new StringBuilder();
                                            String param = ruleMap.get("param") == null ? "" : ruleMap.get("param").toString();
                                            String secretkey = ruleMap.get("secretkey") == null ? "" : ruleMap.get("secretkey").toString();
                                            if (StringUtils.isNotEmpty(param)) {
                                                parStringBuilder.append(param);
                                                if (StringUtils.isNotEmpty(secretkey)) {
                                                    parStringBuilder.append(",");
                                                    parStringBuilder.append(secretkey);
                                                }
                                            } else {
                                                if (StringUtils.isNotEmpty(secretkey)) {
                                                    parStringBuilder.append(secretkey);
                                                }
                                            }
                                            algoPara = parStringBuilder.toString();

                                            // 脱敏后的数据
                                            //判断当前执行是否使用自定义算法
                                            Algorithm algorithm = algorithmMap.get(algoName);
                                            String sparefield1 = algorithm.getSparefield1();
                                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                                tmpMaskData = MaskAlgFactory.getMaskData(data, algoName, algoPara, algorithmMap);
                                            }else {
                                                //自定义算法
                                                String jarname = algorithm.getJarname();//jar包名称
                                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                                String methodname = algorithm.getMethodname();//方法名
                                                Map<String, String> paramMap = new HashMap<>();
                                                paramMap.put("jarname",jarname);
                                                paramMap.put("funcnamepath",funcnamepath);
                                                paramMap.put("methodname",methodname);
                                                paramMap.put("maskData",data);
                                                tmpMaskData = AlgorithmUtils.invokeJarMethod(paramMap);
                                            }

                                        }
                                    } catch (Exception e) {
                                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algoName + "参数:" + algoPara.toString());

                                        // TODO 异常数据处置
                                        Long ruleDtoId = ruleDto.getId();
                                        MaskstrategyIdentifymaskstrategiesdetail maskStrategyFileUnformatSub = nonFormattedDisposalFilesMap.get(ruleDtoId);
                                        String abnormalDataHandling = maskStrategyFileUnformatSub.getSparefield4();
                                        log.warn("异常数据处置算法名为: {}", abnormalDataHandling);
                                        tmpMaskData = abnormalDataHandling(String.valueOf(i + 1), data, abnormalDataHandling, abnormalHandlingAlarmMap,algorithmService);
                                    }
                                    flag = true;
                                    break;
                                }
                            }
                        }
                        if (!flag) {
                            // 非敏感的数据，原文输出
                            tmpMaskData = data;
                        } else if (maskFieldIndex != null && !maskFieldIndex.contains(i)) {
                            //记录脱敏字段的下标
                            maskFieldIndex.add(i);
                        }
                        //字符串数据需要拼接单引号恢复成原样
                        if (isStr) {
                            tmpMaskData = "'" + tmpMaskData + "'";
                        }
                        // 拼接每个单独的数据
                        maskDataSBuilder.append(tmpMaskData + splitstr);
                    }
                }
                maskingData = maskDataSBuilder.toString().substring(0, maskDataSBuilder.toString().length() - 1);
                //4.还原成原字符串样式
                if (maskingData.length() - 3 > 0 && maskingData.substring(maskingData.length() - 3, maskingData.length()).equalsIgnoreCase("$&$")) {
                    maskLineData = StringUtils.replace(maskingData, "$&$", "", 10) + splitstr;
                } else {
                    maskLineData = StringUtils.replace(maskingData, "$&$", "", 10);
                }
                maskDataList.add(maskLineData);
            }
            return maskDataList;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
