package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskHbasetaskconfig;
import com.wzsec.modules.mask.service.MaskHbasetaskconfigService;
import com.wzsec.modules.mask.service.dto.MaskHbasetaskconfigQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2024-10-14
*/
// @Api(tags = "hbase任务配置管理")
@RestController
@RequestMapping("/api/maskHbasetaskconfig")
public class MaskHbasetaskconfigController {

    private final MaskHbasetaskconfigService maskHbasetaskconfigService;

    public MaskHbasetaskconfigController(MaskHbasetaskconfigService maskHbasetaskconfigService) {
        this.maskHbasetaskconfigService = maskHbasetaskconfigService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskHbasetaskconfig:list')")
    public void download(HttpServletResponse response, MaskHbasetaskconfigQueryCriteria criteria) throws IOException {
        maskHbasetaskconfigService.download(maskHbasetaskconfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询hbase任务配置")
    // @ApiOperation("查询hbase任务配置")
    @PreAuthorize("@el.check('maskHbasetaskconfig:list')")
    public ResponseEntity<Object> getMaskHbasetaskconfigs(MaskHbasetaskconfigQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskHbasetaskconfigService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增hbase任务配置")
    // @ApiOperation("新增hbase任务配置")
    @PreAuthorize("@el.check('maskHbasetaskconfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskHbasetaskconfig resources){
        return new ResponseEntity<>(maskHbasetaskconfigService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改hbase任务配置")
    // @ApiOperation("修改hbase任务配置")
    @PreAuthorize("@el.check('maskHbasetaskconfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskHbasetaskconfig resources){
        maskHbasetaskconfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除hbase任务配置")
    // @ApiOperation("删除hbase任务配置")
    @PreAuthorize("@el.check('maskHbasetaskconfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskHbasetaskconfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取新增任务号")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(maskHbasetaskconfigService.getMAXTaskName(), HttpStatus.CREATED);
    }


    @Log("执行HBase脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Long id, HttpServletRequest request) {
        maskHbasetaskconfigService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
