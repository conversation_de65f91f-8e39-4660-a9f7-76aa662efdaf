package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2024-10-21
*/
@Data
public class MaskstrategyIdentifymaskstrategiesDto implements Serializable {

    /** 主键 */
    private Integer id;

    /** 策略名称 */
    private String strategyname;

    /** 策略描述 */
    private String strategydesc;

    /** 状态(0：启用，1：禁用) */
    private String status;

    /** 创建用户 */
    private String createuser;

    /** 创建时间 */
    private Timestamp createtime;

    /** 更新用户 */
    private String updateuser;

    /** 更新时间 */
    private Timestamp updatetime;

    /** 备注 */
    private String remark;

    /** 是否审批 */
    private String sparefield1;

    /** 审批状态 */
    private String sparefield2;

    /** 审批人 */
    private String sparefield3;

    /** 审批时间 */
    private String sparefield4;

    private Boolean enabled;
}