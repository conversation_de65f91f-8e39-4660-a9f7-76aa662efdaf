package com.wzsec.service.mapper;

import com.wzsec.domain.LogAlarmSettings;
import com.wzsec.service.dto.LogAlarmSettingsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:46:47+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class LogAlarmSettingsMapperImpl implements LogAlarmSettingsMapper {

    @Override
    public LogAlarmSettingsDto toDto(LogAlarmSettings entity) {
        if ( entity == null ) {
            return null;
        }

        LogAlarmSettingsDto logAlarmSettingsDto = new LogAlarmSettingsDto();

        logAlarmSettingsDto.setCreatetime( entity.getCreatetime() );
        logAlarmSettingsDto.setCreateuser( entity.getCreateuser() );
        logAlarmSettingsDto.setFaultname( entity.getFaultname() );
        logAlarmSettingsDto.setId( entity.getId() );
        logAlarmSettingsDto.setLevel( entity.getLevel() );
        logAlarmSettingsDto.setMessage( entity.getMessage() );
        logAlarmSettingsDto.setNote( entity.getNote() );
        logAlarmSettingsDto.setSparefield1( entity.getSparefield1() );
        logAlarmSettingsDto.setSparefield2( entity.getSparefield2() );
        logAlarmSettingsDto.setSparefield3( entity.getSparefield3() );
        logAlarmSettingsDto.setSparefield4( entity.getSparefield4() );
        logAlarmSettingsDto.setSparefield5( entity.getSparefield5() );
        logAlarmSettingsDto.setState( entity.getState() );
        logAlarmSettingsDto.setThreshold( entity.getThreshold() );
        logAlarmSettingsDto.setUpdatetime( entity.getUpdatetime() );
        logAlarmSettingsDto.setUpdateuser( entity.getUpdateuser() );

        return logAlarmSettingsDto;
    }

    @Override
    public List<LogAlarmSettingsDto> toDto(List<LogAlarmSettings> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LogAlarmSettingsDto> list = new ArrayList<LogAlarmSettingsDto>( entityList.size() );
        for ( LogAlarmSettings logAlarmSettings : entityList ) {
            list.add( toDto( logAlarmSettings ) );
        }

        return list;
    }

    @Override
    public LogAlarmSettings toEntity(LogAlarmSettingsDto dto) {
        if ( dto == null ) {
            return null;
        }

        LogAlarmSettings logAlarmSettings = new LogAlarmSettings();

        logAlarmSettings.setCreatetime( dto.getCreatetime() );
        logAlarmSettings.setCreateuser( dto.getCreateuser() );
        logAlarmSettings.setFaultname( dto.getFaultname() );
        logAlarmSettings.setId( dto.getId() );
        logAlarmSettings.setLevel( dto.getLevel() );
        logAlarmSettings.setMessage( dto.getMessage() );
        logAlarmSettings.setNote( dto.getNote() );
        logAlarmSettings.setSparefield1( dto.getSparefield1() );
        logAlarmSettings.setSparefield2( dto.getSparefield2() );
        logAlarmSettings.setSparefield3( dto.getSparefield3() );
        logAlarmSettings.setSparefield4( dto.getSparefield4() );
        logAlarmSettings.setSparefield5( dto.getSparefield5() );
        logAlarmSettings.setState( dto.getState() );
        logAlarmSettings.setThreshold( dto.getThreshold() );
        logAlarmSettings.setUpdatetime( dto.getUpdatetime() );
        logAlarmSettings.setUpdateuser( dto.getUpdateuser() );

        return logAlarmSettings;
    }

    @Override
    public List<LogAlarmSettings> toEntity(List<LogAlarmSettingsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<LogAlarmSettings> list = new ArrayList<LogAlarmSettings>( dtoList.size() );
        for ( LogAlarmSettingsDto logAlarmSettingsDto : dtoList ) {
            list.add( toEntity( logAlarmSettingsDto ) );
        }

        return list;
    }
}
