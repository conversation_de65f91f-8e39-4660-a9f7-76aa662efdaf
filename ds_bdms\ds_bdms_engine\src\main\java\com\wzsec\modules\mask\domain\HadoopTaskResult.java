package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2020-11-17
*/
@Entity
@Data
@Table(name="sdd_mask_hadooptaskresult")
public class HadoopTaskResult implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 作业提交人姓名 */
    @Column(name = "username")
    private String username;

    /** 作业平台 */
    @Column(name = "platform")
    private String platform;

    /** 文件格式 */
    @Column(name = "fileformat")
    private String fileformat;

    /** 数据输入路径 */
    @Column(name = "datainputpath")
    private String datainputpath;

    /** 数据输出路径 */
    @Column(name = "dataoutputpath")
    private String dataoutputpath;

    /** 数据文件输入路径 */
    @Column(name = "datafileinputpath")
    private String datafileinputpath;

    /** 数据文件输出路径 */
    @Column(name = "datafileoutputpath")
    private String datafileoutputpath;

    /** 数据分隔符 */
    @Column(name = "datasplit")
    private String datasplit;

    /** 脱敏配置参数 */
    @Column(name = "configargs")
    private String configargs;

    /** 起始时间 */
    @Column(name = "jobstarttime")
    private String jobstarttime;

    /** 结束时间 */
    @Column(name = "jobendtime")
    private String jobendtime;

    /** 用时（秒） */
    @Column(name = "jobtotaltime")
    private String jobtotaltime;

    /** 作业状态（执行中、执行成功、执行失败） */
    @Column(name = "jobstatus")
    private String jobstatus;

    /** 数据行数 */
    @Column(name = "datarows")
    private String datarows;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 脱敏前数据 */
    @Column(name = "beforemaskdata")
    private String beforemaskdata;

    /** 脱敏后数据 */
    @Column(name = "aftermaskdata")
    private String aftermaskdata;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 任务号 */
    @Column(name = "taskname")
    private String taskname;

    public void copy(HadoopTaskResult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
