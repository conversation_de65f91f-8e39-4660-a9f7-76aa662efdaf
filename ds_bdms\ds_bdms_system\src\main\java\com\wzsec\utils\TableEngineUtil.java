package com.wzsec.utils;

import lombok.Data;

import java.util.List;

@Data
public class TableEngineUtil {
    private String primaryTable;//主键表
    private String primaryColumn;//主键字段
    private List<ForeignTableEngine> foreignInfoList;//对应的外键字段信息

    @Data
    public static class ForeignTableEngine{
        private String foreignTable;//外键表
        private String foreignColumn;//外键字段
    }

    /**
     * 判断当前表是否存在指定外键
     * @param foreignTable 外键表
     * @param foreignColumn 外键字段
     * @return
     */
    public boolean isForeignKeysExist(String foreignTable, String foreignColumn){
        if (foreignInfoList != null && foreignInfoList.size() > 0) {
            for (ForeignTableEngine foreignTableEngine : foreignInfoList) {
                if (foreignTable.equals(foreignTableEngine.getForeignTable()) &&
                        foreignColumn.equals(foreignTableEngine.getForeignColumn())
                ) {
                    return true;
                }
            }
        }
        return false;
    }
}
