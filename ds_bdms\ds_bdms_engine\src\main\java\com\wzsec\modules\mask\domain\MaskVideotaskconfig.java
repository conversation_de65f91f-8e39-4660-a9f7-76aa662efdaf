package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2022-04-21
*/
@Entity
@Data
@Table(name="sdd_mask_videotaskconfig")
public class MaskVideotaskconfig implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 原始视频目录 */
    @Column(name = "inputdirectory")
    private String inputdirectory;

    /** 输入文件格式 */
    @Column(name = "inputfileformat")
    private String inputfileformat;

    /** 计算资源 */
    @Column(name = "computeresources")
    private String computeresources;

    /** 脱敏对象 */
    @Column(name = "maskobject")
    private String maskobject;

    /** 任务状态 */
    @Column(name = "state")
    private String state;

    /** 输出文件格式 */
    @Column(name = "outputfileformat")
    private String outputfileformat;

    /** 脱敏后视频或图片目录 */
    @Column(name = "outputdirectory")
    private String outputdirectory;

    /** 执行状态 */
    @Column(name = "executionstate")
    private String executionstate;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(MaskVideotaskconfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}