package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-09
*/
@Data
public class MaskStrategyTableDto implements Serializable {

    /** 主键 */
    private Integer id;

    /** 策略名称 */
    private String strategyname;

    /** 策略描述 */
    private String strategydesc;

    /** 策略类型 */
    private String strategytype;

    /** 状态 */
    private String status;

    /** 状态 */
    private Boolean enabled;

    /** 表ID */
    private String tabid;

    /** 表名 */
    private String tabename;

    /** 表中文名 */
    private String tabcname;

    /** 库名 */
    private String dbname;

    /** 数据源类型 */
    private String sourcetype;

    /** 来源平台 */
    private String levelVal;

    /** 创建用户 */
    private String createuser;

    /** 创建时间 */
    private Timestamp createtime;

    /** 更新用户 */
    private String updateuser;

    /** 更新时间 */
    private Timestamp updatetime;

    /** 备注 */
    private String memo;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;
}
