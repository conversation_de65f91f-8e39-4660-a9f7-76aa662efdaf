package com.wzsec.modules.rule.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2020-04-03
*/
@Entity
@Data
@Table(name="sdd_rule")
public class Rule implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 规则中文名 */
    @Column(name = "cname")
    //@NotBlank
    private String cname;

    /** 规则英文名 */
    @Column(name = "ename")
    //@NotBlank
    private String ename;

    /** 规则类型（1正则，2程序算法，3自定义算法） */
    @Column(name = "ruletype")
    //@NotBlank
    private String ruletype;

    /** 适用对象英文名 */
    @Column(name = "applytypename")
    // //@NotBlank
    private String applytypename;

    /** 适用对象中文名 */
    @Column(name = "applytypecname")
    //@NotBlank
    private String applytypecname;

    /** 正则表达式 */
    @Column(name = "regexps")
    private String regexps;

    /** 类路径 */
    @Column(name = "classpath")
    private String classpath;

    /** 方法名 */
    @Column(name = "methodname")
    private String methodname;

    /** 状态(0启用,1禁用) */
    @Column(name = "state")
    //@NotBlank
    private String state;

    /** 规则说明 */
    @Column(name = "des")
    private String des;

    /** 备注 */
    @Column(name = "note")
    private String note;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(Rule source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
