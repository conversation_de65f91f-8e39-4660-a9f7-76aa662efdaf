package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.FileTaskResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* <AUTHOR>
* @date 2020-11-12
*/
public interface FileTaskResultRepository extends JpaRepository<FileTaskResult, Integer>, JpaSpecificationExecutor<FileTaskResult> {
    @Query(value = "select * from sdd_mask_filetaskresult where taskname = ?1 order by id desc limit 1", nativeQuery = true)
    FileTaskResult findByTaskName(String taskname);
}
