package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2021-01-15
*/
@Data
public class MaskAuditResultV1Dto implements Serializable {

    private Integer id;

    /** 任务名称 */
    private String taskname;

    /** 系统标识 */
    private String systemid;

    /** 检测类型 */
    private String checktype;

    /** 输出路径 */
    private String outpath;

    /** 风险程度（0：无，1：低危，2：中危，3：高危） */
    private String risk;

    /** 风险程度统计次数 */
    private String count;

    /** 检测时间 */
    private String checktime;

    /** 提交人 */
    private String submitter;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;
}
