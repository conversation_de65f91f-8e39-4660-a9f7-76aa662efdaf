package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.DBTaskConfig;
import com.wzsec.modules.mask.service.DBTaskConfigService;
import com.wzsec.modules.mask.service.dto.DBTaskConfigDto;
import com.wzsec.modules.mask.service.dto.DBTaskConfigQueryCriteria;
import com.wzsec.modules.quartz.config.DBTaskScanConfig;
import com.wzsec.utils.Const;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020-11-11
 */
// @Api(tags = "数据库脱敏任务管理管理")
@RestController
@RequestMapping("/api/dbtaskconfig")
public class DBTaskConfigController {

    private final DBTaskConfigService dbtaskconfigService;

    private final DBTaskScanConfig scanConfig;

    public DBTaskConfigController(DBTaskConfigService dbtaskconfigService, DBTaskScanConfig scanConfig) {
        this.dbtaskconfigService = dbtaskconfigService;
        this.scanConfig = scanConfig;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('dbtaskconfig:list')")
    public void download(HttpServletResponse response, DBTaskConfigQueryCriteria criteria) throws IOException {
        dbtaskconfigService.download(dbtaskconfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询数据库脱敏任务管理")
    // @ApiOperation("查询数据库脱敏任务管理")
    @PreAuthorize("@el.check('dbtaskconfig:list')")
    public ResponseEntity<Object> getDbtaskconfigs(DBTaskConfigQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(dbtaskconfigService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增数据库脱敏任务管理")
    // @ApiOperation("新增数据库脱敏任务管理")
    @PreAuthorize("@el.check('dbtaskconfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody DBTaskConfig resources) {
        String whetherApprove = resources.getEnablemoderation();
        if (whetherApprove.equals(Const.STATIC_DESENSITIZATION_STRATEGY_APPROVAL_YES)) { //需要审批初始审批状态为未审批
            resources.setApprovalstatus(Const.STRATEGY_APPROVAL_STATUS_NOT_APPROVED); //未审批
        }
        DBTaskConfigDto taskDto = dbtaskconfigService.create(resources);

        if (taskDto != null && Const.TASK_STATE_USE.equals(taskDto.getSubmittype()) && Const.TASK_SUBMITTYPE_AUTO.equals(taskDto.getSubmittype())) {
            if (whetherApprove.equals(Const.STATIC_DESENSITIZATION_STRATEGY_APPROVAL_NO)) {  //新增-无需审批即添加到定时任务
                scanConfig.addJob(taskDto);
            }
        }

        return new ResponseEntity<>(dbtaskconfigService.create(resources), HttpStatus.CREATED);
    }

    @Log("获取数据库脱敏任务新增任务号")
    // @ApiOperation("获取数据库脱敏任务新增任务号")
    @PreAuthorize("@el.check('dbtaskconfig:add')")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(dbtaskconfigService.getMAXTaskName(), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改数据库脱敏任务管理")
    // @ApiOperation("修改数据库脱敏任务管理")
    @PreAuthorize("@el.check('dbtaskconfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody DBTaskConfig resources) {
        //提交方式改为手动提交,将定时时间置空
        if (resources.getSubmittype().equals(Const.TASK_SUBMITTYPE_HAND)) {  //手动执行
            resources.setCron("");
        }

        String whetherApprove = resources.getEnablemoderation(); //改为需要审批,审批状态为未审批
        if (whetherApprove.equals(Const.STATIC_DESENSITIZATION_STRATEGY_APPROVAL_YES)) { //需要审批初始审批状态为未审批
            resources.setSparefield2(Const.STRATEGY_APPROVAL_STATUS_NOT_APPROVED); //未审批
        } else {   //不需要审批,即将审批状态,审批人,审批时间置为空值
            resources.setApprovalstatus(""); //审批状态
            resources.setApprover(""); //审批人
            resources.setApprovaltime(""); //审批时间
        }
        dbtaskconfigService.update(resources);

        DBTaskConfigDto taskDto = dbtaskconfigService.findById(resources.getId());
        if (taskDto != null && Const.TASK_SUBMITTYPE_AUTO.equals(taskDto.getSubmittype())) {   //定时执行
            if (whetherApprove.equals(Const.STATIC_DESENSITIZATION_STRATEGY_APPROVAL_NO) || //无需审批
                    resources.getApprovalstatus().equals(Const.STRATEGY_APPROVAL_STATUS_PASS)) { //审批通过
                scanConfig.updateJob(taskDto);
            }
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除数据库脱敏任务管理")
    // @ApiOperation("删除数据库脱敏任务管理")
    @PreAuthorize("@el.check('dbtaskconfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        for (Integer id : ids) {
            DBTaskConfigDto taskDto = dbtaskconfigService.findById(id);
            scanConfig.deleteJob(taskDto);
        }
        dbtaskconfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("根据数据源id获取表名")
    // @ApiOperation("根据数据源id获取表名")
    @PutMapping(value = "/getTabnameBySource/{sourceid}")
    @PreAuthorize("@el.check('dbtaskconfig:list')")
    public ResponseEntity<Object> getTabnameBySource(@PathVariable String sourceid) {
        //new ResponseEntity<>()
        return new ResponseEntity<>(dbtaskconfigService.getTabnameBySource(sourceid), HttpStatus.OK);
    }

    @Log("执行数据库静态脱敏任务")
    // @ApiOperation("在引擎执行数据库脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    // @PreAuthorize("@el.check('dbtaskconfig:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Integer id, HttpServletRequest request) {
        dbtaskconfigService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


    @Log("停止数据库静态脱敏任务")
    // @ApiOperation("在引擎执行数据库脱敏任务")
    @PutMapping(value = "/commandFromEngine/{id}/{cid}")
    @PreAuthorize("@el.check('dbtaskconfig:edit')")
    public ResponseEntity<Object> commandFromEngine(@PathVariable Integer id, @PathVariable Integer cid, HttpServletRequest request) {
        dbtaskconfigService.commandFromEngine(id, cid, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("更改审批状态")
    @GetMapping(value = "/changeApprovalStatus/{id}/{approvalStatus}")
    public ResponseEntity<Object> changeApprovalStatus(@PathVariable Integer id, @PathVariable String approvalStatus) {
        dbtaskconfigService.changeApprovalStatus(id, approvalStatus);
        DBTaskConfigDto taskDto = dbtaskconfigService.findById(id);
        if (taskDto != null && Const.TASK_SUBMITTYPE_AUTO.equals(taskDto.getSubmittype())) {   //定时执行
            if (approvalStatus.equals(Const.STRATEGY_APPROVAL_STATUS_PASS)) { //审批通过
                scanConfig.updateJob(taskDto);
            }
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取库表水印列")
    @PostMapping(value = "/getDBWatermarkColumn")
    public ResponseEntity<Object> getDBWatermarkColumn(Long sourceId, String tableName, Integer strategyId) {
        return new ResponseEntity<>(dbtaskconfigService.getDBWatermarkColumn(sourceId, tableName, strategyId),HttpStatus.OK);
    }

}
