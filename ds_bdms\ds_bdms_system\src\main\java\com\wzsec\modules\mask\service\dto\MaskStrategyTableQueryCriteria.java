package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2020-11-09
*/
@Data
public class MaskStrategyTableQueryCriteria{

    /** 状态开关 */
    @Query
    private String status;

    /** 策略类型 */
    @Query
    private String strategytype;

    /** 是否需要审批 */
    @Query
    private String sparefield1;

    /** 审批状态 */
    @Query
    private String sparefield2;

    @Query(blurry = "strategyname,strategydesc,dbname,tabename,tabcname,createuser,createtime,updateuser,updatetime")
    private String blurry;

    @Query(type = Query.Type.IN)
    private List<String> dbname;
}
