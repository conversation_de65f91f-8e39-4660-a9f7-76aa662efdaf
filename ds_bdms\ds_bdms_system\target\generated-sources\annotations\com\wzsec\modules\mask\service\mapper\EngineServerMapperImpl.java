package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.EngineServer;
import com.wzsec.modules.mask.service.dto.EngineServerDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:04+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EngineServerMapperImpl implements EngineServerMapper {

    @Override
    public EngineServerDto toDto(EngineServer entity) {
        if ( entity == null ) {
            return null;
        }

        EngineServerDto engineServerDto = new EngineServerDto();

        engineServerDto.setApplication( entity.getApplication() );
        engineServerDto.setCreatetime( entity.getCreatetime() );
        engineServerDto.setCreateuser( entity.getCreateuser() );
        engineServerDto.setId( entity.getId() );
        engineServerDto.setIp( entity.getIp() );
        engineServerDto.setIsvalid( entity.getIsvalid() );
        engineServerDto.setName( entity.getName() );
        engineServerDto.setNote( entity.getNote() );
        engineServerDto.setPort( entity.getPort() );
        engineServerDto.setSparefield1( entity.getSparefield1() );
        engineServerDto.setSparefield2( entity.getSparefield2() );
        engineServerDto.setSparefield3( entity.getSparefield3() );
        engineServerDto.setSparefield4( entity.getSparefield4() );
        engineServerDto.setSparefield5( entity.getSparefield5() );
        engineServerDto.setState( entity.getState() );
        engineServerDto.setUpdatetime( entity.getUpdatetime() );
        engineServerDto.setUpdateuser( entity.getUpdateuser() );

        return engineServerDto;
    }

    @Override
    public List<EngineServerDto> toDto(List<EngineServer> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EngineServerDto> list = new ArrayList<EngineServerDto>( entityList.size() );
        for ( EngineServer engineServer : entityList ) {
            list.add( toDto( engineServer ) );
        }

        return list;
    }

    @Override
    public EngineServer toEntity(EngineServerDto dto) {
        if ( dto == null ) {
            return null;
        }

        EngineServer engineServer = new EngineServer();

        engineServer.setApplication( dto.getApplication() );
        engineServer.setCreatetime( dto.getCreatetime() );
        engineServer.setCreateuser( dto.getCreateuser() );
        engineServer.setId( dto.getId() );
        engineServer.setIp( dto.getIp() );
        engineServer.setIsvalid( dto.getIsvalid() );
        engineServer.setName( dto.getName() );
        engineServer.setNote( dto.getNote() );
        engineServer.setPort( dto.getPort() );
        engineServer.setSparefield1( dto.getSparefield1() );
        engineServer.setSparefield2( dto.getSparefield2() );
        engineServer.setSparefield3( dto.getSparefield3() );
        engineServer.setSparefield4( dto.getSparefield4() );
        engineServer.setSparefield5( dto.getSparefield5() );
        engineServer.setState( dto.getState() );
        engineServer.setUpdatetime( dto.getUpdatetime() );
        engineServer.setUpdateuser( dto.getUpdateuser() );

        return engineServer;
    }

    @Override
    public List<EngineServer> toEntity(List<EngineServerDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EngineServer> list = new ArrayList<EngineServer>( dtoList.size() );
        for ( EngineServerDto engineServerDto : dtoList ) {
            list.add( toEntity( engineServerDto ) );
        }

        return list;
    }
}
