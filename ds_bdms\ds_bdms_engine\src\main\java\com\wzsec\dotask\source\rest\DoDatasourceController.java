package com.wzsec.dotask.source.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

// import io.swagger.annotations.*;

/**
 * <AUTHOR>
 * @date 2020-04-03
 */
// @Api(tags = "敏感数据发现数据源管理")
@RestController
@RequestMapping("/engine/datasource")
public class DoDatasourceController {

    private final DatasourceService datasourceService;

    public DoDatasourceController(DatasourceService datasourceService) {
        this.datasourceService = datasourceService;
    }

    @Log("测试数据源")
    // @ApiOperation("测试数据源")
    @PostMapping(value = "/test/{id}")
//    @PreAuthorize("@el.check('datasource:edit')")
    public ResponseEntity<Object> test(@PathVariable Long id) {
        //new ResponseEntity<>()
        return new ResponseEntity<>(datasourceService.test(id), HttpStatus.OK);
    }

    @Log("数据源数据库不存在就新增")
    @PostMapping(value = "/createDataBaseIfNoExist/{id}")
    @PreAuthorize("@el.check('datasource:edit')")
    public ResponseEntity<Object> createDataBaseIfNoExist(@PathVariable Long id) {
        return new ResponseEntity<>(datasourceService.createDataBaseIfNoExist(id), HttpStatus.OK);
    }

}
