package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskTablestructure;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-14
*/
public interface MaskTablestructureRepository extends JpaRepository<MaskTablestructure, Integer>, JpaSpecificationExecutor<MaskTablestructure> {


    @Query(value = "select * from sdd_mask_tablestructure GROUP BY tablename, dbname", nativeQuery = true)
    List<MaskTablestructure> queryingTableInformation();

    @Query(value = "select * from sdd_mask_tablestructure where tablename = ?1", nativeQuery = true)
    List<MaskTablestructure> queryingTableInformation(String tablename);

    @Query(value = "select * from sdd_mask_tablestructure where dbname= ?1 and tablename= ?2 and attribute is not null order by id asc", nativeQuery = true)
    List<MaskTablestructure> getTableStructureByName(String dbname, String tablename);
}
