package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2021-06-20
*/
@Entity
@Data
@Table(name="sdd_mask_strategy_file_format_sub")
public class MaskStrategyFileFormatSub implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 策略id */
    @Column(name = "strategyid")
    private Integer strategyid;

    /** 列数 */
    @Column(name = "columnnum")
    private Integer columnnum;

    /** 列含义 */
    @Column(name = "columndesc")
    private String columndesc;

    /** 敏感等级 */
    @Column(name = "sen_level")
    private String senLevel;

    /** 脱敏规则id */
    @Column(name = "ruleid")
    private Integer ruleid;

    /** 脱敏算法id */
    @Column(name = "algorithmid")
    private Integer algorithmid;

    /** 参数 */
    @Column(name = "param")
    private String param;

    /** 密钥 */
    @Column(name = "secretkey")
    private String secretkey;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskStrategyFileFormatSub source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
