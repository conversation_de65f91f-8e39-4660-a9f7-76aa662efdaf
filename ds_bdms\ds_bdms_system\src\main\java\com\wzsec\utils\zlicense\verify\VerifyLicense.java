package com.wzsec.utils.zlicense.verify;

import com.wzsec.utils.zlicense.de.schlichtherle.license.*;
import com.wzsec.utils.zlicense.verify.LicenseManagerHolder;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.prefs.Preferences;

/**
 * VerifyLicense
 * <AUTHOR>
 */
public class VerifyLicense {
	//common param
	private static String PUBLICALIAS = "";
	private static String STOREPWD = "";
	private static String SUBJECT = "";
	private static String licPath = "";
	private static String pubPath = "";
	
	public void setParam(String propertiesPath) throws IOException {
		Properties prop = new Properties();
		System.out.println("propertiesPath="+propertiesPath);		
		InputStream in = new FileInputStream(propertiesPath);		
		prop.load(in);		
		PUBLICALIAS = prop.getProperty("PUBLICALIAS");
		STOREPWD = prop.getProperty("STOREPWD");
		SUBJECT = prop.getProperty("SUBJECT");
		licPath = prop.getProperty("licPath");
		pubPath = prop.getProperty("pubPath");
	}

	public boolean verify() throws Exception  {		

		LicenseManager licenseManager = LicenseManagerHolder
				.getLicenseManager(initLicenseParams());
		
		// install license file
		try {
			licenseManager.install(new File(licPath));
			System.out.println("License file instal successfully!");				
		} catch (Exception e) {
			String moreInfo ="License file instal failure";
			System.out.println(moreInfo);
			throw e;
		}
		
		// verify license file
		boolean licenseIsValid = true;
		try {
			licenseIsValid = licenseManager.newVerify();
					
			/*System.out.println(licenseContent.getExtra()); 
			LicenseCheckModel licenseCheckModel = (LicenseCheckModel) licenseContent.getExtra();
			licenseCheckModel.getCPUSerial();
			
			boolean dateIsValid = VerifyUtil.getIsValid(VerifyUtil.getNowDate(),
					VerifyUtil.getDateFromGMT(licenseContent.getNotAfter().toString()));
			if(dateIsValid){
				System.out.println("Certificate Valid");
			}else{
				System.out.println("Certificate Expiration");
			}
			
			licenseIsValid = dateIsValid;
			*/

			System.out.println("License file verify successfully!");
		} catch (Exception e) {	
			String moreInfo ="License file verify failure";			
			System.out.println(moreInfo); 
			throw e;
		}
		return licenseIsValid;
	}

	private static LicenseParam initLicenseParams() {
		Preferences preference = Preferences
				.userNodeForPackage(zlicense.verify.VerifyLicense.class);
		CipherParam cipherParam = new DefaultCipherParam(STOREPWD);

		KeyStoreParam privateStoreParam = new DefaultKeyStoreParam(
				com.wzsec.utils.zlicense.verify.VerifyLicense.class, pubPath, PUBLICALIAS, STOREPWD, null);
		LicenseParam licenseParams = new DefaultLicenseParam(SUBJECT,
				preference, privateStoreParam, cipherParam);
		return licenseParams;
	}
}