package com.wzsec.modules.api.rule;

import com.wzsec.modules.api.algo.*;
import com.wzsec.modules.sdd.api.service.dto.ApiRuleDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2021年2月1日 下午5:37:51
 */
public class DataMaskManager {

    /**
     * @param dataStr
     * @param apiRuleDtoList
     * @return String
     * @Description 获取脱敏后字符串数据
     * <AUTHOR>
     * @date 2021年2月1日 下午5:42:18
     */
    public static String getMaskData(String dataStr, List<ApiRuleDto> apiRuleDtoList) {
        // 去除空格
        String maskData = dataStr.trim();
        for (ApiRuleDto apiRuleDto : apiRuleDtoList) {
            String rule = apiRuleDto.getRulename();
            if (rule != null) {
                if (rule.toLowerCase().startsWith("p_")) {
                    // 程序检测规则
                    if (ProRuleFactory.checkDataByProRule(maskData, rule)) {
                        maskData = getMaskData(maskData, apiRuleDto.getAlgorithm(), apiRuleDto.getParam());
                        break;
                    }
                    // System.out.println("脱敏前"+data+"脱敏后"+maskData);
                } else {
                    if (maskData.matches(rule)) {
                        maskData = getMaskData(maskData, apiRuleDto.getAlgorithm(), apiRuleDto.getParam());
                        break;
                    }
                }
            }
        }
        return maskData;
    }

    /**
     * @param data:数据
     * @param algorithm:算法
     * @param param:参数
     * @Description 获取脱敏后数据
     * @date 2020-12-24
     */
    private static String getMaskData(String data, String algorithm, String param) {
        String[] params = param.split(",");
        if (algorithm.equals(AlgoConsts.RESULTALGO_TYPE_MASK)) {   //掩码
            return HIDECODE.encrypt(data, params[0], params[1], params[2]);
        } else if (algorithm.equals(AlgoConsts.RESULTALGO_TYPE_ADD_MASK)) {   //地址掩码
            return ADDRESSMask.encrypt(data, param);
        } else if (algorithm.equals(AlgoConsts.RESULTALGO_TYPE_NUMBERROUND)) {   //数字取整
            return NUMBERROUND.encrypt(data);
        } else if (algorithm.equals(AlgoConsts.RESULTALGO_TYPE_CUTOUT)) { //截断
            return REPLACEMask.encrypt(data,params[0],params[1]);
        } else if (algorithm.equals(AlgoConsts.RESULTALGO_TYPE_REPLACE)) {  //替换
            return REPLACEMask.encrypt(data,params[0],params[1]);
        }else if (algorithm.equals(AlgoConsts.RESULTALGO_TYPE_AESALGORITHM)) { //AES算法
            try {
                return AESMask.encrypt(data);
            } catch (Exception e) {
                System.out.println("填写参数有误,请重新输入");
            }
        }else if (algorithm.equals(AlgoConsts.RESULTALGO_TYPE_TIMERANDOM)) { //时间随机
            return TimeRandomMask.encrypt(data);
        }

        System.out.println(data + "--" + algorithm + "--" + param);
        return data;
    }
}
