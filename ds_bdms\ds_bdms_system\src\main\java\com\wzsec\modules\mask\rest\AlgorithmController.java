package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.mask.domain.Algorithm;
import com.wzsec.modules.mask.service.AlgorithmService;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import com.wzsec.modules.mask.service.dto.AlgorithmQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2020-11-03
 */
// @Api(tags = "数据脱敏算法管理管理")
@RestController
@RequestMapping("/api/algorithm")
public class AlgorithmController {

    private final AlgorithmService algorithmService;

    public AlgorithmController(AlgorithmService algorithmService) {
        this.algorithmService = algorithmService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('algorithm:list')")
    public void download(HttpServletResponse response, AlgorithmQueryCriteria criteria) throws IOException {
        algorithmService.download(algorithmService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询数据脱敏算法管理")
    //@ApiOperation("查询数据脱敏算法管理")
//    @AnonymousAccess
    @PreAuthorize("@el.check('algorithm:list')")
    public ResponseEntity<Object> getAlgorithms(AlgorithmQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(algorithmService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增数据脱敏算法管理")
    // @ApiOperation("新增数据脱敏算法管理")
    @PreAuthorize("@el.check('algorithm:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody Algorithm resources) {
        String algorithmname = resources.getAlgorithmname();
        String algenglishname = resources.getAlgenglishname();
        int algorithmcount = algorithmService.findAlgorithmCountByAlgorithmName(algorithmname);
        if (algorithmcount > 0) {
            throw new BadRequestException("算法名称不能重复");
        }
        algorithmcount = algorithmService.findAlgorithmCountByAlgorithmEName(algenglishname);
        if (algorithmcount > 0) {
            throw new BadRequestException("算法英文名不能重复");
        }
        return new ResponseEntity<>(algorithmService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改数据脱敏算法管理")
    // @ApiOperation("修改数据脱敏算法管理")
    @PreAuthorize("@el.check('algorithm:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody Algorithm resources) {
        String algorithmname = resources.getAlgorithmname();
        String algenglishname = resources.getAlgenglishname();
        AlgorithmDto byId = algorithmService.findById(resources.getId());
        if (!byId.getAlgorithmname().equals(algorithmname)) {
            int algorithmcount = algorithmService.findAlgorithmCountByAlgorithmName(algorithmname);
            if (algorithmcount > 0) {
                throw new BadRequestException("算法名称不能重复");
            }
        }
        if (!byId.getAlgenglishname().equals(algenglishname)) {
            int algorithmcount = algorithmService.findAlgorithmCountByAlgorithmEName(algenglishname);
            if (algorithmcount > 0) {
                throw new BadRequestException("算法英文名不能重复");
            }
        }
        algorithmService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除数据脱敏算法管理")
    // @ApiOperation("删除数据脱敏算法管理")
    @PreAuthorize("@el.check('algorithm:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        algorithmService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("返回所有算法信息")
    // @ApiOperation("返回所有算法信息")
    @PostMapping(value = "/getAlgorithm")
    public ResponseEntity<Object> getAlgorithm() {
        return new ResponseEntity<>(algorithmService.getThreeAlgorithms(), HttpStatus.OK);
    }

    @Log("返回所有算法信息")
    // @ApiOperation("返回所有算法信息")
    @PostMapping(value = "/getAlgorithmByScope/{sparefield1}")
    public ResponseEntity<Object> getAlgorithmByScope(@PathVariable String sparefield1) {
        return new ResponseEntity<>(algorithmService.getAlgorithmByScope(sparefield1), HttpStatus.OK);
    }

    @Log("获取可用于分段规则的算法信息")
    @PostMapping(value = "/getExtractAlgorithm")
    public ResponseEntity<Object> getExtractAlgorithm() {
        return new ResponseEntity<>(algorithmService.getExtractAlgorithm(), HttpStatus.OK);
    }

    //@Log("返回敏感数据发现算法信息")
    @PostMapping(value = "/findAlgorithm")
    public ResponseEntity<Object> findAlgorithm() {
        return new ResponseEntity<>(algorithmService.findAlgorithm(), HttpStatus.OK);
    }


    @GetMapping(value = "/static")
    @Log("获取静态脱敏算法")
    public ResponseEntity<Object> findStaticDesensitizationAlgorithm(@RequestParam(value = "appKey") String appKey) {
        if (!appKey.equals("43aa4a05b55dafc2895d5fa354b1e818")) {
            throw new BadRequestException("接口拒绝访问");
        }
        return new ResponseEntity<>(algorithmService.findStaticDesensitizationAlgorithm(), HttpStatus.OK);
    }

    public static void main(String[] args) {
        try {
            String jarPath = "C:\\Users\\<USER>\\Desktop\\db\\测试自定义算法\\TestDisorder.jar";
            File jarFile = new File(jarPath);
            URL jarUrl = jarFile.toURI().toURL();
            URLClassLoader classLoader = new URLClassLoader(new URL[]{jarUrl});
            Class<?> targetClass = classLoader.loadClass("TestDisorder");
            // 尝试获取方法
            Method method = targetClass.getMethod("encrypt", String.class);
            Object instance = targetClass.getDeclaredConstructor().newInstance();
            String result = (String) method.invoke(instance, "张三李四王五赵六");
            System.out.println(result);
            classLoader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
