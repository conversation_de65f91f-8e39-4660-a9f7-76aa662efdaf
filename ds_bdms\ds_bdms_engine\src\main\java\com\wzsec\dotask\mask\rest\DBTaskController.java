package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoDBTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-07-13
 */
// @Api(tags = "数据库脱敏任务管理")
@RestController
@RequestMapping("/engine/db/task")
public class DBTaskController {

    private final DoDBTaskService doDBTaskService;

    private final UserService userService;

    public DBTaskController(DoDBTaskService doDBTaskService, UserService userService) {
        this.doDBTaskService = doDBTaskService;
        this.userService = userService;
    }

    @Log("执行数据库脱敏脱敏任务")
    // @ApiOperation("执行数据库脱敏任务")
    @PostMapping(value = "/exec/{id}")
    @PreAuthorize("@el.check('dbtaskconfig:edit')")
    public ResponseEntity<Object> execution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doDBTaskService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("定时执行数据库脱敏任务")
    // @ApiOperation("定时执行数据库脱敏任务")
    @PostMapping(value = "/timingexec/{id}")
    //@PreAuthorize("@el.check('wptask:edit')")
    public ResponseEntity<Object> timingexecution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        doDBTaskService.execution(id, Const.TASK_SUBMITTYPE_AUTOSUBMIT);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


    @Log("操作数据库脱敏任务")
    @PostMapping(value = "/command/{id}/{cid}")
    public ResponseEntity<Object> executeTask(@PathVariable Integer id, @PathVariable Integer cid) {
        System.out.println("开始发送停止指令：" + id);
        doDBTaskService.operationInstructions(id, cid);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
