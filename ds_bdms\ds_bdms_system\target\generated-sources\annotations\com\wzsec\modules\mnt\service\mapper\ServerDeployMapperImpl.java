package com.wzsec.modules.mnt.service.mapper;

import com.wzsec.modules.mnt.domain.ServerDeploy;
import com.wzsec.modules.mnt.service.dto.ServerDeployDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ServerDeployMapperImpl implements ServerDeployMapper {

    @Override
    public ServerDeployDto toDto(ServerDeploy entity) {
        if ( entity == null ) {
            return null;
        }

        ServerDeployDto serverDeployDto = new ServerDeployDto();

        serverDeployDto.setAccount( entity.getAccount() );
        serverDeployDto.setCreateTime( entity.getCreateTime() );
        serverDeployDto.setId( entity.getId() );
        serverDeployDto.setIp( entity.getIp() );
        serverDeployDto.setName( entity.getName() );
        serverDeployDto.setPassword( entity.getPassword() );
        serverDeployDto.setPort( entity.getPort() );

        return serverDeployDto;
    }

    @Override
    public List<ServerDeployDto> toDto(List<ServerDeploy> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ServerDeployDto> list = new ArrayList<ServerDeployDto>( entityList.size() );
        for ( ServerDeploy serverDeploy : entityList ) {
            list.add( toDto( serverDeploy ) );
        }

        return list;
    }

    @Override
    public ServerDeploy toEntity(ServerDeployDto dto) {
        if ( dto == null ) {
            return null;
        }

        ServerDeploy serverDeploy = new ServerDeploy();

        serverDeploy.setAccount( dto.getAccount() );
        serverDeploy.setCreateTime( dto.getCreateTime() );
        serverDeploy.setId( dto.getId() );
        serverDeploy.setIp( dto.getIp() );
        serverDeploy.setName( dto.getName() );
        serverDeploy.setPassword( dto.getPassword() );
        serverDeploy.setPort( dto.getPort() );

        return serverDeploy;
    }

    @Override
    public List<ServerDeploy> toEntity(List<ServerDeployDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ServerDeploy> list = new ArrayList<ServerDeploy>( dtoList.size() );
        for ( ServerDeployDto serverDeployDto : dtoList ) {
            list.add( toEntity( serverDeployDto ) );
        }

        return list;
    }
}
