package com.wzsec.dotask.mask.service.impl;

import cn.god.mask.common.Algorithm;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzsec.config.Log;
import com.wzsec.dotask.mask.service.DoFileTaskService;
import com.wzsec.dotask.mask.service.excute.file.*;
import com.wzsec.modules.alarm.config.MonitorRiskAlarmData;
import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.mask.domain.FileTaskConfig;
import com.wzsec.modules.mask.domain.FileTaskResult;
import com.wzsec.modules.mask.domain.MaskStrategyFileFormatSub;
import com.wzsec.modules.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.mask.repository.AlgorithmRepository;
import com.wzsec.modules.mask.repository.EngineServerRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.*;
import com.wzsec.modules.mask.service.mapper.FileTaskConfigMapper;
import com.wzsec.modules.mask.service.mapper.FileTaskResultMapper;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.modules.system.service.dto.DictDetailDto;
import com.wzsec.modules.system.service.dto.DictDetailQueryCriteria;
import com.wzsec.utils.*;
import com.wzsec.utils.minio.MinioFileInfo;
import com.wzsec.watermark.alg.WaterMarkAlgFactory;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


import org.springframework.core.env.Environment;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import com.fasterxml.jackson.core.type.TypeReference;


// 默认不使用缓存
//import org.springframework.cache.annotation.CacheConfig;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR>
 * @date 2020-11-12
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "fileTask")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoFileTaskServiceImpl implements DoFileTaskService {

    private final FileTaskResultService fileTaskResultService;

    private final FileTaskConfigService fileTaskConfigService;

    private final DatasourceService datasourceService;

    private final FileTaskConfigMapper fileTaskConfigMapper;

    private final FileTaskResultMapper fileTaskResultMapper;

    private final AlgorithmService algorithmService;

    private final MaskStrategyFileMainService maskStrategyFileMainService;

    private final MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService;

    private final MaskruleService maskruleService;

    private final RuleService ruleService;

    private final MaskStrategyFileFormatSubService maskStrategyFileFormatSubService;

    private final DmAlarmdisposalService dmAlarmdisposalService;

    private final SystemLogService logService;

    private final MaskTaskresultrecordsService maskTaskresultrecordsService;

    private final AlgorithmRepository algorithmRepository;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    @Autowired
    private ServerProperties serverProperties;

    private final EngineServerRepository engineServerRepository;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private DictDetailService dictDetailService;

    @Autowired
    Environment environment;

    public DoFileTaskServiceImpl(FileTaskConfigService fileTaskConfigService, FileTaskResultService fileTaskResultService, DatasourceService datasourceService,
                                 FileTaskConfigMapper fileTaskConfigMapper, FileTaskResultMapper fileTaskResultMapper, AlgorithmService algorithmService, MaskStrategyFileMainService maskStrategyFileMainService,
                                 MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService, RuleService ruleService, MaskruleService maskruleService,
                                 MaskStrategyFileFormatSubService maskStrategyFileFormatSubService, DmAlarmdisposalService dmAlarmdisposalService, SystemLogService logService,
                                 MaskTaskresultrecordsService maskTaskresultrecordsService, AlgorithmRepository algorithmRepository,
                                 EngineServerRepository engineServerRepository, SdkOperationrecordRepository sdkOperationrecordRepository, SdkApplyconfigRepository sdkApplyconfigRepository) {
        this.fileTaskConfigService = fileTaskConfigService;
        this.fileTaskResultService = fileTaskResultService;
        this.datasourceService = datasourceService;
        this.fileTaskConfigMapper = fileTaskConfigMapper;
        this.fileTaskResultMapper = fileTaskResultMapper;
        this.algorithmService = algorithmService;
        this.maskStrategyFileMainService = maskStrategyFileMainService;
        this.maskStrategyFileUnformatSubService = maskStrategyFileUnformatSubService;
        this.ruleService = ruleService;
        this.maskruleService = maskruleService;
        this.maskStrategyFileFormatSubService = maskStrategyFileFormatSubService;
        this.dmAlarmdisposalService = dmAlarmdisposalService;
        this.logService = logService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.algorithmRepository = algorithmRepository;
        this.engineServerRepository = engineServerRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
    }

    @Async//异步执行
    @Override
    public void execution(Integer id, String submituser) {
        log.info("开始执行文件脱敏任务");
        boolean flag = true;
        // 任务开始时间
        String startTime = DateUtil.getNowTime();
        long startTimeLong = System.currentTimeMillis();

        FileTaskConfig fileTaskConfig = null;
        FileTaskConfigDto fileTaskConfigDto = fileTaskConfigService.findById(id);
        FileTaskResult fileTaskResult = null;
        fileTaskConfig = fileTaskConfigMapper.toEntity(fileTaskConfigDto);
        FileTaskResult fileResult = fileTaskResultService.findByTaskName(fileTaskConfig.getTaskname());


        //任务状态修改为执行中
        fileTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTING);
        fileTaskConfigService.update(fileTaskConfig);
        //输入路径
        String inputpath = fileTaskConfig.getInputpath();
        String inputfiletype = fileTaskConfigDto.getInputfiletype();

        //更新进度条
        fileTaskConfigService.alterTaskExecutionAnticipate("30", id);


        String engine = fileTaskConfig.getTaskexecuteengine();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String engineIp = ipPort[0];
        String enginePort = ipPort[1];

        //TODO 将所使用的引擎，任务负载数+1
        if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
            engineServerRepository.addKingbaseCountByIpPort(engineIp, enginePort);
        }else{
            engineServerRepository.addCountByIpPort(engineIp, enginePort);
        }

        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(engineIp+""+enginePort);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        String username = null;

        if (submituser != null) {
            username = submituser;
        } else {
            username = "程序提交";
        }

        // TODO 异常数据处理告警
        Map<String, String> abnormalHandlingAlarmMap = new HashMap<>();

        //输入数据源信息
        String inPutDataSourceType = null, inPutDatasourceIp = null, inPutDatasourceAccount = null, inPutDatasourcePassword = null;
        Integer inPutDatasourcePort = null;
        String inPutDataSourceUrl = null;
        if (fileTaskConfigDto.getInputdatasourceid()!=null){
            Long inPutSourceId = Long.valueOf(fileTaskConfigDto.getInputdatasourceid());
            DatasourceDto inPutDatasource = datasourceService.findById(inPutSourceId);
            inPutDataSourceUrl = inPutDatasource.getSrcurl();
            inPutDataSourceType = inPutDatasource.getType();//数据源类型
            if (inPutDataSourceType.equalsIgnoreCase(Const.FILE_MINIO)){
                inPutDatasourceIp = inPutDatasource.getSrcip();
                inPutDatasourcePort = Integer.valueOf(inPutDatasource.getSrcport());
            }else {
                String[] hostNameAndPort = inPutDatasource.getSrcurl().split(":|：");
                inPutDatasourceIp = hostNameAndPort[0];//数据源ip
                inPutDatasourcePort = hostNameAndPort.length > 1 ? Integer.valueOf(hostNameAndPort[1]) : Const.DEFAULT_PORT;//数据源端口
            }
            inPutDatasourceAccount = inPutDatasource.getUsername();//数据源账号
            inPutDatasourcePassword = inPutDatasource.getPassword();//数据源密码
            if (StringUtils.isNotEmpty(inPutDatasourcePassword)) {
                try {
                    String decrypt = AES.decrypt(inPutDatasourcePassword, Const.AES_SECRET_KEY);
                    inPutDatasourcePassword = decrypt;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        //输出数据源信息
        String outPutDataSourceType = null, outPutDatasourceIp = null, outPutDatasourceAccount = null, outPutDatasourcePassword = null;
        Integer outPutDatasourcePort = null;
        String outPutDataSourceUrl = null;

        DatasourceDto outPutDatasource = new DatasourceDto();
        if(fileTaskConfigDto.getOutputdatasourceid() != null){
            Long outPutSourceId = Long.valueOf(fileTaskConfigDto.getOutputdatasourceid());
            outPutDatasource = datasourceService.findById(outPutSourceId);
            outPutDataSourceUrl = outPutDatasource.getSrcurl();
        }
        outPutDataSourceType = outPutDatasource.getType();//数据源类型
        outPutDatasourceAccount = outPutDatasource.getUsername();//数据源账号
        outPutDatasourcePassword = outPutDatasource.getPassword();//数据源密码
        if (StringUtils.isNotEmpty(outPutDatasourcePassword)) {
            try {
                String decrypt = AES.decrypt(outPutDatasourcePassword, Const.AES_SECRET_KEY);
                outPutDatasourcePassword = decrypt;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (Const.FILE_FTP.equalsIgnoreCase(outPutDataSourceType) || Const.FILE_SFTP.equalsIgnoreCase(outPutDataSourceType)) {
            String[] hostNameAndPort = outPutDatasource.getSrcurl().split(":|：");
            outPutDatasourceIp = hostNameAndPort[0];//数据源ip
            outPutDatasourcePort = hostNameAndPort.length > 1 ? Integer.valueOf(hostNameAndPort[1]) : Const.DEFAULT_PORT;//数据源端口
        }

        FileTaskResult fileTaskResultTemp = new FileTaskResult();
        fileTaskResultTemp.setTaskname(fileTaskConfigDto.getTaskname());
        fileTaskResultTemp.setInputpath(fileTaskConfigDto.getInputpath());
        fileTaskResultTemp.setInputfiletype(fileTaskConfigDto.getInputfiletype());
        fileTaskResultTemp.setSplitstr(fileTaskConfigDto.getSplitstr());
        fileTaskResultTemp.setStrategyname(String.valueOf(fileTaskConfigDto.getStrategyid()));
        fileTaskResultTemp.setOutputtype(fileTaskConfigDto.getOutputtype());
        if (Const.TASK_DATATYPE_DB.equals(fileTaskConfigDto.getOutputtype())) {
            fileTaskResultTemp.setOutputpath(outPutDatasource.getDbname());
            fileTaskResultTemp.setOutputname(fileTaskConfigDto.getTablename());
        } else {
            fileTaskResultTemp.setOutputpath(fileTaskConfigDto.getOutputdirectory());
            fileTaskResultTemp.setOutputname(fileTaskConfigDto.getOutputfiletype());
        }
        fileTaskResultTemp.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTING);
        fileTaskResultTemp.setUsername(username);
        fileTaskResultTemp.setCreatetime(DateUtil.getNowTime());
        fileTaskResultTemp.setJobstarttime(startTime);
        FileTaskResultDto fileTaskResultDto = fileTaskResultService.create(fileTaskResultTemp);

        fileTaskResult = fileTaskResultMapper.toEntity(fileTaskResultDto);
        // 保存脱敏后的数据到的位置
        String outputType = fileTaskConfigDto.getOutputtype();
        // 查脱敏策略
        Integer strategyid = fileTaskConfigDto.getStrategyid();
        // 根据策略id查询脱敏策略( 使用文件的脱敏策略类 )
        MaskStrategyFileMainDto strategyMainDto = maskStrategyFileMainService.findById(strategyid);

        // 获取建表字段名
        List<String> fileList = maskStrategyFileFormatSubService.queryFiles(strategyid);

        String fieldnames = String.join(",", fileList);

        String outputPath = fileTaskConfigDto.getOutputdirectory();

        if (!Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
            //将输出路径最后面的目录分隔符去掉
            String regex = "[/\\\\]+$";
            outputPath = outputPath.replaceAll(regex, "");
        }

        String outputfiletype = fileTaskConfigDto.getOutputfiletype();


        //远程文件本地缓存路径 配置文件路径 + 当前任务的任务号
        String localCachePath = ConstEngine.sddEngineConfs.get("ftp.download.save.localpath") + File.separator + fileTaskConfigDto.getTaskname();
        //minio缓存路径
        String minioCachePath = ConstEngine.sddEngineConfs.get("minio.download.save.localpath") + "/" + fileTaskConfigDto.getTaskname();
        String inputPath = fileTaskConfigDto.getInputpath();//输入路径

        // 获取输入目录下文件内容
        log.info("执行脱敏任务开始");
        // 脱敏前数据前5条
        String beforemaskdata = "";
        // 脱敏后数据前5条
        String aftermaskdata = "";
        //数据行数
        int totallines = 0;

        List<MinioFileInfo> minioFileInfos = new ArrayList<>();
        try {
            //TODO FTP文件缓存到本地
            if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                String localInPath = localCachePath + File.separator + "in";
                // 指定目录路径
                Path directoryPath = Paths.get(localInPath);
                // 判断目录是否存在
                if (!Files.exists(directoryPath)) {
                    Files.createDirectories(directoryPath);
                }
                boolean downloadFileFlag = true;
                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType)) {
                    downloadFileFlag = SFTPUtil.downloadFile(inPutDatasourceIp, inPutDatasourcePort, inPutDatasourceAccount, inPutDatasourcePassword, inputPath, localInPath);
                } else if (Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                    downloadFileFlag = FTPUtil.downloadFileByFilePath(inPutDatasourceIp, inPutDatasourcePort, inPutDatasourceAccount, inPutDatasourcePassword, inputPath, localInPath);
                }
                if (!downloadFileFlag) {
                    throw new Exception(inputPath + "路径下内容，缓存到本地失败");
                }
                inputPath = localInPath;
            }else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                String localInPath = minioCachePath + "/" + "in";
                // 指定目录路径
                Path directoryPath = Paths.get(localInPath);
                // 判断目录是否存在
                if (!Files.exists(directoryPath)) {
                    Files.createDirectories(directoryPath);
                }
                String bucketName = MinioUtil.queryBucketName(inPutDataSourceUrl);
                //判断是文件夹还是文件
                if (inputpath.endsWith("/")) {
                    //按文件夹处理,查询minio目录下所有文件
                    List<MinioFileInfo> minioFileInfoList = MinioUtil.listFilesWithInfo(inPutDataSourceUrl, inPutDatasourceAccount, inPutDatasourcePassword, inputPath).stream()
                            .filter(t -> t.getFileName().endsWith(queryFileType(inputfiletype))).collect(Collectors.toList());
                    if (fileResult == null || StringUtils.isEmpty(fileResult.getMiniodata())){
                        minioFileInfos.addAll(minioFileInfoList);
                    }else {
                        String miniodata = fileResult.getMiniodata();
                        List<MinioFileInfo> fileInfos = fromJson(miniodata);
                        for (MinioFileInfo minioFileInfo : minioFileInfoList) {
                            String fileName = minioFileInfo.getFileName();
                            Long lastModified = minioFileInfo.getLastModified();
                            boolean minioFlag = true;
                            for (MinioFileInfo fileInfo : fileInfos) {
                                if (fileInfo.getFileName().equals(fileName) && lastModified.longValue() == fileInfo.getLastModified().longValue()){
                                    minioFlag = false;
                                    break;
                                }
                            }
                            if (minioFlag) {
                                minioFileInfos.add(minioFileInfo);
                            }
                        }
                    }
                    List<String> files = minioFileInfos.stream().map(t -> t.getFileName()).collect(Collectors.toList());
                    for (String fileName : files) {
                        String fileType = queryFileType(inputfiletype);
                        if (!fileName.endsWith(fileType)){
                            continue;
                        }
                        boolean downloadFileFlag = MinioUtil.downloadFileByFilePath(inPutDataSourceUrl,inPutDatasourceAccount,inPutDatasourcePassword,bucketName,inPutDataSourceUrl,fileName,localInPath);
                        if (!downloadFileFlag){
                            throw new Exception(inputPath + "路径下内容，缓存到本地失败");
                        }
                    }
                }else {
                    //按文件处理
                    if (!inputpath.contains(".")){
                        throw new RuntimeException("输入路径配置错误,文件要加前缀/和文件类型后缀,文件夹结尾用/");
                    }
                    String newpath = inputpath.substring(1);
                    boolean isSaveMinioFile = true;
                    MinioFileInfo minioFileInfo = MinioUtil.getSingleFileMetadata(inPutDataSourceUrl, inPutDatasourceAccount, inPutDatasourcePassword, newpath);
                    if (fileResult != null && StringUtils.isNotEmpty(fileResult.getMiniodata())){
                        String miniodata = fileResult.getMiniodata();
                        List<MinioFileInfo> fileInfos = fromJson(miniodata);
                        List<MinioFileInfo> collect = fileInfos.stream().filter(t -> t.getFileName().equals(minioFileInfo.getFileName()) && t.getLastModified().longValue() == minioFileInfo.getLastModified().longValue()).collect(Collectors.toList());
                        if (ObjectUtil.isNotEmpty(collect)){
                            isSaveMinioFile = false;
                        }
                    }
                    if (isSaveMinioFile){
                        minioFileInfos.add(minioFileInfo);
                        boolean downloadFileFlag = MinioUtil.downloadFileByFilePath(inPutDataSourceUrl,inPutDatasourceAccount,inPutDatasourcePassword,bucketName,inPutDataSourceUrl,newpath,localInPath);
                        if (!downloadFileFlag){
                            throw new Exception(inputPath + "路径下内容，缓存到本地失败");
                        }
                    }
                }
                inputPath = localInPath;
            }

            //List<HashMap<String, List<String>>> lineDataList = FileReadService.readDirFile(fileTaskConfigDto);
//            List<HashMap<String, List<String>>> lineDataList = FileReadService.readDirFile(fileTaskConfigDto, inputPath);
            List<HashMap<String, List<String>>> lineDataList = null;
            List<SQLFileUtil> sqlFileUtilList = null;
            if (Const.FILETASK_OUT_TYPE_SQL.equals(outputfiletype) || Const.FILETASK_OUT_TYPE_DMP.equals(outputfiletype)) {
                String suffix = null;
                if (Const.FILETASK_OUT_TYPE_SQL.equals(outputfiletype)) {
                    suffix = ".sql";
                } else if (Const.FILETASK_OUT_TYPE_DMP.equals(outputfiletype)) {
                    suffix = ".dmp";
                }
                sqlFileUtilList = FileReadService.readSqlFile(inputPath, suffix);
            } else {
                lineDataList = FileReadService.readDirFile(fileTaskConfigDto, inputPath);
            }
            //文件分隔符
            String splitstr = fileTaskConfigDto.getSplitstr();
            //是否设置水印
            Boolean isWatermark = Const.ISWATERMARK_YES.equals(fileTaskConfigDto.getIswatermark());
            //数据提供方
            String dataProvider = fileTaskConfigDto.getDataprovider();
            //数据使用方
            String dataUse = fileTaskConfigDto.getDatause();
            // 判断策略是格式化文件策略还是非格式化文件策略
            if ("0".equals(strategyMainDto.getStrategytype())) {
                // 格式化文件策略
                String postionAlgoParasStrByStrategyName = fileTaskConfigDto.getMaskstrategystr();

                // TODO 获取格式化文件字段处置规则
                Map<Integer, MaskStrategyFileFormatSub> maskStrategyFileFormatSubMap = maskStrategyFileFormatSubService.queryFormatFile(strategyid);

                //String fieldnames = fileTaskConfigDto.getExtractfield();
                String tablename = fileTaskConfigDto.getTablename();
                tablename = tablename + "_" + DateUtil.formatDate(new Date());
                String[] fieldnamaearr = fieldnames.split(",");

                HashMap<String, Algorithm> algorithmMap = algorithmService.getAlgorithmByEName();
                List<Map<String, String>> maskDataLists = new ArrayList<Map<String, String>>();

                //TODO 原设置水印逻辑，计算设置水印的下标
                //计算出要设置水印的下标
                Integer watermarkIndex = null;
                String waterMarkInfo = null;
                if (isWatermark) {
                    waterMarkInfo = WaterMarkAlgFactory.getWaterMarkInfo(dataProvider, dataUse, true);
                    MaskingConfigInfo[] maskingConfigInfoArray = FileReadService.getMaskingConfigInfoArray(postionAlgoParasStrByStrategyName);
                    //设置水印，默认在最后一位添加；如若最后一位使用了可逆算法，改为倒数第二位，以此类推
                    for (int i = maskingConfigInfoArray.length - 1; i >= 0; i--) {
                        String algoName = maskingConfigInfoArray[i].getAlgoName(); //脱敏算法
                        //String algoPara = maskingConfigArray[i].getAlgoParameter(); //脱敏参数
                        Boolean isWatermarkFlag;
                        if (StringUtils.isEmpty(algoName)) {
                            isWatermarkFlag = true;
                        } else {
                            com.wzsec.modules.mask.domain.Algorithm algorithm = algorithmService.findByAlgorithmName(algoName);
                            isWatermarkFlag = Const.ALGORITHM_IS_REVERSIBLE_NO.equals(algorithm.getIsreversible());
                        }

                        //如果这一列没有选择脱敏算法，或所选的算法不可逆，选择这一列数据写入水印
                        if (isWatermarkFlag) {
                            watermarkIndex = i;
                            break;
                        }
                    }
                }

                // TODO 按脱敏策略对数据进行脱敏
                for (HashMap<String, List<String>> lineDataMap : lineDataList) {
                    for (Map.Entry<String, List<String>> entry : lineDataMap.entrySet()) {
                        String fileName = entry.getKey().substring(0, entry.getKey().indexOf("."));
                        List<String> list = entry.getValue();
                        List<String> maskDataList = FileReadService.getMaskDataList(list, postionAlgoParasStrByStrategyName, splitstr, algorithmMap, maskStrategyFileFormatSubMap, abnormalHandlingAlarmMap);

                        totallines += list.size();
                        if ("".equals(beforemaskdata)) {
                            beforemaskdata = FileReadService.listToStr(list);
                            aftermaskdata = FileReadService.listToStr(maskDataList);
                        }
                        if (Const.TASK_DATATYPE_DB.equals(outputType)) {
                            FileReadService.getMaskDataLists(maskDataLists, maskDataList, fieldnames, splitstr);
                        } else {
                            //TODO 原设置水印逻辑，数据最后一位拼接不可见字符
                            //如果水印下标不为空，将对应下标位的数据设置水印
                            if (watermarkIndex != null && maskDataList.size() > 0) {
                                Integer waterLineSpacing = Integer.valueOf(ConstEngine.sddEngineConfs.get("waterLineSpacing"));
                                //数据行数大于设置的水印行间距，就按照间隔数注入水印
                                if (maskDataList.size() >= waterLineSpacing) {
                                    for (int i = 0; i < maskDataList.size(); i++) {
                                        Integer index = i + 1;
                                        if (index % waterLineSpacing == 0) {//该行等于水印行间距的倍数(例如100、200、300等)
                                            String[] split = maskDataList.get(i).split(splitstr);
                                            String data = split[watermarkIndex] + waterMarkInfo;
                                            split[watermarkIndex] = data;
                                            String joinData = String.join(splitstr, split);
                                            maskDataList.set(i, joinData);
                                        }
                                    }
                                } else {
                                    // 小于水印行间隔，随机抽取一行注入水印
                                    Random random = new Random();
                                    int randomNumber = random.nextInt(maskDataList.size());
                                    String[] split = maskDataList.get(randomNumber).split(splitstr);
                                    String data = split[watermarkIndex] + waterMarkInfo;
                                    split[watermarkIndex] = data;
                                    String joinData = String.join(splitstr, split);
                                    maskDataList.set(randomNumber, joinData);
                                }
                            }

                            String markFilePath = null;
                            // 保存到文件
                            if (Const.FILETASK_OUT_TYPE_TXT.equals(outputfiletype) ||
                                    Const.FILETASK_OUT_TYPE_DAT.equals(outputfiletype) ||
                                    Const.FILETASK_OUT_TYPE_DEL.equals(outputfiletype)
                            ) {
                                String outputName = "";
                                if (Const.FILETASK_OUT_TYPE_TXT.equals(outputfiletype)) {
                                    //保存txt文件
                                    outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".txt";
                                } else if (Const.FILETASK_OUT_TYPE_DAT.equals(outputfiletype)) {
                                    //保存dat文件
                                    outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".dat";
                                } else if (Const.FILETASK_OUT_TYPE_DEL.equals(outputfiletype)) {
                                    //保存del文件
                                    outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".del";
                                }
                                //添加文件名
                                fileTaskResult = setOutputname(fileTaskResult,outputName);
                                String fileOutPath;
                                //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                } else {
                                    fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                }
                                createFile(fileOutPath);
                                WirteFileService.wirteTxtFile(maskDataList, fileOutPath);
                            } else if (Const.FILETASK_OUT_TYPE_CSV.equals(outputfiletype)) {
                                //保存csv文件
                                String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".csv";
                                //添加文件名
                                fileTaskResult = setOutputname(fileTaskResult,outputName);
                                //String outputName = fileName + "_mark_" + DateUtil.formatDate(new Date()) + ".txt";
                                //markFilePath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;

                                String fileOutPath;
                                //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                } else {
                                    fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                }
                                createFile(fileOutPath);
                                WirteFileService.wirteCsvFile(maskDataList, fileOutPath);
                            } else if (Const.FILETASK_OUT_TYPE_EXCEL.equals(outputfiletype)) {
                                //保存excel文件
                                String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date());
                                //添加文件名
                                fileTaskResult = setOutputname(fileTaskResult,outputName);
                                //String outputName = fileName + "_mark_" + DateUtil.formatDate(new Date()) + ".xlsx";
                                //2003版xls
                                //wirteFileService.wirteExcelFile(maskDataList,fieldnamaearr.length,outputPath,outputName);
                                //2007版xlsx
                                //markFilePath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;

                                String fileOutPath;
                                //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = localCachePath + File.separator + "out";
                                } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = minioCachePath + File.separator + "out";
                                } else {
                                    fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname();
                                }
                                createFile(fileOutPath);
                                if (entry.getKey().endsWith("xls")) {
                                    //2003版xls
                                    outputName += ".xls";
                                    WirteFileService.wirteExcelFile(maskDataList, fieldnamaearr.length, outputPath, outputName);
                                } else {
                                    //2007版xlsx
                                    outputName += ".xlsx";
                                    WirteFileService.wirteXSSFWorkbook(maskDataList, fieldnamaearr.length, fileOutPath, outputName, splitstr);
                                }
                            } else if (Const.FILETASK_OUT_TYPE_DOC.equals(outputfiletype) || Const.FILETASK_OUT_TYPE_DOCX.equals(outputfiletype)){
                                //保存word文件
                                String outputName = "";
                                if (Const.FILETASK_OUT_TYPE_DOC.equals(outputfiletype)){
                                    outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".doc";
                                }else {
                                    outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".docx";
                                }
                                //添加文件名
                                fileTaskResult = setOutputname(fileTaskResult,outputName);
                                String fileOutPath;
                                //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                } else  if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                } else {
                                    fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                }
                                createFile(fileOutPath);
                                WirteFileService.writeWordFile(maskDataList, fileOutPath);
                            } else if (Const.FILETASK_OUT_TYPE_PPT.equals(outputfiletype)|| Const.FILETASK_OUT_TYPE_PPTX.equals(outputfiletype)){
                                //保存ppt文件
                                String outputName = "";
                                if (Const.FILETASK_OUT_TYPE_PPT.equals(outputfiletype)){
                                    outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".ppt";
                                }else {
                                    outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".pptx";
                                }
                                //添加文件名
                                fileTaskResult = setOutputname(fileTaskResult,outputName);
                                String fileOutPath;
                                //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                } else {
                                    fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                }
                                createFile(fileOutPath);
                                WirteFileService.writeWordFile(maskDataList, fileOutPath);
                            } else if (Const.FILETASK_OUT_TYPE_PDF.equals(outputfiletype)){
                                //保存pdf文件
                                String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".pdf";
                                //添加文件名
                                fileTaskResult = setOutputname(fileTaskResult,outputName);
                                String fileOutPath;
                                //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                } else {
                                    fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                }
                                createFile(fileOutPath);
                                WirteFileService.writePdfFile(maskDataList, fileOutPath);
                            }else if (Const.FILETASK_OUT_TYPE_PNG.equals(outputfiletype) || Const.FILETASK_OUT_TYPE_JPG.equals(outputfiletype)){
                                //保存png、jpg文件
                                String endings = Const.FILETASK_OUT_TYPE_PNG.equals(outputfiletype) ? ".png":".jpg";
                                String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + endings;
                                //添加文件名
                                fileTaskResult = setOutputname(fileTaskResult,outputName);
                                String fileOutPath;
                                //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                    fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                } else {
                                    fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                }
                                WirteFileService.writePngFile(maskDataList, fileOutPath);
                            }

                            //TODO 脱敏生成文件名含有_mark_文件，将_mark_文件加注水印后，会生成_mask_文件
                            /*if (StringUtils.isNotEmpty(markFilePath)) {
                                WaterMark watermark = new WaterMark();
                                String IDs = String.format("数据提供方:%s,数据使用方:%s", dataProvider, dataUse);
                                String filePathOutput = markFilePath.replace("_mark_", "_mask_");
                                Boolean isSuccess = watermark.AddWaterMarkInfo(markFilePath, filePathOutput, IDs, null, null, null, splitstr, "2");//2默认使用暗水印
                                //删除临时创建的mark文件，保留mask文件
                                if (isSuccess) {
                                    FileUtils.deleteFile(markFilePath);
                                }
                                log.info(isSuccess ? "水印加注成功" : "水印加注失败");
                            }*/
                        }
                    }
                }
                if (Const.TASK_DATATYPE_DB.equals(outputType)) {
                    // 1.查目标源配置
                    int outputSourceId = fileTaskConfigDto.getOutputdatasourceid();
                    DatasourceDto datasourceDto = datasourceService.findById(new Long(outputSourceId));
                    // 保存到库表
                    FileReadService.insertData2NewTable(maskDataLists, datasourceDto.getDbname(), tablename, datasourceDto.getSrcurl(), datasourceDto.getUsername(), datasourceDto.getPassword(),
                            datasourceDto.getType(), datasourceDto.getDriverprogram(), fieldnames);
                }
            } else if ("1".equals(strategyMainDto.getStrategytype())) {
                // 非格式化文件策略
                // 加载白名单 非格式化文件脱敏会用到
                Const.nameWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.NAME_SIGN, Const.whiteList_SIGN);
                Const.addreWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.ADDRESS_SIGN, Const.whiteList_SIGN);
                Const.phoneWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.PHONE_SIGN, Const.whiteList_SIGN);
                // 根据策略ID查询非格式化文件脱敏策略
                List<Map<String, Object>> ruleList = new ArrayList<>();
                List<MaskStrategyFileUnformatSub> maskStrategySubList = maskStrategyFileUnformatSubService.findByStrategyid(strategyMainDto.getId());

                // TODO 获取非格式化文件字段处置规则
                Map<Integer, MaskStrategyFileUnformatSub> nonFormattedDisposalFilesMap = new LinkedHashMap<>();
                for (MaskStrategyFileUnformatSub maskStrategyFileUnformatSub : maskStrategySubList) {
                    nonFormattedDisposalFilesMap.put(maskStrategyFileUnformatSub.getMaskruleid(), maskStrategyFileUnformatSub);
                }

                List<com.wzsec.modules.mask.domain.Algorithm> algorithmList = algorithmRepository.findAll();
                Map<String, String> algNameMap = new HashMap<>();
                for (com.wzsec.modules.mask.domain.Algorithm algorithm : algorithmList) {
                    algNameMap.put(algorithm.getId().toString(), algorithm.getAlgenglishname());
                }

                for (MaskStrategyFileUnformatSub maskStrategyFileUnformatSub : maskStrategySubList) {
                    Map<String, Object> ruleMap = new HashMap<>();
                    MaskruleDto maskruleDto = maskruleService.findById(maskStrategyFileUnformatSub.getMaskruleid());
                    RuleDto ruleDto = ruleService.findById(maskStrategyFileUnformatSub.getSenruleid().longValue());
                    ruleMap.put("senRule", ruleDto);
                    if (Const.ALG_CONFIG_WAY_ENTIRETY.equals(maskruleDto.getSparefield2())) {
                        AlgorithmDto algorithmDto = algorithmService.findById(maskStrategyFileUnformatSub.getAlgorithmid());
                        ruleMap.put("algorithm", algorithmDto);
                        ruleMap.put("param", maskStrategyFileUnformatSub.getParam());
                        ruleMap.put("secretkey", maskStrategyFileUnformatSub.getSecretkey());
                    } else {
                        String extractconfig = maskruleDto.getSparefield3() != null ? maskruleDto.getSparefield3() : null;
                        String extractalgid = maskruleDto.getSparefield4() != null ? maskruleDto.getSparefield4() : null;
                        StringBuffer stringBuffer = new StringBuffer();
                        if (StringUtils.isNotEmpty(extractconfig) && StringUtils.isNotEmpty(extractalgid)) {
                            String[] extractConfigArr = extractconfig.split(";");
                            String[] extractAlgIdArr = extractalgid.split(";");
                            for (int i = 0; i < extractAlgIdArr.length; i++) {
                                String algId = extractAlgIdArr[i];
                                String algName = algNameMap.get(algId);
                                String parKey = Const.algorithmParameterSecretKeyMap().get(algName);
                                stringBuffer.append("|");
                                stringBuffer.append(algName + "$");
                                stringBuffer.append(parKey + "$");
                                stringBuffer.append(extractConfigArr[i]);
                            }
                        }
                        ruleMap.put("maskstrategystr", stringBuffer);
                    }
                    ruleList.add(ruleMap);
                }

                if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
                    inputPath = inputPath + extractPath(inputpath);
                }

                HashMap<String, Algorithm> algorithmMap = algorithmService.getAlgorithmByEName();
                if (Const.FILETASK_OUT_TYPE_SQL.equals(outputfiletype) || Const.FILETASK_OUT_TYPE_DMP.equals(outputfiletype)) {
                    for (SQLFileUtil sqlFileUtil : sqlFileUtilList) {
                        //保存sql文件
                        String outputName = sqlFileUtil.getFileName() + "_mask_" + DateUtil.formatDate(new Date()) + sqlFileUtil.getFileSuffix();
                        String fileOutPath;
                        //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = localCachePath + File.separator + "out";
                        } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
                            fileOutPath = minioCachePath + File.separator + "out";
                        } else {
                            fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname();
                        }

                        List<SingleTableSQLFileUtil> singleTableSQLFileUtilList = sqlFileUtil.getSingleTableSQLFileUtilList();
                        for (SingleTableSQLFileUtil singleTableSQLFileUtil : singleTableSQLFileUtilList) {
                            ArrayList<String> list = singleTableSQLFileUtil.readInsertDataStr(splitstr);
                            List<Integer> maskFieldIndex = new ArrayList<>();
                            List<String> maskDataList = FileReadService.getMaskDataListForUnformatFile(list, ruleList, splitstr, algorithmMap, maskFieldIndex, nonFormattedDisposalFilesMap, abnormalHandlingAlarmMap);

                            totallines += list.size();
                            if ("".equals(beforemaskdata) && list.size() != 0) {
                                beforemaskdata = FileReadService.listToStr(list);
                                aftermaskdata = FileReadService.listToStr(maskDataList);
                            }
                            createFile(fileOutPath);
                            WirteFileService.writeSqlFile(singleTableSQLFileUtil, maskFieldIndex, maskDataList, splitstr, fileOutPath, outputName);
                        }
                    }

                    //数据清空
                    sqlFileUtilList = null;
                } else if (Const.FILETASK_OUT_TYPE_JSON.equals(outputfiletype)) {
                    Map<String, String> jsonFileMap = FileReadService.readFileBySuffix(inputPath, ".json");
                    for (Map.Entry<String, String> entry : jsonFileMap.entrySet()) {
                        String fileInPath = entry.getKey();
                        String fileName = entry.getValue().replace(".json", "");
                        String fileOutPath = null;

                        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = localCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".json";
                        } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
                            fileOutPath = minioCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".json";;
                        } else {
                            fileOutPath = outputPath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".json";
                        }
                        createFile(fileOutPath);
                        boolean jsonFileMask = JsonFileUtils.jsonFileMask(fileInPath, fileOutPath, ruleList, algorithmMap);
                        if (!jsonFileMask) {
                            throw new Exception("json文件脱敏失败，文件名：" + entry.getValue());
                        }
                    }
                } else if (Const.FILETASK_OUT_TYPE_XML.equals(outputfiletype)) {
                    Map<String, String> xmlFileMap = FileReadService.readFileBySuffix(inputPath, ".xml");
                    for (Map.Entry<String, String> entry : xmlFileMap.entrySet()) {
                        String fileInPath = entry.getKey();
                        String fileName = entry.getValue().replace(".xml", "");
                        String fileOutPath = null;

                        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = localCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".xml";
                        } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = minioCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".xml";
                        } else {
                            fileOutPath = outputPath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".xml";
                        }
                        createFile(fileOutPath);
                        boolean xmlFileMask = XmlFileUtils.xmlFileMask(fileInPath, fileOutPath, ruleList, algorithmMap);
                        if (!xmlFileMask) {
                            throw new Exception("xml文件脱敏失败，文件名：" + entry.getValue());
                        }
                    }
                } else if (Const.FILETASK_OUT_TYPE_DCM.equals(outputfiletype)) {
                    Map<String, String> xmlFileMap = FileReadService.readFileBySuffix(inputPath, ".dcm");
                    for (Map.Entry<String, String> entry : xmlFileMap.entrySet()) {
                        String fileInPath = entry.getKey();
                        String fileName = entry.getValue().replace(".dcm", "");
                        String fileOutPath = null;
                        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = localCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".dcm";
                        } else {
                            fileOutPath = outputPath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".dcm";
                        }
                        boolean dicomFileMask = DcmUtil.dcmFileMask(fileInPath, fileOutPath, ruleList, algorithmMap);
                        totallines = 1;
                        List<String> beforemaskdataList = DcmUtil.getDcmFileExample(fileInPath);
                        List<String> aftermaskdataList = DcmUtil.getDcmFileMaskExample(fileInPath,ruleList,algorithmMap);
                        if ("".equals(beforemaskdata) && beforemaskdataList.size() != 0) {
                            beforemaskdata = FileReadService.listToStr(beforemaskdataList);
                            aftermaskdata = FileReadService.listToStr(aftermaskdataList);
                        }
                        if (!dicomFileMask) {
                            throw new Exception("dcm文件脱敏失败，文件名：" + entry.getValue());
                        }
                    }
                } else if (Const.FILETASK_OUT_TYPE_DIC.equals(outputfiletype)) {
                    Map<String, String> xmlFileMap = FileReadService.readFileBySuffix(inputPath, ".dic");
                    for (Map.Entry<String, String> entry : xmlFileMap.entrySet()) {
                        String fileInPath = entry.getKey();
                        String fileName = entry.getValue().replace(".dic", "");
                        String fileOutPath = null;
                        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = localCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".dic";
                        } else {
                            fileOutPath = outputPath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".dic";
                        }
                        boolean dicomFileMask = DcmUtil.dcmFileMask(fileInPath, fileOutPath, ruleList, algorithmMap);
                        if (!dicomFileMask) {
                            throw new Exception("dic文件脱敏失败，文件名：" + entry.getValue());
                        }
                    }
                } else if (Const.FILETASK_OUT_TYPE_DOC.equals(outputfiletype) || Const.FILETASK_OUT_TYPE_DOCX.equals(outputfiletype)){
                    String suffix = Const.FILETASK_OUT_TYPE_DOC.equals(outputfiletype)?".doc":".docx";
                    Map<String, String> xmlFileMap = new HashMap<>();
                    if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
                        xmlFileMap = FileReadService.readFileBySuffixList(inputPath, suffix);
                    }else {
                        xmlFileMap = FileReadService.readFileBySuffix(inputPath, suffix);
                    }
                    for (Map.Entry<String, String> entry : xmlFileMap.entrySet()) {
                        String fileInPath = "";
                        String fileName = "";
                        String minioFilePath = "";
                        if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
                            fileInPath = entry.getKey();
                            minioFilePath = fileInPath.replace("\\", "/").replaceAll(inputPath,"").replace(entry.getValue(),"");
                            if (minioFilePath.equals("/")){
                                minioFilePath = "";
                            }
                            fileName = entry.getValue().replace(suffix, "");
                        }else {
                            fileInPath = entry.getKey();
                            fileName = entry.getValue().replace(suffix, "");
                        }
                        String fileOutPath = null;
                        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = localCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                        } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                            if (StringUtils.isNotEmpty(minioFilePath)){
                                fileOutPath = minioCachePath + File.separator + "out" + File.separator + minioFilePath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                            }else {
                                fileOutPath = minioCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                            }
                        } else {
                            fileOutPath = outputPath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                        }
                        createFile(fileOutPath);
                        boolean dicomFileMask = WordUtil.wordFileMask(fileInPath, fileOutPath, ruleList, algorithmMap,splitstr);
                        if (!dicomFileMask) {
//                            throw new Exception("word文件脱敏失败，文件名：" + entry.getValue());
                            log.error("word文件脱敏失败，文件名：" + entry.getValue());
                            continue;
                        }
                    }
                } else if (Const.FILETASK_OUT_TYPE_PDF.equals(outputfiletype)){
                    String suffix =".pdf";
                    Map<String, String> xmlFileMap = new HashMap<>();
                    if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
                        xmlFileMap = FileReadService.readFileBySuffixList(inputPath, suffix);
                    }else {
                        xmlFileMap = FileReadService.readFileBySuffix(inputPath, suffix);
                    }
                    for (Map.Entry<String, String> entry : xmlFileMap.entrySet()) {
                        String fileInPath = "";
                        String fileName = "";
                        String minioFilePath = "";
                        if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)){
                            fileInPath = entry.getKey();
                            minioFilePath = fileInPath.replace("\\", "/").replaceAll(inputPath,"").replace(entry.getValue(),"");
                            if (minioFilePath.equals("/")){
                                minioFilePath = "";
                            }
                            fileName = entry.getValue().replace(suffix, "");
                        }else {
                            fileInPath = entry.getKey();
                            fileName = entry.getValue().replace(suffix, "");
                        }
                        String fileOutPath = null;
                        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                            fileOutPath = localCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                        } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                            if (StringUtils.isNotEmpty(minioFilePath)){
                                fileOutPath = minioCachePath + File.separator + "out" + File.separator + minioFilePath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                            }else {
                                fileOutPath = minioCachePath + File.separator + "out" + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                            }
                        } else {
                            fileOutPath = outputPath + File.separator + fileName + "_mask_" + DateUtil.formatDate(new Date()) + suffix;
                        }
                        createFile(fileOutPath);
                        boolean dicomFileMask = PdfUtil.pdfFileMask(fileInPath, fileOutPath, ruleList, algorithmMap,splitstr);
                        if (!dicomFileMask) {
//                            throw new Exception("pdf文件脱敏失败，文件名：" + entry.getValue());
                            log.error("pdf文件脱敏失败，文件名：" + entry.getValue());
                            continue;
                        }
                    }
                } else {
                    for (HashMap<String, List<String>> lineDataMap : lineDataList) {
                        for (Map.Entry<String, List<String>> entry : lineDataMap.entrySet()) {
                            String fileName = entry.getKey().substring(0, entry.getKey().indexOf("."));
                            List<String> list = entry.getValue();
                            List<String> maskDataList = FileReadService.getMaskDataListForUnformatFile(list, ruleList, splitstr, algorithmMap, null, nonFormattedDisposalFilesMap, abnormalHandlingAlarmMap);
                            totallines += list.size();
                            if ("".equals(beforemaskdata)) {
                                beforemaskdata = FileReadService.listToStr(list);
                                aftermaskdata = FileReadService.listToStr(maskDataList);
                            }
                            if (Const.TASK_DATATYPE_DB.equals(outputType)) {
                                // TODO 更改后的文件脱敏暂时不支持输出到数据库
                                // FileReadService.getMaskDataLists(maskDataLists,maskDataList,fieldnames,splitstr);
                            } else {
                                // 保存到文件
                                if (Const.FILETASK_OUT_TYPE_TXT.equals(outputfiletype) ||
                                        Const.FILETASK_OUT_TYPE_DAT.equals(outputfiletype) ||
                                        Const.FILETASK_OUT_TYPE_DEL.equals(outputfiletype)
                                ) {
                                    String outputName = "";
                                    if (Const.FILETASK_OUT_TYPE_TXT.equals(outputfiletype)) {
                                        //保存txt文件
                                        outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".txt";
                                    } else if (Const.FILETASK_OUT_TYPE_DAT.equals(outputfiletype)) {
                                        //保存dat文件
                                        outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".dat";
                                    } else if (Const.FILETASK_OUT_TYPE_DEL.equals(outputfiletype)) {
                                        //保存del文件
                                        outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".del";
                                    }
                                    //添加文件名
                                    fileTaskResult = setOutputname(fileTaskResult,outputName);
                                    String fileOutPath;
                                    //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                    if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                    } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType) ) {
                                        fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                    } else {
                                        fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                    }

                                    WirteFileService.wirteTxtFile(maskDataList, fileOutPath);
                                } else if (Const.FILETASK_OUT_TYPE_CSV.equals(outputfiletype)) {
                                    //保存csv文件
                                    String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".csv";
                                    //添加文件名
                                    fileTaskResult = setOutputname(fileTaskResult,outputName);
                                    String fileOutPath;
                                    //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                    if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                    } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType) ) {
                                        fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                    } else {
                                        fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                    }
                                    WirteFileService.wirteCsvFile(maskDataList, fileOutPath);
                                } else if (Const.FILETASK_OUT_TYPE_EXCEL.equals(outputfiletype)) {
                                    //保存excel文件
                                    String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".xlsx";
                                    //添加文件名
                                    fileTaskResult = setOutputname(fileTaskResult,outputName);
                                    //2003版xls
                                    //wirteFileService.wirteExcelFile(maskDataList,fieldnamaearr.length,outputPath,outputName);
                                    //2007版xlsx

                                    String fileOutPath;
                                    //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                    if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = localCachePath + File.separator + "out";
                                    } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = minioCachePath + File.separator + "out";
                                    } else {
                                        fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname();
                                    }
                                    WirteFileService.wirteUnFormatXSSFWorkbook(maskDataList, splitstr, fileOutPath, outputName);
                                } else if (Const.FILETASK_OUT_TYPE_DOC.equals(outputfiletype) || Const.FILETASK_OUT_TYPE_DOCX.equals(outputfiletype)){
                                    String outputName = "";
                                    if (Const.FILETASK_OUT_TYPE_DOC.equals(outputfiletype)){
                                        //保存doc文件
                                        outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".doc";
                                    } else if (Const.FILETASK_OUT_TYPE_DOCX.equals(outputfiletype)){
                                        //保存docx文件
                                        outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".docx";
                                    }
                                    //添加文件名
                                    fileTaskResult = setOutputname(fileTaskResult,outputName);
                                    String fileOutPath;
                                    //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                    if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                    } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                    } else {
                                        fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                    }
                                    WirteFileService.writeWordFile(maskDataList, fileOutPath);
                                } else if (Const.FILETASK_OUT_TYPE_PDF.equals(outputfiletype)){
                                    String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + ".pdf";;
                                    //添加文件名
                                    fileTaskResult = setOutputname(fileTaskResult,outputName);
                                    String fileOutPath;
                                    //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                    if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                    } else if (Const.FILE_MINIO.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = minioCachePath + File.separator + "out" + File.separator + outputName;
                                    } else {
                                        fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                    }
                                    WirteFileService.writePdfFile(maskDataList, fileOutPath);
                                }else if (Const.FILETASK_OUT_TYPE_PNG.equals(outputfiletype) || Const.FILETASK_OUT_TYPE_JPG.equals(outputfiletype)){
                                    String endings = Const.FILETASK_OUT_TYPE_PNG.equals(outputfiletype) ? ".png" : ".jpg";
                                    String outputName = fileName + "_mask_" + DateUtil.formatDate(new Date()) + endings;
                                    //添加文件名
                                    fileTaskResult = setOutputname(fileTaskResult,outputName);
                                    String fileOutPath;
                                    //输入路径与本地缓存路径相同，说明本地有缓存文件，按照本地缓存文件来设置输出路径
                                    if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                                        fileOutPath = localCachePath + File.separator + "out" + File.separator + outputName;
                                    } else {
                                        fileOutPath = outputPath + File.separator + fileTaskConfigDto.getTaskname() + File.separator + outputName;
                                    }
                                    WirteFileService.writePngFile(maskDataList, fileOutPath);
                                }
                            }
                        }
                    }
                }
            }


            //TODO 输出类型为文件
            if (Const.TASK_DATATYPE_FILE.equals(outputType)) {
                boolean uploadResult = true;
                //TODO 且输出的数据源为SFTP、FTP，将本地缓存的文件输出到输出源上
                if (Const.FILE_SFTP.equalsIgnoreCase(outPutDataSourceType)) {
                    String remotePath = localCachePath + File.separator + "out";
                    String uploadingPath = outputPath + Const.SEPARATOR_LINUX + fileTaskConfigDto.getTaskname();
                    uploadResult = SFTPUtil.uploadingFile(outPutDatasourceIp, outPutDatasourcePort, outPutDatasourceAccount, outPutDatasourcePassword, remotePath, uploadingPath);
                } else if (Const.FILE_FTP.equalsIgnoreCase(outPutDataSourceType)) {
                    String remotePath = localCachePath + File.separator + "out";
                    String uploadingPath = outputPath + Const.SEPARATOR_LINUX + fileTaskConfigDto.getTaskname();
                    uploadResult = FTPUtil.uploadingFile(outPutDatasourceIp, outPutDatasourcePort, outPutDatasourceAccount, outPutDatasourcePassword, remotePath, uploadingPath);
                } else if (Const.FILE_MINIO.equalsIgnoreCase(outPutDataSourceType)) {
                    String remotePath = minioCachePath + "/" + "out";
                    String bucketName = MinioUtil.queryBucketName(outPutDataSourceUrl);
                    String fileType = queryFileType(outputfiletype);
                    String uploadingPath = outputPath ;
                    uploadResult = MinioUtil.uploadingFile(remotePath,uploadingPath,bucketName,outPutDataSourceUrl, outPutDatasourceAccount, outPutDatasourcePassword,fileType);
                }
                if (!uploadResult){
                    throw new Exception(outPutDataSourceType + "上传失败");
                }
            }
        } catch (Exception e) {
            flag = false;
            log.info("执行文件脱敏任务失败");
            e.printStackTrace();

            String taskname = fileTaskConfigDto.getTaskname();
            String fileType = fileTaskConfigDto.getInputfiletype();
            //TODO 写入系统监控-异常日志 且首页告警统计
            Log log = new Log();
            String description = "任务号:" + taskname + ",文件路径：" + fileType + "，执行执行文件脱敏任务出现异常";
            log.setDescription(description);
            StackTraceElement[] stackTrace = e.getStackTrace();
            String method = stackTrace[0].getClassName()+"."+stackTrace[0].getMethodName();
            log.setMethod(method);
            String parameters = "{taskname:" + taskname + "}";
            log.setParams(parameters);
            long endTime = System.currentTimeMillis();
            int time = (int) ((endTime - startTimeLong) / 1000);
            log.setTime((long) time);
            log.setCreateTime(new Timestamp(System.currentTimeMillis()));

            //将打印到控制台的内容，转为字符串,存放到日志里
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            String exInfo = sw.toString();

            //前面需拼接com.wzsec.exception.BadRequestException:  格式，以免影响之前逻辑
            log.setExceptionDetail("com.wzsec.exception.BadRequestException: " + e.getMessage()+"\n\t" +
                    "at"+exInfo);
            logService.saveLogs(log);
        }finally {
            //上传完成 删除临时文件
            try {
                // 创建 File 对象
                File fileOrDirectory = new File( ConstEngine.sddEngineConfs.get("minio.download.save.localpath") + "/" + fileTaskConfigDto.getTaskname());
                // 检查路径是否存在
                if (fileOrDirectory.exists() && fileOrDirectory.isDirectory()) {
                    // 获取目录及其子目录中的所有文件
                    List<File> inFileList = getAllFilesRecursively(fileOrDirectory);
                    if (ObjectUtil.isNotEmpty(inFileList)){
                        for (File file : inFileList) {
                            // 检查是否是文件（非目录）
                            if (file.isFile()) {
                                // 删除文件
                                if (file.delete()) {
                                    System.out.println("已删除文件: " + file.getName());
                                } else {
                                    System.out.println("无法删除文件: " + file.getName());
                                }
                            } else {
                                System.out.println(file.getName() + " 是一个目录，跳过删除。");
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("删除临时文件失败：{}", e.getMessage());
            }



        }

        // 结束时间
        String endTime = DateUtil.getNowTime();
        // 执行总时间
        String totalTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(startTime, endTime));
        fileTaskResult.setJobendtime(endTime);
        fileTaskResult.setJobtotaltime(totalTime);
        fileTaskResult.setUpdatetime(endTime);
        fileTaskResult.setTotallines(totallines);
        fileTaskResult.setBeforemaskdata(beforemaskdata);
        fileTaskResult.setAftermaskdata(aftermaskdata);

        fileTaskConfig.setSparefield5("100");

        // 状态
        String taskResultStatus = Const.TASK_RESULT_EXECUTE_SUCCESS;
        if (flag) {
            log.info("任务ID【" + id + "】,保存到" + (Const.TASK_DATATYPE_DB.equals(outputType) ? "库表" : "文件") + "成功");
            try {
                if (fileResult != null && StringUtils.isNotEmpty(fileResult.getMiniodata())){
                    String miniodata = fileResult.getMiniodata();
                    List<MinioFileInfo> minioFileNewList = fromJson(miniodata);
                    // 合并文件信息
                    mergeFileInfos(minioFileNewList, minioFileInfos);
                    fileTaskResult.setMiniodata(toJson(minioFileNewList));
                }else {
                    String miniodata = toJson(minioFileInfos);
                    fileTaskResult.setMiniodata(miniodata);
                }
            } catch (Exception e) {
                log.error("解析失败");
                throw new RuntimeException(e);
            }


            // 更新结果
            fileTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_SUCCESS);
            fileTaskResultService.update(fileTaskResult);
            // 更新任务表
            fileTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS);
            fileTaskConfigService.update(fileTaskConfig);
            //插入任务结果记录表
            MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
            maskTaskresultrecords.setTaskname(fileTaskResult.getTaskname());
            maskTaskresultrecords.setTasktype(Const.MASK_TASK_FILE);
            maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE);
            maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
            maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
            maskTaskresultrecordsService.create(maskTaskresultrecords);

            sdkOperationrecord.setObjectname(fileTaskResult.getTaskname());
            sdkOperationrecord.setOperation("文件数据脱敏任务执行成功");
        } else {
            log.info("任务ID【" + id + "】,保存到" + (Const.TASK_DATATYPE_DB.equals(outputType) ? "库表" : "文件") + "失败");
            // 更新结果
            fileTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_FAIL);
            fileTaskResultService.update(fileTaskResult);
            // 更新任务表
            fileTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
            Random random = new Random();
            int randomNumber = random.nextInt((50 - 30) + 1) + 30;
            fileTaskConfig.setSparefield5(String.valueOf(randomNumber));

            fileTaskConfigService.update(fileTaskConfig);

            //插入任务结果记录表
            MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
            maskTaskresultrecords.setTaskname(fileTaskResult.getTaskname());
            maskTaskresultrecords.setTasktype(Const.MASK_TASK_FILE);
            maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE);
            maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
            maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
            maskTaskresultrecordsService.create(maskTaskresultrecords);

            sdkOperationrecord.setObjectname(fileTaskResult.getTaskname());
            sdkOperationrecord.setOperation("文件数据脱敏任务执行失败");

            //将执行文件失败脱敏任务告警推送至syslog
            DmAlarmdisposal dmAlarmdisposal = new DmAlarmdisposal();
            // TODO 事件详情定义
            String str = "数据脱敏：任务ID：{},目录：{},执行时间：{},文件数据脱敏任务执行失败";
            String eventDetails = cn.hutool.core.util.StrUtil.format(str, fileTaskResult.getTaskname(), fileTaskResult.getInputpath(), startTime);
            dmAlarmdisposal.setCircumstantiality(eventDetails); //事件详情
            dmAlarmdisposal.setChecktime(cn.hutool.core.date.DateUtil.now());
            dmAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED); //处置状态
            dmAlarmdisposal.setEventrule(Const.DICT_FILE_EXECUTION_TASK_ERROR);//规则
            try {
                String IP = InetAddress.getLocalHost().getHostAddress();
                String port = environment.getProperty("server.port");
                dmAlarmdisposal.setSourceip(IP);//源ip
                dmAlarmdisposal.setSourceport(port);//源端口
                dmAlarmdisposal.setAccount(fileTaskResult.getUsername());//事件相关用户名
                dmAlarmdisposal.setReservefield2(fileTaskResult.getOutputtype());
                dmAlarmdisposal.setReservefield3("2");
                dmAlarmdisposal.setDestinationip(IP);//目标IP
                dmAlarmdisposal.setDestinationport(port);//目标端口
            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
            dmAlarmdisposalService.create(dmAlarmdisposal);
            //告警推送syslog
            new MonitorRiskAlarmData().sendDmExample(dmAlarmdisposal);

        }

        //插入SDK操作记录
        //TODO ******** 加入if判断 sdkApplyconfig为null 报空指针
        if (ObjectUtil.isNotEmpty(sdkApplyconfig)){
            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
        }
        sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_FILEMASK);
        sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
        sdkOperationrecordRepository.save(sdkOperationrecord);

        //TODO FTP本地缓存文件文件删除
        if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
            File file = new File(localCachePath);
            FileUtil.deleteFiles(file);
        }


        //TODO 将所使用的引擎，任务负载数-1
        if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
            engineServerRepository.reduceKingbaseCountByIpPort(engineIp, enginePort);
        }else {
            engineServerRepository.reduceCountByIpPort(engineIp, enginePort);
        }

        // TODO 脱敏字段异常处置告警,写入告警记录表
        if (!abnormalHandlingAlarmMap.isEmpty()) {
            boolean formatFile = strategyMainDto.getStrategytype().equals(Const.FILETASKTYPE_FORMATTING);
            desensitizationAbnormalAlarm(abnormalHandlingAlarmMap, fileTaskResult, formatFile);
        }

        log.info("任务ID【" + id + "】,执行文件脱敏任务结束");
        log.info("任务ID【" + id + "】,执行完毕");
    }

    public static void mergeFileInfos(List<MinioFileInfo> minioFileNewList,
                                      List<MinioFileInfo> minioFileInfos) {
        // 构建 fileName 到 MinioFileInfo 的映射
        Map<String, MinioFileInfo> fileInfoMap = new HashMap<>();
        for (MinioFileInfo info : minioFileInfos) {
            fileInfoMap.put(info.getFileName(), info);
        }

        // 处理现有元素：更新 lastModified
        for (MinioFileInfo newInfo : minioFileNewList) {
            MinioFileInfo existingInfo = fileInfoMap.get(newInfo.getFileName());
            if (existingInfo != null) {
                newInfo.setLastModified(existingInfo.getLastModified());
            }
        }

        // 添加新元素：处理 minioFileNewList 中不存在的 fileName
        for (Map.Entry<String, MinioFileInfo> entry : fileInfoMap.entrySet()) {
            String fileName = entry.getKey();
            boolean exists = false;

            // 检查是否存在同名文件
            for (MinioFileInfo newInfo : minioFileNewList) {
                if (newInfo.getFileName().equals(fileName)) {
                    exists = true;
                    break;
                }
            }

            // 不存在则添加
            if (!exists) {
                minioFileNewList.add(entry.getValue());
            }
        }
    }

    public static String extractPath(String path) {
        if (path.endsWith("/") && path.length() == 1){
            return "";
        }
        // 查找最后一个 '/' 的位置
        int lastSlashIndex = path.lastIndexOf('/');
        if (lastSlashIndex > 0) {
            // 截取从开头到倒数第二个 '/' 的部分
            return path.substring(0, lastSlashIndex);
        } else {
            return path; // 如果没有 '/'，返回原始路径
        }
    }

    /**
     * 文件不存在就创建
     * @param fileOutPath
     */
    private void createFile(String fileOutPath) {
        // 创建 File 对象
        File file = new File(fileOutPath);

        // 获取文件的父目录
        File parentDir = file.getParentFile();

        // 如果父目录不存在，创建父目录
        if (parentDir != null && !parentDir.exists()) {
            boolean isDirCreated = parentDir.mkdirs();
            if (!isDirCreated) {
                System.out.println("无法创建父目录: " + parentDir.getAbsolutePath());
                return;
            }
        }

        // 检查文件是否存在
        if (!file.exists()) {
            try {
                // 创建文件
                boolean isCreated = file.createNewFile();
                if (isCreated) {
                    System.out.println("文件已创建: " + file.getAbsolutePath());
                } else {
                    System.out.println("文件创建失败！");
                }
            } catch (IOException e) {
                System.out.println("创建文件时发生错误: " + e.getMessage());
            }
        } else {
            System.out.println("文件已存在: " + file.getAbsolutePath());
        }
    }

    /**
     * 获取文件类型后缀
     * @param inputfiletype
     * @return
     */
    private String queryFileType(String inputfiletype) {
        switch (inputfiletype) {
            case "1":
                return "txt";
            case "2":
                return "xlsx";
            case "3":
                return "csv";
            case "4":
                return "dat";
            case "5":
                return "sql";
            case "6":
                return "dmp";
            case "7":
                return "del";
            case "8":
                return "json";
            case "9":
                return "xml";
            case "10":
                return "dcm";
            case "11":
                return "dic";
            case "12":
                return "doc";
            case "13":
                return "docx";
            case "14":
                return "pdf";
            case "15":
                return "ppt";
            case "16":
                return "pptx";
            case "17":
                return "png";
            case "18":
                return "jpg";
        }
        log.error("未知文件类型：{}",inputfiletype);
        return null;
    }

    /**
     * 设置文件名
     * @param fileTaskResult
     * @param outputName
     * @return com.wzsec.modules.mask.domain.FileTaskResult
     * <AUTHOR>
     * @Date 11:22 2025/1/22
     **/
    private FileTaskResult setOutputname(FileTaskResult fileTaskResult, String outputName) {
        String filenames = fileTaskResult.getFilenames();
        if (StringUtils.isEmpty(filenames)){
            fileTaskResult.setFilenames(outputName);
        }else {
            filenames = filenames + ";" + outputName;
            fileTaskResult.setFilenames(filenames);
        }
        return fileTaskResult;
    }


    /**
     * 脱敏字段异常处置告警,写入告警记录表(非主干代码记录日志异常仅输出警告)
     *
     * @param abnormalHandlingAlarmMap 异常处置方式
     * @param fileTaskResult           文件任务结果
     * @param formatFile               是否为格式化文件
     */
    private void desensitizationAbnormalAlarm(Map<String, String> abnormalHandlingAlarmMap,
                                              FileTaskResult fileTaskResult,
                                              boolean formatFile) {
        try {
            // 拼接字段处置方式
            StringBuilder result = new StringBuilder();
            Set<String> nonFormattedSet = new HashSet<>();
            for (Map.Entry<String, String> entry : abnormalHandlingAlarmMap.entrySet()) {
                if (formatFile) {
                    result.append("列:").append(entry.getKey()).append(", 异常数据处置方式:").append(entry.getValue()).append("; ");
                } else {
                    nonFormattedSet.add(entry.getValue());
                }
            }

            // 如果需要格式化，去掉最后一个 "; "
            if (formatFile && result.length() > 0) {
                result.setLength(result.length() - 2);  // 去除最后的 "; "
            } else if (!formatFile && !nonFormattedSet.isEmpty()) {
                result.append("异常数据处置方式:").append(String.join(", ", nonFormattedSet));
            }


            String template = "文件脱敏任务名:{}, 输入路径:{}, {} ";
            String alarmDetails = StrUtil.format(template, fileTaskResult.getTaskname(), fileTaskResult.getInputpath(), result);

            String ip = InetAddress.getLocalHost().getHostAddress();
            String port = environment.getProperty("server.port");
            DmAlarmdisposal maskAlarmdisposal = new DmAlarmdisposal();
            maskAlarmdisposal.setCircumstantiality(alarmDetails);
            maskAlarmdisposal.setChecktime(DateUtil.getNowTime());
            maskAlarmdisposal.setReservefield2(fileTaskResult.getOutputtype());
            maskAlarmdisposal.setReservefield3(Const.RISK_MIDDLE); //事件等级-中
            maskAlarmdisposal.setSourceip(ip);
            maskAlarmdisposal.setSourceport(port);
            maskAlarmdisposal.setDestinationip(ip);
            maskAlarmdisposal.setDestinationport(port);
            maskAlarmdisposal.setAccount(fileTaskResult.getUsername());//事件相关用户名
            maskAlarmdisposal.setEventrule(Const.DICT_DESENSITIZATION_ABNORMAL_DATA_HANDLING); //事件名称
            dmAlarmdisposalService.create(maskAlarmdisposal);
            //告警推送syslog
            MonitorRiskAlarmData.sendDmExample(maskAlarmdisposal);
        } catch (UnknownHostException e) {
            log.warn("写入统计查询-告警记录或推送syslog异常");
        }
    }


    @Override
    public Object getMaskResultPreview(Integer id) {
        List<Object> maskDataList = new ArrayList<>();
        try {
            FileTaskConfigDto fileTaskConfigDto = fileTaskConfigService.findById(id);
            //输入数据源信息
            String inPutDataSourceType = null, inPutDatasourceIp = null, inPutDatasourceAccount = null, inPutDatasourcePassword = null;
            Integer inPutDatasourcePort = null;
            if (fileTaskConfigDto.getInputdatasourceid() != null) {
                Long inPutSourceId = Long.valueOf(fileTaskConfigDto.getInputdatasourceid());
                DatasourceDto inPutDatasource = datasourceService.findById(inPutSourceId);
                inPutDataSourceType = inPutDatasource.getType();//数据源类型
                String[] hostNameAndPort = inPutDatasource.getSrcurl().split(":|：");
                inPutDatasourceIp = hostNameAndPort[0];//数据源ip
                inPutDatasourcePort = hostNameAndPort.length > 1 ? Integer.valueOf(hostNameAndPort[1]) : Const.DEFAULT_PORT;//数据源端口
                inPutDatasourceAccount = inPutDatasource.getUsername();//数据源账号
                inPutDatasourcePassword = inPutDatasource.getPassword();//数据源密码
                if (StringUtils.isNotEmpty(inPutDatasourcePassword)) {
                    try {
                        String decrypt = AES.decrypt(inPutDatasourcePassword, Const.AES_SECRET_KEY);
                        inPutDatasourcePassword = decrypt;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            Integer strategyid = fileTaskConfigDto.getStrategyid();
            // 根据策略id查询脱敏策略( 使用文件的脱敏策略类 )
            MaskStrategyFileMainDto strategyMainDto = maskStrategyFileMainService.findById(strategyid);

            //远程文件本地缓存路径 配置文件路径 + 当前任务的任务号
            String localCachePath = ConstEngine.sddEngineConfs.get("ftp.download.save.localpath") + File.separator + fileTaskConfigDto.getTaskname();
            String inputPath = fileTaskConfigDto.getInputpath();//输入路径

            //TODO FTP文件缓存到本地
            if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                String localInPath = localCachePath + File.separator + "in";
                // 指定目录路径
                Path directoryPath = Paths.get(localInPath);
                // 判断目录是否存在
                if (!Files.exists(directoryPath)) {
                    Files.createDirectories(directoryPath);
                }
                boolean downloadFileFlag = true;
                if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType)) {
                    downloadFileFlag = SFTPUtil.downloadFile(inPutDatasourceIp, inPutDatasourcePort, inPutDatasourceAccount, inPutDatasourcePassword, inputPath, localInPath);
                } else if (Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                    downloadFileFlag = FTPUtil.downloadFileByFilePath(inPutDatasourceIp, inPutDatasourcePort, inPutDatasourceAccount, inPutDatasourcePassword, inputPath, localInPath);
                }
                if (!downloadFileFlag) {
                    throw new Exception(inputPath + "路径下内容，缓存到本地失败");
                }
                inputPath = localInPath;
            }

            List<String> beforeMaskDataList = new ArrayList<>();
            //只取1个文件，且只取前5条数据
            List<HashMap<String, List<String>>> lineDataList = FileReadService.readDirFirstFile(fileTaskConfigDto, inputPath);
            for (Map.Entry<String, List<String>> entry : lineDataList.get(0).entrySet()) {
                List<String> value = entry.getValue();
                beforeMaskDataList = value.stream()
                        .limit(5)
                        .collect(Collectors.toList());
                break;
            }

            //文件分隔符
            String splitstr = fileTaskConfigDto.getSplitstr();
            // 判断策略是格式化文件策略还是非格式化文件策略
            if ("0".equals(strategyMainDto.getStrategytype())) {
                // 格式化文件策略
                String postionAlgoParasStrByStrategyName = fileTaskConfigDto.getMaskstrategystr();

                HashMap<String, Algorithm> algorithmMap = algorithmService.getAlgorithmByEName();
                List<String> afterMaskDataList = FileReadService.getMaskDataList(beforeMaskDataList, postionAlgoParasStrByStrategyName, splitstr, algorithmMap);
                for (int i = 0; i < beforeMaskDataList.size(); i++) {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("beforemaskdata", beforeMaskDataList.get(i));
                    dataMap.put("aftermaskdata", afterMaskDataList.get(i));
                    maskDataList.add(dataMap);
                }
            }
            //TODO  暂不考虑非格式化文件脱敏后预览效果


            //TODO FTP本地缓存文件文件删除
            if (Const.FILE_SFTP.equalsIgnoreCase(inPutDataSourceType) || Const.FILE_FTP.equalsIgnoreCase(inPutDataSourceType)) {
                File file = new File(localCachePath);
                FileUtil.deleteFiles(file);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            return maskDataList;
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(1 % 100);
//        String filepathInput = "D:\\testfile\\mark\\userinfo.xlsx";
//        String filepathOutput = "D:\\testfile\\mark\\userinfo_mark.xlsx";
//        String dataProvider = "xxx大数据局";
//        String dataUser = "xxx教育局";
//        String IDs = String.format("数据提供方:%s,数据使用方:%s", dataProvider, dataUser);
//        String displayIDs = String.format("仅限%s单位使用", dataUser);
//        Boolean edit = true;
//        String password = null;
//        String split = ",";
//        String algorithmValue = "1";
//        WaterMark watermark = new WaterMark();
//        Boolean isSuccess = watermark.AddWaterMarkInfo(filepathInput, filepathOutput, IDs, displayIDs, edit, null, split, algorithmValue);
//        System.out.println("水印是否加注成功:" + isSuccess);
//        String wmsrc = watermark.TraceWaterMarkInfo(filepathOutput);
//        System.out.println("水印溯源信息:" + wmsrc);
//
    }

    /**
     * 递归获取目录及其子目录中的所有文件
     *
     * @param directory 目录
     * @return 包含所有文件的列表
     */
    public static List<File> getAllFilesRecursively(File directory) {
        List<File> fileList = new ArrayList<>();

        // 获取目录下的所有文件和子目录
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                // 如果是文件，添加到列表中
                if (file.isFile()) {
                    fileList.add(file);
                } else if (file.isDirectory()) {
                    // 如果是子目录，递归获取子目录中的文件
                    fileList.addAll(getAllFilesRecursively(file));
                }
            }
        }
        return fileList;
    }

    private static final ObjectMapper mapper = new ObjectMapper();

    // 将列表转换为JSON字符串
    public static String toJson(List<MinioFileInfo> fileInfoList) throws Exception {
        return mapper.writeValueAsString(fileInfoList);
    }

    // 将JSON字符串转换回列表
    public static List<MinioFileInfo> fromJson(String json) throws Exception {
        return mapper.readValue(json, new TypeReference<List<MinioFileInfo>>() {});
    }
}
