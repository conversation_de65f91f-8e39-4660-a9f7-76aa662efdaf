package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskVideotaskconfig;
import com.wzsec.modules.mask.service.dto.MaskVideotaskconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskVideotaskconfigMapperImpl implements MaskVideotaskconfigMapper {

    @Override
    public MaskVideotaskconfigDto toDto(MaskVideotaskconfig entity) {
        if ( entity == null ) {
            return null;
        }

        MaskVideotaskconfigDto maskVideotaskconfigDto = new MaskVideotaskconfigDto();

        maskVideotaskconfigDto.setComputeresources( entity.getComputeresources() );
        maskVideotaskconfigDto.setCreatetime( entity.getCreatetime() );
        maskVideotaskconfigDto.setCreateuser( entity.getCreateuser() );
        maskVideotaskconfigDto.setExecutionstate( entity.getExecutionstate() );
        maskVideotaskconfigDto.setId( entity.getId() );
        maskVideotaskconfigDto.setInputdirectory( entity.getInputdirectory() );
        maskVideotaskconfigDto.setInputfileformat( entity.getInputfileformat() );
        maskVideotaskconfigDto.setMaskobject( entity.getMaskobject() );
        maskVideotaskconfigDto.setOutputdirectory( entity.getOutputdirectory() );
        maskVideotaskconfigDto.setOutputfileformat( entity.getOutputfileformat() );
        maskVideotaskconfigDto.setRemark( entity.getRemark() );
        maskVideotaskconfigDto.setSparefield1( entity.getSparefield1() );
        maskVideotaskconfigDto.setSparefield2( entity.getSparefield2() );
        maskVideotaskconfigDto.setSparefield3( entity.getSparefield3() );
        maskVideotaskconfigDto.setSparefield4( entity.getSparefield4() );
        maskVideotaskconfigDto.setSparefield5( entity.getSparefield5() );
        maskVideotaskconfigDto.setState( entity.getState() );
        maskVideotaskconfigDto.setTaskname( entity.getTaskname() );
        maskVideotaskconfigDto.setUpdatetime( entity.getUpdatetime() );
        maskVideotaskconfigDto.setUpdateuser( entity.getUpdateuser() );

        return maskVideotaskconfigDto;
    }

    @Override
    public List<MaskVideotaskconfigDto> toDto(List<MaskVideotaskconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskVideotaskconfigDto> list = new ArrayList<MaskVideotaskconfigDto>( entityList.size() );
        for ( MaskVideotaskconfig maskVideotaskconfig : entityList ) {
            list.add( toDto( maskVideotaskconfig ) );
        }

        return list;
    }

    @Override
    public MaskVideotaskconfig toEntity(MaskVideotaskconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskVideotaskconfig maskVideotaskconfig = new MaskVideotaskconfig();

        maskVideotaskconfig.setComputeresources( dto.getComputeresources() );
        maskVideotaskconfig.setCreatetime( dto.getCreatetime() );
        maskVideotaskconfig.setCreateuser( dto.getCreateuser() );
        maskVideotaskconfig.setExecutionstate( dto.getExecutionstate() );
        maskVideotaskconfig.setId( dto.getId() );
        maskVideotaskconfig.setInputdirectory( dto.getInputdirectory() );
        maskVideotaskconfig.setInputfileformat( dto.getInputfileformat() );
        maskVideotaskconfig.setMaskobject( dto.getMaskobject() );
        maskVideotaskconfig.setOutputdirectory( dto.getOutputdirectory() );
        maskVideotaskconfig.setOutputfileformat( dto.getOutputfileformat() );
        maskVideotaskconfig.setRemark( dto.getRemark() );
        maskVideotaskconfig.setSparefield1( dto.getSparefield1() );
        maskVideotaskconfig.setSparefield2( dto.getSparefield2() );
        maskVideotaskconfig.setSparefield3( dto.getSparefield3() );
        maskVideotaskconfig.setSparefield4( dto.getSparefield4() );
        maskVideotaskconfig.setSparefield5( dto.getSparefield5() );
        maskVideotaskconfig.setState( dto.getState() );
        maskVideotaskconfig.setTaskname( dto.getTaskname() );
        maskVideotaskconfig.setUpdatetime( dto.getUpdatetime() );
        maskVideotaskconfig.setUpdateuser( dto.getUpdateuser() );

        return maskVideotaskconfig;
    }

    @Override
    public List<MaskVideotaskconfig> toEntity(List<MaskVideotaskconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskVideotaskconfig> list = new ArrayList<MaskVideotaskconfig>( dtoList.size() );
        for ( MaskVideotaskconfigDto maskVideotaskconfigDto : dtoList ) {
            list.add( toEntity( maskVideotaskconfigDto ) );
        }

        return list;
    }
}
