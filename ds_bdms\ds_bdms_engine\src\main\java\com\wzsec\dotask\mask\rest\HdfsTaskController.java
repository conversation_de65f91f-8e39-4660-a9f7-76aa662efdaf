package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoHdfsTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.SecurityUtils;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-11-16
 */
// @Api(tags = "HDFS数据脱敏任务管理")
@RestController
@RequestMapping("/engine/hdfs/task")
public class HdfsTaskController {

    private final DoHdfsTaskService doHdfsTaskService;

    private final UserService userService;

    public HdfsTaskController(DoHdfsTaskService doHdfsTaskService, UserService userService) {
        this.doHdfsTaskService = doHdfsTaskService;
        this.userService = userService;
    }

    @Log("执行数据源脱敏任务")
    // @ApiOperation("执行数据源脱敏任务")
    @PostMapping(value = "/exec/{id}")
    @PreAuthorize("@el.check('hadoopTaskConfig:edit')")
    public ResponseEntity<Object> execution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doHdfsTaskService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
