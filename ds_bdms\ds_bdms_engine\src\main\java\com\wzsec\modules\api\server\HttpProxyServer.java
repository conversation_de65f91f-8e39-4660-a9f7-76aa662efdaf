package com.wzsec.modules.api.server;

import com.wzsec.modules.api.crt.CertPool;
import com.wzsec.modules.api.crt.CertUtil;
import com.wzsec.modules.api.exception.HttpProxyExceptionHandle;
import com.wzsec.modules.api.handler.HttpProxyServerHandler;
import com.wzsec.modules.api.intercept.HttpProxyInterceptInitializer;
import com.wzsec.modules.api.intercept.HttpTunnelIntercept;
import com.wzsec.modules.api.proxy.ProxyConfig;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.SslHandler;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import lombok.extern.slf4j.Slf4j;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class HttpProxyServer {


    //http代理隧道握手成功
    public final static HttpResponseStatus SUCCESS = new HttpResponseStatus(200,
            "Connection established");
    public final static HttpResponseStatus UNAUTHORIZED = new HttpResponseStatus(407,
            "Unauthorized");

    private HttpProxyCACertFactory caCertFactory;
    private HttpProxyServerConfig serverConfig;
    private HttpProxyInterceptInitializer proxyInterceptInitializer;
    private HttpTunnelIntercept tunnelIntercept;
    private HttpProxyExceptionHandle httpProxyExceptionHandle;
    private ProxyConfig proxyConfig;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;

    private Boolean startSSL = false;

    private void init() {
        if (serverConfig == null) {
            serverConfig = new HttpProxyServerConfig();
        }
        serverConfig.setProxyLoopGroup(new NioEventLoopGroup(serverConfig.getProxyGroupThreads()));

        if (serverConfig.isHandleSsl()) {
            // 张坤祥 2021年6月22日19:32:28  作者遗留问题
            // 此块代码作用不大，serverConfig中多数属性未使用，如果isHandleSsl为fasle，HttpProxyInitializer类中会报null
            try {
                serverConfig.setClientSslCtx(
                        SslContextBuilder.forClient().trustManager(InsecureTrustManagerFactory.INSTANCE)
                                .build());
                ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
                X509Certificate caCert;
                PrivateKey caPriKey;
                if (caCertFactory == null) {
                    caCert = CertUtil.loadCert(classLoader.getResourceAsStream("ca.crt"));
                    caPriKey = CertUtil.loadPriKey(classLoader.getResourceAsStream("ca_private.der"));
                } else {
                    caCert = caCertFactory.getCACert();
                    caPriKey = caCertFactory.getCAPriKey();
                }
                //读取CA证书使用者信息
                serverConfig.setIssuer(CertUtil.getSubject(caCert));
                //读取CA证书有效时段(server证书有效期超出CA证书的，在手机上会提示证书不安全)
                serverConfig.setCaNotBefore(caCert.getNotBefore());
                serverConfig.setCaNotAfter(caCert.getNotAfter());
                //CA私钥用于给动态生成的网站SSL证书签证
                serverConfig.setCaPriKey(caPriKey);
                //生产一对随机公私钥用于网站SSL证书动态创建
                KeyPair keyPair = CertUtil.genKeyPair();
                serverConfig.setServerPriKey(keyPair.getPrivate());
                serverConfig.setServerPubKey(keyPair.getPublic());
            } catch (Exception e) {
                serverConfig.setHandleSsl(false);
                log.warn("SSL init fail,cause:" + e.getMessage());
            }
        }
        if (proxyInterceptInitializer == null) {
            proxyInterceptInitializer = new HttpProxyInterceptInitializer();
        }
        if (httpProxyExceptionHandle == null) {
            httpProxyExceptionHandle = new HttpProxyExceptionHandle();
        }
    }

    public HttpProxyServer serverConfig(HttpProxyServerConfig serverConfig) {
        this.serverConfig = serverConfig;
        return this;
    }

    public HttpProxyServer proxyInterceptInitializer(
            HttpProxyInterceptInitializer proxyInterceptInitializer) {
        this.proxyInterceptInitializer = proxyInterceptInitializer;
        return this;
    }

    public HttpProxyServer httpProxyExceptionHandle(
            HttpProxyExceptionHandle httpProxyExceptionHandle) {
        this.httpProxyExceptionHandle = httpProxyExceptionHandle;
        return this;
    }

    public HttpProxyServer proxyConfig(ProxyConfig proxyConfig) {
        this.proxyConfig = proxyConfig;
        return this;
    }

    public HttpProxyServer caCertFactory(HttpProxyCACertFactory caCertFactory) {
        this.caCertFactory = caCertFactory;
        return this;
    }

    public HttpProxyServer tunnelIntercept(HttpTunnelIntercept tunnelIntercept) {
        this.tunnelIntercept = tunnelIntercept;
        return this;
    }

    public HttpProxyServer startSSL(Boolean startSSL) {
        this.startSSL = startSSL;
        return this;
    }


    public void start(int port) {
        start(null, port);
    }

    public void start(String ip, int port) {
        try {
            ChannelFuture channelFuture = doBind(ip, port);
            CountDownLatch latch = new CountDownLatch(1);
            channelFuture.addListener(future -> {
                if (future.cause() != null) {
                    httpProxyExceptionHandle.startCatch(future.cause());
                }
                latch.countDown();
            });
            latch.await();
            channelFuture.channel().closeFuture().sync();
        } catch (Exception e) {
            httpProxyExceptionHandle.startCatch(e);
        } finally {
            close();
        }
    }

    public CompletionStage<Void> startAsync(int port) throws Exception {
        return startAsync(null, port);
    }

    public CompletionStage<Void> startAsync(String ip, int port) throws Exception {
        ChannelFuture channelFuture = doBind(ip, port);

        CompletableFuture<Void> future = new CompletableFuture<>();
        channelFuture.addListener(start -> {
            if (start.isSuccess()) {
                future.complete(null);
                shutdownHook();
            } else {
                future.completeExceptionally(start.cause());
                close();
            }
        });
        return future;
    }

    private ChannelFuture doBind(String ip, int port) throws Exception {
        init();
        bossGroup = new NioEventLoopGroup(serverConfig.getBossGroupThreads());
        workerGroup = new NioEventLoopGroup(serverConfig.getWorkerGroupThreads());

        //SSL证书
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        X509Certificate caCert = CertUtil.loadCert(classLoader.getResourceAsStream("ca.crt"));
        PrivateKey caPriKey = CertUtil.loadPriKey(classLoader.getResourceAsStream("ca_private.der"));
        SslContext sslContext = SslContextBuilder.forServer(caPriKey, caCert).build();

        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                //                .option(ChannelOption.SO_BACKLOG, 100)
                .handler(new LoggingHandler(LogLevel.DEBUG))
                .childHandler(new ChannelInitializer<Channel>() {


                    @Override
                    protected void initChannel(Channel ch) throws Exception {

                        // 张坤祥 2021年6月22日19:36:28
                        // 此块此块代码用于加入SSL支持，实现HTTPS访问，设置后HTTP无法访问
                        if (startSSL) {
                            // 加入SslHandler实现HTTPS
                            SslHandler sslHandler = sslContext.newHandler(ch.alloc());
                            ch.pipeline().addLast("sslHandle", sslHandler);
                        }

                        ch.pipeline().addLast("httpCodec", new HttpServerCodec());
                        ch.pipeline().addLast("serverHandle",
                                new HttpProxyServerHandler(serverConfig, proxyInterceptInitializer, tunnelIntercept, proxyConfig,
                                        httpProxyExceptionHandle));
                    }
                });
        log.info("DDM_API Service Started Successfully, Port: " + port);
        return ip == null ? bootstrap.bind(port) : bootstrap.bind(ip, port);
    }

    /**
     * 释放资源
     */
    public void close() {
        EventLoopGroup eventLoopGroup = serverConfig.getProxyLoopGroup();
        if (!(eventLoopGroup.isShutdown() || eventLoopGroup.isShuttingDown())) {
            eventLoopGroup.shutdownGracefully();
        }
        if (!(bossGroup.isShutdown() || bossGroup.isShuttingDown())) {
            bossGroup.shutdownGracefully();
        }
        if (!(workerGroup.isShutdown() || workerGroup.isShuttingDown())) {
            workerGroup.shutdownGracefully();
        }

        CertPool.clear();
    }

    /**
     * 注册JVM关闭的钩子以释放资源
     */
    public void shutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(this::close, "Server Shutdown Thread"));
    }

}
