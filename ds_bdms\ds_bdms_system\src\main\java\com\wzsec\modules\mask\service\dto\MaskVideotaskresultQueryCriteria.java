package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2022-04-21
*/
@Data
public class MaskVideotaskresultQueryCriteria{

    /** 精确 */
    @Query
    private String taskname;

    /** 精确 */
    @Query
    private String taskstatus;

    /** 精确 */
    @Query
    private String inputdirectory;

    /** 精确 */
    @Query
    private String inputfileformat;

    /** 精确 */
    @Query
    private String beforepicamount;

    /** 精确 */
    @Query
    private String maskobject;

    /** 精确 */
    @Query
    private String outputdirectory;

    /** 精确 */
    @Query
    private String outputfileformat;

    /** 精确 */
    @Query
    private String afterpicamount;
    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<String> createtime;

    @Query(blurry = "taskname,taskstatus,inputdirectory,inputfileformat,beforepicamount,maskobject,outputdirectory,outputfileformat,afterpicamount")
    private String blurry;
}
