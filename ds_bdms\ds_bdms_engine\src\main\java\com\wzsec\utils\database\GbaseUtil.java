package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;


/**
 * 创建GBASE表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description 通过java List<Map<String,
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2019年11月4日
 */
public class GbaseUtil extends DatabaseUtil {

    private final static Logger log = LoggerFactory.getLogger(DatabaseUtil.class);

    private static String JDBC_DRIVER = "com.gbase.jdbc.Driver";

    /**
     * @Title: getInsert2TableSqlAndPatams
     * @Description: TODO
     * <AUTHOR>
     * @date 2019年11月4日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Title: getTableFieldInfo
     * @Description: 查询表字段信息
     * <AUTHOR>
     * @date 2019年11月4日
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            Class.forName(JDBC_DRIVER);
            conn = DriverManager.getConnection(dburl, username, password);
            String sql = "select column_name,column_comment,data_type from information_schema.columns where table_name='表名' and table_schema='数据库名'";
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery("SHOW FULL COLUMNS FROM `" + tableName + "`;");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("Field", rs.getString("Field"));
                    fieldInfoMap.put("Type", rs.getString("Type"));
                    fieldInfoMap.put("Null", rs.getString("Null"));
                    fieldInfoMap.put("Key", rs.getString("Key"));
                    fieldInfoMap.put("Default", rs.getString("Default"));
                    fieldInfoMap.put("Extra", rs.getString("Extra"));
                    fieldInfoMap.put("Privileges", rs.getString("Privileges"));
                    fieldInfoMap.put("Comment", rs.getString("Comment"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        closeCon(rs, stmt, conn);
        return fieldInfoList;
    }

    /**
     * @Title: getInsert2TableSqlAndPatams
     * @Description: TODO
     * <AUTHOR>
     * @date 2019年11月4日
     */
    //@Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Title: getInsert2TableSqlAndPatams
     * @Description: TODO
     * <AUTHOR>
     * @date 2019年11月4日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Title: getCreateTableSql
     * @Description: TODO
     * <AUTHOR>
     * @date 2019年11月4日
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                String fieldname = fieldInfo.get("Field");
                if (!obj.keySet().contains(fieldname)) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldname).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldname)) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        if (!"NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                            sb.append(" DEFAULT NULL");
                        }
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r) DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @Title: getCreateTableSql
     * @Description: TODO
     * <AUTHOR>
     * @date 2019年11月4日
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                String fieldname = fieldInfo.get("Field");
                if (!obj.keySet().contains(fieldname)) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldname).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldname)) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        if (!"NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                            sb.append(" DEFAULT NULL");
                        }
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r) DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    /**
     * @param rs：结果集
     * @param st：预编译的SQL语句的对象
     * @param conn：连接对象
     * @return void：
     * @Description 释放连接
     * <AUTHOR>
     * @date 2019年10月15日 上午9:46:42
     */
    public static void closeCon(ResultSet rs, Statement st, Connection conn) {
        try {
            if (rs != null) {
                rs.close(); // 关闭结果集
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (st != null) {
                    st.close(); // 关闭Statement
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null) {
                        conn.close(); // 关闭连接
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @Description:获取数据库连接
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static Connection getConn(String jdbcDriver, String url, String username, String password) {
        Connection conn = null;
        try {
            conn = DatabaseUtil.getConn(JDBC_DRIVER, url, username, password);// 打开连接
        } catch (Exception ex) {
            System.out.println("获取数据库连接出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        }
        return conn;
    }

    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn,
                                                        String dbnames) {
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = new HashMap<String, String>();
        try {
            String strSQL = "select `table_schema`,`table_name` from `information_schema`.tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                boolean containsFlag = dbnames.contains(",");
                String[] split = dbnames.split(",");
                if (containsFlag && split.length > 1){
                    String sql = " where `table_schema` in (";
                    for (int i = 0; i < split.length; i++) {
                        if (i == 0){
                            sql = sql + "'" + split[i] + "'";
                        }else {
                            sql = sql + ",'" + split[i] + "'";
                        }
                    }
                    sql = sql + ") ";
                    strSQL = strSQL + sql;
                }else {
                    strSQL += " where `table_schema` in ('" + dbnames + "') ";
                }
            }
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String dbName = rs.getString(1);
                String table_name = rs.getString(2);
                if (dbTabMap.containsKey(dbName)) {
                    dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + table_name);
                } else {
                    dbTabMap.put(dbName, table_name);
                }
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            // log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    /**
     * @Description:获取数据库表中数据
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += "  LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                //                StringBuffer sub = new StringBuffer();
//                for (int i = 1; i <= columnCount; i++) {
//                    if (i > 1) {
//                        sub.append(";;");
//                    }
//                    sub.append(rs.getObject(i));
//                }
//                tabDataList.add(sub.toString());

                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }


    /**
     * @Description:获取数据库某个字段非空数据
     * <AUTHOR>
     * @date 2021-03-12
     */
    public static List<String[]> getFieldDataList(Connection conn, String dbname, String tabname, String field, Integer lineNum) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select " + field + " from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " where " + field + " is not null and " + field + " !=''  LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(Connection conn, String dbname,
                                                String tabname) {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname
                    + "' and table_schema='" + dbname + "' ";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } catch (Exception ex) {
            // log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return list;
    }

    public static void main(String[] args) {
        String dburl = "****************************************************************************";
        String username = "root";
        String password = "root";
        String tableName = "t_userinfo";

        try {
            Connection conn = DriverManager.getConnection(dburl, username, password);
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SHOW FULL COLUMNS FROM `" + tableName + "`;");
            if (rs != null) {
                while (rs.next()) {
                    System.out.println(rs.getString("Field"));
                }
            }
        } catch (SQLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
/*		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {
			Class.forName(JDBC_DRIVER);
			conn = (Connection) DriverManager.getConnection(dburl,username,password);
			//String sql = "select column_name,column_comment,data_type from information_schema.columns where table_name='表名' and table_schema='数据库名'";
			stmt = (PreparedStatement) conn.createStatement();// 执行创建表
			rs = stmt.executeQuery("SHOW FULL COLUMNS FROM `" + tableName + "`;");
			if (rs != null) {
				while (rs.next()) {
					System.out.println(rs.getString("Field"));
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}*/
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT COLUMN_NAME,COLUMN_COMMENT FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COLUMN_COMMENT"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表信息
     * @Description 查询表信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:56
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            tableInfoMap = new HashMap<String, String>();
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TABLE_NAME,TABLE_COMMENT,TABLE_ROWS,ROUND(((data_length + index_length) / 1024 / 1024), 2) AS Size_MB FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
                tableInfoMap.put("tableRows", rs.getString("TABLE_ROWS"));
                tableInfoMap.put("dataSize", rs.getString("Size_MB"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }

        return tableInfoMap;
    }

    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 15:19
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        String driverProgram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");

        // 如果密码经过加密，则进行解密（假设AES加密，需要您自己的AES解密方法）
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败");
                return msgMap;
            }
        }

        String dbname = map.get("dbname");
        String srcport = map.get("srcport");
        String srcip = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 加载GBase JDBC驱动
            Class.forName(driverProgram);
            // 构建GBase JDBC URL（请根据实际驱动文档调整URL格式）
            String jdbcUrl = "jdbc:gbase://" + srcip + ":" + srcport + "/gbase"; // 注意GBase可能不需要在URL中指定数据库名
            // 建立连接
            connection = DriverManager.getConnection(jdbcUrl, username, password);
            // 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            resultSet = metaData.getCatalogs();
            boolean databaseExists = false;
            while (resultSet.next()) {
                String databaseName = resultSet.getString(1);
                if (dbname.equals(databaseName)) {
                    databaseExists = true;
                    break;
                }
            }
            // 如果数据库不存在，则创建数据库
            if (!databaseExists) {
                statement = connection.createStatement();
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }

        } catch (Exception e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 关闭资源
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return msgMap;
    }
}
