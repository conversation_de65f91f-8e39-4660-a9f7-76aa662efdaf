package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditJarLogResult;
import com.wzsec.modules.mask.service.dto.MaskAuditJarLogResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskAuditJarLogResultMapperImpl implements MaskAuditJarLogResultMapper {

    @Override
    public MaskAuditJarLogResultDto toDto(MaskAuditJarLogResult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditJarLogResultDto maskAuditJarLogResultDto = new MaskAuditJarLogResultDto();

        maskAuditJarLogResultDto.setAccount( entity.getAccount() );
        maskAuditJarLogResultDto.setAlgorithmname( entity.getAlgorithmname() );
        maskAuditJarLogResultDto.setAlgorithmpackagename( entity.getAlgorithmpackagename() );
        maskAuditJarLogResultDto.setChecktime( entity.getChecktime() );
        maskAuditJarLogResultDto.setEndtime( entity.getEndtime() );
        maskAuditJarLogResultDto.setId( entity.getId() );
        maskAuditJarLogResultDto.setIpaddress( entity.getIpaddress() );
        maskAuditJarLogResultDto.setIssuccessful( entity.getIssuccessful() );
        maskAuditJarLogResultDto.setLinenumber( entity.getLinenumber() );
        maskAuditJarLogResultDto.setNote( entity.getNote() );
        maskAuditJarLogResultDto.setSparefield1( entity.getSparefield1() );
        maskAuditJarLogResultDto.setSparefield2( entity.getSparefield2() );
        maskAuditJarLogResultDto.setSparefield3( entity.getSparefield3() );
        maskAuditJarLogResultDto.setSparefield4( entity.getSparefield4() );
        maskAuditJarLogResultDto.setStarttime( entity.getStarttime() );
        maskAuditJarLogResultDto.setStrategy( entity.getStrategy() );
        maskAuditJarLogResultDto.setTaskname( entity.getTaskname() );
        maskAuditJarLogResultDto.setTotaltime( entity.getTotaltime() );

        return maskAuditJarLogResultDto;
    }

    @Override
    public List<MaskAuditJarLogResultDto> toDto(List<MaskAuditJarLogResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditJarLogResultDto> list = new ArrayList<MaskAuditJarLogResultDto>( entityList.size() );
        for ( MaskAuditJarLogResult maskAuditJarLogResult : entityList ) {
            list.add( toDto( maskAuditJarLogResult ) );
        }

        return list;
    }

    @Override
    public MaskAuditJarLogResult toEntity(MaskAuditJarLogResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditJarLogResult maskAuditJarLogResult = new MaskAuditJarLogResult();

        maskAuditJarLogResult.setAccount( dto.getAccount() );
        maskAuditJarLogResult.setAlgorithmname( dto.getAlgorithmname() );
        maskAuditJarLogResult.setAlgorithmpackagename( dto.getAlgorithmpackagename() );
        maskAuditJarLogResult.setChecktime( dto.getChecktime() );
        maskAuditJarLogResult.setEndtime( dto.getEndtime() );
        maskAuditJarLogResult.setId( dto.getId() );
        maskAuditJarLogResult.setIpaddress( dto.getIpaddress() );
        maskAuditJarLogResult.setIssuccessful( dto.getIssuccessful() );
        maskAuditJarLogResult.setLinenumber( dto.getLinenumber() );
        maskAuditJarLogResult.setNote( dto.getNote() );
        maskAuditJarLogResult.setSparefield1( dto.getSparefield1() );
        maskAuditJarLogResult.setSparefield2( dto.getSparefield2() );
        maskAuditJarLogResult.setSparefield3( dto.getSparefield3() );
        maskAuditJarLogResult.setSparefield4( dto.getSparefield4() );
        maskAuditJarLogResult.setStarttime( dto.getStarttime() );
        maskAuditJarLogResult.setStrategy( dto.getStrategy() );
        maskAuditJarLogResult.setTaskname( dto.getTaskname() );
        maskAuditJarLogResult.setTotaltime( dto.getTotaltime() );

        return maskAuditJarLogResult;
    }

    @Override
    public List<MaskAuditJarLogResult> toEntity(List<MaskAuditJarLogResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditJarLogResult> list = new ArrayList<MaskAuditJarLogResult>( dtoList.size() );
        for ( MaskAuditJarLogResultDto maskAuditJarLogResultDto : dtoList ) {
            list.add( toEntity( maskAuditJarLogResultDto ) );
        }

        return list;
    }
}
