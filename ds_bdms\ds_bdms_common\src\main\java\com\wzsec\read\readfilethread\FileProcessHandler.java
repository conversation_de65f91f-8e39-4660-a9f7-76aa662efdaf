package com.wzsec.read.readfilethread;

import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @ClassName: FileProcessHandler
 * @Description: 文件处理接口
 * @date 2019年7月31日
 */
public interface FileProcessHandler {
    /**
     * @param fileName：文件名称（完整地址）
     * @param params：参数集合
     * @param countDownLatch：线程管理
     * @param countDownLatchAll：总线程管理
     * @Description: 线程具体执行方法
     * <AUTHOR>
     * @date 2019年7月31日
     */
    void exec(String fileName, Map<String, Object> params, CountDownLatch countDownLatch,
              CountDownLatch countDownLatchAll);


    /**
     * @param data:多行数据，以换行或回车结尾
     * @param params：参数集合
     * @param currentFilePath：当前处理文件完整路径
     * @Description: NIO读取数据具体执行方法
     * <AUTHOR>
     * @date 2021年7月6日09:36:21
     */
    void processLineString(String data, Map<String, Object> params, String currentFilePath);
}
