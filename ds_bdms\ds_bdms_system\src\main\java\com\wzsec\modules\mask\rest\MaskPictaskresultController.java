package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskPictaskresult;
import com.wzsec.modules.mask.service.MaskPictaskresultService;
import com.wzsec.modules.mask.service.dto.MaskPictaskresultQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2022-04-20
*/
// @Api(tags = "图片脱敏任务结果管理")
@RestController
@RequestMapping("/api/maskPictaskresult")
public class MaskPictaskresultController {

    private final MaskPictaskresultService maskPictaskresultService;

    public MaskPictaskresultController(MaskPictaskresultService maskPictaskresultService) {
        this.maskPictaskresultService = maskPictaskresultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskPictaskresult:list')")
    public void download(HttpServletResponse response, MaskPictaskresultQueryCriteria criteria) throws IOException {
        maskPictaskresultService.download(maskPictaskresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询图片脱敏任务结果")
    // @ApiOperation("查询图片脱敏任务结果")
    @PreAuthorize("@el.check('maskPictaskresult:list')")
    public ResponseEntity<Object> getMaskPictaskresults(MaskPictaskresultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskPictaskresultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增图片脱敏任务结果")
    // @ApiOperation("新增图片脱敏任务结果")
    @PreAuthorize("@el.check('maskPictaskresult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskPictaskresult resources){
        return new ResponseEntity<>(maskPictaskresultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改图片脱敏任务结果")
    // @ApiOperation("修改图片脱敏任务结果")
    @PreAuthorize("@el.check('maskPictaskresult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskPictaskresult resources){
        maskPictaskresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除图片脱敏任务结果")
    // @ApiOperation("删除图片脱敏任务结果")
    @PreAuthorize("@el.check('maskPictaskresult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskPictaskresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
