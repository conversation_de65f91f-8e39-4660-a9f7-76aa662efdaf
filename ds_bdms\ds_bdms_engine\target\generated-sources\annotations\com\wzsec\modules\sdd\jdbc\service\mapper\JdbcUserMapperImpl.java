package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcDb;
import com.wzsec.modules.sdd.jdbc.domain.JdbcUser;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcDbSmallDto;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcUserDto;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JdbcUserMapperImpl implements JdbcUserMapper {

    @Autowired
    private JdbcDbMapper jdbcDbMapper;

    @Override
    public JdbcUserDto toDto(JdbcUser entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcUserDto jdbcUserDto = new JdbcUserDto();

        jdbcUserDto.setCreatetime( entity.getCreatetime() );
        jdbcUserDto.setEmail( entity.getEmail() );
        jdbcUserDto.setEnabled( entity.getEnabled() );
        jdbcUserDto.setId( entity.getId() );
        jdbcUserDto.setJdbcDbs( jdbcDbSetToJdbcDbSmallDtoSet( entity.getJdbcDbs() ) );
        jdbcUserDto.setLastLoginTime( entity.getLastLoginTime() );
        jdbcUserDto.setLastPasswordResetTime( entity.getLastPasswordResetTime() );
        jdbcUserDto.setNickName( entity.getNickName() );
        jdbcUserDto.setPassword( entity.getPassword() );
        jdbcUserDto.setPhone( entity.getPhone() );
        jdbcUserDto.setRole( entity.getRole() );
        jdbcUserDto.setSex( entity.getSex() );
        jdbcUserDto.setSparefield1( entity.getSparefield1() );
        jdbcUserDto.setSparefield2( entity.getSparefield2() );
        jdbcUserDto.setSparefield3( entity.getSparefield3() );
        jdbcUserDto.setSparefield4( entity.getSparefield4() );
        jdbcUserDto.setSparefield5( entity.getSparefield5() );
        jdbcUserDto.setUsername( entity.getUsername() );

        return jdbcUserDto;
    }

    @Override
    public List<JdbcUserDto> toDto(List<JdbcUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcUserDto> list = new ArrayList<JdbcUserDto>( entityList.size() );
        for ( JdbcUser jdbcUser : entityList ) {
            list.add( toDto( jdbcUser ) );
        }

        return list;
    }

    @Override
    public JdbcUser toEntity(JdbcUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcUser jdbcUser = new JdbcUser();

        jdbcUser.setCreatetime( dto.getCreatetime() );
        jdbcUser.setEmail( dto.getEmail() );
        jdbcUser.setEnabled( dto.getEnabled() );
        jdbcUser.setId( dto.getId() );
        jdbcUser.setJdbcDbs( jdbcDbSmallDtoSetToJdbcDbSet( dto.getJdbcDbs() ) );
        jdbcUser.setLastLoginTime( dto.getLastLoginTime() );
        jdbcUser.setLastPasswordResetTime( dto.getLastPasswordResetTime() );
        jdbcUser.setNickName( dto.getNickName() );
        jdbcUser.setPassword( dto.getPassword() );
        jdbcUser.setPhone( dto.getPhone() );
        jdbcUser.setRole( dto.getRole() );
        jdbcUser.setSex( dto.getSex() );
        jdbcUser.setSparefield1( dto.getSparefield1() );
        jdbcUser.setSparefield2( dto.getSparefield2() );
        jdbcUser.setSparefield3( dto.getSparefield3() );
        jdbcUser.setSparefield4( dto.getSparefield4() );
        jdbcUser.setSparefield5( dto.getSparefield5() );
        jdbcUser.setUsername( dto.getUsername() );

        return jdbcUser;
    }

    @Override
    public List<JdbcUser> toEntity(List<JdbcUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcUser> list = new ArrayList<JdbcUser>( dtoList.size() );
        for ( JdbcUserDto jdbcUserDto : dtoList ) {
            list.add( toEntity( jdbcUserDto ) );
        }

        return list;
    }

    protected JdbcDbSmallDto jdbcDbToJdbcDbSmallDto(JdbcDb jdbcDb) {
        if ( jdbcDb == null ) {
            return null;
        }

        JdbcDbSmallDto jdbcDbSmallDto = new JdbcDbSmallDto();

        jdbcDbSmallDto.setId( jdbcDb.getId() );
        jdbcDbSmallDto.setLogicdbcname( jdbcDb.getLogicdbcname() );
        jdbcDbSmallDto.setLogicdbename( jdbcDb.getLogicdbename() );

        return jdbcDbSmallDto;
    }

    protected Set<JdbcDbSmallDto> jdbcDbSetToJdbcDbSmallDtoSet(Set<JdbcDb> set) {
        if ( set == null ) {
            return null;
        }

        Set<JdbcDbSmallDto> set1 = new HashSet<JdbcDbSmallDto>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( JdbcDb jdbcDb : set ) {
            set1.add( jdbcDbToJdbcDbSmallDto( jdbcDb ) );
        }

        return set1;
    }

    protected JdbcDb jdbcDbSmallDtoToJdbcDb(JdbcDbSmallDto jdbcDbSmallDto) {
        if ( jdbcDbSmallDto == null ) {
            return null;
        }

        JdbcDb jdbcDb = new JdbcDb();

        jdbcDb.setId( jdbcDbSmallDto.getId() );
        jdbcDb.setLogicdbcname( jdbcDbSmallDto.getLogicdbcname() );
        jdbcDb.setLogicdbename( jdbcDbSmallDto.getLogicdbename() );

        return jdbcDb;
    }

    protected Set<JdbcDb> jdbcDbSmallDtoSetToJdbcDbSet(Set<JdbcDbSmallDto> set) {
        if ( set == null ) {
            return null;
        }

        Set<JdbcDb> set1 = new HashSet<JdbcDb>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( JdbcDbSmallDto jdbcDbSmallDto : set ) {
            set1.add( jdbcDbSmallDtoToJdbcDb( jdbcDbSmallDto ) );
        }

        return set1;
    }
}
