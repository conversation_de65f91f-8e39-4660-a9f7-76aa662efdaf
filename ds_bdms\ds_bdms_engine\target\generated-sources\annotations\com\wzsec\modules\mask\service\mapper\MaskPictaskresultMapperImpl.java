package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskPictaskresult;
import com.wzsec.modules.mask.service.dto.MaskPictaskresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskPictaskresultMapperImpl implements MaskPictaskresultMapper {

    @Override
    public MaskPictaskresultDto toDto(MaskPictaskresult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskPictaskresultDto maskPictaskresultDto = new MaskPictaskresultDto();

        maskPictaskresultDto.setAfterpicamount( entity.getAfterpicamount() );
        maskPictaskresultDto.setBeforepicamount( entity.getBeforepicamount() );
        maskPictaskresultDto.setId( entity.getId() );
        maskPictaskresultDto.setInputdirectory( entity.getInputdirectory() );
        maskPictaskresultDto.setInputfileformat( entity.getInputfileformat() );
        maskPictaskresultDto.setJobendtime( entity.getJobendtime() );
        maskPictaskresultDto.setJobtotaltime( entity.getJobtotaltime() );
        maskPictaskresultDto.setMaskobject( entity.getMaskobject() );
        maskPictaskresultDto.setOutputdirectory( entity.getOutputdirectory() );
        maskPictaskresultDto.setOutputfileformat( entity.getOutputfileformat() );
        maskPictaskresultDto.setRemark( entity.getRemark() );
        maskPictaskresultDto.setSparefield1( entity.getSparefield1() );
        maskPictaskresultDto.setSparefield2( entity.getSparefield2() );
        maskPictaskresultDto.setSparefield3( entity.getSparefield3() );
        maskPictaskresultDto.setSparefield4( entity.getSparefield4() );
        maskPictaskresultDto.setSparefield5( entity.getSparefield5() );
        maskPictaskresultDto.setTaskname( entity.getTaskname() );
        maskPictaskresultDto.setTaskstatus( entity.getTaskstatus() );

        return maskPictaskresultDto;
    }

    @Override
    public List<MaskPictaskresultDto> toDto(List<MaskPictaskresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskPictaskresultDto> list = new ArrayList<MaskPictaskresultDto>( entityList.size() );
        for ( MaskPictaskresult maskPictaskresult : entityList ) {
            list.add( toDto( maskPictaskresult ) );
        }

        return list;
    }

    @Override
    public MaskPictaskresult toEntity(MaskPictaskresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskPictaskresult maskPictaskresult = new MaskPictaskresult();

        maskPictaskresult.setAfterpicamount( dto.getAfterpicamount() );
        maskPictaskresult.setBeforepicamount( dto.getBeforepicamount() );
        maskPictaskresult.setId( dto.getId() );
        maskPictaskresult.setInputdirectory( dto.getInputdirectory() );
        maskPictaskresult.setInputfileformat( dto.getInputfileformat() );
        maskPictaskresult.setJobendtime( dto.getJobendtime() );
        maskPictaskresult.setJobtotaltime( dto.getJobtotaltime() );
        maskPictaskresult.setMaskobject( dto.getMaskobject() );
        maskPictaskresult.setOutputdirectory( dto.getOutputdirectory() );
        maskPictaskresult.setOutputfileformat( dto.getOutputfileformat() );
        maskPictaskresult.setRemark( dto.getRemark() );
        maskPictaskresult.setSparefield1( dto.getSparefield1() );
        maskPictaskresult.setSparefield2( dto.getSparefield2() );
        maskPictaskresult.setSparefield3( dto.getSparefield3() );
        maskPictaskresult.setSparefield4( dto.getSparefield4() );
        maskPictaskresult.setSparefield5( dto.getSparefield5() );
        maskPictaskresult.setTaskname( dto.getTaskname() );
        maskPictaskresult.setTaskstatus( dto.getTaskstatus() );

        return maskPictaskresult;
    }

    @Override
    public List<MaskPictaskresult> toEntity(List<MaskPictaskresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskPictaskresult> list = new ArrayList<MaskPictaskresult>( dtoList.size() );
        for ( MaskPictaskresultDto maskPictaskresultDto : dtoList ) {
            list.add( toEntity( maskPictaskresultDto ) );
        }

        return list;
    }
}
