package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileUnformatSubDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskStrategyFileUnformatSubMapperImpl implements MaskStrategyFileUnformatSubMapper {

    @Override
    public MaskStrategyFileUnformatSubDto toDto(MaskStrategyFileUnformatSub entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFileUnformatSubDto maskStrategyFileUnformatSubDto = new MaskStrategyFileUnformatSubDto();

        maskStrategyFileUnformatSubDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyFileUnformatSubDto.setDataname( entity.getDataname() );
        maskStrategyFileUnformatSubDto.setId( entity.getId() );
        maskStrategyFileUnformatSubDto.setMaskruleid( entity.getMaskruleid() );
        maskStrategyFileUnformatSubDto.setParam( entity.getParam() );
        maskStrategyFileUnformatSubDto.setSecretkey( entity.getSecretkey() );
        maskStrategyFileUnformatSubDto.setSenruleid( entity.getSenruleid() );
        maskStrategyFileUnformatSubDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFileUnformatSubDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFileUnformatSubDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFileUnformatSubDto.setSparefield4( entity.getSparefield4() );
        maskStrategyFileUnformatSubDto.setStrategyid( entity.getStrategyid() );

        return maskStrategyFileUnformatSubDto;
    }

    @Override
    public List<MaskStrategyFileUnformatSubDto> toDto(List<MaskStrategyFileUnformatSub> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFileUnformatSubDto> list = new ArrayList<MaskStrategyFileUnformatSubDto>( entityList.size() );
        for ( MaskStrategyFileUnformatSub maskStrategyFileUnformatSub : entityList ) {
            list.add( toDto( maskStrategyFileUnformatSub ) );
        }

        return list;
    }

    @Override
    public MaskStrategyFileUnformatSub toEntity(MaskStrategyFileUnformatSubDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyFileUnformatSub maskStrategyFileUnformatSub = new MaskStrategyFileUnformatSub();

        maskStrategyFileUnformatSub.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyFileUnformatSub.setDataname( dto.getDataname() );
        maskStrategyFileUnformatSub.setId( dto.getId() );
        maskStrategyFileUnformatSub.setMaskruleid( dto.getMaskruleid() );
        maskStrategyFileUnformatSub.setParam( dto.getParam() );
        maskStrategyFileUnformatSub.setSecretkey( dto.getSecretkey() );
        maskStrategyFileUnformatSub.setSenruleid( dto.getSenruleid() );
        maskStrategyFileUnformatSub.setSparefield1( dto.getSparefield1() );
        maskStrategyFileUnformatSub.setSparefield2( dto.getSparefield2() );
        maskStrategyFileUnformatSub.setSparefield3( dto.getSparefield3() );
        maskStrategyFileUnformatSub.setSparefield4( dto.getSparefield4() );
        maskStrategyFileUnformatSub.setStrategyid( dto.getStrategyid() );

        return maskStrategyFileUnformatSub;
    }

    @Override
    public List<MaskStrategyFileUnformatSub> toEntity(List<MaskStrategyFileUnformatSubDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyFileUnformatSub> list = new ArrayList<MaskStrategyFileUnformatSub>( dtoList.size() );
        for ( MaskStrategyFileUnformatSubDto maskStrategyFileUnformatSubDto : dtoList ) {
            list.add( toEntity( maskStrategyFileUnformatSubDto ) );
        }

        return list;
    }
}
