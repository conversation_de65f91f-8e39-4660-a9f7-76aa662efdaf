package com.wzsec.modules.sdd.jdbc.repository;

import com.wzsec.modules.sdd.jdbc.domain.JdbcUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-04-15
 */
public interface JdbcUserRepository extends JpaRepository<JdbcUser, Long>, JpaSpecificationExecutor<JdbcUser> {

    /**
     * 根据用户名查询
     *
     * @param username 用户名
     * @return /
     */
    JdbcUser findByUsername(String username);

    /**
     * 根据姓名查询
     *
     * @param nickName 姓名
     * @return /
     */
    JdbcUser findByNickName(String nickName);

    /**
     * 根据邮箱查询
     *
     * @param email 邮箱
     * @return /
     */
    JdbcUser findByEmail(String email);


    @Modifying
    @Transactional
    @Query(value = "update ddms_jdbc_user set password = ?1 ,last_password_reset_time = ?2  where id = ?3", nativeQuery = true)
    void resetPassword(String password, Date last_password_reset_time, long id);

}