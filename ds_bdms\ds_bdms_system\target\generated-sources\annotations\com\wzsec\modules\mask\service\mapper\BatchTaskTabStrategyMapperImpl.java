package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.BatchTaskTabConfig;
import com.wzsec.modules.mask.service.dto.BatchTaskTabConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:18+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class BatchTaskTabStrategyMapperImpl implements BatchTaskTabStrategyMapper {

    @Override
    public BatchTaskTabConfig toEntity(BatchTaskTabConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        BatchTaskTabConfig batchTaskTabConfig = new BatchTaskTabConfig();

        batchTaskTabConfig.setId( dto.getId() );
        if ( dto.getBatchtaskid() != null ) {
            batchTaskTabConfig.setBatchtaskid( dto.getBatchtaskid().longValue() );
        }
        batchTaskTabConfig.setTableid( dto.getTableid() );
        batchTaskTabConfig.setTabename( dto.getTabename() );
        batchTaskTabConfig.setTabcname( dto.getTabcname() );
        batchTaskTabConfig.setStrategyid( dto.getStrategyid() );
        batchTaskTabConfig.setConlimit( dto.getConlimit() );
        batchTaskTabConfig.setIsrewritetab( dto.getIsrewritetab() );
        batchTaskTabConfig.setIswatermark( dto.getIswatermark() );
        batchTaskTabConfig.setDataprovider( dto.getDataprovider() );
        batchTaskTabConfig.setDatause( dto.getDatause() );
        batchTaskTabConfig.setWatermarkcol( dto.getWatermarkcol() );
        batchTaskTabConfig.setSparefield1( dto.getSparefield1() );
        batchTaskTabConfig.setSparefield2( dto.getSparefield2() );
        batchTaskTabConfig.setSparefield3( dto.getSparefield3() );

        return batchTaskTabConfig;
    }

    @Override
    public BatchTaskTabConfigDto toDto(BatchTaskTabConfig entity) {
        if ( entity == null ) {
            return null;
        }

        BatchTaskTabConfigDto batchTaskTabConfigDto = new BatchTaskTabConfigDto();

        batchTaskTabConfigDto.setId( entity.getId() );
        if ( entity.getBatchtaskid() != null ) {
            batchTaskTabConfigDto.setBatchtaskid( entity.getBatchtaskid().intValue() );
        }
        batchTaskTabConfigDto.setTableid( entity.getTableid() );
        batchTaskTabConfigDto.setTabename( entity.getTabename() );
        batchTaskTabConfigDto.setTabcname( entity.getTabcname() );
        batchTaskTabConfigDto.setStrategyid( entity.getStrategyid() );
        batchTaskTabConfigDto.setConlimit( entity.getConlimit() );
        batchTaskTabConfigDto.setIsrewritetab( entity.getIsrewritetab() );
        batchTaskTabConfigDto.setIswatermark( entity.getIswatermark() );
        batchTaskTabConfigDto.setDataprovider( entity.getDataprovider() );
        batchTaskTabConfigDto.setDatause( entity.getDatause() );
        batchTaskTabConfigDto.setWatermarkcol( entity.getWatermarkcol() );
        batchTaskTabConfigDto.setSparefield1( entity.getSparefield1() );
        batchTaskTabConfigDto.setSparefield2( entity.getSparefield2() );
        batchTaskTabConfigDto.setSparefield3( entity.getSparefield3() );

        return batchTaskTabConfigDto;
    }

    @Override
    public List<BatchTaskTabConfig> toEntity(List<BatchTaskTabConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<BatchTaskTabConfig> list = new ArrayList<BatchTaskTabConfig>( dtoList.size() );
        for ( BatchTaskTabConfigDto batchTaskTabConfigDto : dtoList ) {
            list.add( toEntity( batchTaskTabConfigDto ) );
        }

        return list;
    }

    @Override
    public List<BatchTaskTabConfigDto> toDto(List<BatchTaskTabConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BatchTaskTabConfigDto> list = new ArrayList<BatchTaskTabConfigDto>( entityList.size() );
        for ( BatchTaskTabConfig batchTaskTabConfig : entityList ) {
            list.add( toDto( batchTaskTabConfig ) );
        }

        return list;
    }
}
