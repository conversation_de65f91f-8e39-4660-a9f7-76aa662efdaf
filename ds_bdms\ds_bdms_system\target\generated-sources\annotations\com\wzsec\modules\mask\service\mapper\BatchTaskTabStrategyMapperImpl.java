package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.BatchTaskTabConfig;
import com.wzsec.modules.mask.service.dto.BatchTaskTabConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class BatchTaskTabStrategyMapperImpl implements BatchTaskTabStrategyMapper {

    @Override
    public BatchTaskTabConfigDto toDto(BatchTaskTabConfig entity) {
        if ( entity == null ) {
            return null;
        }

        BatchTaskTabConfigDto batchTaskTabConfigDto = new BatchTaskTabConfigDto();

        if ( entity.getBatchtaskid() != null ) {
            batchTaskTabConfigDto.setBatchtaskid( entity.getBatchtaskid().intValue() );
        }
        batchTaskTabConfigDto.setConlimit( entity.getConlimit() );
        batchTaskTabConfigDto.setDataprovider( entity.getDataprovider() );
        batchTaskTabConfigDto.setDatause( entity.getDatause() );
        batchTaskTabConfigDto.setId( entity.getId() );
        batchTaskTabConfigDto.setIsrewritetab( entity.getIsrewritetab() );
        batchTaskTabConfigDto.setIswatermark( entity.getIswatermark() );
        batchTaskTabConfigDto.setSparefield1( entity.getSparefield1() );
        batchTaskTabConfigDto.setSparefield2( entity.getSparefield2() );
        batchTaskTabConfigDto.setSparefield3( entity.getSparefield3() );
        batchTaskTabConfigDto.setStrategyid( entity.getStrategyid() );
        batchTaskTabConfigDto.setTabcname( entity.getTabcname() );
        batchTaskTabConfigDto.setTabename( entity.getTabename() );
        batchTaskTabConfigDto.setTableid( entity.getTableid() );
        batchTaskTabConfigDto.setWatermarkcol( entity.getWatermarkcol() );

        return batchTaskTabConfigDto;
    }

    @Override
    public List<BatchTaskTabConfigDto> toDto(List<BatchTaskTabConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BatchTaskTabConfigDto> list = new ArrayList<BatchTaskTabConfigDto>( entityList.size() );
        for ( BatchTaskTabConfig batchTaskTabConfig : entityList ) {
            list.add( toDto( batchTaskTabConfig ) );
        }

        return list;
    }

    @Override
    public BatchTaskTabConfig toEntity(BatchTaskTabConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        BatchTaskTabConfig batchTaskTabConfig = new BatchTaskTabConfig();

        if ( dto.getBatchtaskid() != null ) {
            batchTaskTabConfig.setBatchtaskid( dto.getBatchtaskid().longValue() );
        }
        batchTaskTabConfig.setConlimit( dto.getConlimit() );
        batchTaskTabConfig.setDataprovider( dto.getDataprovider() );
        batchTaskTabConfig.setDatause( dto.getDatause() );
        batchTaskTabConfig.setId( dto.getId() );
        batchTaskTabConfig.setIsrewritetab( dto.getIsrewritetab() );
        batchTaskTabConfig.setIswatermark( dto.getIswatermark() );
        batchTaskTabConfig.setSparefield1( dto.getSparefield1() );
        batchTaskTabConfig.setSparefield2( dto.getSparefield2() );
        batchTaskTabConfig.setSparefield3( dto.getSparefield3() );
        batchTaskTabConfig.setStrategyid( dto.getStrategyid() );
        batchTaskTabConfig.setTabcname( dto.getTabcname() );
        batchTaskTabConfig.setTabename( dto.getTabename() );
        batchTaskTabConfig.setTableid( dto.getTableid() );
        batchTaskTabConfig.setWatermarkcol( dto.getWatermarkcol() );

        return batchTaskTabConfig;
    }

    @Override
    public List<BatchTaskTabConfig> toEntity(List<BatchTaskTabConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<BatchTaskTabConfig> list = new ArrayList<BatchTaskTabConfig>( dtoList.size() );
        for ( BatchTaskTabConfigDto batchTaskTabConfigDto : dtoList ) {
            list.add( toEntity( batchTaskTabConfigDto ) );
        }

        return list;
    }
}
