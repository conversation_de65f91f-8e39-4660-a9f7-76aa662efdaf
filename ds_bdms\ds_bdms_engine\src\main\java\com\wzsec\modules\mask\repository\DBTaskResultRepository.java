package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.DBTaskResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2020-11-12
 */
@Transactional
public interface DBTaskResultRepository extends JpaRepository<DBTaskResult, Integer>, JpaSpecificationExecutor<DBTaskResult> {

    /**
     * 更新任务状态
     *
     * @param taskName 任务名称
     * @param status   状态
     */
    @Modifying
    @Query(value = "UPDATE sdd_mask_dbtaskresult SET taskstatus=?2 WHERE taskname=?1", nativeQuery = true)
    void updateTaskStatusByTaskName(String taskName, String status);

    @Query(value = "select * from sdd_mask_dbtaskresult WHERE ipaddress = ?1 and dbname = ?2", nativeQuery = true)
    DBTaskResult findInfoByIPandDbname(String srcurl, String dbname);
}
