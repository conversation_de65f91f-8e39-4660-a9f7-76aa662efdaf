package com.wzsec.dotask.mask.service.excute.hdfs;

import java.io.IOException;
import java.util.*;

import cn.god.mask.common.MaskAlgFactory;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.dotask.mask.service.excute.file.MaskingConfigInfo;
import com.wzsec.dotask.mask.service.excute.file.MaskingHelper;
import com.wzsec.dotask.sdd.service.excute.common.RuleManager;
import com.wzsec.modules.mask.domain.Algorithm;
import com.wzsec.modules.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.mask.repository.AlgorithmRepository;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.utils.AlgorithmUtils;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.SneakyThrows;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * @Description: 脱敏Map
 * <AUTHOR>
 * @date 2017-1-3
 */
public class DataMaskingMapper extends Mapper<LongWritable, Text, NullWritable, Text> {
	private static Logger log = Logger.getLogger(DataMaskingMapper.class);

	private String splitStr;
	private String strategyType;
	private List<JSONObject> ruleList;
	private MaskingConfigInfo[] maskConfigInfoArr;

	public String getSplitStr() {
		return splitStr;
	}

	public void setSplitStr(String splitStr) {
		this.splitStr = splitStr;
	}

	public String getStrategyType() {
		return strategyType;
	}

	public void setStrategyType(String strategyType) {
		this.strategyType = strategyType;
	}

	public List<JSONObject> getRuleList() {
		return ruleList;
	}

	public void setRuleList(List<JSONObject> ruleList) {
		this.ruleList = ruleList;
	}

	public MaskingConfigInfo[] getMaskConfigInfoArr() {
		return maskConfigInfoArr;
	}

	public void setMaskConfigInfoArr(MaskingConfigInfo[] maskConfigInfoArr) {
		this.maskConfigInfoArr = maskConfigInfoArr;
	}

	@Autowired
	private AlgorithmRepository algorithmRepository;

	/**
	 * @Description: 脱敏Map前数据初始化
	 * <AUTHOR>
	 * @date 2017-1-3
	 */
	@Override
	protected void setup(Context context) throws IOException, InterruptedException {
		super.setup(context);
		Configuration conf = context.getConfiguration();
		// 获取分割符
		this.splitStr = conf.get("splitStr");
		log.info("Map Receive Split Str:" + conf.get("splitStr"));
		// 获取脱敏策略类型
		this.strategyType = conf.get("strategyType");
		if ("1".equals(this.strategyType)) {
			// 获取敏感识别规则 将json字符串转成对象 List<JSONObject>
			this.ruleList = (List<JSONObject>) JSONArray.parse(conf.get("ruleList"));
		}
		// 获取脱敏配置信息
		String maskingConfigInfoStr = conf.get("maskingConfigInfoStr");
		log.info("Map Receive Masking Config Info:" + maskingConfigInfoStr);
		this.maskConfigInfoArr = getMaskingConfigInfoArray(maskingConfigInfoStr);
	}

//	public static void main(String[] args) throws Exception {
//		MaskingConfigInfo[] maskingConfigInfoArray = getMaskingConfigInfoArray("1|SM4_ENC$50d7f3ec855ef191");
//		String maskLineData = getMaskingLineData("0000297E0FB2A4142653915FD37E8723	9	nios	499	[0,20)	[0,20)	0	0	0	0", "\t", maskingConfigInfoArray);
//		System.out.println(maskLineData);
//	}

	/**
	 * @Description: 脱敏Map操作
	 * <AUTHOR>
	 * @date 2017-1-3
	 */
	@SneakyThrows
	public void map(LongWritable key, Text value,
					Context context)
			throws IOException, InterruptedException {
		// 1.获取分隔符
		// String split = "\\" + this.splitStr;
		String split = this.splitStr;
		// 判断脱敏策略类型 （0:格式化文件，1:非格式化文件）
		if ("0".equals(this.strategyType)) {
			// 格式化文件策略
			// 脱敏配置信息
			MaskingConfigInfo[] maskingConfigArray = this.maskConfigInfoArr;
			// 2.获取行数据
			String lineData = value.toString();
			// 3.脱敏行数据
			String maskLineData = getMaskingLineData(lineData, split, maskingConfigArray,algorithmRepository);
			// 4.传递
			context.write(NullWritable.get(), new Text(maskLineData));
			// context.getCounter("DataMasking","DataLines").increment(1L);
		} else if ("1".equals(this.strategyType)) {
			// 非格式化文件策略

			// 获取行数据
			String lineData = value.toString();
			// 脱敏行数据
			String maskLineData = getMaskDataListForUnformatFile(lineData, this.ruleList, split);
			// 传递
			context.write(NullWritable.get(), new Text(maskLineData));
		}
	}

	/**
	 * 获取非格式化文件的脱敏数据
	 * <AUTHOR>
	 * @date 2021-06-21
	 * @param lineData
	 * @param ruleList
	 * @param splitstr
	 * @return
	 */
	public static String getMaskDataListForUnformatFile(String lineData, List<JSONObject> ruleList, String splitstr) throws Exception {
		try {
			StringBuilder maskDataSBuilder = new StringBuilder();
			// String newLine = MaskingHelper.replaceWhile(lineData, splitstr);
			String[] arrData = StringUtils.split(lineData, splitstr);
			String maskingData = "";
			if (arrData.length > 0) {
				for (String data : arrData) {
					String tmpMaskData = "";
					boolean flag = false;
					if (ruleList != null) {
						for (JSONObject ruleMap : ruleList) {
							// JSONObject 为 Map<String, Object>
							Map<String, Object> innerMap = ruleMap.getInnerMap();
							RuleDto ruleDto = JSONObject.toJavaObject(JSONObject.parseObject(innerMap.get("senRule").toString()),RuleDto.class);
							boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
							if (checkSensResult) {
								// 该数据为敏感数据
								// 脱敏算法
								AlgorithmDto algorithmDto = JSONObject.toJavaObject(JSONObject.parseObject(innerMap.get("algorithm").toString()),AlgorithmDto.class);
								StringBuilder paramStr = new StringBuilder();
								String param = ruleMap.get("param") == null ? "" : ruleMap.get("param").toString();
								String secretkey = ruleMap.get("secretkey") == null ? "" : ruleMap.get("secretkey").toString();
								if (StringUtils.isNotEmpty(param)) {
									paramStr.append(param);
									if (StringUtils.isNotEmpty(secretkey)) {
										paramStr.append(",");
										paramStr.append(secretkey);
									}
								} else {
									if (StringUtils.isNotEmpty(secretkey)) {
										paramStr.append(secretkey);
									}
								}
								// 脱敏后的数据
								String sparefield1 = algorithmDto.getSparefield1();
								if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
									tmpMaskData = MaskAlgFactory.getMaskData(data, algorithmDto.getAlgenglishname() , paramStr.toString());
								}else {
									//自定义算法
									String jarname = algorithmDto.getJarname();//jar包名称
									String funcnamepath = algorithmDto.getFuncnamepath(); //方法路径
									String methodname = algorithmDto.getMethodname();//方法名
									Map<String, String> paramMap = new HashMap<>();
									paramMap.put("jarname",jarname);
									paramMap.put("funcnamepath",funcnamepath);
									paramMap.put("methodname",methodname);
									paramMap.put("maskData",data);
									tmpMaskData = AlgorithmUtils.invokeJarMethod(paramMap);
								}

								flag = true;
								break;
							}
						}
					}
					if (!flag) {
						// 非敏感的数据，原文输出
						tmpMaskData = data;
					}
					// 拼接每个单独的数据
					maskDataSBuilder.append(tmpMaskData + splitstr);
				}
			}
			maskingData = maskDataSBuilder.toString().substring(0, maskDataSBuilder.toString().length() -1);
			//4.还原成原字符串样式
			/*if(maskingData.substring(maskingData.length()-3, maskingData.length()).equalsIgnoreCase("$&$")){
				maskLineData = StringUtils.replace(maskingData, "$&$", "", 10) + splitstr;
			}
			else{
				maskLineData = StringUtils.replace(maskingData, "$&$", "", 10);
			}*/
			return maskingData;
		}catch (Exception e) {
			throw e;
		}
	}

	/**
	 * @Description: 得到脱敏行数据
	 * <AUTHOR>
	 * @date 2017-1-4
	 */
	public static String getMaskingLineData(String lineData, String split, MaskingConfigInfo[] maskingConfigArray,AlgorithmRepository algorithmRepository) throws Exception {

		String maskLineData = "";
		StringBuilder maskDataSBuilder = new StringBuilder();

		try{
			if (null != maskingConfigArray && maskingConfigArray.length > 0) {
				// 1.对原始行数据进行处理
				//String newLine = MaskingHelper.replaceWhile(lineData, split);
				String[] arrData = lineData.split(split);
				//String[] arrData = StringUtils.split(lineData, split);

				// 2.遍历脱敏配置策略
				for (int i = 0; i < maskingConfigArray.length; i++) {
					String tmpMaskData = "";
					int arrDataIndex = maskingConfigArray[i].getMaskingPostion() - 1;
					try {
						String algoPara = maskingConfigArray[i].getAlgoParameter(); // 脱敏参数
						String[] algoParaArr = new String[5]; // 某种脱敏算法参数数组
						if (algoPara != "") {
							if (algoPara.contains(",")) {
								algoParaArr = algoPara.split(",");
							} else {
								algoParaArr[0] = algoPara;
							}
						}
						// 没考虑抽取数据情况
						/*arrData[arrDataIndex] =MaskingHelper.getMaskingData(arrData[arrDataIndex],
								maskingConfigArray[i].getAlgoName(), algoParaArr);*/
						// 考虑抽取数据情况
						/*tmpMaskData = MaskingHelper.getMaskingData(arrData[arrDataIndex],
								maskingConfigArray[i].getAlgoName(), algoParaArr);*/

						/*
						 * 统一从脱敏算法包调取
						 * tmpMaskData = MaskingHelper.getMaskingData(arrData[arrDataIndex],
						 * maskingConfigArray[i].getAlgoName(), algoParaArr);
						 */
						String algoName = maskingConfigArray[i].getAlgoName();
						//获取当前算法
						Algorithm algorithm = algorithmRepository.findByAlgorithmName(algoName);
						String sparefield1 = algorithm.getSparefield1();
						if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
							tmpMaskData = MaskAlgFactory.getMaskData(arrData[arrDataIndex],
									maskingConfigArray[i].getAlgoName(), algoPara);
						}else {
							//自定义算法
							String jarname = algorithm.getJarname();//jar包名称
							String funcnamepath = algorithm.getFuncnamepath(); //方法路径
							String methodname = algorithm.getMethodname();//方法名
							Map<String, String> paramMap = new HashMap<>();
							paramMap.put("jarname",jarname);
							paramMap.put("funcnamepath",funcnamepath);
							paramMap.put("methodname",methodname);
							paramMap.put("maskData",arrData[arrDataIndex]);
							tmpMaskData = AlgorithmUtils.invokeJarMethod(paramMap);
						}

						// System.out.println(arrData[arrDataIndex]);
					} catch (Exception e) {
						throw e;
					}

					maskDataSBuilder.append(tmpMaskData + split);
				}
				// 3.将字符串数组用分隔符连接成字符串
				//maskLineData = MaskingHelper.arrayJoinString(arrData, split, 0, arrData.length-1);
				maskLineData = maskDataSBuilder.toString().substring(0, maskDataSBuilder.toString().length() - 1);

				// 4.还原成原字符串样式
				/*if (maskingData.substring(maskingData.length() - 3, maskingData.length()).equalsIgnoreCase("$&$")) {
					maskLineData = StringUtils.replace(maskingData, "$&$", "", 10) + split;
				} else {
					maskLineData = StringUtils.replace(maskingData, "$&$", "", 10);
				}*/
			} else {
				maskLineData = lineData;
			}
		}catch (Exception e) {
			log.info("lineData: "+lineData);
			throw e;
		}

		return maskLineData;
	}

	/**
	 * @Description: 将脱敏配置信息特定字符串转换为脱敏配置信息数组，如下列格式:
	 * 1|MD5$9876543210123456;2|DATE_EARLY_INT$1,2;3|IDNUMBER 1
	 * MD5 9876543210123456 2 DATE_EARLY_INT 1,2 3 IDNUMBER
	 * <AUTHOR>
	 * @date 2017-1-4
	 */
	public static MaskingConfigInfo[] getMaskingConfigInfoArray(String maskingConfigInfoStr) {
		MaskingConfigInfo[] maskConfigInfoArr = null;
		if (null != maskingConfigInfoStr && maskingConfigInfoStr.length() > 0) {
			if (maskingConfigInfoStr.contains(";")) { // 1|MD5$9876543210123456;2|DATE_EARLY_INT$1,2;3|IDNUMBER
				String[] configItemArr = maskingConfigInfoStr.split(";");
				maskConfigInfoArr = new MaskingConfigInfo[configItemArr.length];
				for (int i = 0; i < configItemArr.length; i++) {
					String[] postionAlgoParaArr = configItemArr[i].split("\\|");
					int postion = Integer.parseInt(postionAlgoParaArr[0]); // 脱敏位置

					String algoName = ""; // 算法名称
					String algoPara = ""; // 算法参数

					if (postionAlgoParaArr.length > 1) {
						String algoNamePara = postionAlgoParaArr[1];
						if (algoNamePara.contains("$")) { // MD5$9876543210123456
							String[] algoNameParaArr = algoNamePara.split("\\$");
							algoName = algoNameParaArr[0];
							algoPara = algoNameParaArr[1];
						} else { // IDNUMBER
							algoName = algoNamePara;
							algoPara = "";
						}
					}

					MaskingConfigInfo nMaskConfigInfo = new MaskingConfigInfo(postion, algoName, algoPara,false,null);
					maskConfigInfoArr[i] = nMaskConfigInfo;
				}
			} else {
				// 1|MD5$9876543210123456 3|IDNUMBER
				maskConfigInfoArr = new MaskingConfigInfo[1];
				String[] postionAlgoPara = maskingConfigInfoStr.split("\\|");
				int postion = Integer.parseInt(postionAlgoPara[0]);
				String algoName = "";
				String algoPara = "";
				if (postionAlgoPara[1].contains("$")) {
					String[] algoParaArr = postionAlgoPara[1].split("\\$");
					algoName = algoParaArr[0];
					algoPara = algoParaArr[1];
				} else {
					algoName = postionAlgoPara[1];
					algoPara = "";
				}
				MaskingConfigInfo sMaskConfigInfo = new MaskingConfigInfo(postion, algoName, algoPara,false,null);
				maskConfigInfoArr[0] = sMaskConfigInfo;
			}
		}

		return maskConfigInfoArr;
	}

}
