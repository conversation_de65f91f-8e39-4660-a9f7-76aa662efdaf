package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskStrategyFileFormatSub;
import com.wzsec.modules.mask.service.MaskStrategyFileFormatSubService;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileFormatSubDto;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileFormatSubQueryCriteria;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-20
 */
// @Api(tags = "格式化文件策略(字段)管理")
@RestController
@RequestMapping("/api/maskStrategyFileFormatSub")
public class MaskStrategyFileFormatSubController {

    private final MaskStrategyFileFormatSubService maskStrategyFileFormatSubService;

    public MaskStrategyFileFormatSubController(MaskStrategyFileFormatSubService maskStrategyFileFormatSubService) {
        this.maskStrategyFileFormatSubService = maskStrategyFileFormatSubService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskStrategyFileFormatSub:list')")
    public void download(HttpServletResponse response, MaskStrategyFileFormatSubQueryCriteria criteria) throws IOException {
        maskStrategyFileFormatSubService.download(maskStrategyFileFormatSubService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询格式化文件策略(字段)")
    // @ApiOperation("查询格式化文件策略(字段)")
    @PreAuthorize("@el.check('maskStrategyFileFormatSub:list')")
    public ResponseEntity<Object> getMaskStrategyFileFormatSubs(MaskStrategyFileFormatSubQueryCriteria criteria, Pageable pageable) {
        List<MaskStrategyFileFormatSubDto> maskStrategyFileFormatSubDtos = maskStrategyFileFormatSubService.queryAll(criteria);
        return new ResponseEntity<>(PageUtil.toPage(maskStrategyFileFormatSubDtos, maskStrategyFileFormatSubDtos.size()), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增格式化文件策略(字段)")
    // @ApiOperation("新增格式化文件策略(字段)")
    @PreAuthorize("@el.check('maskStrategyFileFormatSub:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskStrategyFileFormatSub resources) {
        return new ResponseEntity<>(maskStrategyFileFormatSubService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改格式化文件策略(字段)")
    // @ApiOperation("修改格式化文件策略(字段)")
    @PreAuthorize("@el.check('maskStrategyFileFormatSub:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskStrategyFileFormatSub resources) {
        maskStrategyFileFormatSubService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除格式化文件策略(字段)")
    // @ApiOperation("删除格式化文件策略(字段)")
    @PreAuthorize("@el.check('maskStrategyFileFormatSub:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskStrategyFileFormatSubService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    // @ApiOperation("保存字段数据")
    @PutMapping(value = "save")
    public ResponseEntity<HttpStatus> save(@RequestBody List<MaskStrategyFileFormatSub> maskStrategyFileFormatSubs) {
        maskStrategyFileFormatSubService.save(maskStrategyFileFormatSubs);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
