package com.wzsec.modules.sdd.api.service.mapper;

import com.wzsec.modules.sdd.api.domain.ApiUrlmapping;
import com.wzsec.modules.sdd.api.service.dto.ApiUrlmappingDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiUrlmappingMapperImpl implements ApiUrlmappingMapper {

    @Override
    public ApiUrlmappingDto toDto(ApiUrlmapping entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUrlmappingDto apiUrlmappingDto = new ApiUrlmappingDto();

        apiUrlmappingDto.setApiname( entity.getApiname() );
        apiUrlmappingDto.setCreatetime( entity.getCreatetime() );
        apiUrlmappingDto.setCreateuser( entity.getCreateuser() );
        apiUrlmappingDto.setId( entity.getId() );
        apiUrlmappingDto.setIsjson( entity.getIsjson() );
        apiUrlmappingDto.setIsmask( entity.getIsmask() );
        apiUrlmappingDto.setMethod( entity.getMethod() );
        apiUrlmappingDto.setNote( entity.getNote() );
        apiUrlmappingDto.setProtocol( entity.getProtocol() );
        apiUrlmappingDto.setProxyapi( entity.getProxyapi() );
        apiUrlmappingDto.setServerapi( entity.getServerapi() );
        apiUrlmappingDto.setServerhost( entity.getServerhost() );
        apiUrlmappingDto.setServerport( entity.getServerport() );
        apiUrlmappingDto.setSparefield1( entity.getSparefield1() );
        apiUrlmappingDto.setSparefield2( entity.getSparefield2() );
        apiUrlmappingDto.setSparefield3( entity.getSparefield3() );
        apiUrlmappingDto.setSparefield4( entity.getSparefield4() );
        apiUrlmappingDto.setSparefield5( entity.getSparefield5() );
        apiUrlmappingDto.setSplit( entity.getSplit() );
        apiUrlmappingDto.setUpdatetime( entity.getUpdatetime() );
        apiUrlmappingDto.setUpdateuser( entity.getUpdateuser() );

        return apiUrlmappingDto;
    }

    @Override
    public List<ApiUrlmappingDto> toDto(List<ApiUrlmapping> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUrlmappingDto> list = new ArrayList<ApiUrlmappingDto>( entityList.size() );
        for ( ApiUrlmapping apiUrlmapping : entityList ) {
            list.add( toDto( apiUrlmapping ) );
        }

        return list;
    }

    @Override
    public ApiUrlmapping toEntity(ApiUrlmappingDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUrlmapping apiUrlmapping = new ApiUrlmapping();

        apiUrlmapping.setApiname( dto.getApiname() );
        apiUrlmapping.setCreatetime( dto.getCreatetime() );
        apiUrlmapping.setCreateuser( dto.getCreateuser() );
        apiUrlmapping.setId( dto.getId() );
        apiUrlmapping.setIsjson( dto.getIsjson() );
        apiUrlmapping.setIsmask( dto.getIsmask() );
        apiUrlmapping.setMethod( dto.getMethod() );
        apiUrlmapping.setNote( dto.getNote() );
        apiUrlmapping.setProtocol( dto.getProtocol() );
        apiUrlmapping.setProxyapi( dto.getProxyapi() );
        apiUrlmapping.setServerapi( dto.getServerapi() );
        apiUrlmapping.setServerhost( dto.getServerhost() );
        apiUrlmapping.setServerport( dto.getServerport() );
        apiUrlmapping.setSparefield1( dto.getSparefield1() );
        apiUrlmapping.setSparefield2( dto.getSparefield2() );
        apiUrlmapping.setSparefield3( dto.getSparefield3() );
        apiUrlmapping.setSparefield4( dto.getSparefield4() );
        apiUrlmapping.setSparefield5( dto.getSparefield5() );
        apiUrlmapping.setSplit( dto.getSplit() );
        apiUrlmapping.setUpdatetime( dto.getUpdatetime() );
        apiUrlmapping.setUpdateuser( dto.getUpdateuser() );

        return apiUrlmapping;
    }

    @Override
    public List<ApiUrlmapping> toEntity(List<ApiUrlmappingDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUrlmapping> list = new ArrayList<ApiUrlmapping>( dtoList.size() );
        for ( ApiUrlmappingDto apiUrlmappingDto : dtoList ) {
            list.add( toEntity( apiUrlmappingDto ) );
        }

        return list;
    }
}
