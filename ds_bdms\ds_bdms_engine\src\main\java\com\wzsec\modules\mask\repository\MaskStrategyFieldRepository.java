package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskStrategyField;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2020-11-09
*/
public interface MaskStrategyFieldRepository extends JpaRepository<MaskStrategyField, Integer>, JpaSpecificationExecutor<MaskStrategyField> {


    @Query(value = "select id from sdd_mask_strategy_field where stategytableid = ?1",nativeQuery = true)
    Integer[] findFieldIdsById(Integer userid);


    //@Query(value = "select s.fieldename,a.algenglishname,a.algorithmname,s.param,s.secretkey from sdd_mask_strategy_field s LEFT JOIN sdd_algorithm a on s.algorithmid = a.id where s.sparefield1 = '0' and stategytableid = ?1",nativeQuery = true)
//    @Query(value = "select s.fieldename,a.algenglishname,a.algorithmname,s.param,s.secretkey," +
//            "r.sparefield2 as 'isextract',r.sparefield3 as 'extractconfig',r.sparefield4 as 'extractalgid', " +
//            "s.sparefield4 as 'abnormalhandle' " + //异常数据处置
//            "from sdd_mask_strategy_field s " +
//            "left join sdd_mask_rule r on r.id = s.ruleid " +
//            "left join sdd_algorithm a on a.id = r.algorithmid " +
//            "where s.sparefield1 = '0' and s.stategytableid = ?1",nativeQuery = true)
    //kingbase 不支持上面写法
    @Query(value = "select s.fieldename,a.sparefield1,a.algenglishname,a.algorithmname,s.param,s.secretkey," +
            "r.sparefield2 as isextract,r.sparefield3 as extractconfig,r.sparefield4 as extractalgid, " +
            "s.sparefield4 as abnormalhandle " + //异常数据处置
            "from sdd_mask_strategy_field s " +
            "left join sdd_mask_rule r on r.id = s.ruleid " +
            "left join sdd_algorithm a on a.id = r.algorithmid " +
            "where s.sparefield1 = '0' and s.stategytableid = ?1",nativeQuery = true)
    List<Map<String,Object>> getMaskAlgoInfoByStrategyId(Integer strategyid);

    @Query(value = "select * from sdd_mask_strategy_field  where stategytableid = ?1",nativeQuery = true)
    List<MaskStrategyField> getMaskStrategyFieldByStrategyid(Integer strategyid);

    @Query(value = "select f.fieldename from sdd_mask_strategy_field f where f.stategytableid = ?1", nativeQuery = true)
    String[] getFieldByStrategyid(String strategyid);
}
