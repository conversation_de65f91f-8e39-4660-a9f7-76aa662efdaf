package com.wzsec.utils.database;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;

/**
 * 创建Oracle表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description 通过java List<Map<String,
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2019年10月11日 上午10:46:44
 */
public class OracleUtil extends DatabaseUtil {

    private final static Logger log = LoggerFactory.getLogger(DatabaseUtil.class);

    private static String JDBC_DRIVER = "oracle.jdbc.OracleDriver";
    private static String SQL_PREFIX1 = "ALTER SESSION  SET NLS_COMP='ANSI'";
    private static String SQL_PREFIX2 = "ALTER SESSION  SET NLS_SORT='binary_ci'";

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月17日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            System.err.println();
            sb.append("INSERT ALL ");

            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append(" INTO  " + "\"" + dbname + "\"" + "." + aa).append(tableName).append(aa + " (");
                for (String column : fields) {
                    sb.append("" + aa).append(column.toUpperCase()).append(aa + ", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();

                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);

                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月17日
     */
    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            System.err.println();
            sb.append("INSERT ALL ");

            for (Map<String, Object> map : objList) {
                sb.append(" INTO  " + aa).append(tableName).append(aa + " (");
                for (String column : fields) {
                    sb.append("" + aa).append(column).append(aa + ", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();

                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);

                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }


    /**
     * @param tableName：表名
     * @param dburl：数据库连接信息
     * @param username：数据库用户名
     * @param password：数据库密码
     * @return List<Map < String, String>>：数据库字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2019年10月12日 下午3:15:07
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            String template = "select COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,COLUMN_ID from all_tab_columns  " +
                    "where TABLE_NAME = '{}' AND OWNER = '{}' ORDER BY COLUMN_ID";
            if (StringUtils.isNotBlank(tableName) && StringUtils.isNotBlank(dbName)) {
                String sql = StrUtil.format(template, tableName, dbName.toUpperCase());
                rs = stmt.executeQuery(sql);
            }
            // ResultSet primaryKey = stmt.executeQuery("select COLUMN_NAME from
            // user_cons_columns cu, user_constraints au where
            // cu.constraint_name = au.constraint_name and au.constraint_type =
            // 'P' and au.table_name ='" + tableName + "'");
            // System.err.println(primaryKey);
            if (rs != null) {
                fieldInfoList = new ArrayList<>();

                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    /*fieldInfoMap.put("COLUMN_NAME", rs.getString("COLUMN_NAME"));// 字段名
                    fieldInfoMap.put("DATA_TYPE", rs.getString("DATA_TYPE"));// 字段类型
                    fieldInfoMap.put("DATA_LENGTH", rs.getString("DATA_LENGTH"));// 数据长度
                    if (rs.getString("DATA_PRECISION") == null) {
                        fieldInfoMap.put("DATA_PRECISION", "");
                    } else {
                        fieldInfoMap.put("DATA_PRECISION", rs.getString("DATA_PRECISION"));// 数据长度(和上方不同)
                    }
                    if (rs.getString("DATA_SCALE") == null) {
                        fieldInfoMap.put("DATA_SCALE", "");
                    } else {
                        fieldInfoMap.put("DATA_SCALE", rs.getString("DATA_SCALE"));// 获取数据精度
                    }
                    fieldInfoMap.put("NULLABLE", rs.getString("NULLABLE"));// 获取是否为空
                    fieldInfoMap.put("COLUMN_ID", rs.getString("COLUMN_ID"));// 字段序号*/

                    //TODO 所有DBUtil 字段信息尽量统一，以便支持不同数据源类型之间脱敏
                    fieldInfoMap.put("FieldEName", rs.getString("COLUMN_NAME"));//字段名
                    fieldInfoMap.put("FieldCName", null);
                    fieldInfoMap.put("FieldType", rs.getString("DATA_TYPE") + "(" + rs.getString("DATA_LENGTH") + ")");//格式：varchar(255)
                    //ORACLE特有
                    if (rs.getString("DATA_PRECISION") == null) {
                        fieldInfoMap.put("DATA_PRECISION", "");
                    } else {
                        fieldInfoMap.put("DATA_PRECISION", rs.getString("DATA_PRECISION"));// 数据长度(和上方不同)
                    }
                    if (rs.getString("DATA_SCALE") == null) {
                        fieldInfoMap.put("DATA_SCALE", "");
                    } else {
                        fieldInfoMap.put("DATA_SCALE", rs.getString("DATA_SCALE"));// 获取数据精度
                    }
                    fieldInfoMap.put("NULLABLE", rs.getString("NULLABLE"));// 获取是否为空
                    fieldInfoMap.put("COLUMN_ID", rs.getString("COLUMN_ID"));// 字段序号
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return fieldInfoList;
    }


    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE \"TEST\".\"").append(tableName).append("\" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("COLUMN_NAME"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("\"").append(fieldInfo.get("COLUMN_NAME")).append("\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("COLUMN_NAME"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar2(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("DATA_TYPE"));// 类型
                    sb.append(" ").append("(" + fieldInfo.get("DATA_LENGTH") + ")");// 长度
                }

                /*
                 * if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                 * sb.append(" NOT NULL"); } if
                 * ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra")))
                 * {// 判断非空 sb.append(" AUTO_INCREMENT");// 自增 } else { if
                 * (fieldInfo.get("Default") != null) {
                 * sb.append(" DEFAULT '").append(fieldInfo.get("Default")).
                 * append("'");// 默认值 } else { sb.append(" DEFAULT NULL"); } }
                 */
                /*
                 * if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                 * sb.append(" PRIMARY KEY");// 主键 }
                 */
                /*
                 * if (fieldInfo.get("Comment") != null &&
                 * !"".equals(fieldInfo.get("Comment"))) {
                 * sb.append(" COMMENT '").append(fieldInfo.get("Comment")).
                 * append("'"); }
                 */
                sb.append(",\n");
            }
            sql = sb.toString();

            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }


    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) {
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = new HashMap<String, String>();
        try {
            /*String strSQL = "select owner,TABLE_NAME from all_tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where owner in (" + "'" + dbnames + "'" + ") ";
            }*/
            // 排除系统用户
            String strSQL = "select TABLE_NAME,OWNER from all_tables " +
                    "where owner not in('MDSYS','OUTLN','CTXSYS','OLAPSYS','FLOWS_FILES','OWBSYS','HR','SYSTEM','EXFSYS','APEX_030200','SCOTT','DBSNMP','ORDSYS','SYSMAN','OE','SH','PM','XDB','APPQOSSYS','ORDDATA','IX','SYS','WMSYS')";
            if (StringUtils.isNotEmpty(dbnames)) {
                strSQL += " and owner in (" + "'" + dbnames + "'" + ") ";
            }
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            setIgnoreCase(stmt);
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String dbName = rs.getString("owner");// 库名
                String tabname = rs.getString("TABLE_NAME");// 表名
                if (dbTabMap.containsKey(dbName)) {
                    dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + tabname);
                } else {
                    dbTabMap.put(dbName, tabname);
                }
            }
            conn.commit();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static Map<String, String> getAllDbAndTabMap(String dburl, String username, String password, String dbnames) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = new HashMap<String, String>();
        try {
            String strSQL = "select owner,TABLE_NAME from all_tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where owner in (" + dbnames + ") ";
            }
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            setIgnoreCase(stmt);
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String dbName = rs.getString("owner");// 库名
                String tabname = rs.getString("TABLE_NAME");// 表名
                if (dbTabMap.containsKey(dbName)) {
                    dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + tabname);
                } else {
                    dbTabMap.put(dbName, tabname);
                }
            }
            conn.commit();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return dbTabMap;
    }

    /**
     * @Description:获取数据库表中数据
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + "\"" + dbname + "\"" + "." + "\"" + tabname + "\"";
            if (lineNum != null && lineNum != 0)
                strSQL += " where rownum <= " + lineNum + " order by rownum";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                //                StringBuffer sub = new StringBuffer();
//                for (int i = 1; i <= columnCount; i++) {
//                    if (i > 1) {
//                        sub.append(";;");
//                    }
//                    sub.append(rs.getObject(i));
//                }
//                tabDataList.add(sub.toString());

                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println(ex);
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }


    /**
     * @Description:获取数据库某个字段非空数据
     * <AUTHOR>
     * @date 2021-03-12
     */
    public static List<String[]> getFieldDataList(Connection conn, String dbname, String tabname, String field, Integer lineNum) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select " + "\"" + field + "\"" + " from " + "\"" + tabname + "\"";
            if (lineNum != null && lineNum != 0)
                strSQL += " where " + "\"" + field + "\"" + " is not null and " + field + " !='' and rownum <= " + lineNum + " order by rownum";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println(ex);
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + "\"" + tabname + "\"";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return count;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(Connection conn, String dbname,
                                                String tablename) {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> fieldList = new ArrayList<String>();
        try {
            String sql = "select COLUMN_NAME from all_tab_columns  where table_name= '" + tablename + "' and owner ='"
                    + dbname + "' ";
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            setIgnoreCase(stmt);
            rs = stmt.executeQuery(sql);
            conn.commit();
            while (rs.next()) {
                fieldList.add(rs.getString(1));
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static List<String> getTabDataList(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String> tabDataList = new ArrayList<String>();
        try {
            String strSQL = "select * from " + tabname + " order by rand() LIMIT 100 ";
            conn = getConn(JDBC_DRIVER, dburl + "/" + dbname, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                StringBuffer sub = new StringBuffer();
                for (int i = 1; i <= columnCount; i++) {
                    if (i > 1) {
                        sub.append(Const.DB_TAB_DATA_JOIN);
                    }
                    sub.append(rs.getObject(i));
                }
                tabDataList.add(sub.toString());
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            conn = getConn(JDBC_DRIVER, dburl + "/" + dbname, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }

    /**
     * @param tablename：表名
     * @param dbname：库名
     * @param url：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return List<String>
     * @throws Exception
     * @Description 通过获取到表字段信息
     * <AUTHOR>
     * @date 2020年8月7日 下午8:23:25
     */
    public static List<String> getFieldNameList(String url, String dbUserName, String dbPassword, String dbname,
                                                String tablename, String tablenameAlias) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> fieldList = new ArrayList<String>();
        try {
            String sql = "select COLUMN_NAME from all_tab_columns where table_name= '" + tablename + "' and owner ='"
                    + dbname + "' ORDER BY  COLUMN_ID";
            conn = getConn(JDBC_DRIVER, url, dbUserName, dbPassword);
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            setIgnoreCase(stmt);
            rs = stmt.executeQuery(sql);
            conn.commit();
            while (rs.next()) {
                fieldList.add(tablenameAlias + "." + rs.getString(1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return fieldList;
    }

    /**
     * @param tablename：表名
     * @param dbname：库名
     * @param url：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return List<String>
     * @throws Exception
     * @Description 通过获取到表字段信息
     * <AUTHOR>
     * @date 2020年8月7日 下午8:23:25
     */
    public static List<String> getFieldNameList(String url, String dbUserName, String dbPassword, String dbname,
                                                String tablename) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> fieldList = new ArrayList<String>();
        try {
            String sql = "select COLUMN_NAME from all_tab_columns where table_name= '" + tablename + "' and owner ='"
                    + dbname + "' ORDER BY  COLUMN_ID";
            conn = getConn(JDBC_DRIVER, url, dbUserName, dbPassword);
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            setIgnoreCase(stmt);
            rs = stmt.executeQuery(sql);
            conn.commit();
            while (rs.next()) {
                fieldList.add(rs.getString(1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return fieldList;
    }


    /**
     * @param tableName：表名
     * @param dbUrl：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return List<Map < String, String>>：数据库字段信息
     * @throws Exception
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2019年10月12日 下午3:15:07
     */
    protected List<Map<String, String>> getTableFieldInfo(String tableName, String dbUrl, String dbUserName,
                                                          String dbPassword) throws Exception {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, dbUserName, dbPassword);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(
                    "select COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,COLUMN_ID from all_tab_columns  where table_name= '"
                            + tableName + "'");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();

                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("COLUMN_NAME", rs.getString("COLUMN_NAME"));// 字段名
                    fieldInfoMap.put("DATA_TYPE", rs.getString("DATA_TYPE"));// 字段类型
                    fieldInfoMap.put("DATA_LENGTH", rs.getString("DATA_LENGTH"));// 数据长度
                    if (rs.getString("DATA_PRECISION") == null) {
                        fieldInfoMap.put("DATA_PRECISION", "");
                    } else {
                        fieldInfoMap.put("DATA_PRECISION", rs.getString("DATA_PRECISION"));// 数据长度(和上方不同)
                    }
                    if (rs.getString("DATA_SCALE") == null) {
                        fieldInfoMap.put("DATA_SCALE", "");
                    } else {
                        fieldInfoMap.put("DATA_SCALE", rs.getString("DATA_SCALE"));// 获取数据精度
                    }
                    fieldInfoMap.put("NULLABLE", rs.getString("NULLABLE"));// 获取是否为空
                    fieldInfoMap.put("COLUMN_ID", rs.getString("COLUMN_ID"));// 字段序号
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            throw e;
        } finally {
            closeCon(rs, stmt, conn);
        }
        return fieldInfoList;
    }


    /**
     * @param tablename：表名
     * @param dbname：库名
     * @param url：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return Map<String, String>  Map<字段,表名>
     * @throws Exception
     * @Description 获取到多个表中独有的字段
     * <AUTHOR>
     * @date 2020年8月13日 下午8:23:25
     */
    public static Map<String, String> getFieldTabMap(String url, String dbUserName, String dbPassword, String dbname,
                                                     String tablename) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        //统计多个表中重复字段
        List<String> fieldList = new ArrayList<String>();
        Map<String, String> fieldTabMap = new HashMap<String, String>();
        String[] tabnames = tablename.split(",");
        try {
            conn = getConn(JDBC_DRIVER, url, dbUserName, dbPassword);
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            setIgnoreCase(stmt);
            for (int i = 0; i < tabnames.length; i++) {
                tablename = tabnames[i].split(" ")[0];
                String sql = "";
                if (tablename.contains(".")) {
                    String strDbname = tablename.split("\\.")[0];
                    String strTabname = tablename.split("\\.")[1];
                    sql = "select COLUMN_NAME from all_tab_columns where table_name= '" + strTabname + "' and owner ='" + strDbname + "' ";
                } else {
                    sql = "select COLUMN_NAME from all_tab_columns where table_name= '" + tablename + "' and owner ='" + dbname + "' ";
                }
                rs = stmt.executeQuery(sql);
                conn.commit();
                while (rs.next()) {
                    String field = rs.getString("COLUMN_NAME");
                    if (fieldTabMap.containsKey(field)) {
                        if (!fieldList.contains(field)) {
                            fieldList.add(field);
                        }
                    } else {
                        fieldTabMap.put(field, tablename);
                    }
                }
            }
            //删除多个表中重复字段
            for (int i = 0; i < fieldList.size(); i++) {
                if (fieldTabMap.containsKey(fieldList.get(i))) {
                    fieldTabMap.remove(fieldList.get(i));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return fieldTabMap;
    }


    /**
     * @Description 设置oracle本次会话忽略大小写
     * <AUTHOR>
     * @date 2020年2月13日
     */
    public static void setIgnoreCase(Statement stmt) {
        try {
            stmt.execute(SQL_PREFIX1);
            stmt.execute(SQL_PREFIX2);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();
            String sql = "SELECT acc.COLUMN_NAME, acc.COMMENTS,atc.DATA_TYPE FROM  ALL_TAB_COLUMNS atc " +
                    "LEFT JOIN ALL_COL_COMMENTS acc ON atc.OWNER = acc.OWNER AND atc.TABLE_NAME = acc.TABLE_NAME AND atc.COLUMN_NAME = acc.COLUMN_NAME " +
                    "WHERE atc.OWNER = '" + dbName + "' AND atc.TABLE_NAME = '" + tableName + "' " +
                    "ORDER BY atc.COLUMN_ID";
            rs = stmt.executeQuery(sql);
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COMMENTS"));
                    fieldInfoMap.put("fieldType", rs.getString("DATA_TYPE"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(rs, stmt, null);
        return fieldInfoList;
    }


    /**
     * @param url：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return Map<String, String>
     * @throws Exception
     * @Description 通过库表名获取到真实的库表名
     * <AUTHOR>
     * @date 2020年11月25日
     */
    public static Map<String, String> getTabOwnerAndName(String url, String dbUserName, String dbPassword) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        Map<String, String> map = new HashMap<String, String>();
        try {
            String sql = "select OWNER,SYNONYM_NAME,TABLE_OWNER,TABLE_NAME from all_synonyms where TABLE_OWNER NOT IN('APEX_030200','SYS','DBSNMP','ORDSYS','MDSYS','OLAPSYS','CTXSYS','OWBSYS','XDB','EXFSYS','WMSYS','SYSMAN','SYSTEM','APPQOSSYS','ORDDATA','FLOWS_FILES') ";
            conn = getConn(JDBC_DRIVER, url, dbUserName, dbPassword);
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
            setIgnoreCase(stmt);
            rs = stmt.executeQuery(sql);
            conn.commit();
            while (rs.next()) {
                map.put(rs.getString("OWNER") + rs.getString("SYNONYM_NAME"), rs.getString("TABLE_OWNER") + Const.DB_TAB_FIELD_JOIN + rs.getString("TABLE_NAME"));
            }
            log.info("获取用户表对应的真实库表成功");
        } catch (Exception e) {
            log.error("获取用户表对应的真实库表出现异常");
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return map;
    }

    /**
     * @param dbUrl：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return List<String>：数据库表名
     * @throws SQLException
     * @throws Exception
     * @Description 查询数据源中所有库表名
     * <AUTHOR>
     * @date 2020年8月13日 下午3:15:07
     */
    public static String getDataBaseCharset(String dbUrl, String dbUserName, String dbPassword) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        String dataBaseCharset = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, dbUserName, dbPassword);// 打开连接
            stmt = conn.createStatement();
            String sql = "select userenv('language') from dual ";
            rs = stmt.executeQuery(sql);
            while (rs.next()) {
                dataBaseCharset = rs.getString(1);// 编码
                if (dataBaseCharset.endsWith(Const.ORACLE_CHARSET_GBK)) {
                    dataBaseCharset = Const.ORACLE_CHARSET_GBK;
                } else {
                    dataBaseCharset = Const.ORACLE_CHARSET_UTF8;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return dataBaseCharset;
    }


    /**
     * @param url：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return int
     * @throws Exception
     * @Description 判断查询的表是不是系统表
     * <AUTHOR>
     * @date 2020年11月25日
     */
    public static int isSystemTable(String url, String dbUserName, String dbPassword, String table) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        int num = 0;
        try {
            String sql = "select count(*) from all_synonyms where OWNER = 'PUBLIC' and  SYNONYM_NAME in(" + table + ") ";
            conn = getConn(JDBC_DRIVER, url, dbUserName, dbPassword);
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            while (rs.next()) {
                num = rs.getInt(1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return num;
    }


    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表信息
     * @Description 查询表信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:56
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<String, String>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TABLE_NAME,NUM_ROWS,ROUND((AVG_ROW_LEN * num_rows / 1024 / 1024), 2) AS data_size_mb FROM all_tables WHERE owner = '" + dbName + "' and table_name = '" + tableName + "'");
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableRows", rs.getString("NUM_ROWS"));
                tableInfoMap.put("dataSize", rs.getString("data_size_mb"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月17日
     */
    //@Override
    //TODO 原始逻辑，批量写入，多条写入的数据拼到1条sql执行
    /*public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        String aa = "\"";
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT ALL ");

            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append(" INTO  " + "\"" + dbname + "\"" + "." + aa).append(tableName).append(aa + " (");
                for (String column : fields) {
                    sb.append("" + aa).append(column).append(aa + ", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("");
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(")");
                sql = sb.toString();

                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);

                sql += " )SELECT 1 FROM DUAL";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }*/

    //TODO 单条数据多批次写入，每条写入的数据用;分开
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            String insertTable = "INSERT INTO \"" + dbname.toUpperCase() + "\".\"" + tableName + "\" (";

            for (int i = start; i < end; i++) {
                sb.append(insertTable);
                Map<String, Object> map = objList.get(i);
                for (String column : fields) {
                    //sb.append("\""+column+"\"").append(", ");
                    sb.append(column).append(", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(");");
                sql = sb.toString();

                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);

                sql += " );";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }


    public static Map<String, Object> getInsertTableSqlAndPatams(int start,int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            String insertTable = "INSERT INTO \""+dbname.toUpperCase()+"\".\"" + tableName + "\" (";

            for (int i = start; i < end; i++) {
                sb.append(insertTable);
                Map<String, Object> map= objList.get(i);
                for (String column : fields) {
                    //sb.append("\""+column+"\"").append(", ");
                    sb.append(column).append(", ");
                }
                String sql = sb.toString();
                int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(") VALUES ");
                sb.append("(");
                for (String column : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?");
                    sb.append(", ");
                    params.add(map.get(column));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append(");");
                sql = sb.toString();

                // lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);

                sql += " );";
                sqlAndParams = new HashMap<>();
                sqlAndParams.put("sql", sql);
                sqlAndParams.put("params", params.toArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            //sb.append("CREATE TABLE \""+dbname.toUpperCase()+"\".").append(tableName).append(" (\r\n");
            sb.append("CREATE TABLE " + dbname.toUpperCase() + ".").append(tableName).append(" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }

                sb.append(fieldInfo.get("FieldEName"));// 字段名
                String fieldType = " " + fieldInfo.get("FieldType");

                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar

                    //TODO  oracle 19c版本，char、varchar类型需要手动设置成char

                    /*//oracle的char、varchar等类型默认存储byte，需手动设置成char，否则存储中文时会长度不够
                    if (fieldType.toLowerCase().contains("char")){
                        int i = fieldType.indexOf(")");
                        fieldType = fieldType.substring(0, i) +" char)";
                    } else {
                        //其他字段类型脱敏数据一律用varchar存储，以免数据脱敏后原字段类型存储错误
                        fieldType = " varchar2(255 char)";
                    }
                    sb.append(fieldType);

                    //varchar、char等类型需要指定字段长度
                } else if(fieldType.toLowerCase().contains("char")){
                    //oracle的char、varchar等类型默认存储byte，需手动设置成char，否则存储中文时会长度不够
                    int i = fieldType.indexOf(")");
                    fieldType = fieldType.substring(0, i) +" char)";
                    sb.append(" "+fieldType);// 类型

                    //orcale的日期类型写入暂改为字符串类型
                } else if (fieldInfo.get("FieldType").toLowerCase().contains("date")){
                    sb.append(" varchar2(50 char)");// 类型
                */


                    //TODO oracle 其他版本不需要设置char
                    fieldType = " varchar2(255)";
                    sb.append(fieldType);
                } else if (fieldType.toLowerCase().contains("char")) {
                    int i = fieldType.indexOf(")");
                    fieldType = fieldType.substring(0, i) + ")";
                    sb.append(" " + fieldType);// 类型
                    //其他数据库的decimal类型的精度指定可能会超过oracle的限制，这里将精度固定 小数10位大都够用
                } else if (fieldType.toLowerCase().contains("decimal")) {
                    sb.append(" decimal(38,10)");// 类型
                } else if (fieldType.toLowerCase().contains("int")) {
                    //oracle不兼容bigint、mediumint、multipoint等类型，统一换成integer
                    sb.append(" integer");
                } else if (fieldType.toLowerCase().contains("double")) {
                    //oracle不兼容double类型，换成integer
                    sb.append(" float");
                } else if (fieldType.toLowerCase().contains("text")) {
                    //oracle不兼容text类型，换成CLOB
                    sb.append(" CLOB");
                } else {
                    if (fieldType.contains("(")) {
                        //data、integer等类型无需指定字段长度，需截取
                        int i = fieldType.indexOf("(");
                        fieldType = " " + fieldType.substring(0, i);
                    }
                    sb.append(fieldType);// 类型
                }


                /*
                 * if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                 * sb.append(" NOT NULL"); } if
                 * ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra")))
                 * {// 判断非空 sb.append(" AUTO_INCREMENT");// 自增 } else { if
                 * (fieldInfo.get("Default") != null) {
                 * sb.append(" DEFAULT '").append(fieldInfo.get("Default")).
                 * append("'");// 默认值 } else { sb.append(" DEFAULT NULL"); } }
                 */
                /*
                 * if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                 * sb.append(" PRIMARY KEY");// 主键 }
                 */
                /*
                 * if (fieldInfo.get("Comment") != null &&
                 * !"".equals(fieldInfo.get("Comment"))) {
                 * sb.append(" COMMENT '").append(fieldInfo.get("Comment")).
                 * append("'"); }
                 */
                sb.append(",\n");
            }
            sql = sb.toString();

            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT OBJECT_NAME AS procedure_name, OBJECT_TYPE AS object_type, OWNER AS schema_name FROM ALL_OBJECTS " +
                "WHERE OBJECT_TYPE = 'PROCEDURE' AND OWNER = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, dbName.toUpperCase());
            rs = stmt.executeQuery();
            while (rs.next()) {
                String procedureName = rs.getString("procedure_name");


                String querySourceCode = "SELECT TEXT FROM ALL_SOURCE WHERE TYPE = 'PROCEDURE' AND OWNER = ? AND NAME = ? ORDER BY LINE";
                PreparedStatement pstmtSourceCode = conn.prepareStatement(querySourceCode);
                pstmtSourceCode.setString(1, dbName.toUpperCase());
                pstmtSourceCode.setString(2, procedureName);
                ResultSet rsSourceCode = pstmtSourceCode.executeQuery();

                List<String> sourceLines = new ArrayList<>();
                while (rsSourceCode.next()) {
                    sourceLines.add(rsSourceCode.getString("TEXT"));
                }

                StringBuilder createProcSQL = new StringBuilder();
                createProcSQL.append("CREATE OR REPLACE PROCEDURE ").append(procedureName).append(" AS \n");
                for (String line : sourceLines) {
                    createProcSQL.append(line.replaceAll("^\\s+", "")).append("\t");
                }
                //createProcSQL.append("END ").append(procedureName).append(";");

                rsSourceCode.close();
                pstmtSourceCode.close();

                sqlList.add(createProcSQL.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT OBJECT_NAME AS procedure_name, OBJECT_TYPE AS object_type, OWNER AS schema_name FROM ALL_OBJECTS " +
                "WHERE OBJECT_TYPE = 'FUNCTION' AND OWNER = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, dbName.toUpperCase());
            rs = stmt.executeQuery();
            while (rs.next()) {
                String procedureName = rs.getString("procedure_name");


                String querySourceCode = "SELECT TEXT FROM ALL_SOURCE WHERE TYPE = 'FUNCTION' AND OWNER = ? AND NAME = ? ORDER BY LINE";
                PreparedStatement pstmtSourceCode = conn.prepareStatement(querySourceCode);
                pstmtSourceCode.setString(1, dbName.toUpperCase());
                pstmtSourceCode.setString(2, procedureName);
                ResultSet rsSourceCode = pstmtSourceCode.executeQuery();

                List<String> sourceLines = new ArrayList<>();
                while (rsSourceCode.next()) {
                    sourceLines.add(rsSourceCode.getString("TEXT"));
                }

                StringBuilder createProcSQL = new StringBuilder();
                createProcSQL.append("CREATE OR REPLACE FUNCTION ").append(procedureName).append(" AS \n");
                for (String line : sourceLines) {
                    createProcSQL.append(line.replaceAll("^\\s+", "")).append("\t");
                }
                //createProcSQL.append("END ").append(procedureName).append(";");

                rsSourceCode.close();
                pstmtSourceCode.close();

                sqlList.add(createProcSQL.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();

        //触发器内容依附在对应的表上的sql
        String sql = "SELECT * FROM user_triggers WHERE TABLE_OWNER = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            //String schema = conn.getSchema();
            stmt.setString(1, dbName.toUpperCase());
            rs = stmt.executeQuery();
            while (rs.next()) {
                String triggerName = rs.getString("trigger_name");
                String tableName = rs.getString("table_name");
                String triggeringEvent = rs.getString("triggering_event");
                String whenClause = rs.getString("when_clause");
                String description = rs.getString("description");

                // Generate the complete create trigger statement
                StringBuilder createTriggerSql = new StringBuilder("CREATE OR REPLACE TRIGGER ")
                        .append(triggerName)
                        .append(" BEFORE ")
                        .append(triggeringEvent)
                        .append(" ON ").append(tableName)
                        .append(" FOR EACH ROW ");

                if (whenClause != null && !whenClause.trim().isEmpty()) {
                    createTriggerSql.append("WHEN (").append(whenClause).append(") ");
                }

                createTriggerSql.append("BEGIN\n")
                        .append(description)
                        .append("\nEND;\n");

                sqlList.add(createTriggerSql.toString());

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT view_name, text FROM user_views "; //过滤系统视图
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            //String schema = conn.getSchema();
            //stmt.setString(1,schema);
            rs = stmt.executeQuery();
            while (rs.next()) {
                String viewName = rs.getString("view_name");
                String viewText = rs.getString("text");

                // Generate the complete create view statement
                StringBuilder createViewSql = new StringBuilder("CREATE OR REPLACE VIEW ")
                        .append(viewName)
                        .append(" AS ")
                        .append(viewText);
                sqlList.add(createViewSql.toString().trim());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT * FROM user_sequences";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            //String schema = conn.getSchema();
            //stmt.setString(1,schema);
            rs = stmt.executeQuery();
            while (rs.next()) {
                String sequenceName = rs.getString("sequence_name");
                int incrementBy = rs.getInt("increment_by");
                long minValue = rs.getLong("min_value");
                long maxValue = rs.getLong("max_value");
                boolean isCycle = rs.getString("cycle_flag").equalsIgnoreCase("YES");
                boolean isOrder = rs.getString("order_flag").equalsIgnoreCase("YES");
                int cacheSize = rs.getInt("cache_size");

                // Generate the complete create sequence statement
                StringBuilder createSequenceSql = new StringBuilder("CREATE SEQUENCE ")
                        .append(sequenceName)
                        .append(" INCREMENT BY ").append(incrementBy)
                        .append(" MINVALUE ").append(minValue)
                        .append(" MAXVALUE ").append(maxValue);

                if (isCycle) {
                    createSequenceSql.append(" CYCLE");
                } else {
                    createSequenceSql.append(" NOCYCLE");
                }

                if (isOrder) {
                    createSequenceSql.append(" ORDER");
                } else {
                    createSequenceSql.append(" NOORDER");
                }

                if (cacheSize > 0) {
                    createSequenceSql.append(" CACHE ").append(cacheSize);
                } else {
                    createSequenceSql.append(" NOCACHE");
                }

                sqlList.add(createSequenceSql.toString().trim());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT ind.index_name, ind.table_name, ind_col.column_name, ind.uniqueness, ind.index_type " +
                "FROM user_indexes ind " +
                "JOIN user_ind_columns ind_col ON ind.index_name = ind_col.index_name " +
                "WHERE ind.TABLE_OWNER = ? " +
                "ORDER BY ind.index_name, ind_col.column_position";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, inDBName.toUpperCase());
            rs = stmt.executeQuery();

            while (rs.next()) {
                String indexName = rs.getString("index_name");
                String tableName = rs.getString("table_name");
                String columnName = rs.getString("column_name");
                String uniqueness = rs.getString("uniqueness");

                String indexDef = "";
                if ("UNIQUE".equals(uniqueness) && "ID".equalsIgnoreCase(columnName)) {
                    indexDef = "ALTER TABLE " + tableName + " ADD PRIMARY KEY (" + columnName + ")";
                } else {
                    indexDef = "CREATE INDEX " + indexName + " ON " + tableName + " (" + columnName + ")";
                }
                sqlList.add(indexDef);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    public static void main(String[] args) {
//        String url = "***********************************************";
//        String user = "test";
//        String password = "test";
//        String dbName = "TEST";
//        OracleUtil oracleUtil = new OracleUtil();//
//        List<String> triggerSql = oracleUtil.getFunctionSql(url, user, password, dbName);
//        System.out.println(JSON.toJSONString(triggerSql));
//        System.out.println(triggerSql.get(0));

        // 数据库连接的URL，格式为：jdbc:oracle:thin:@<hostname>:<port>:<SID>
        // 或者使用服务名（Service Name）：jdbc:oracle:thin:@//<hostname>:<port>/<serviceName>
        // 这里我们假设使用SID连接到一个已经存在的数据库

        // JDBC驱动名以及数据库URL
        final String JDBC_DRIVER = "oracle.jdbc.driver.OracleDriver";
        final String DB_URL = "********************************************";
        String dbname = "audit2";
        // 数据库用户名和密码
        final String USER = "system";
        final String PASS = "system";

        Connection connection  = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            // 注册JDBC驱动
            Class.forName(JDBC_DRIVER);
            // 打开连接
            System.out.println("连接数据库...");
            connection  = DriverManager.getConnection(DB_URL, USER, PASS);
            // 3. 获取数据库元数据（这里我们不需要检查数据库是否存在，因为 Oracle 不允许直接创建数据库，而是通过创建表空间等方式来管理）
            // 但是，我们可以检查某个特定的表空间或模式是否存在（这取决于您的需求）

            // 4. 在 Oracle 中，创建数据库的操作通常是由数据库管理员通过 SQL*Plus 或其他工具完成的，而不是通过 JDBC
            // 如果您需要创建新的表空间或模式，请确保您有足够的权限，并使用相应的 SQL 命令
            // 这里我们假设您只是想检查并可能创建一个新的模式（用户）

            // 检查模式（用户）是否存在（这是一个简化的示例，实际中可能需要更复杂的逻辑）
            DatabaseMetaData metaData = connection.getMetaData();
            resultSet = metaData.getSchemas(); // 在 Oracle 中，这可能返回的是所有用户的列表
            boolean schemaExists = false;
            while (resultSet.next()) {
                String schemaName = resultSet.getString(1);
                if (dbname.equalsIgnoreCase(schemaName)) {
                    schemaExists = true;
                    break;
                }
            }

            // 5. 如果模式不存在，则创建它（需要 DBA 权限或适当的权限）
            // 注意：在 Oracle 中，创建用户（模式）通常涉及更多的步骤和权限考虑
            if (!schemaExists) {
                statement = connection.createStatement();
                // 这里是一个简化的创建用户的 SQL，实际中可能需要更多的参数和设置
                // 请确保您有足够的权限来执行这个操作，并且根据您的安全策略来设置密码等
                String createUserSQL = "CREATE USER " + dbname + " IDENTIFIED BY your_password";
                statement.executeUpdate(createUserSQL);
                // 可能还需要授予该用户一定的权限，比如创建表等
                String grantPrivilegesSQL = "GRANT CONNECT, RESOURCE TO " + dbname;
                statement.executeUpdate(grantPrivilegesSQL);
                System.out.println("用户（模式）" + dbname + "已创建！");
//                msgMap.put("code", "DATABASE_CREATE"); // 假设这是您定义的常量来表示数据库（或模式）已创建
            } else {
//                msgMap.put("code", "DATABASE_EXISTS"); // 假设这是您定义的常量来表示数据库（或模式）已存在
            }
        } catch (SQLException se) {
            // 处理JDBC错误
            se.printStackTrace();
        } catch (Exception e) {
            // 处理Class.forName错误
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (statement != null) statement.close();
            } catch (SQLException se2) {
            } // 什么都不做
            try {
                if (connection != null) connection.close();
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
        System.out.println("程序结束!");
    }

    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 11:19
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();
        String dbname = map.get("dbname");
        String srcurl = map.get("srcurl");
        String username = map.get("username");
        String password = map.get("password");
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Connection connection  = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            // 注册JDBC驱动
            Class.forName(JDBC_DRIVER);
            // 打开连接
            connection  = DriverManager.getConnection(srcurl, username, password);
            DatabaseMetaData metaData = connection.getMetaData();
            resultSet = metaData.getSchemas();
            boolean schemaExists = false;
            while (resultSet.next()) {
                String schemaName = resultSet.getString(1);
                if (dbname.equalsIgnoreCase(schemaName)) {
                    schemaExists = true;
                    break;
                }
            }

            // 如果模式不存在，则创建它（需要 DBA 权限或适当的权限）
            if (!schemaExists) {
                statement = connection.createStatement();
                String createUserSQL = "CREATE USER " + dbname + " <NAME_EMAIL>";
                statement.executeUpdate(createUserSQL);
                // 可能还需要授予该用户一定的权限，比如创建表等
                String grantPrivilegesSQL = "GRANT CONNECT, RESOURCE TO " + dbname;
                statement.executeUpdate(grantPrivilegesSQL);
                System.out.println("用户（模式）" + dbname + "已创建！");
                msgMap.put("code",Const.DATABASE_CREATE);
                msgMap.put("msg","数据库"+dbname+"已创建");
            } else {
                msgMap.put("code",Const.DATABASE_EXIST);
                msgMap.put("msg","数据库"+dbname+"已存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            msgMap.put("code",Const.DATABASE_ERROR);
            msgMap.put("msg","连接数据库失败");
        } finally {
            // 关闭资源
            try {
                if (statement != null) statement.close();
            } catch (SQLException se2) {
            }
            try {
                if (connection != null) connection.close();
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
        return msgMap;
    }
}
