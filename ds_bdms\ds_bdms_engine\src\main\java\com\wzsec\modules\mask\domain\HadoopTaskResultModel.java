/*********************************************************************
 *
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 *
 *  [2015] - [2020] China Telecom Corporation Limited,
 *  All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 **********************************************************************/
package com.wzsec.modules.mask.domain;


import lombok.Data;

/**
 * @ClassName: HadoopTaskResultModel
 * @Description:
 * <AUTHOR> by wangqi
 * @date 2020-11-16
 */
@Data
public class HadoopTaskResultModel {

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 行数
	 */
	private int count;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 输入路径
	 */
	private String inputpath;

	/**
	 * 输出路径
	 */
	private String outputPath;
}
