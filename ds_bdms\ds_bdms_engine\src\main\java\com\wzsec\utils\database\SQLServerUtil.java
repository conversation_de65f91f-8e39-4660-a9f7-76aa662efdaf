package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;

/**
 * 创建sqlserver表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description 通过java List<Map<String,
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2020年2月7日
 */
public class SQLServerUtil extends DatabaseUtil {

    private final static Logger log = LoggerFactory.getLogger(DatabaseUtil.class);

    private static String JDBC_DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";

    public static void main(String[] args) {
        // 加载驱动类
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        // 建立连接
        String url = "jdbc:sqlserver://<serverName>:<port>;databaseName=<databaseName>";
        String user = "<user>";
        String password = "<password>";
        try (Connection conn = DriverManager.getConnection(url, user, password)) {
            // 使用conn连接进行数据库操作
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }


    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2020年2月7日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append(column).append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2020年2月7日
     */
    //@Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2020年2月7日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append(column).append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }


    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2020年2月7日
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("Field"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("Field")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("Field"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        sb.append(" DEFAULT NULL");
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)ENGINE=InnoDB DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2020年2月7日
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields,String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("COLUMN_NAME"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append(fieldInfo.get("COLUMN_NAME"));// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("TYPE_NAME"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("TYPE_NAME"));// 类型
                    //sb.append(" ").append("(" + fieldInfo.get("PRECISION") + ")");// 长度
                    if(fieldInfo.get("TYPE_NAME").contains("CHAR") || fieldInfo.get("TYPE_NAME").contains("char")){
                        sb.append(" ").append("(" + fieldInfo.get("PRECISION") + ")");// 长度
                    }
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("IS_NULLABLE"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if (null != fieldInfo.get("KEY") && !"".equalsIgnoreCase(fieldInfo.get("KEY"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-05-12
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            String strSQL = "select COLUMN_NAME from information_schema.COLUMNS where table_name = '" + tabname + "';";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            if (rs != null) {
                while (rs.next()) {
                    list.add(rs.getString(1));
                }
            }
        } catch (Exception ex) {
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return list;
    }


    /**
     * @Description:获取数据库表数据
     * <AUTHOR>
     * @date 2020-05-12
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select top " + lineNum + " * from " + tabname + " order by rand()";
            if (lineNum != null && lineNum != 0)
                strSQL = "select * from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                //                StringBuffer sub = new StringBuffer();
//                for (int i = 1; i <= columnCount; i++) {
//                    if (i > 1) {
//                        sub.append(";;");
//                    }
//                    sub.append(rs.getObject(i));
//                }
//                tabDataList.add(sub.toString());

                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }


    /**
     * @Description:获取数据库某个字段非空数据
     * <AUTHOR>
     * @date 2021-03-12
     */
    public static List<String[]> getFieldDataList(Connection conn, String dbname, String tabname, String field, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select top " + lineNum + " " + field + " from " + tabname + " where " + field + "  is not null and " + field + " !='' order by rand()";
            if (lineNum == null || lineNum == 0)
                strSQL = "select " + field + " from " + tabname + " where " + field + "  is not null and " + field + " !=''";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-05-12
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }


    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "SELECT " + "'" + dbnames + "'" + "as 'dbname' ,name as tname FROM SysObjects Where XType='U'";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String dbName = rs.getString(1);
                String table_name = rs.getString(2);
                if (!"sys".equals(dbName) && !"mysql".equals(dbName) && !"information_schema".equals(dbName) && !"performance_schema".equals(dbName)) {
                    if (dbTabMap.containsKey(dbName)) {
                        dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + table_name);
                    } else {
                        dbTabMap.put(dbName, table_name);
                    }
                }

            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            String sql = "SELECT c.name AS COLUMN_NAME, ep.value AS COLUMN_COMMENT,t.name AS COLUMN_TYPE FROM ys.columns c " +
                    "INNER JOIN sys.types t ON c.user_type_id = t.user_type_id " +
                    "LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID('t_user')  AND ep.minor_id = c.column_id " +
                    "WHERE  c.object_id = OBJECT_ID('t_user') " +
                    "ORDER BY c.column_id;";
            rs = stmt.executeQuery("SELECT COLUMN_NAME,COLUMN_COMMENT FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COLUMN_COMMENT"));
                    fieldInfoMap.put("fieldType", rs.getString("COLUMN_TYPE"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * @param tableName：表名
     * @param dburl：数据库连接信息
     * @param username：数据库用户名
     * @param password：数据库密码
     * @return List<Map < String, String>>：数据库字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年2月7日
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            //ResultSet rs = stmt.executeQuery("SHOW FULL COLUMNS FROM `" + tableName + "`;");
            ResultSet rs = stmt.executeQuery("sp_columns " + tableName);
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();

                    fieldInfoMap.put("TABLE_QUALLIFIER", rs.getString("TABLE_QUALIFIER"));// 表空间
                    fieldInfoMap.put("TABLE_OWNER", rs.getString("TABLE_OWNER"));// 表用户
                    fieldInfoMap.put("TABLE_NAME", rs.getString("TABLE_NAME"));// 表名
                    fieldInfoMap.put("COLUMN_NAME", rs.getString("COLUMN_NAME"));// 字段名称
                    fieldInfoMap.put("TYPE_NAME", rs.getString("TYPE_NAME"));// 字段类型
                    fieldInfoMap.put("PRECISION", rs.getString("PRECISION"));// 字段长度
                    fieldInfoMap.put("DATA_TYPE", rs.getString("DATA_TYPE"));// 数据长度
                    fieldInfoMap.put("IS_NULLABLE", rs.getString("IS_NULLABLE"));// 是否为空
                    fieldInfoList.add(fieldInfoMap);
                }
            }
            rs = stmt.executeQuery("SELECT TABLE_NAME,COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE "
                    + "WHERE TABLE_NAME = '" + tableName + "'");
            if (rs != null) {
                while (rs.next()) {
                    if (null != fieldInfoList) {
                        for (Map<String, String> map : fieldInfoList) {
                            if (map.get("COLUMN_NAME").equals(rs.getString("COLUMN_NAME"))) {
                                map.put("KEY", "YES");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return fieldInfoList;
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表信息
     * @Description 查询表信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:56
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            tableInfoMap = new HashMap<String, String>();
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TABLE_NAME,TABLE_COMMENT,TABLE_ROWS,ROUND(((data_length + index_length) / 1024 / 1024), 2) AS Size_MB FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
                tableInfoMap.put("tableRows", rs.getString("TABLE_ROWS"));
                tableInfoMap.put("dataSize", rs.getString("Size_MB"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs,stmt,null);
        }
        return tableInfoMap;
    }

    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 14:00
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        // 1. 从输入参数中获取数据库连接信息
        String driverProgram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");
        // 解密密码（如果需要）
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }

        String dbname = map.get("dbname");
        String srcPort = map.get("srcport");
        String srcIp = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 注册 JDBC 驱动
            Class.forName(driverProgram);
            // 2. 打开连接到 SQL Server 数据库（
            String jdbcUrl = "jdbc:sqlserver://" + srcIp + ":" + srcPort + ";databaseName=master";
            connection = DriverManager.getConnection(jdbcUrl, username, password);

            // 3. 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            String checkDatabaseSQL = "SELECT name FROM sys.databases WHERE name = '" + dbname + "'";
            statement = connection.createStatement();
            resultSet = statement.executeQuery(checkDatabaseSQL);
            boolean databaseExists = resultSet.next();

            // 4. 如果数据库不存在，则创建它
            if (!databaseExists) {
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "JDBC驱动未找到：" + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
        return msgMap;
    }
}
