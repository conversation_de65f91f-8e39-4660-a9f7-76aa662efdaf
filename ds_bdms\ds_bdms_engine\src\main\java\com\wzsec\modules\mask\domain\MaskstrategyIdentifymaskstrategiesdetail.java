package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-21
*/
@Entity
@Data
@Table(name="sdd_maskstrategy_identifymaskstrategiesdetail")
public class MaskstrategyIdentifymaskstrategiesdetail implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 策略id */
    @Column(name = "strategyid")
    private Integer strategyid;

    /** 数据名称 */
    @Column(name = "dataname")
    private String dataname;

    /** 识别规则id */
    @Column(name = "senruleid")
    private Integer senruleid;

    /** 脱敏规则id */
    @Column(name = "maskruleid")
    private Integer maskruleid;

    /** 脱敏算法id */
    @Column(name = "algorithmid")
    private Integer algorithmid;

    /** 参数 */
    @Column(name = "param")
    private String param;

    /** 密钥 */
    @Column(name = "secretkey")
    private String secretkey;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 异常数据处置 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(MaskstrategyIdentifymaskstrategiesdetail source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}