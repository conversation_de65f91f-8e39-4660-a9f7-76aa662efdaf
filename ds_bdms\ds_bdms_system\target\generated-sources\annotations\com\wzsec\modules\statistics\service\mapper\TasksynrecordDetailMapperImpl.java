package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.TasksynrecordDetail;
import com.wzsec.modules.statistics.service.dto.TasksynrecordDetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:01+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class TasksynrecordDetailMapperImpl implements TasksynrecordDetailMapper {

    @Override
    public TasksynrecordDetailDto toDto(TasksynrecordDetail entity) {
        if ( entity == null ) {
            return null;
        }

        TasksynrecordDetailDto tasksynrecordDetailDto = new TasksynrecordDetailDto();

        tasksynrecordDetailDto.setCreatetime( entity.getCreatetime() );
        tasksynrecordDetailDto.setCreateuser( entity.getCreateuser() );
        tasksynrecordDetailDto.setDbname( entity.getDbname() );
        tasksynrecordDetailDto.setFieldcname( entity.getFieldcname() );
        tasksynrecordDetailDto.setFieldname( entity.getFieldname() );
        tasksynrecordDetailDto.setId( entity.getId() );
        tasksynrecordDetailDto.setSparefield1( entity.getSparefield1() );
        tasksynrecordDetailDto.setSparefield2( entity.getSparefield2() );
        tasksynrecordDetailDto.setSparefield3( entity.getSparefield3() );
        tasksynrecordDetailDto.setSyndetail( entity.getSyndetail() );
        tasksynrecordDetailDto.setTablecname( entity.getTablecname() );
        tasksynrecordDetailDto.setTablename( entity.getTablename() );
        tasksynrecordDetailDto.setTaskname( entity.getTaskname() );
        tasksynrecordDetailDto.setTasknumber( entity.getTasknumber() );

        return tasksynrecordDetailDto;
    }

    @Override
    public List<TasksynrecordDetailDto> toDto(List<TasksynrecordDetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TasksynrecordDetailDto> list = new ArrayList<TasksynrecordDetailDto>( entityList.size() );
        for ( TasksynrecordDetail tasksynrecordDetail : entityList ) {
            list.add( toDto( tasksynrecordDetail ) );
        }

        return list;
    }

    @Override
    public TasksynrecordDetail toEntity(TasksynrecordDetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        TasksynrecordDetail tasksynrecordDetail = new TasksynrecordDetail();

        tasksynrecordDetail.setCreatetime( dto.getCreatetime() );
        tasksynrecordDetail.setCreateuser( dto.getCreateuser() );
        tasksynrecordDetail.setDbname( dto.getDbname() );
        tasksynrecordDetail.setFieldcname( dto.getFieldcname() );
        tasksynrecordDetail.setFieldname( dto.getFieldname() );
        tasksynrecordDetail.setId( dto.getId() );
        tasksynrecordDetail.setSparefield1( dto.getSparefield1() );
        tasksynrecordDetail.setSparefield2( dto.getSparefield2() );
        tasksynrecordDetail.setSparefield3( dto.getSparefield3() );
        tasksynrecordDetail.setSyndetail( dto.getSyndetail() );
        tasksynrecordDetail.setTablecname( dto.getTablecname() );
        tasksynrecordDetail.setTablename( dto.getTablename() );
        tasksynrecordDetail.setTaskname( dto.getTaskname() );
        tasksynrecordDetail.setTasknumber( dto.getTasknumber() );

        return tasksynrecordDetail;
    }

    @Override
    public List<TasksynrecordDetail> toEntity(List<TasksynrecordDetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TasksynrecordDetail> list = new ArrayList<TasksynrecordDetail>( dtoList.size() );
        for ( TasksynrecordDetailDto tasksynrecordDetailDto : dtoList ) {
            list.add( toEntity( tasksynrecordDetailDto ) );
        }

        return list;
    }
}
