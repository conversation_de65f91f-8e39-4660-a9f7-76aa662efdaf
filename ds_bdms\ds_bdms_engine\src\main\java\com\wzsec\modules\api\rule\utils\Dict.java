package com.wzsec.modules.api.rule.utils;

/**
 * @ClassName: Dict
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年11月27日
 */
public class Dict {

	public static final String[] firstName = new String[] { "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈", "褚", "卫",
			"蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许", "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏", "陶", "姜", "戚",
			"谢", "邹", "喻", "柏", "水", "窦", "章", "云", "苏", "潘", "葛", "奚", "范", "彭", "郎", "鲁", "韦", "昌", "马", "苗", "凤",
			"花", "方", "俞", "任", "袁", "柳", "酆", "鲍", "史", "唐", "费", "廉", "岑", "薛", "雷", "贺", "倪", "汤", "滕", "殷", "罗",
			"毕", "郝", "邬", "安", "常", "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍", "余", "元", "卜", "顾", "孟", "平", "黄",
			"和", "穆", "萧", "尹", "姚", "邵", "湛", "汪", "祁", "毛", "禹", "狄", "米", "贝", "明", "臧", "计", "伏", "成", "戴", "谈",
			"宋", "茅", "庞", "熊", "纪", "舒", "屈", "项", "祝", "董", "梁", "杜", "阮", "蓝", "闵", "席", "季", "麻", "强", "贾", "路",
			"娄", "危", "江", "童", "颜", "郭", "梅", "盛", "林", "刁", "钟", "徐", "邱", "骆", "高", "夏", "蔡", "田", "樊", "胡", "凌",
			"霍", "虞", "万", "支", "柯", "昝", "管", "卢", "莫", "经", "房", "裘", "缪", "干", "解", "应", "宗", "丁", "宣", "贲", "邓",
			"郁", "单", "杭", "洪", "包", "诸", "左", "石", "崔", "吉", "钮", "龚", "程", "嵇", "邢", "滑", "裴", "陆", "荣", "翁", "荀",
			"羊", "於", "惠", "甄", "曲", "家", "封", "芮", "羿", "储", "靳", "汲", "邴", "糜", "松", "井", "段", "富", "巫", "乌", "焦",
			"巴", "弓", "牧", "隗", "山", "谷", "车", "侯", "宓", "蓬", "全", "郗", "班", "仰", "秋", "仲", "伊", "宫", "宁", "仇", "栾",
			"暴", "甘", "钭", "厉", "戎", "祖", "武", "符", "刘", "景", "詹", "束", "龙", "叶", "幸", "司", "韶", "郜", "黎", "蓟", "薄",
			"印", "宿", "白", "怀", "蒲", "邰", "从", "鄂", "索", "咸", "籍", "赖", "卓", "蔺", "屠", "蒙", "池", "乔", "阴", "鬱", "胥",
			"能", "苍", "双", "闻", "莘", "党", "翟", "谭", "贡", "劳", "逄", "姬", "申", "扶", "堵", "冉", "宰", "郦", "雍", "卻", "璩",
			"桑", "桂", "濮", "牛", "寿", "通", "边", "扈", "燕", "冀", "郏", "浦", "尚", "农", "温", "别", "庄", "晏", "柴", "瞿", "阎",
			"充", "慕", "连", "茹", "习", "宦", "艾", "鱼", "容", "向", "古", "易", "慎", "戈", "廖", "庾", "终", "暨", "居", "衡", "步",
			"都", "耿", "满", "弘", "匡", "国", "文", "寇", "广", "禄", "阙", "东", "欧", "殳", "沃", "利", "蔚", "越", "夔", "隆", "师",
			"巩", "厍", "聂", "晁", "勾", "敖", "融", "冷", "訾", "辛", "阚", "那", "简", "饶", "空", "曾", "毋", "沙", "乜", "养", "鞠",
			"须", "丰", "巢", "关", "蒯", "相", "查", "后", "荆", "红", "游", "竺", "权", "逯", "盖", "益", "桓", "公", "晋", "楚", "闫",
			"法", "汝", "鄢", "涂", "钦", "归", "海", "岳", "帅", "缑", "亢", "况", "后", "有", "郈", "琴", "商", "牟", "佘", "佴", "伯",
			"赏", "墨", "哈", "谯", "笪", "年", "爱", "阳", "佟", "言", "福" };

	public static final String[] doubleSurName = new String[] { "万俟", "司马", "上官", "欧阳", "夏侯", "诸葛", "闻人", "东方", "赫连",
			"皇甫", "尉迟", "公羊", "澹台", "公冶", "宗政", "濮阳", "淳于", "单于", "太叔", "申屠", "公孙", "仲孙", "轩辕", "令狐", "钟离", "宇文", "长孙",
			"慕容", "鲜于", "闾丘", "司徒", "司空", "亓官", "司寇", "仉督", "子车", "颛孙", "端木", "巫马", "公西", "漆雕", "乐正", "壤驷", "公良", "拓跋",
			"夹谷", "宰父", "谷粱", "段干", "百里", "东郭", "南门", "呼延", "归海", "羊舌", "微生", "梁丘", "左丘", "东门", "西门", "南宫", "第五" };

	public static final String[] adressChar = new String[] { "省", "市", "路", "街", "楼", "区", "县", "乡", "镇", "村", "组", "号",
			"自治州", "自治区", "特别行政区", "地区", "北京", "天津", "安徽", "安庆", "蚌埠", "亳州", "巢湖", "池州", "滁州", "阜阳", "合肥", "淮北", "淮南",
			"黄山", "六安", "澳门", "香港", "福建", "福州", "龙岩", "南平", "宁德", "莆田", "泉州", "厦门", "漳州", "甘肃", "白银", "定西", "甘南", "嘉峪关",
			"金昌", "酒泉", "兰州", "临夏", "陇南", "平凉", "广东", "潮州", "东莞", "佛山", "广州", "河源", "惠州", "江门", "揭阳", "茂名", "梅州", "清远",
			"汕头", "汕尾", "韶关", "深圳", "阳江", "云浮", "湛江", "肇庆", "中山", "珠海", "广西", "百色", "北海", "崇左", "防城港", "贵港", "桂林", "河池",
			"贺州", "来宾", "柳州", "南宁", "钦州", "梧州", "玉林", "贵州", "安顺", "毕节", "贵阳", "六盘水", "海南", "海口", "三亚", "河北", "保定", "沧州",
			"承德", "邯郸", "衡水", "廊坊", "秦皇岛", "石家庄", "唐山", "邢台", "张家口", "海东", "海南", "海西", "黄南", "西宁", "玉树", "河南", "安阳",
			"鹤壁", "焦作", "开封", "洛阳", "漯河", "南阳", "平顶山", "濮阳", "三门峡", "商丘", "新乡", "信阳", "许昌", "郑州", "周口", "驻马店", "庆阳",
			"天水", "武威", "黑龙江", "大庆", "大兴安岭", "哈尔滨", "鹤岗", "黑河", "鸡西", "佳木斯", "牡丹江", "七台河", "齐齐哈尔", "双鸭山", "绥化", "伊春",
			"银川", "中卫", "青海", "果洛", "海北", "湖北", "鄂州", "恩施", "黄冈", "黄石", "荆门", "荆州", "十堰", "随州", "武汉", "咸宁", "襄樊", "孝感",
			"宜昌", "黔东", "黔南", "黔西", "铜仁", "遵义", "张掖", "湖南", "长沙", "常德", "郴州", "衡阳", "怀化", "娄底", "邵阳", "湘潭", "湘西", "益阳",
			"永州", "岳阳", "张家界", "株洲", "宁夏", "固原", "石嘴山", "吴忠", "吉林", "白城", "白山", "长春", "吉林", "辽源", "四平", "松原", "通化",
			"延边", "资阳", "自贡", "西藏", "阿里", "昌都", "拉萨", "林芝", "那曲", "日喀则", "山南", "江苏", "常州", "淮安", "连云港", "南京", "南通",
			"苏州", "宿迁", "泰州", "无锡", "徐州", "盐城", "扬州", "镇江", "江西", "抚州", "赣州", "吉安", "景德镇", "九江", "南昌", "萍乡", "上饶", "新余",
			"宜春", "鹰潭", "辽宁", "鞍山", "本溪", "朝阳", "大连", "丹东", "抚顺", "阜新", "葫芦岛", "锦州", "辽阳", "盘锦", "沈阳", "铁岭", "营口",
			"内蒙古", "阿拉善盟", "巴彦淖尔", "包头", "赤峰", "鄂尔多斯", "呼和浩特", "呼伦贝尔", "通辽", "乌海", "乌兰察布", "锡林郭勒盟", "兴安盟", "宿州", "铜陵",
			"芜湖", "宣城", "山东", "滨州", "德州", "东营", "菏泽", "济南", "济宁", "莱芜", "聊城", "临沂", "青岛", "日照", "泰安", "威海", "潍坊", "烟台",
			"枣庄", "淄博", "山西", "长治", "大同", "晋城", "晋中", "临汾", "吕梁", "朔州", "太原", "忻州", "阳泉", "运城", "陕西", "安康", "宝鸡", "汉中",
			"商洛", "铜川", "渭南", "西安", "咸阳", "延安", "榆林", "四川", "阿坝", "巴中", "成都", "达州", "德阳", "甘孜", "广安", "广元", "乐山", "凉山",
			"泸州", "眉山", "绵阳", "内江", "南充", "攀枝花", "遂宁", "雅安", "宜宾", "新疆", "阿克苏", "阿勒泰", "巴音郭楞蒙古", "博尔塔拉蒙古", "昌吉", "哈密",
			"和田", "喀什", "克拉玛依", "克孜勒苏柯尔克孜", "塔城", "吐鲁番", "乌鲁木齐", "伊犁哈萨克", "云南", "保山", "楚雄", "大理", "德宏", "迪庆", "红河",
			"昆明", "丽江", "临沧", "怒江", "普洱", "曲靖", "文山", "西双版纳", "玉溪", "昭通", "马鞍山", "浙江", "杭州", "湖州", "嘉兴", "金华", "丽水",
			"宁波", "衢州", "绍兴", "台州", "温州", "舟山", "重庆", "台湾", "台北", "高雄", "基隆", "台中", "台南", "新竹", "嘉义" };

	public static void main(String[] args) {
		/*
		 * String ss =
		 * "赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏陶姜戚谢邹喻柏水窦章云苏潘葛奚范彭郎鲁韦昌马苗凤花方俞任袁柳酆鲍史唐费廉岑薛雷贺倪汤滕殷罗毕郝邬安常乐于时傅皮卞齐康伍余元卜顾孟平黄和穆萧尹姚邵湛汪祁毛禹狄米贝明臧计伏成戴谈宋茅庞熊纪舒屈项祝董梁杜阮蓝闵席季麻强贾路娄危江童颜郭梅盛林刁钟徐邱骆高夏蔡田樊胡凌霍虞万支柯昝管卢莫经房裘缪干解应宗丁宣贲邓郁单杭洪包诸左石崔吉钮龚程嵇邢滑裴陆荣翁荀羊於惠甄曲家封芮羿储靳汲邴糜松井段富巫乌焦巴弓牧隗山谷车侯宓蓬全郗班仰秋仲伊宫宁仇栾暴甘钭厉戎祖武符刘景詹束龙叶幸司韶郜黎蓟薄印宿白怀蒲邰从鄂索咸籍赖卓蔺屠蒙池乔阴鬱胥能苍双闻莘党翟谭贡劳逄姬申扶堵冉宰郦雍卻璩桑桂濮牛寿通边扈燕冀郏浦尚农温别庄晏柴瞿阎充慕连茹习宦艾鱼容向古易慎戈廖庾终暨居衡步都耿满弘匡国文寇广禄阙东欧殳沃利蔚越夔隆师巩厍聂晁勾敖融冷訾辛阚那简饶空曾毋沙乜养鞠须丰巢关蒯相查后荆红游竺权逯盖益桓公";
		 * String sss = ""; for (int i = 0; i < ss.length(); i++) { sss += "\""
		 * + ss.substring(i, i + 1) + "\","; } System.out.println(sss);
		 */
	}

}
