package com.wzsec.utils.rule;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2019年9月25日
 * @Description MEID、IMEI检测
 */
public class DeviceUtil {

    /**
     * @Description
     * <AUTHOR>
     * @version 下午2:47:12
     */
    public static void main(String[] args) {
		
		/* Date date1 = new Date(202556L);
	     System.out.println(date1);
	     Date date2 = new Date(142719L);
	     System.out.println(date2);
	     */
        Date date3 = new Date(39560265010725L);
        System.out.println(date3);
	     
		 /*System.out.println(formatMEID("864678032960146"));
         System.out.println(getMEID15("8646 7803 2960 14"));*/            
        /*System.out.println(formatImei("105747839859712"));
        
        Date d = new Date();
        String s =String.valueOf(d.getTime());
        System.out.println(s.length());
        System.out.println(s);*/

        System.out.println(formatIMEI("864678032960146"));
        System.out.println(getIMEI15("86467803296014"));
    }

    /**
     * @Description 格式化MEID
     * <AUTHOR>
     * @version 下午3:55:23
     */
    public static String formatMEID(String meid) {
        int dxml = meid.length();
        if (dxml != 14 && dxml != 16) {
            return meid;
        }
        String meidRes = "";
        if (dxml == 14) {
            meidRes = meid + getMEID15(meid);
        }
        if (dxml == 16) {
            meidRes = meid.substring(2) + getMEID15(meid.substring(2));
        }
        return meidRes;
    }

    /**
     * 格式化IMEI
     * 因为IMEI格式不统一，长度有14位和16位的，所以，为了统一，将14位和16位的MEID，统一设置为15位的 设置格式：
     * 如果IMEI长度为14位，那么直接得到第15位，如果MEID长度为16位，那么直接在根据前14位得到第15位
     * 如果IMEI长度为其他长度，那么直接返回原值
     *
     * @param imei
     * @return
     */
    public static String formatIMEI(String imei) {
        int dxml = imei.length();
        if (dxml != 14 && dxml != 16) {
            return imei;
        }
        String imeiRes = "";
        if (dxml == 14) {
            imeiRes = imei + getIMEI15(imei);
        }
        if (dxml == 16) {
            imeiRes = imei + getIMEI15(imei.substring(0, 14));
        }
        return imeiRes;
    }

    /**
     * 根据MEID的前14位，得到第15位的校验位
     * MEID校验码算法：
     * (1).将偶数位数字分别乘以2，分别计算个位数和十位数之和，注意是16进制数
     * (2).将奇数位数字相加，再加上上一步算得的值
     * (3).如果得出的数个位是0则校验位为0，否则为10(这里的10是16进制)减去个位数
     * 如：AF 01 23 45 0A BC DE 偶数位乘以2得到F*2=1E 1*2=02 3*2=06 5*2=0A A*2=14 C*2=18 E*2=1C,
     * 计算奇数位数字之和和偶数位个位十位之和，得到 A+(1+E)+0+2+2+6+4+A+0+(1+4)+B+(1+8)+D+(1+C)=64
     * 校验位 10-4 = C
     *
     * @param meid
     * @return
     */
    public static String getMEID15(String meid) {
        if (meid.length() == 14) {
            String myStr[] = {"a", "b", "c", "d", "e", "f"};
            int sum = 0;
            for (int i = 0; i < meid.length(); i++) {
                String param = meid.substring(i, i + 1);
                for (int j = 0; j < myStr.length; j++) {
                    if (param.equalsIgnoreCase(myStr[j])) {
                        param = "1" + String.valueOf(j);
                    }
                }
                if (i % 2 == 0) {
                    sum = sum + Integer.parseInt(param);
                } else {
                    sum = sum + 2 * Integer.parseInt(param) % 16;
                    sum = sum + 2 * Integer.parseInt(param) / 16;
                }
            }
            if (sum % 16 == 0) {
                return "0";
            } else {
                int result = 16 - sum % 16;
                if (result > 9) {
                    result += 65 - 10;
                }
                return result + "";
            }
        } else {
            return "";
        }
    }

    /**
     * 根据IMEI的前14位，得到第15位的校验位
     * IMEI校验码算法：
     * (1).将偶数位数字分别乘以2，分别计算个位数和十位数之和
     * (2).将奇数位数字相加，再加上上一步算得的值
     * (3).如果得出的数个位是0则校验位为0，否则为10减去个位数
     * 如：35 89 01 80 69 72 41 偶数位乘以2得到5*2=10 9*2=18 1*2=02 0*2=00 9*2=18 2*2=04 1*2=02,计算奇数位数字之和和偶数位个位十位之和，
     * 得到 3+(1+0)+8+(1+8)+0+(0+2)+8+(0+0)+6+(1+8)+7+(0+4)+4+(0+2)=63
     * 校验位 10-3 = 7
     *
     * @param imei
     * @return
     */
    public static String getIMEI15(String imei) {
        if (imei.length() == 14) {
            char[] imeiChar = imei.toCharArray();
            int resultInt = 0;
            for (int i = 0; i < imeiChar.length; i++) {
                int a = Integer.parseInt(String.valueOf(imeiChar[i]));
                i++;
                final int temp = Integer.parseInt(String.valueOf(imeiChar[i])) * 2;
                final int b = temp < 10 ? temp : temp - 9;
                resultInt += a + b;
            }
            resultInt %= 10;
            resultInt = resultInt == 0 ? 0 : 10 - resultInt;
            return resultInt + "";
        } else {
            return "";
        }
    }

    /**
     * 字符串转换为Ascii
     * @param value
     * @return
     */
    public static String stringToAscii(String value) {
        StringBuffer sbu = new StringBuffer();
        char[] chars = value.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if(i != chars.length - 1){
                sbu.append((int)chars[i]).append(",");
            }else {
                sbu.append((int)chars[i]);
            }
        }
        return sbu.toString();
    }
}
