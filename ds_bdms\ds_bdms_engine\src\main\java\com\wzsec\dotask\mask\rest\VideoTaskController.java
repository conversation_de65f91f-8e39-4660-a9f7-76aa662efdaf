package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoVideoTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/engine/video/task")
public class VideoTaskController {

    private final DoVideoTaskService doVideoTaskService;

    private final UserService userService;

    public VideoTaskController(DoVideoTaskService doVideoTaskService, UserService userService) {
        this.doVideoTaskService = doVideoTaskService;
        this.userService = userService;
    }

    @Log("执行视频脱敏任务")
    @PostMapping(value = "/exec/{id}")
    @PreAuthorize("@el.check('maskVideotaskconfig:edit')")
    public ResponseEntity<Object> execution(@PathVariable Integer id) {
        System.out.println("执行视频脱敏任务：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doVideoTaskService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("定时执行数据库脱敏任务")
    @PostMapping(value = "/timingexec/{id}")
    public ResponseEntity<Object> timingexecution(@PathVariable Integer id) {
        System.out.println("定时执行视频脱敏任务：" + id);
        doVideoTaskService.execution(id, Const.TASK_SUBMITTYPE_AUTOSUBMIT);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
