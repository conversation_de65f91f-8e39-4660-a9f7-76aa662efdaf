package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskTablestructure;
import com.wzsec.modules.mask.service.dto.MaskTablestructureDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskTablestructureMapperImpl implements MaskTablestructureMapper {

    @Override
    public MaskTablestructureDto toDto(MaskTablestructure entity) {
        if ( entity == null ) {
            return null;
        }

        MaskTablestructureDto maskTablestructureDto = new MaskTablestructureDto();

        maskTablestructureDto.setAttribute( entity.getAttribute() );
        maskTablestructureDto.setCreatetime( entity.getCreatetime() );
        maskTablestructureDto.setDbname( entity.getDbname() );
        maskTablestructureDto.setFieldcname( entity.getFieldcname() );
        maskTablestructureDto.setFieldename( entity.getFieldename() );
        maskTablestructureDto.setFieldid( entity.getFieldid() );
        maskTablestructureDto.setId( entity.getId() );
        maskTablestructureDto.setSource( entity.getSource() );
        maskTablestructureDto.setSparefield1( entity.getSparefield1() );
        maskTablestructureDto.setSparefield2( entity.getSparefield2() );
        maskTablestructureDto.setSparefield3( entity.getSparefield3() );
        maskTablestructureDto.setSparefield4( entity.getSparefield4() );
        maskTablestructureDto.setTablecname( entity.getTablecname() );
        maskTablestructureDto.setTableid( entity.getTableid() );
        maskTablestructureDto.setTablename( entity.getTablename() );
        maskTablestructureDto.setUpdatetime( entity.getUpdatetime() );

        return maskTablestructureDto;
    }

    @Override
    public List<MaskTablestructureDto> toDto(List<MaskTablestructure> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskTablestructureDto> list = new ArrayList<MaskTablestructureDto>( entityList.size() );
        for ( MaskTablestructure maskTablestructure : entityList ) {
            list.add( toDto( maskTablestructure ) );
        }

        return list;
    }

    @Override
    public MaskTablestructure toEntity(MaskTablestructureDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskTablestructure maskTablestructure = new MaskTablestructure();

        maskTablestructure.setAttribute( dto.getAttribute() );
        maskTablestructure.setCreatetime( dto.getCreatetime() );
        maskTablestructure.setDbname( dto.getDbname() );
        maskTablestructure.setFieldcname( dto.getFieldcname() );
        maskTablestructure.setFieldename( dto.getFieldename() );
        maskTablestructure.setFieldid( dto.getFieldid() );
        maskTablestructure.setId( dto.getId() );
        maskTablestructure.setSource( dto.getSource() );
        maskTablestructure.setSparefield1( dto.getSparefield1() );
        maskTablestructure.setSparefield2( dto.getSparefield2() );
        maskTablestructure.setSparefield3( dto.getSparefield3() );
        maskTablestructure.setSparefield4( dto.getSparefield4() );
        maskTablestructure.setTablecname( dto.getTablecname() );
        maskTablestructure.setTableid( dto.getTableid() );
        maskTablestructure.setTablename( dto.getTablename() );
        maskTablestructure.setUpdatetime( dto.getUpdatetime() );

        return maskTablestructure;
    }

    @Override
    public List<MaskTablestructure> toEntity(List<MaskTablestructureDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskTablestructure> list = new ArrayList<MaskTablestructure>( dtoList.size() );
        for ( MaskTablestructureDto maskTablestructureDto : dtoList ) {
            list.add( toEntity( maskTablestructureDto ) );
        }

        return list;
    }
}
