package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2021-01-19
*/
@Entity
@Data
@Table(name="sdd_maskaudit_logresult")
public class MaskAuditLogResult implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名称 */
    @Column(name = "taskname")
    private String taskname;

    /** 客户名称 */
    @Column(name = "custname")
    private String custname;

    /** 客户简称 */
    @Column(name = "cust_simplename")
    private String custSimplename;

    /** 用户id */
    @Column(name = "userid")
    private String userid;

    /** 应用id */
    @Column(name = "appid")
    private String appid;

    /** 应用名称 */
    @Column(name = "appname")
    private String appname;

    /** 日志标识 */
    @Column(name = "logsign")
    private String logsign;

    /** 接口编码 */
    @Column(name = "apicode")
    private String apicode;

    /** 接口名称 */
    @Column(name = "apiname")
    private String apiname;

    /** 接口方法 */
    @Column(name = "apimethod")
    private String apimethod;

    /** 接口类型 */
    @Column(name = "apitype")
    private String apitype;

    /** 接口url */
    @Column(name = "url")
    private String url;

    /** 风险程度（0：高，1：低） */
    @Column(name = "risk")
    private String risk;

    /** 风险程度统计次数 */
    @Column(name = "checkcount")
    private Integer checkcount;

    /** 检测时间 */
    @Column(name = "checktime")
    private String checktime;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskAuditLogResult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
