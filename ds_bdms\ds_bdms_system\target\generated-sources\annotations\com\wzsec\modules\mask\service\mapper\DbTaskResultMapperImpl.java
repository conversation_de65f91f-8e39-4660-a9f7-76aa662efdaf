package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DBTaskResult;
import com.wzsec.modules.mask.service.dto.DBTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:20+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DbTaskResultMapperImpl implements DbTaskResultMapper {

    @Override
    public DBTaskResult toEntity(DBTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        DBTaskResult dBTaskResult = new DBTaskResult();

        dBTaskResult.setId( dto.getId() );
        dBTaskResult.setTaskname( dto.getTaskname() );
        dBTaskResult.setTabname( dto.getTabname() );
        dBTaskResult.setDbname( dto.getDbname() );
        dBTaskResult.setIpaddress( dto.getIpaddress() );
        dBTaskResult.setOutputname( dto.getOutputname() );
        dBTaskResult.setOutputpath( dto.getOutputpath() );
        dBTaskResult.setOutputipaddress( dto.getOutputipaddress() );
        dBTaskResult.setOutputtype( dto.getOutputtype() );
        dBTaskResult.setTotallines( dto.getTotallines() );
        dBTaskResult.setMasklines( dto.getMasklines() );
        dBTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        dBTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        dBTaskResult.setStrategyjson( dto.getStrategyjson() );
        dBTaskResult.setTaskstatus( dto.getTaskstatus() );
        dBTaskResult.setUsername( dto.getUsername() );
        dBTaskResult.setJobstarttime( dto.getJobstarttime() );
        dBTaskResult.setJobendtime( dto.getJobendtime() );
        dBTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        dBTaskResult.setRemark( dto.getRemark() );
        dBTaskResult.setSparefield1( dto.getSparefield1() );
        dBTaskResult.setSparefield2( dto.getSparefield2() );
        dBTaskResult.setSparefield3( dto.getSparefield3() );
        dBTaskResult.setSparefield4( dto.getSparefield4() );
        dBTaskResult.setSparefield5( dto.getSparefield5() );

        return dBTaskResult;
    }

    @Override
    public DBTaskResultDto toDto(DBTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        DBTaskResultDto dBTaskResultDto = new DBTaskResultDto();

        dBTaskResultDto.setId( entity.getId() );
        dBTaskResultDto.setTaskname( entity.getTaskname() );
        dBTaskResultDto.setTabname( entity.getTabname() );
        dBTaskResultDto.setDbname( entity.getDbname() );
        dBTaskResultDto.setIpaddress( entity.getIpaddress() );
        dBTaskResultDto.setOutputname( entity.getOutputname() );
        dBTaskResultDto.setOutputpath( entity.getOutputpath() );
        dBTaskResultDto.setOutputipaddress( entity.getOutputipaddress() );
        dBTaskResultDto.setOutputtype( entity.getOutputtype() );
        dBTaskResultDto.setTotallines( entity.getTotallines() );
        dBTaskResultDto.setMasklines( entity.getMasklines() );
        dBTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        dBTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        dBTaskResultDto.setStrategyjson( entity.getStrategyjson() );
        dBTaskResultDto.setTaskstatus( entity.getTaskstatus() );
        dBTaskResultDto.setUsername( entity.getUsername() );
        dBTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        dBTaskResultDto.setJobendtime( entity.getJobendtime() );
        dBTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        dBTaskResultDto.setRemark( entity.getRemark() );
        dBTaskResultDto.setSparefield1( entity.getSparefield1() );
        dBTaskResultDto.setSparefield2( entity.getSparefield2() );
        dBTaskResultDto.setSparefield3( entity.getSparefield3() );
        dBTaskResultDto.setSparefield4( entity.getSparefield4() );
        dBTaskResultDto.setSparefield5( entity.getSparefield5() );

        return dBTaskResultDto;
    }

    @Override
    public List<DBTaskResult> toEntity(List<DBTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DBTaskResult> list = new ArrayList<DBTaskResult>( dtoList.size() );
        for ( DBTaskResultDto dBTaskResultDto : dtoList ) {
            list.add( toEntity( dBTaskResultDto ) );
        }

        return list;
    }

    @Override
    public List<DBTaskResultDto> toDto(List<DBTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DBTaskResultDto> list = new ArrayList<DBTaskResultDto>( entityList.size() );
        for ( DBTaskResult dBTaskResult : entityList ) {
            list.add( toDto( dBTaskResult ) );
        }

        return list;
    }
}
