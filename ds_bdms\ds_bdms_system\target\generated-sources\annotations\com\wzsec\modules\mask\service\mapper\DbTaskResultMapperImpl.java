package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DBTaskResult;
import com.wzsec.modules.mask.service.dto.DBTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:01+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DbTaskResultMapperImpl implements DbTaskResultMapper {

    @Override
    public DBTaskResultDto toDto(DBTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        DBTaskResultDto dBTaskResultDto = new DBTaskResultDto();

        dBTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        dBTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        dBTaskResultDto.setDbname( entity.getDbname() );
        dBTaskResultDto.setId( entity.getId() );
        dBTaskResultDto.setIpaddress( entity.getIpaddress() );
        dBTaskResultDto.setJobendtime( entity.getJobendtime() );
        dBTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        dBTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        dBTaskResultDto.setMasklines( entity.getMasklines() );
        dBTaskResultDto.setOutputipaddress( entity.getOutputipaddress() );
        dBTaskResultDto.setOutputname( entity.getOutputname() );
        dBTaskResultDto.setOutputpath( entity.getOutputpath() );
        dBTaskResultDto.setOutputtype( entity.getOutputtype() );
        dBTaskResultDto.setRemark( entity.getRemark() );
        dBTaskResultDto.setSparefield1( entity.getSparefield1() );
        dBTaskResultDto.setSparefield2( entity.getSparefield2() );
        dBTaskResultDto.setSparefield3( entity.getSparefield3() );
        dBTaskResultDto.setSparefield4( entity.getSparefield4() );
        dBTaskResultDto.setSparefield5( entity.getSparefield5() );
        dBTaskResultDto.setStrategyjson( entity.getStrategyjson() );
        dBTaskResultDto.setTabname( entity.getTabname() );
        dBTaskResultDto.setTaskname( entity.getTaskname() );
        dBTaskResultDto.setTaskstatus( entity.getTaskstatus() );
        dBTaskResultDto.setTotallines( entity.getTotallines() );
        dBTaskResultDto.setUsername( entity.getUsername() );

        return dBTaskResultDto;
    }

    @Override
    public List<DBTaskResultDto> toDto(List<DBTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DBTaskResultDto> list = new ArrayList<DBTaskResultDto>( entityList.size() );
        for ( DBTaskResult dBTaskResult : entityList ) {
            list.add( toDto( dBTaskResult ) );
        }

        return list;
    }

    @Override
    public DBTaskResult toEntity(DBTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        DBTaskResult dBTaskResult = new DBTaskResult();

        dBTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        dBTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        dBTaskResult.setDbname( dto.getDbname() );
        dBTaskResult.setId( dto.getId() );
        dBTaskResult.setIpaddress( dto.getIpaddress() );
        dBTaskResult.setJobendtime( dto.getJobendtime() );
        dBTaskResult.setJobstarttime( dto.getJobstarttime() );
        dBTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        dBTaskResult.setMasklines( dto.getMasklines() );
        dBTaskResult.setOutputipaddress( dto.getOutputipaddress() );
        dBTaskResult.setOutputname( dto.getOutputname() );
        dBTaskResult.setOutputpath( dto.getOutputpath() );
        dBTaskResult.setOutputtype( dto.getOutputtype() );
        dBTaskResult.setRemark( dto.getRemark() );
        dBTaskResult.setSparefield1( dto.getSparefield1() );
        dBTaskResult.setSparefield2( dto.getSparefield2() );
        dBTaskResult.setSparefield3( dto.getSparefield3() );
        dBTaskResult.setSparefield4( dto.getSparefield4() );
        dBTaskResult.setSparefield5( dto.getSparefield5() );
        dBTaskResult.setStrategyjson( dto.getStrategyjson() );
        dBTaskResult.setTabname( dto.getTabname() );
        dBTaskResult.setTaskname( dto.getTaskname() );
        dBTaskResult.setTaskstatus( dto.getTaskstatus() );
        dBTaskResult.setTotallines( dto.getTotallines() );
        dBTaskResult.setUsername( dto.getUsername() );

        return dBTaskResult;
    }

    @Override
    public List<DBTaskResult> toEntity(List<DBTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DBTaskResult> list = new ArrayList<DBTaskResult>( dtoList.size() );
        for ( DBTaskResultDto dBTaskResultDto : dtoList ) {
            list.add( toEntity( dBTaskResultDto ) );
        }

        return list;
    }
}
