package com.wzsec.utils.zlicense.verify;

import com.wzsec.utils.zlicense.de.schlichtherle.license.*;
import com.wzsec.utils.zlicense.verify.LicenseManagerHolder;
import com.wzsec.utils.zlicense.verify.VerifyLicense;

import com.wzsec.utils.zlicense.de.schlichtherle.license.LicenseManager;

import java.io.*;
import java.util.Properties;
import java.util.prefs.Preferences;

/**
 * VerifyLicense
 * <AUTHOR>
 */
public class RequestVerifyLicense {
	//common param
	private static String PUBLICALIAS = "";
	private static String STOREPWD = "";
	private static String SUBJECT = "";
	private static String licPath = "";
	private static String pubPath = "";
	
	public void setParam(String propertiesPath) throws IOException {
//		Properties prop = new Properties();
//		System.out.println("propertiesPath="+propertiesPath);
//		InputStream in = getClass().getResourceAsStream(propertiesPath);
//		in = new FileInputStream(propertiesPath);
		//in = request.getSession().getServletContext().getResourceAsStream("/images/someimage.jpg");
//		prop.load(in);
//		PUBLICALIAS = prop.getProperty("PUBLICALIAS");
//		STOREPWD = prop.getProperty("STOREPWD");
//		SUBJECT = prop.getProperty("SUBJECT");
//		licPath = prop.getProperty("licPath");
//		pubPath = prop.getProperty("pubPath");
		PUBLICALIAS = getParamFromProp("PUBLICALIAS").toString();
		STOREPWD = getParamFromProp("STOREPWD").toString();
		SUBJECT = getParamFromProp("SUBJECT").toString();
		licPath = getParamFromProp("licPath").toString();
		pubPath = getParamFromProp("pubPath").toString();
	}

	public boolean verify()  {		

		LicenseManager licenseManager = LicenseManagerHolder.getLicenseManager(initLicenseParams());
		// install license file
		try {
			licenseManager.install(new File(licPath));
					
		} catch (Exception e) {
			//e.printStackTrace();			
			String moreInfo ="License file verify failure";
			System.out.println("moreInfo="+moreInfo);
		}
		// verify license file
		try {
			licenseManager.verify();
			
		} catch (Exception e) {
			//e.printStackTrace();			
			String moreInfo ="License file verify failure";
			System.out.println("moreInfo="+moreInfo);
		}
		return true;
	}

	/**
	 * 证书参数初始化
	 * @return
	 */
	private static LicenseParam initLicenseParams() {
		Preferences preference = Preferences
				.userNodeForPackage(VerifyLicense.class);
		CipherParam cipherParam = new DefaultCipherParam(STOREPWD);
		KeyStoreParam privateStoreParam = new DefaultKeyStoreParam(VerifyLicense.class, pubPath, PUBLICALIAS, STOREPWD, null);
		LicenseParam licenseParams = new DefaultLicenseParam(SUBJECT,preference, privateStoreParam, cipherParam);
		return licenseParams;
	}

	//读取配置文件中的值
	public static Object getParamFromProp(String key) {
		InputStream is = RequestVerifyLicense.class.getClassLoader().getResourceAsStream("verifyparam.properties");
		BufferedReader br = new BufferedReader(new InputStreamReader(is));
		Properties props = new Properties();
		try {
			props.load(br);
			return props.get(key);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
}