package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DBTaskConfig;
import com.wzsec.modules.mask.service.dto.DBTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:06+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DbTaskConfigMapperImpl implements DbTaskConfigMapper {

    @Override
    public DBTaskConfigDto toDto(DBTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        DBTaskConfigDto dBTaskConfigDto = new DBTaskConfigDto();

        dBTaskConfigDto.setApprovalstatus( entity.getApprovalstatus() );
        dBTaskConfigDto.setApprovaltime( entity.getApprovaltime() );
        dBTaskConfigDto.setApprover( entity.getApprover() );
        dBTaskConfigDto.setCreatetime( entity.getCreatetime() );
        dBTaskConfigDto.setCreateuser( entity.getCreateuser() );
        dBTaskConfigDto.setCron( entity.getCron() );
        dBTaskConfigDto.setDataprovider( entity.getDataprovider() );
        dBTaskConfigDto.setDatause( entity.getDatause() );
        dBTaskConfigDto.setEnablemoderation( entity.getEnablemoderation() );
        dBTaskConfigDto.setErrordirectory( entity.getErrordirectory() );
        dBTaskConfigDto.setExecutionstate( entity.getExecutionstate() );
        dBTaskConfigDto.setId( entity.getId() );
        dBTaskConfigDto.setInputdatasourceid( entity.getInputdatasourceid() );
        dBTaskConfigDto.setIswatermark( entity.getIswatermark() );
        dBTaskConfigDto.setOutputdatasourceid( entity.getOutputdatasourceid() );
        dBTaskConfigDto.setOutputdirectory( entity.getOutputdirectory() );
        dBTaskConfigDto.setOutputtype( entity.getOutputtype() );
        dBTaskConfigDto.setRemark( entity.getRemark() );
        dBTaskConfigDto.setSparefield1( entity.getSparefield1() );
        dBTaskConfigDto.setSparefield2( entity.getSparefield2() );
        dBTaskConfigDto.setSparefield3( entity.getSparefield3() );
        dBTaskConfigDto.setSparefield4( entity.getSparefield4() );
        dBTaskConfigDto.setSparefield5( entity.getSparefield5() );
        dBTaskConfigDto.setState( entity.getState() );
        dBTaskConfigDto.setStrategyid( entity.getStrategyid() );
        dBTaskConfigDto.setSubmittype( entity.getSubmittype() );
        dBTaskConfigDto.setTablename( entity.getTablename() );
        dBTaskConfigDto.setTaskname( entity.getTaskname() );
        dBTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        dBTaskConfigDto.setUpdateuser( entity.getUpdateuser() );
        dBTaskConfigDto.setWatermarkcol( entity.getWatermarkcol() );

        return dBTaskConfigDto;
    }

    @Override
    public List<DBTaskConfigDto> toDto(List<DBTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DBTaskConfigDto> list = new ArrayList<DBTaskConfigDto>( entityList.size() );
        for ( DBTaskConfig dBTaskConfig : entityList ) {
            list.add( toDto( dBTaskConfig ) );
        }

        return list;
    }

    @Override
    public DBTaskConfig toEntity(DBTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        DBTaskConfig dBTaskConfig = new DBTaskConfig();

        dBTaskConfig.setApprovalstatus( dto.getApprovalstatus() );
        dBTaskConfig.setApprovaltime( dto.getApprovaltime() );
        dBTaskConfig.setApprover( dto.getApprover() );
        dBTaskConfig.setCreatetime( dto.getCreatetime() );
        dBTaskConfig.setCreateuser( dto.getCreateuser() );
        dBTaskConfig.setCron( dto.getCron() );
        dBTaskConfig.setDataprovider( dto.getDataprovider() );
        dBTaskConfig.setDatause( dto.getDatause() );
        dBTaskConfig.setEnablemoderation( dto.getEnablemoderation() );
        dBTaskConfig.setErrordirectory( dto.getErrordirectory() );
        dBTaskConfig.setExecutionstate( dto.getExecutionstate() );
        dBTaskConfig.setId( dto.getId() );
        dBTaskConfig.setInputdatasourceid( dto.getInputdatasourceid() );
        dBTaskConfig.setIswatermark( dto.getIswatermark() );
        dBTaskConfig.setOutputdatasourceid( dto.getOutputdatasourceid() );
        dBTaskConfig.setOutputdirectory( dto.getOutputdirectory() );
        dBTaskConfig.setOutputtype( dto.getOutputtype() );
        dBTaskConfig.setRemark( dto.getRemark() );
        dBTaskConfig.setSparefield1( dto.getSparefield1() );
        dBTaskConfig.setSparefield2( dto.getSparefield2() );
        dBTaskConfig.setSparefield3( dto.getSparefield3() );
        dBTaskConfig.setSparefield4( dto.getSparefield4() );
        dBTaskConfig.setSparefield5( dto.getSparefield5() );
        dBTaskConfig.setState( dto.getState() );
        dBTaskConfig.setStrategyid( dto.getStrategyid() );
        dBTaskConfig.setSubmittype( dto.getSubmittype() );
        dBTaskConfig.setTablename( dto.getTablename() );
        dBTaskConfig.setTaskname( dto.getTaskname() );
        dBTaskConfig.setUpdatetime( dto.getUpdatetime() );
        dBTaskConfig.setUpdateuser( dto.getUpdateuser() );
        dBTaskConfig.setWatermarkcol( dto.getWatermarkcol() );

        return dBTaskConfig;
    }

    @Override
    public List<DBTaskConfig> toEntity(List<DBTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DBTaskConfig> list = new ArrayList<DBTaskConfig>( dtoList.size() );
        for ( DBTaskConfigDto dBTaskConfigDto : dtoList ) {
            list.add( toEntity( dBTaskConfigDto ) );
        }

        return list;
    }
}
