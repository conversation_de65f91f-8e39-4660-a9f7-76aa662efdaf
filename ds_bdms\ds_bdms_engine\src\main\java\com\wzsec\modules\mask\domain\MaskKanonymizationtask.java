package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

//import javax.validation.constraints.*;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Entity
@Data
@Table(name="sdd_mask_kanonymizationtask")
public class MaskKanonymizationtask implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 状态 */
    @Column(name = "state")
    private String state;

    /** 数据库 */
    @Column(name = "databasename")
    private String databasename;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 表名 */
    @Column(name = "tabname")
    private String tabname;

    /** 数据库连接地址 */
    @Column(name = "url")
    private String url;

    /** 用户名 */
    @Column(name = "username")
    private String username;

    /** 密码 */
    @Column(name = "pass")
    private String pass;

    /** 策略 */
    @Column(name = "strategy")
    private String strategy;

    /** 文件输出目录 */
    @Column(name = "outputdirectory")
    private String outputdirectory;

    /** 异常目录 */
    @Column(name = "errordirectory")
    private String errordirectory;

    /** 创建用户id */
    @Column(name = "createuserid")
    private Integer createuserid;

    /** 创建时间 */
    @Column(name = "createtime")
    private String createtime;

    /** 更新用户id */
    @Column(name = "updateuserid")
    private Integer updateuserid;

    /** 更新时间 */
    @Column(name = "updatetime")
    private String updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** K值大小 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 抑制度 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 任务执行状态(0初始创建,1执行中,2执行成功,3执行失败,4提交失败) */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(MaskKanonymizationtask source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
