package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskAuditResultV1;
import com.wzsec.modules.mask.service.MaskAuditResultV1Service;
import com.wzsec.modules.mask.service.dto.MaskAuditResultV1QueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-01-15
*/
// @Api(tags = "脱敏审计概要结果管理")
@RestController
@RequestMapping("/api/maskAuditResultV1")
public class MaskAuditResultV1Controller {

    private final MaskAuditResultV1Service maskAuditResultV1Service;

    public MaskAuditResultV1Controller(MaskAuditResultV1Service maskAuditResultV1Service) {
        this.maskAuditResultV1Service = maskAuditResultV1Service;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskAuditResultV1:list')")
    public void download(HttpServletResponse response, MaskAuditResultV1QueryCriteria criteria) throws IOException {
        maskAuditResultV1Service.download(maskAuditResultV1Service.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询脱敏审计概要结果")
    // @ApiOperation("查询脱敏审计概要结果")
    @PreAuthorize("@el.check('maskAuditResultV1:list')")
    public ResponseEntity<Object> getMaskAuditResultV1s(MaskAuditResultV1QueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskAuditResultV1Service.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增脱敏审计概要结果")
    // @ApiOperation("新增脱敏审计概要结果")
    @PreAuthorize("@el.check('maskAuditResultV1:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskAuditResultV1 resources){
        return new ResponseEntity<>(maskAuditResultV1Service.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改脱敏审计概要结果")
    // @ApiOperation("修改脱敏审计概要结果")
    @PreAuthorize("@el.check('maskAuditResultV1:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskAuditResultV1 resources){
        maskAuditResultV1Service.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除脱敏审计概要结果")
    // @ApiOperation("删除脱敏审计概要结果")
    @PreAuthorize("@el.check('maskAuditResultV1:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskAuditResultV1Service.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
