package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.FileTaskConfig;
import com.wzsec.modules.mask.service.dto.FileTaskConfigDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
* <AUTHOR>
* @date 2020-11-11
*/
public interface FileTaskConfigRepository extends JpaRepository<FileTaskConfig, Integer>, JpaSpecificationExecutor<FileTaskConfig> {

    @Query(value = "select MAX(`taskname`) from sdd_mask_filetaskconfig where taskname like concat(?1,'%')", nativeQuery = true)
    String findMAXTasknoByPrefix(String prefix);

    /**
     * 通过任务号查询
     * @param taskName
     * @return
     */
    @Query(value = "select * from sdd_mask_filetaskconfig where taskname = ?1", nativeQuery = true)
    FileTaskConfig findByTaskName(String taskName);

    @Modifying
    @Transactional
    @Query(value = "update sdd_mask_filetaskconfig set sparefield5 = ?1 where id = ?2", nativeQuery = true)
    void alterTaskExecutionAnticipate(String anticipate, Integer id);
}
