package com.wzsec.modules.alarm.service.impl;

import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.dto.DmAlarmdisposalDto;
import com.wzsec.modules.alarm.service.mapper.DmAlarmdisposalMapper;
import com.wzsec.modules.alarm.service.repository.DmAlarmdisposalRepository;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.alarm.service.dto.DmAlarmdisposalQueryCriteria;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-07
 */
@Service
//@CacheConfig(cacheNames = "dmAlarmdisposal")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DmAlarmdisposalServiceImpl implements DmAlarmdisposalService {

    private final DmAlarmdisposalRepository dmAlarmdisposalRepository;


    private final DmAlarmdisposalMapper dmAlarmdisposalMapper;

    public DmAlarmdisposalServiceImpl(DmAlarmdisposalRepository dmAlarmdisposalRepository, DmAlarmdisposalMapper dmAlarmdisposalMapper) {
        this.dmAlarmdisposalRepository = dmAlarmdisposalRepository;
        this.dmAlarmdisposalMapper = dmAlarmdisposalMapper;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(DmAlarmdisposalQueryCriteria criteria, Pageable pageable) {
        Page<DmAlarmdisposal> page = dmAlarmdisposalRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(dmAlarmdisposalMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<DmAlarmdisposalDto> queryAll(DmAlarmdisposalQueryCriteria criteria) {
        return dmAlarmdisposalMapper.toDto(dmAlarmdisposalRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public DmAlarmdisposalDto findById(Integer id) {
        DmAlarmdisposal dmAlarmdisposal = dmAlarmdisposalRepository.findById(id).orElseGet(DmAlarmdisposal::new);
        ValidationUtil.isNull(dmAlarmdisposal.getId(), "dmAlarmdisposal", "id", id);
        return dmAlarmdisposalMapper.toDto(dmAlarmdisposal);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public DmAlarmdisposalDto create(DmAlarmdisposal resources) {
        return dmAlarmdisposalMapper.toDto(dmAlarmdisposalRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(DmAlarmdisposal resources) {
        DmAlarmdisposal dmAlarmdisposal = dmAlarmdisposalRepository.findById(resources.getId()).orElseGet(DmAlarmdisposal::new);
        ValidationUtil.isNull(dmAlarmdisposal.getId(), "dmAlarmdisposal", "id", resources.getId());
        dmAlarmdisposal.copy(resources);
        dmAlarmdisposalRepository.save(dmAlarmdisposal);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            dmAlarmdisposalRepository.deleteById(id);
        }
    }

}