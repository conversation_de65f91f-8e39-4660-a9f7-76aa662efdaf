package com.wzsec.dotask.mask.service.excute.kafka;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

@Repository
public class SRedisRepository {

	@Autowired
	private StringRedisTemplate template;

	/**
	 * 向redis中写入键值对
	 * 
	 * @param key
	 * @param value
	 */
	public void setValue(long timeout, String key, String value) {
		ValueOperations<String, String> ops = template.opsForValue();
		ops.set(key, value, timeout, TimeUnit.MINUTES);
	}

	/**
	 * 设置key的失效时间
	 * 
	 * @param timeout
	 * @param key
	 */
	public void setTimeOut(long timeout, String key) {
		template.expire(key, timeout, TimeUnit.MINUTES);
	}

	/**
	 * 向redis中指定键追加值，不存在创建，存在在末尾追加
	 * 
	 * @param key
	 * @param value
	 */
	public void appendValue(String key, String value) {
		ValueOperations<String, String> ops = template.opsForValue();
		ops.append(key, value);
	}

	/**
	 * 从redis中根据键取值
	 * 
	 * @param key
	 * @return
	 */
	public String getValue(String key) {
		ValueOperations<String, String> ops = template.opsForValue();
		return ops.get(key);
	}

}
