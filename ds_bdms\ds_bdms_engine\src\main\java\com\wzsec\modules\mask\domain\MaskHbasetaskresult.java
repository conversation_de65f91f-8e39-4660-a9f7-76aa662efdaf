package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Entity
@Data
@Table(name="sdd_mask_hbasetaskresult")
public class MaskHbasetaskresult implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 工单号 */
    @Column(name = "worksheet")
    private String worksheet;

    /** 批次号 */
    @Column(name = "batchnumber")
    private String batchnumber;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 表名 */
    @Column(name = "tabname")
    private String tabname;

    /** 策略名 */
    @Column(name = "strategyname")
    private String strategyname;

    /** 行数 */
    @Column(name = "count")
    private String count;

    /** 输出方式 */
    @Column(name = "outputtype")
    private String outputtype;

    /** 输出表名 */
    @Column(name = "outputtablename")
    private String outputtablename;

    /** 分隔符 */
    @Column(name = "datasplit")
    private String datasplit;

    /** 数据输出目录 */
    @Column(name = "dataoutputdir")
    private String dataoutputdir;

    /** 起始时间 */
    @Column(name = "jobstarttime")
    private String jobstarttime;

    /** 结束时间 */
    @Column(name = "jobendtime")
    private String jobendtime;

    /** 任务总耗时 */
    @Column(name = "jobtotaltime")
    private String jobtotaltime;

    /** 作业状态（执行中、执行成功、执行失败） */
    @Column(name = "jobstatus")
    private String jobstatus;

    /** 创建人 */
    @Column(name = "createuserid")
    private Integer createuserid;

    /** 创建时间 */
    @Column(name = "createtime")
    private String createtime;

    /** 更新人 */
    @Column(name = "updateuserid")
    private Integer updateuserid;

    /** 更新时间 */
    @Column(name = "updatetime")
    private String updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 脱敏前五条数据 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 脱敏后五条数据 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskHbasetaskresult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}