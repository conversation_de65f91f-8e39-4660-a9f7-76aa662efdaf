package com.wzsec.modules.sdd.rule.service.mapper;

import com.wzsec.base.BaseMapper;
import com.wzsec.modules.sdd.rule.domain.Rule;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* <AUTHOR>
* @date 2020-04-03
*/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RuleMapper extends BaseMapper<RuleDto, Rule> {

}