package com.wzsec.dotask.mask.service.excute.db;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import com.wzsec.modules.mask.domain.DBTaskConfig;
import com.wzsec.modules.mask.domain.DBTaskResult;
import com.wzsec.modules.mask.service.AlgorithmService;
import com.wzsec.modules.mask.service.DBTaskConfigService;
import com.wzsec.modules.mask.service.DBTaskResultService;
import com.wzsec.modules.mask.service.MaskStrategyFieldService;
import com.wzsec.modules.mask.service.dto.DBTaskConfigDto;
import com.wzsec.modules.mask.service.dto.DBTaskResultDto;
import com.wzsec.modules.mask.service.mapper.DbtaskconfigMapper;
import com.wzsec.modules.mask.service.mapper.DbtaskresultMapper;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.utils.*;
import com.wzsec.utils.database.DBMaskUtils;
import com.wzsec.utils.database.DatabaseUtil;
import com.wzsec.utils.database.HiveUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.hive.jdbc.HiveStatement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.Date;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 执行数据库脱敏，设置使用分页获取数据版本(MYSQL-OK)，查询大量数据分页时会多次创建练市表导致特别慢。
 * @date 2021年2月4日 下午2:21:31
 */
public class DoDBTaskJob_V1 {

    private final static Logger log = LoggerFactory.getLogger(DoDBTaskJob_V1.class);

    private DBTaskConfigService dbTaskConfigService;

    private DBTaskResultService dBTaskResultService;

    private DatasourceService dataSourceService;

    private MaskStrategyFieldService maskStrategyFieldService;

    private final AlgorithmService algorithmService;

    private final DbtaskconfigMapper dbtaskconfigMapper;

    private final DbtaskresultMapper dbtaskresultMapper;

    public DoDBTaskJob_V1(DBTaskConfigService dbTaskConfigService, DatasourceService datasourceService,
                          MaskStrategyFieldService maskStrategyFieldService, DBTaskResultService dBTaskResultService,
                          AlgorithmService algorithmService, DbtaskconfigMapper dbtaskconfigMapper, DbtaskresultMapper dbtaskresultMapper,
                          Integer id, String submituser) {
        this.dbTaskConfigService = dbTaskConfigService;
        this.dataSourceService = datasourceService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.dBTaskResultService = dBTaskResultService;
        this.algorithmService = algorithmService;
        this.dbtaskconfigMapper = dbtaskconfigMapper;
        this.dbtaskresultMapper = dbtaskresultMapper;
        this.taskid = id;
        this.submituser = submituser;
    }

    private Integer taskid;
    private String submituser;
    Long pageSize = Long.parseLong(ConstEngine.sddEngineConfs.get("dbtask.readdatacount"));// 一次读取1万条数据再写入到数据库

    String startTime = null;
    String endTime = null;
    String totalTime = null;

    String taskname = null;
    String out_type = null;

    String in_dbtype = null;
    String in_fieldNameStr = null;
    String in_dbname = null;
    String in_tabname = null;
    String in_drivername = null;
    String in_url = null;
    String in_username = null;
    String in_password = null;
    String in_limitingcondition = null;

    String out_dbtype = null;
    String out_dbname = null;
    String out_tabame = null;
    String out_drivername = null;
    String out_url = null;
    String out_username = null;
    String out_password = null;
    String out_path = null;
    String out_ip = null;


    Long totalLineCount = 0l;
    Long maskLineCount = 0l;
    Long saveLineCount = 0l;
    int exampleLineCount = 5;
    String beforemaskdata = "";
    String aftermaskdata = "";

    boolean flag = false;
    Map<String, Object> maskAlgoInfoMap = null;
    List<String> fieldNameStrList = new ArrayList<>();

    DBTaskConfig dBTaskConfig = null;
    DBTaskResult dBTaskResult = null;
    HashMap<String, cn.god.mask.common.Algorithm> algorithmMap = null;


    /**
     * <AUTHOR>
     * @Description 执行数据库脱敏任务
     * @date 2021年2月25日16:34:03
     */
    public void exec() {
        // 任务开始时间
        startTime = DateUtil.getNowTime();
        try {
            initParam();

            initResult();

            totalLineCount = getDataCount();

            doMaskData(in_fieldNameStr);

            // 结束时间
            endTime = DateUtil.getNowTime();
            // 执行总时间
            totalTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(startTime, endTime));
            // 更新时间
            dBTaskResult.setJobendtime(endTime);
            dBTaskResult.setJobtotaltime(totalTime);
            dBTaskResult.setOutputipaddress(out_ip);
            dBTaskResult.setOutputpath(out_path);
            dBTaskResult.setOutputname(out_tabame);
            dBTaskResult.setTotallines(totalLineCount.intValue());
            dBTaskResult.setMasklines(maskLineCount.intValue());
            dBTaskResult.setBeforemaskdata(beforemaskdata);
            dBTaskResult.setAftermaskdata(aftermaskdata);

            if (flag) {
                log.info("任务ID【" + taskid + "】,保存到" + ("1".equals(out_type) ? "库表" : "文件") + "成功");
                // 更新结果
                dBTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_SUCCESS);
                dBTaskResultService.update(dBTaskResult);
                // 更新任务表
                dBTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS);
                dbTaskConfigService.update(dBTaskConfig);

            } else {
                log.info("任务ID【" + taskid + "】,保存到" + ("1".equals(out_type) ? "库表" : "文件") + "失败");
                // 更新结果
                dBTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_FAIL);
                dBTaskResultService.update(dBTaskResult);
                // 更新任务表
                dBTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
                dbTaskConfigService.update(dBTaskConfig);
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 更新结果
            dBTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_FAIL);
            dBTaskResultService.update(dBTaskResult);

            // 更新任务表
            dBTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
            dbTaskConfigService.update(dBTaskConfig);
            log.error("数据库脱敏任务执行失败");
        } finally {
            log.info("任务ID【" + taskid + "】,执行脱敏任务结束");
            log.info("任务ID【" + taskid + "】,执行完毕");
        }
    }

    /**
     * <AUTHOR>
     * @Description 读取数据进行脱敏
     * @date 2021年2月25日16:34:03
     */
    private void doMaskData(String fieldNameStr) {
        Connection con = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            int totalPage = (int) (totalLineCount % pageSize == 0 ? totalLineCount / pageSize
                    : Math.ceil(totalLineCount / pageSize));

            boolean isCreateTable = true;
            String[] fieldNameStrs = fieldNameStr.split(",");

            long currentPageNum = 0;
            long totalPageNum = totalLineCount % pageSize == 0 ? (totalLineCount / pageSize)
                    : (totalLineCount / pageSize) + 1;
            for (int pageNum = 0; pageNum < totalPage; pageNum++) {
                resultSet = getResultSet(con, preparedStatement, pageNum);
                log.info("已获取第" + (currentPageNum + 1) + "个结果集，共" + totalPageNum + "个结果集");
                if (resultSet != null) {
                    List<Map<String, Object>> fieldDataList = new ArrayList<>();
                    // 遍历查询结果集
                    Map<String, Object> fieldDataMap = null;
                    while (resultSet.next()) {
                        fieldDataMap = new LinkedHashMap<>();
                        for (String fieldName : fieldNameStrs) {
                            fieldDataMap.put(fieldName, resultSet.getObject(fieldName).toString());
                        }
                        // 行数据写出
                        fieldDataMap = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap);
                        fieldDataList.add(fieldDataMap);
                    }
                    currentPageNum++;
                    log.info("开始保存第" + currentPageNum + "批数据(当前批次" + fieldDataList.size() + "条数据)，共" + totalPageNum
                            + "批数据，已保存" + saveLineCount + "条数据，共" + totalLineCount + "条数据");
                    saveMaskRowData(isCreateTable, fieldDataList);// 批量保存数据，里面设置有批量写入
                    saveLineCount += fieldDataList.size();
                    isCreateTable = false;
                }
            }
        } catch (Exception ex) {
            log.error("连接数据库失败或获取抽取字段的数据出现异常");
            ex.printStackTrace();
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 对数据行进行脱敏
     * @date 2021年2月25日16:34:03
     */
    private Map<String, Object> getMaskRowData(Map<String, Object> maskAlgoInfoMap,
                                               HashMap<String, cn.god.mask.common.Algorithm> algorithmMap, Map<String, Object> fieldDataMap) {
        // 行数据脱敏
        StringBuffer dataline = new StringBuffer();
        StringBuffer maskdataline = new StringBuffer();
        for (String filedName : fieldDataMap.keySet()) {
            String data = fieldDataMap.get(filedName) == null?"":fieldDataMap.get(filedName).toString();
            String result = data;
            // 该字段有数据才进行脱敏
            if (data != null && !"".equals(data)) {
                // 获取字段的脱敏算法信息 MD5$123,412
                String maskAlgoInfo = maskAlgoInfoMap.get(filedName) == null ? "" : maskAlgoInfoMap.get(filedName).toString();
                // 该字段有脱敏算法才进行脱敏
                if (maskAlgoInfo != null && !"".equals(maskAlgoInfo)) {
                    String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                    String algo = AlgoParamInfo[0];
                    String param = "";
                    if (AlgoParamInfo.length > 1) {
                        param = AlgoParamInfo[1];
                    }
                    try {
                        // 数据:data算法名:algo参数:param
                        //判断当前执行是否使用自定义算法
                        Algorithm algorithm = algorithmMap.get(algo);
                        String sparefield1 = algorithm.getSparefield1();
                        if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                            result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                        }else {
                            //自定义算法
                            String jarname = algorithm.getJarname();//jar包名称
                            String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                            String methodname = algorithm.getMethodname();//方法名
                            Map<String, String> paramMap = new HashMap<>();
                            paramMap.put("jarname",jarname);
                            paramMap.put("funcnamepath",funcnamepath);
                            paramMap.put("methodname",methodname);
                            paramMap.put("maskData",data);
                            result = AlgorithmUtils.invokeJarMethod(paramMap);
                        }
                    } catch (Exception e) {
                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algo + "参数:" + param);
                        e.printStackTrace();
                    }
                    fieldDataMap.put(filedName, result);
                }
            }
            if (exampleLineCount > 0) {
                dataline.append(data + ",");
                maskdataline.append(result + ",");
            }
        }
        if (exampleLineCount > 0) {
            beforemaskdata += dataline.toString().substring(0, dataline.toString().length() - 1) + "\n";
            aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
            exampleLineCount--;
        }
        maskLineCount++;
        return fieldDataMap;
    }

    /**
     * <AUTHOR>
     * @Description 批量保存脱敏结果
     * @date 2021年2月25日16:34:03
     */
    private void saveMaskRowData(boolean isCreateTable, List<Map<String, Object>> fieldDataList) {
        // 6.保存脱敏后的数据到的位置
        if (Const.DB_TASK_OUTPUTTYPE_DB.equals(out_type)) {
            flag = DatabaseUtil.insertData2NewTable(isCreateTable, in_dbname, in_tabname, in_url, in_username, in_password,in_dbtype,
                    fieldDataList, out_tabame, out_url, out_username, out_password, fieldNameStrList, out_dbname,out_dbtype, null);
        } else if (Const.DB_TASK_OUTPUTTYPE_EXCEL.equals(out_type)) {
            // 保存到文件（excel)
            flag = ExcelWriteUtil.writedata(out_path, out_tabame, in_tabname, fieldDataList);
        } else if (Const.DB_TASK_OUTPUTTYPE_TXT.equals(out_type)) {
            // 保存到文件（txt)
            flag = FileWriteUtil.wirteTxtFile(out_path, out_tabame, fieldDataList);
        } else if (Const.DB_TASK_OUTPUTTYPE_CSV.equals(out_type)) {
            // 保存到文件（csv)
            flag = FileWriteUtil.wirteCsvFile(out_path, out_tabame, fieldDataList);
        }
    }

    /**
     * <AUTHOR>
     * @Description 初始化脱敏任务所需的参数
     * @date 2021年2月25日16:34:03
     */
    private void initParam() throws Exception {
        DBTaskConfigDto dbTaskConfigDto = dbTaskConfigService.findById(taskid);
        dBTaskConfig = dbtaskconfigMapper.toEntity(dbTaskConfigDto);

        taskname = dBTaskConfig.getTaskname();
        in_tabname = dBTaskConfig.getTablename();
        in_limitingcondition = dBTaskConfig.getSparefield1();
        out_type = dBTaskConfig.getOutputtype();
        out_path = dBTaskConfig.getOutputdirectory();

        // 输入数据源信息
        Integer in_dataSourceId = dBTaskConfig.getInputdatasourceid();
        DatasourceDto in_datasourceDto = dataSourceService.findById(Long.valueOf(in_dataSourceId));
        in_dbtype = in_datasourceDto.getType();
        in_dbname = in_datasourceDto.getDbname();
        in_drivername = in_datasourceDto.getDriverprogram();
        in_url = in_datasourceDto.getSrcurl();
        in_username = in_datasourceDto.getUsername();
        in_password = in_datasourceDto.getPassword();
        //AES解密
        if (StringUtils.isNotEmpty(in_password)) {
            in_password = AES.decrypt(in_password, Const.AES_SECRET_KEY);
        }

        String strategyid = dbTaskConfigDto.getStrategyid();
        List<Map<String, Object>> list = maskStrategyFieldService.getMaskAlgoInfoByStrategyId(strategyid);
        maskAlgoInfoMap = DBMaskUtils.getMaskAlgoInfo(list, null);
        in_fieldNameStr = "";
        for (String fieldName : maskAlgoInfoMap.keySet()) {
            in_fieldNameStr += fieldName + ",";
            String algo = maskAlgoInfoMap.get(fieldName) == null?"":maskAlgoInfoMap.get(fieldName).toString();
            if (!"".equals(algo)) {
                fieldNameStrList.add(fieldName);
            }
        }
        if (in_fieldNameStr.endsWith(",")) { // 如果以","结尾，去除","
            in_fieldNameStr = in_fieldNameStr.substring(0, in_fieldNameStr.lastIndexOf(","));
        }

        // 输出数据源信息
        if ("1".equals(out_type)) {
            Integer out_dataSourceId = dBTaskConfig.getOutputdatasourceid();
            DatasourceDto out_datasourceDto = dataSourceService.findById(Long.valueOf(out_dataSourceId));
            out_dbtype = out_datasourceDto.getType();
            out_dbname = out_datasourceDto.getDbname();
            out_drivername = out_datasourceDto.getDriverprogram();
            out_url = out_datasourceDto.getSrcurl();
            out_username = out_datasourceDto.getUsername();
            out_password = out_datasourceDto.getPassword();
            //AES解密
            if (StringUtils.isNotEmpty(out_password)) {
                out_password = AES.decrypt(out_password, Const.AES_SECRET_KEY);
            }
        }

        // 文件名或表名
        out_tabame = in_tabname + "_MASK_" + DateUtil.formatDate(new Date());
        if (in_dbtype.toLowerCase().contains("dm")) {
            out_tabame = in_tabname + "_MASK_" + DateUtil.formatDate(new Date());
        } else {
            out_tabame = in_tabname + "_mask_" + DateUtil.formatDate(new Date());
        }

        //初始化所有算法
        algorithmMap = algorithmService.getAlgorithmByEName();

        setoutputIpAddress();
    }

    /**
     * <AUTHOR>
     * @Description 获取输出数据IP地址
     * @date 2021年2月25日16:34:03
     */
    private void setoutputIpAddress() {
        if (Const.DB_TASK_OUTPUTTYPE_DB.equals(out_type)) {
            if (in_dbtype.toLowerCase().contains("mysql") || out_url.contains("MYSQL") || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
                String temp1 = out_url.substring(out_url.indexOf("3306/") + 5);
                // outputPath = temp1.substring(0,temp.indexOf("?"));
                if (temp1.contains("?")) {
                    out_path = temp1.substring(0, temp1.indexOf("?"));
                } else {
                    out_path = temp1;
                }
            } else if (in_dbtype.toLowerCase().contains("oracle")) {
                String temp = out_url.substring(out_url.indexOf("@") + 1);
                out_ip = temp.substring(0, temp.indexOf(":"));
                out_path = out_url.substring(out_url.indexOf(":1521:") + 4, out_url.lastIndexOf(":") - 1);
            } else if (in_dbtype.toLowerCase().contains("gbase")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("db2")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("hive2")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {

            }
        } else if (Const.DB_TASK_OUTPUTTYPE_EXCEL.equals(out_type)) {
            // 保存到文件（excel)
            out_ip = "127.0.0.1";
        } else if (Const.DB_TASK_OUTPUTTYPE_TXT.equals(out_type)) {
            // 保存到文件（txt)
            out_ip = "127.0.0.1";
        } else if (Const.DB_TASK_OUTPUTTYPE_CSV.equals(out_type)) {
            // 保存到文件（csv)
            out_ip = "127.0.0.1";
        }
    }

    /**
     * <AUTHOR>
     * @Description 初始化任务结果信息
     * @date 2021年2月25日16:34:03
     */
    private void initResult() {
        DBTaskResult dBTaskResuleDB = new DBTaskResult();
        dBTaskResuleDB.setTaskname(taskname);
        dBTaskResuleDB.setTabname(in_tabname);
        dBTaskResuleDB.setDbname(in_dbname);
        dBTaskResuleDB.setIpaddress(in_url);// 数据源主机或Ip地址
        dBTaskResuleDB.setTaskstatus(Const.TASK_RESULT_EXECUTING);
        dBTaskResuleDB.setOutputtype(out_type);
        dBTaskResuleDB.setUsername(submituser);
        dBTaskResuleDB.setJobstarttime(startTime);
        dBTaskResuleDB.setJobstarttime(startTime);
        DBTaskResultDto dbTaskResultDto = dBTaskResultService.create(dBTaskResuleDB);
        dBTaskResult = dbtaskresultMapper.toEntity(dbTaskResultDto);
    }

    /**
     * <AUTHOR>
     * @Description 从数据源批量获取数据，分页查询
     * @date 2021年2月25日16:34:03
     */
    private ResultSet getResultSet(Connection con, Statement stmt, int pageNum) {
        ResultSet resultSet = null;
        try {
            int beginIndex = pageNum * pageSize.intValue();
            if (in_dbtype.toLowerCase().contains("oracle")) {
                Class.forName("oracle.jdbc.OracleDriver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接oracle数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a limit " + beginIndex + "," + pageSize;
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("mysql") || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                Class.forName("com.mysql.jdbc.Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a limit " + beginIndex + "," + pageSize;
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("gbase")) {
                Class.forName("com.gbase.jdbc.Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a limit " + beginIndex + "," + pageSize;
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {
                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接sqlserver数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a limit " + beginIndex + "," + pageSize;
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("db2")) {
                Class.forName("com.ibm.db2.jcc.DB2Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接db2数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a limit " + beginIndex + "," + pageSize;
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("hive2")) {
                Class.forName("org.apache.hive.jdbc.HiveDriver");
                Connection conn = null;
                HiveStatement hstmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", in_username, in_password, in_url);
                    hstmt = HiveUtil.getStmt(conn);
                    hstmt.setFetchSize(pageSize.intValue());
                    hstmt.setFetchDirection(ResultSet.FETCH_REVERSE);
                    if (null != in_dbname && !in_dbname.equals("")) {
                        String strUseBase = "use " + in_dbname;
                        hstmt.execute(strUseBase);
                    }
                    String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                    if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                        sql += " " + in_limitingcondition;
                    }
                    resultSet = hstmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(hstmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {
                Class.forName("org.postgresql.Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接postgresql数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a limit " + beginIndex + "," + pageSize;
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                Class.forName("com.ibm.db2.jcc.DB2Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接dm数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = in_fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a limit " + beginIndex + "," + pageSize;
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultSet;
    }

    /**
     * <AUTHOR>
     * @Description 获取脱敏数据总条数
     * @date 2021年2月25日16:34:03
     */
    private Long getDataCount() {
        Connection con = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        long count = 0;
        try {
            if (in_dbtype.toLowerCase().contains("oracle")) {
                Class.forName("oracle.jdbc.OracleDriver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接oracle数据库成功");
                String sql = "select * from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select count(*) from (" + sql + ") a";
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("mysql") || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                Class.forName("com.mysql.jdbc.Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接mysql数据库成功");
                String sql = "select * from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select count(*) from (" + sql + ") a";
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("gbase")) {
                Class.forName("com.gbase.jdbc.Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase数据库成功");
                String sql = "select * from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select count(*) from (" + sql + ") a";
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {
                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接sqlserver数据库成功");
                String sql = "select * from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select count(*) from (" + sql + ") a";
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("db2")) {
                Class.forName("com.ibm.db2.jcc.DB2Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接db2数据库成功");
                String sql = "select count(*) from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select * from (" + sql + ") a";
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("hive2")) {
                Class.forName("org.apache.hive.jdbc.HiveDriver");
                Connection conn = null;
                HiveStatement stmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", in_username, in_password, in_url);
                    stmt = HiveUtil.getStmt(conn);
                    if (null != in_dbname && !in_dbname.equals("")) {
                        String strUseBase = "use " + in_dbname;
                        stmt.execute(strUseBase);
                    }
                    String sql = "select * from " + in_tabname;
                    if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                        sql += " " + in_limitingcondition;
                    }
                    sql = "select count(*) from (" + sql + ") a";
                    resultSet = stmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(stmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {
                Class.forName("org.postgresql.Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接postgresql数据库成功");
                String sql = "select * from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                sql = "select count(*) from (" + sql + ") a";
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                Class.forName("com.ibm.db2.jcc.DB2Driver");
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接dm数据库成功");
                String sql = "select * from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                System.out.println(sql);
                sql = "select count(*) from (" + sql + ") a";
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {
            }
            while (resultSet.next()) {
                count = resultSet.getLong(1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }
}
