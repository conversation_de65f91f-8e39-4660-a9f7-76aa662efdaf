package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.KafkaTaskConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @date 2021-02-26
 */
public interface KafkaTaskConfigRepository extends JpaRepository<KafkaTaskConfig, Long>, JpaSpecificationExecutor<KafkaTaskConfig> {

    /**
     * 获取当前topic运行中数量
     *
     * @param topic
     */
    @Query(value = "select count(*) from sdd_mask_kafkataskconfig where status='1' and datatopic = ?1", nativeQuery = true)
    int getRunningTaskCountByTopic(String topic);

}