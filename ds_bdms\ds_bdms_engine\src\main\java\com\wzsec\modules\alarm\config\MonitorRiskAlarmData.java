package com.wzsec.modules.alarm.config;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.modules.system.service.dto.DictDetailDto;
import com.wzsec.modules.system.service.dto.DictDetailQueryCriteria;
import com.wzsec.utils.Const;
import com.wzsec.utils.ConstEngine;
import com.wzsec.utils.StringUtils;
import org.graylog2.syslog4j.Syslog;
import org.graylog2.syslog4j.SyslogConstants;
import org.graylog2.syslog4j.SyslogIF;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.wzsec.utils.DateUtils.formatDate;
import static com.wzsec.utils.DateUtils.getSecondTimestamp;

@Component
public class MonitorRiskAlarmData {

    static String HOST = ConstEngine.sddEngineConfs.get("syslog.host");
    static int PORT = Integer.parseInt(ConstEngine.sddEngineConfs.get("syslog.port"));

    private static MonitorRiskAlarmData monitorRiskAlarmData;

    @Autowired
    private DictDetailService dictDetailService;

    @PostConstruct
    public void init() {
        monitorRiskAlarmData = this;
        monitorRiskAlarmData.dictDetailService = this.dictDetailService;
    }

    /**
     * 将告警信息推送到syslog
     *
     * @param dmAlarmdisposal
     */
    public static void sendDmExample(DmAlarmdisposal dmAlarmdisposal) {

        String isSendSyslog = "0";
        //从字典中取出是否推送syslog
        DictDetailQueryCriteria dictDetailQueryCriteria = new DictDetailQueryCriteria();
        dictDetailQueryCriteria.setDictName("is_send_syslog");
        List<DictDetailDto> dictDetailList = monitorRiskAlarmData.dictDetailService.queryAll(dictDetailQueryCriteria);
        for (DictDetailDto dictDetailDto : dictDetailList) {
            if (dictDetailDto.getLabel().equals(Const.EVENT_IS_SEND_SYSLOG)) {  //是否写入syslog
                isSendSyslog = dictDetailDto.getValue();
            }
        }
        if (Const.EVENT_SEND_SYSLOG.equals(isSendSyslog)) {
            JSONObject joInfo = new JSONObject(true);
            String ID = UUID.randomUUID().toString().replace("-", "").toLowerCase();
            //告警推送syslog字段
            joInfo.put("EVENT_ID", ID);//事件ID（需保证 ID 唯一性），使用 32 位 UUID
            joInfo.put("EVENT_TIME", getSecondTimestamp(formatDate(dmAlarmdisposal.getChecktime())));//事件产生时间，使用秒级时间戳，例如 **********
            joInfo.put("EVENT_TYPE", Const.EVENT_TYPE);//事件类型   默认:数据脱敏
            joInfo.put("SRC_IP", dmAlarmdisposal.getSourceip() == null ? " " : dmAlarmdisposal.getSourceip());//源IP
            joInfo.put("SRC_PORT", dmAlarmdisposal.getSourceport() == null ? " " : dmAlarmdisposal.getSourceport());//源端口
            joInfo.put("DST_IP", dmAlarmdisposal.getDestinationip() == null ? " " : dmAlarmdisposal.getDestinationip());//目标IP
            joInfo.put("DST_PORT", dmAlarmdisposal.getDestinationport() == null ? " " : dmAlarmdisposal.getDestinationport());//目标端口
            joInfo.put("USERNAME", dmAlarmdisposal.getAccount() == null ? " " : dmAlarmdisposal.getAccount());//脱敏任务配置提交人
            joInfo.put("EVENT_LEVEL", dmAlarmdisposal.getReservefield3() == null ? " " : dmAlarmdisposal.getReservefield3());//事件等级，3-高，2-中，1-低,0-无
            joInfo.put("DEVICE_TYPE", dmAlarmdisposal.getReservefield2() == null ? " " : dmAlarmdisposal.getReservefield2());//产生事件的设备类型 库表，文件
            joInfo.put("EVENT_NAME", dmAlarmdisposal.getEventrule() == null ? " " : dmAlarmdisposal.getEventrule());//事件名字
            joInfo.put("EVENT_DETAIL", dmAlarmdisposal.getCircumstantiality() == null ? " " : dmAlarmdisposal.getCircumstantiality());//事件详情

            joInfo.put("EVENT_DEAL_TIME", getSecondTimestamp(new Date()));//事件处置时间，使用秒级时间戳，例如 **********
            joInfo.put("EVENT_DEAL_ID", ID);//处置告警事件编号，需与 EVENT_ID（事件 ID）对应
            joInfo.put("EVENT_WORKFLOW", dmAlarmdisposal.getTreatmentstate());//事件处置状态， 未处置：为空或者 0 处置中：1 已处置：2
            joInfo.put("EVENT_DEAL_OPER", " ");//事件处置人员
            joInfo.put("EVENT_DEAL_MARK", dmAlarmdisposal.getNote() == null ? " " : dmAlarmdisposal.getNote());//事件处置记录描述
            System.out.println(joInfo.toJSONString());
            //发送syslog日志信息
            generate(joInfo.toJSONString());
        }
    }

    /**
     * 客户端
     *
     * @param message
     */
    public static void generate(String message) {
        SyslogIF syslog = Syslog.getInstance(SyslogConstants.TCP);
        syslog.getConfig().setHost(HOST);
        syslog.getConfig().setPort(PORT);
        try {
            syslog.log(0, URLDecoder.decode(message, "utf-8"));
        } catch (UnsupportedEncodingException e) {
            System.out.println("generate log get exception " + e);
        }
    }

    public static void InsertDmAlarmData(String str, String srcname, String srcurl, String dbname, Timestamp updatetime, String createuser, String type,
                                         String eventrule,DmAlarmdisposalService dmAlarmdisposalService){

        DmAlarmdisposal dmAlarmdisposal = new DmAlarmdisposal();
        String eventDetails = cn.hutool.core.util.StrUtil.format(str, srcname,srcurl,dbname,updatetime);

        dmAlarmdisposal.setCircumstantiality(eventDetails); //事件详情
        dmAlarmdisposal.setChecktime(DateUtil.now());
        dmAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED); //处置状态

        dmAlarmdisposal.setEventrule(eventrule);//规则

        String url = srcurl;

        // 可能前段url输入不规范，导致ip端口拆分有问题导致报错，单独处理不要影响数据源连通性测试
        try {
            if(url.contains("//")){
                String hostNameAndPort1 = StringUtils.substringBetween(url,"//","/");
                String[] ips = hostNameAndPort1.split(":");
                String ip = ips[0];
                String port = ips[1];
                dmAlarmdisposal.setSourceip(ip);//源ip
                dmAlarmdisposal.setSourceport(port);//源端口
            }else if(url.contains("@")) {
                String hostNameAndPort1 = StringUtils.substringAfter(url,"@");
                String hostNameAndPort2 = StringUtils.substringBeforeLast(hostNameAndPort1,":");
                String[] ips = hostNameAndPort2.split(":");
                String ip = ips[0];
                String port = ips[1];
                dmAlarmdisposal.setSourceip(ip);//源ip
                dmAlarmdisposal.setSourceport(port);//源端口
            }else {
                String[] ips = url.split(":");
                String ip = ips[0];
                String port = ips[1];
                dmAlarmdisposal.setSourceip(ip);//源ip
                dmAlarmdisposal.setSourceport(port);//源端口
            }
            dmAlarmdisposal.setAccount(createuser);//事件相关用户名
            dmAlarmdisposal.setReservefield2(type);
            dmAlarmdisposal.setReservefield3("2");

            String outUrl = srcurl;
            if(StringUtils.isNotEmpty(outUrl)){
                if(outUrl.contains("//")){
                    String hostNameAndPort = StringUtils.substringBetween(outUrl,"//","/");
                    String[] outIps = hostNameAndPort.split(":");
                    String outIp = outIps[0];
                    String outPort = outIps[1];
                    dmAlarmdisposal.setDestinationip(outIp);//目标IP
                    dmAlarmdisposal.setDestinationport(outPort);//目标端口
                }else if(outUrl.contains("@")){
                    String hostNameAndPort1 = StringUtils.substringAfter(url,"@");
                    String hostNameAndPort2 = StringUtils.substringBeforeLast(hostNameAndPort1,":");
                    String[] outIps = hostNameAndPort2.split(":");
                    String outIp = outIps[0];
                    String outPort = outIps[1];
                    dmAlarmdisposal.setDestinationip(outIp);//目标IP
                    dmAlarmdisposal.setDestinationport(outPort);//目标端口
                } else {
                    String[] outIps = outUrl.split(":");
                    String outIp = outIps[0];
                    String outPort = outIps[1];
                    dmAlarmdisposal.setDestinationip(outIp);//目标IP
                    dmAlarmdisposal.setDestinationport(outPort);//目标端口
                }
                dmAlarmdisposal.setDestinationip(" ");//目标IP
                dmAlarmdisposal.setDestinationport(" ");//目标端口
            }
        } catch (Exception ex){
            ex.getMessage();
        }

        dmAlarmdisposalService.create(dmAlarmdisposal);
        MonitorRiskAlarmData.sendDmExample(dmAlarmdisposal);
    }

}
