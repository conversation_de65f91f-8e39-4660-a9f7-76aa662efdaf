package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-11-11
 */
@Entity
@Data
@Table(name="sdd_mask_filetaskconfig")
public class FileTaskConfig implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 输出数据源 */
    @Column(name = "inputdatasourceid")
    private Integer inputdatasourceid;

    /** 输入路径 */
    @Column(name = "inputpath")
    private String inputpath;

    /** 输入文件类型 */
    @Column(name = "inputfiletype")
    private String inputfiletype;

    /** 分隔符 */
    @Column(name = "splitstr")
    private String splitstr;

    /** 任务状态(0启用,1禁用) */
    @Column(name = "state")
    private String state;

    /** 输出类型（1库表，2文件） */
    @Column(name = "outputtype")
    private String outputtype;

    /** 输出数据源 */
    @Column(name = "outputdatasourceid")
    private Integer outputdatasourceid;

    /** 表名 */
    @Column(name = "tablename")
    private String tablename;

    /** 输出目录 */
    @Column(name = "outputdirectory")
    private String outputdirectory;

    /** 输出文件类型 */
    @Column(name = "outputfiletype")
    private String outputfiletype;

    /** 提交方式（1手动提交，2定时执行）*/
    @Column(name = "submitmethod")
    private String submitmethod;

    /** 执行时间cron表达式 */
    @Column(name = "cron")
    private String cron;

    /** 执行状态：0 :初始创建1:执行中2:成功3:失败4:提交失败 */
    @Column(name = "executionstate")
    private String executionstate;

    /** 抽取字段 */
    @Column(name = "extractfield")
    private String extractfield;

    /** 脱敏策略字符串 */
    @Column(name = "maskstrategystr")
    private String maskstrategystr;

    /** 是否添加水印(1是，2否) */
    @Column(name = "iswatermark")
    private String iswatermark;

    /** 数据提供方 */
    @Column(name = "dataprovider")
    private String dataprovider;

    /** 数据使用方 */
    @Column(name = "datause")
    private String datause;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 任务执行引擎 */
    @Column(name = "taskexecuteengine")
    private String taskexecuteengine;

    /** 备用字段5 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    /** 脱敏策略id */
    @Column(name = "strategyid")
    private Integer strategyid;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    public void copy(FileTaskConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
