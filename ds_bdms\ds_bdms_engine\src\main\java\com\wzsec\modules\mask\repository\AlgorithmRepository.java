package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.Algorithm;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.HashMap;
import java.util.List;

/**
* <AUTHOR>
* @date 2020-11-03
*/
public interface AlgorithmRepository extends JpaRepository<Algorithm, Integer>, JpaSpecificationExecutor<Algorithm> {

    /**
     * 根据算法名称查询算法数量
     * @param algorithmname 算法名称
     */
    @Query(value = "select count(*) from sdd_algorithm where algorithmname = ?1",nativeQuery = true)
    int findAlgorithmCountByAlgorithmName(String algorithmname);

    /**
     * 根据算法英文名称查询算法数量
     * @param algenglishname 算法英文名称
     */
    @Query(value = "select count(*) from sdd_algorithm where algenglishname = ?1",nativeQuery = true)
    int findAlgorithmCountByAlgorithmEName(String algenglishname);

    /**
     *
     * @return
     */
    @Query(value = "select * from sdd_algorithm",nativeQuery = true)
    List<Algorithm> getAlgofuncnameAndFuncnamepathMap();

    /**
     * 查询静态脱敏算法
     *
     * @return {@link Algorithm}
     */
    @Query(value = "SELECT * FROM sdd_algorithm ", nativeQuery = true)
    List<Algorithm> findStaticDesensitizationAlgorithm();

    /**
     *
     * @return
     */
    @Query(value = "select * from sdd_algorithm where algenglishname = ?1",nativeQuery = true)
    Algorithm findByAlgorithmName(String algorithmName);


    @Query(value = "select algenglishname from sdd_algorithm where id = ?1", nativeQuery = true)
    String getAlgoENameByAlgoId(String prefix);
}
