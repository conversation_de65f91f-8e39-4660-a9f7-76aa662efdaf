2025-07-29 11:24:47,579 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 7996 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 11:24:47,593 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 11:24:51,151 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 11:24:51,154 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 11:24:52,933 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1757 ms. Found 88 JPA repository interfaces.
2025-07-29 11:24:53,999 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 11:24:54,001 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 11:24:54,004 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:24:54,004 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:24:54,012 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:24:54,016 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 11:24:54,018 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 11:24:54,019 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:24:54,019 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:24:55,303 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 11:24:55,322 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 11:24:55,326 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 11:24:56,625 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 11:24:56,889 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 11:24:56,917 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 11:24:56,917 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 11:24:56,918 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 11:24:56,918 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 11:24:56,918 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 11:24:56,919 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 11:24:56,922 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 11:24:56,927 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 11:25:00,207 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 11:25:00,958 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 11:25:01,204 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 11:25:01,796 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 11:25:02,713 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 11:25:11,787 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 11:25:11,856 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 11:25:19,066 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-29 11:25:19,553 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 31140 ms
2025-07-29 11:25:26,300 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-29 11:25:26,525 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 11:25:26,643 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 11:25:26,643 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 11:25:26,667 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 11:25:26,674 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 11:25:26,674 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 11:25:26,674 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 11:25:26,675 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@179273ff
2025-07-29 11:25:26,675 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 11:25:31,858 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@16cf05ef, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@34d53f54, org.springframework.security.web.context.SecurityContextPersistenceFilter@332f6402, org.springframework.security.web.header.HeaderWriterFilter@cf91dc2, org.springframework.security.web.authentication.logout.LogoutFilter@238709e6, org.springframework.web.filter.CorsFilter@4137ff9f, com.wzsec.modules.security.security.TokenFilter@5417d740, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@22e9424e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2fcc009e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5dce75f9, org.springframework.security.web.session.SessionManagementFilter@2130262a, org.springframework.security.web.access.ExceptionTranslationFilter@1bd15c71, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3e0b405c]
2025-07-29 11:25:31,923 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 11:25:37,008 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 11:25:37,010 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 11:25:37,010 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 11:25:37,010 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 11:25:37,010 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 11:25:37,010 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 11:25:37,010 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 11:25:37,010 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@63c10f2d
2025-07-29 11:25:37,272 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 11:25:37,375 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-29 11:25:37,379 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 11:25:37,382 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 11:25:37,382 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 11:25:37,382 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 11:25:37,382 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 11:25:37,382 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 11:25:37,383 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:25:37,383 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 11:25:37,383 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:25:37,386 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 11:25:37,387 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 11:25:37,387 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 11:25:37,406 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 50.932 seconds (JVM running for 54.22)
2025-07-29 11:25:38,352 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-29 11:25:38,352 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-29 11:48:52,149 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 11:48:53,604 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-29 11:48:53,606 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 11:48:53,607 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 11:48:53,611 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 11:48:53,753 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 11:48:53,756 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 11:48:53,759 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 11:48:53,800 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 11:48:54,094 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-29 11:49:01,252 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 25464 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 11:49:01,260 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 11:49:04,162 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 11:49:04,164 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 11:49:05,416 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1234 ms. Found 88 JPA repository interfaces.
2025-07-29 11:49:06,199 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 11:49:06,200 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 11:49:06,203 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:49:06,203 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:49:06,208 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:49:06,210 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 11:49:06,212 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 11:49:06,212 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:49:06,212 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:49:07,306 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 11:49:07,332 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 11:49:07,336 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 11:49:08,734 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 11:49:09,118 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 11:49:09,153 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 11:49:09,154 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 11:49:09,156 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 11:49:09,157 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 11:49:09,157 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 11:49:09,158 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 11:49:09,161 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 11:49:09,164 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 11:49:12,552 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 11:49:13,421 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 11:49:13,688 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 11:49:14,410 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 11:49:15,063 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 11:49:26,777 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 11:49:26,832 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 11:49:32,174 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-29 11:49:32,640 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 30755 ms
2025-07-29 11:49:38,913 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-29 11:49:39,162 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 11:49:39,276 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 11:49:39,276 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 11:49:39,302 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 11:49:39,307 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 11:49:39,308 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 11:49:39,308 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 11:49:39,309 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@2d2a8882
2025-07-29 11:49:39,309 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 11:49:43,545 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@78214685, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@72289e5, org.springframework.security.web.context.SecurityContextPersistenceFilter@650d0a90, org.springframework.security.web.header.HeaderWriterFilter@2467d99b, org.springframework.security.web.authentication.logout.LogoutFilter@7b742ec1, org.springframework.web.filter.CorsFilter@bbaf70f, com.wzsec.modules.security.security.TokenFilter@6d4d4108, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@571b6751, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@ff2e200, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@44bc7dc9, org.springframework.security.web.session.SessionManagementFilter@e8b6bb7, org.springframework.security.web.access.ExceptionTranslationFilter@59ad1606, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e1d8699]
2025-07-29 11:49:43,597 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 11:49:49,042 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 11:49:49,043 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 11:49:49,043 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 11:49:49,043 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 11:49:49,043 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 11:49:49,043 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 11:49:49,043 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 11:49:49,045 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a126d6d
2025-07-29 11:49:49,318 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 11:49:49,434 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-29 11:49:49,440 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 11:49:49,442 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 11:49:49,442 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 11:49:49,442 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 11:49:49,442 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 11:49:49,442 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 11:49:49,442 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:49:49,443 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 11:49:49,443 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:49:49,446 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 11:49:49,446 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 11:49:49,447 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 11:49:49,465 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 49.077 seconds (JVM running for 52.926)
2025-07-29 11:49:50,448 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-29 11:49:50,448 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-29 11:54:42,762 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 11:54:42,792 INFO (FrameworkServlet.java:547)- Completed initialization in 29 ms
2025-07-29 11:54:48,415 INFO (DoVideoTaskServiceImpl.java:58)- 开始执行视频脱敏任务
2025-07-29 11:54:49,336 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_input/v1.mp4
2025-07-29 11:54:49,337 INFO (DoVideoTaskServiceImpl.java:91)- 处理前收集到输入文件数量: 0
2025-07-29 11:54:49,337 INFO (DoVideoTaskServiceImpl.java:116)- 根据扩展名判断为视频文件: /data/joy/v_input/v1.mp4, 目录: \data\joy\v_input, 文件名: v1.mp4
2025-07-29 11:54:49,462 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\joy\v_input, isBatch: false, fileName: v1.mp4
2025-07-29 11:55:29,380 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\v_input:/app/input -v \data\joy\v_output:/app/output mosaic_app:latest -type video -input /app/input/v1.mp4 -output /app/output/v1.mp4 -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-29 11:55:29,419 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl.execution(DoVideoTaskServiceImpl.java:142)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl$$FastClassBySpringCGLIB$$d85a2c64.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-29 11:55:29,420 WARN (DoVideoTaskServiceImpl.java:155)- Docker 处理失败，退出码: -1
2025-07-29 11:55:29,682 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_output
2025-07-29 11:55:29,705 INFO (DoVideoTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-29 11:55:30,551 INFO (DoVideoTaskServiceImpl.java:251)- 执行视频脱敏总时长为: 41秒
2025-07-29 11:55:30,551 INFO (DoVideoTaskServiceImpl.java:252)- 任务ID【10】,执行视频脱敏任务结束
2025-07-29 11:55:30,551 INFO (DoVideoTaskServiceImpl.java:253)- 任务ID【10】,执行完毕
2025-07-29 11:56:29,829 INFO (DoVideoTaskServiceImpl.java:58)- 开始执行视频脱敏任务
2025-07-29 11:56:30,113 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_input/v1.wmv
2025-07-29 11:56:30,113 INFO (DoVideoTaskServiceImpl.java:91)- 处理前收集到输入文件数量: 0
2025-07-29 11:56:30,113 INFO (DoVideoTaskServiceImpl.java:116)- 根据扩展名判断为视频文件: /data/joy/v_input/v1.wmv, 目录: \data\joy\v_input, 文件名: v1.wmv
2025-07-29 11:56:30,113 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\joy\v_input, isBatch: false, fileName: v1.wmv
2025-07-29 12:22:24,831 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\v_input:/app/input -v \data\joy\v_output:/app/output mosaic_app:latest -type video -input /app/input/v1.wmv -output /app/output/v1.wmv -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-29 12:22:24,867 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl.execution(DoVideoTaskServiceImpl.java:142)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl$$FastClassBySpringCGLIB$$d85a2c64.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-29 12:22:24,869 WARN (DoVideoTaskServiceImpl.java:155)- Docker 处理失败，退出码: -1
2025-07-29 12:22:25,166 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_output
2025-07-29 12:22:25,170 INFO (DoVideoTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-29 12:22:25,703 INFO (DoVideoTaskServiceImpl.java:251)- 执行视频脱敏总时长为: 1556秒
2025-07-29 12:22:25,704 INFO (DoVideoTaskServiceImpl.java:252)- 任务ID【10】,执行视频脱敏任务结束
2025-07-29 12:22:25,704 INFO (DoVideoTaskServiceImpl.java:253)- 任务ID【10】,执行完毕
2025-07-29 12:58:14,137 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 12:58:16,016 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-29 12:58:16,019 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 12:58:16,019 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 12:58:16,022 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 12:58:16,149 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 12:58:16,153 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 12:58:16,156 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 12:58:16,238 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 12:58:16,551 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-29 12:58:36,042 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 8404 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 12:58:36,049 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 12:58:40,357 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 12:58:40,358 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 12:58:42,170 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1781 ms. Found 88 JPA repository interfaces.
2025-07-29 12:58:43,218 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 12:58:43,220 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 12:58:43,223 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 12:58:43,223 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 12:58:43,227 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 12:58:43,227 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 12:58:43,229 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 12:58:43,230 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 12:58:43,230 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 12:58:44,615 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 12:58:44,635 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 12:58:44,639 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 12:58:46,813 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 12:58:47,275 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 12:58:47,304 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 12:58:47,304 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 12:58:47,305 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 12:58:47,305 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 12:58:47,305 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 12:58:47,307 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 12:58:47,310 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 12:58:47,313 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 12:58:51,167 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 12:58:52,266 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 12:58:52,567 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 12:58:53,558 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 12:58:54,542 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 12:59:06,638 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 12:59:06,720 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 12:59:14,789 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-29 12:59:15,502 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 38499 ms
2025-07-29 12:59:25,569 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-29 12:59:26,056 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 12:59:26,385 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 12:59:26,385 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 12:59:26,409 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 12:59:26,415 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 12:59:26,415 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 12:59:26,416 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 12:59:26,418 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@8cf705e
2025-07-29 12:59:26,418 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 12:59:35,464 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5b2b7738, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4130052, org.springframework.security.web.context.SecurityContextPersistenceFilter@4515c6f, org.springframework.security.web.header.HeaderWriterFilter@49b6177a, org.springframework.security.web.authentication.logout.LogoutFilter@c2e0be2, org.springframework.web.filter.CorsFilter@416e5cb, com.wzsec.modules.security.security.TokenFilter@6ac3ef0b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@52d7889a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@43023c0d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1d1cfdac, org.springframework.security.web.session.SessionManagementFilter@43329de5, org.springframework.security.web.access.ExceptionTranslationFilter@77871825, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@60880394]
2025-07-29 12:59:35,529 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 12:59:44,738 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 12:59:44,739 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 12:59:44,739 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 12:59:44,739 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 12:59:44,740 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 12:59:44,740 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 12:59:44,740 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 12:59:44,740 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@43567fca
2025-07-29 12:59:45,197 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 12:59:45,451 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-29 12:59:45,455 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 12:59:45,456 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 12:59:45,456 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 12:59:45,457 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 12:59:45,457 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 12:59:45,457 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 12:59:45,457 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 12:59:45,457 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 12:59:45,457 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 12:59:45,462 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 12:59:45,462 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 12:59:45,463 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 12:59:45,480 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 70.985 seconds (JVM running for 75.755)
2025-07-29 12:59:47,066 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-29 12:59:47,066 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-29 12:59:58,150 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 12:59:58,155 INFO (FrameworkServlet.java:547)- Completed initialization in 4 ms
2025-07-29 13:00:04,602 INFO (DoVideoTaskServiceImpl.java:58)- 开始执行视频脱敏任务
2025-07-29 13:00:05,475 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_input/v1.wmv
2025-07-29 13:00:05,475 INFO (DoVideoTaskServiceImpl.java:91)- 处理前收集到输入文件数量: 0
2025-07-29 13:00:05,475 INFO (DoVideoTaskServiceImpl.java:116)- 根据扩展名判断为视频文件: /data/joy/v_input/v1.wmv, 目录: \data\joy\v_input, 文件名: v1.wmv
2025-07-29 13:00:05,481 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\joy\v_input, isBatch: false, fileName: v1.wmv
2025-07-29 13:00:05,483 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/joy/v_input:/app/input -v /data/joy/v_output:/app/output mosaic_app:latest -type video -input /app/input/v1.wmv -output /app/output/v1.wmv -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-29 13:00:05,515 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl.execution(DoVideoTaskServiceImpl.java:142)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl$$FastClassBySpringCGLIB$$d85a2c64.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-29 13:00:05,516 WARN (DoVideoTaskServiceImpl.java:155)- Docker 处理失败，退出码: -1
2025-07-29 13:00:05,737 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_output
2025-07-29 13:00:05,752 INFO (DoVideoTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-29 13:00:06,844 INFO (DoVideoTaskServiceImpl.java:251)- 执行视频脱敏总时长为: 1秒
2025-07-29 13:00:06,844 INFO (DoVideoTaskServiceImpl.java:252)- 任务ID【10】,执行视频脱敏任务结束
2025-07-29 13:00:06,844 INFO (DoVideoTaskServiceImpl.java:253)- 任务ID【10】,执行完毕
2025-07-29 13:35:25,397 INFO (DoVideoTaskServiceImpl.java:58)- 开始执行视频脱敏任务
2025-07-29 13:35:25,804 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_input
2025-07-29 13:35:25,805 INFO (DoVideoTaskServiceImpl.java:91)- 处理前收集到输入文件数量: 0
2025-07-29 13:35:25,805 INFO (DoVideoTaskServiceImpl.java:126)- 根据路径特征判断为目录: \data\joy\v_input
2025-07-29 13:35:25,807 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\joy\v_input, isBatch: true, fileName: null
2025-07-29 13:35:25,808 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/joy/v_input:/app/input -v /data/joy/v_output:/app/output mosaic_app:latest -type video -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-29 13:35:25,823 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl.execution(DoVideoTaskServiceImpl.java:142)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl$$FastClassBySpringCGLIB$$d85a2c64.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-29 13:35:25,824 WARN (DoVideoTaskServiceImpl.java:155)- Docker 处理失败，退出码: -1
2025-07-29 13:35:25,998 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_output
2025-07-29 13:35:26,002 INFO (DoVideoTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-29 13:35:26,507 INFO (DoVideoTaskServiceImpl.java:251)- 执行视频脱敏总时长为: 0秒
2025-07-29 13:35:26,508 INFO (DoVideoTaskServiceImpl.java:252)- 任务ID【9】,执行视频脱敏任务结束
2025-07-29 13:35:26,508 INFO (DoVideoTaskServiceImpl.java:253)- 任务ID【9】,执行完毕
2025-07-29 13:37:21,448 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-29 13:37:21,813 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-29 13:37:21,813 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-29 13:37:21,813 INFO (DoPictureTaskServiceImpl.java:120)- 根据扩展名判断为文件: /data/joy/p_input/idcard.jpg, 目录: \data\joy\p_input, 文件名: idcard.jpg
2025-07-29 13:37:21,813 INFO (DoPictureTaskServiceImpl.java:141)- 单文件模式 - inputPath: \data\joy\p_input, isBatch: false, fileName: idcard.jpg
2025-07-29 13:37:21,814 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\joy\p_input, isBatch: false, fileName: idcard.jpg
2025-07-29 13:37:21,814 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/joy/p_input:/app/input -v /data/joy/p_output:/app/output mosaic_app:latest -type image -input /app/input/idcard.jpg -output /app/output/idcard.jpg -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-29 13:37:21,823 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:148)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-29 13:37:21,823 WARN (DoPictureTaskServiceImpl.java:161)- Docker 处理失败，退出码: -1
2025-07-29 13:37:22,014 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_output
2025-07-29 13:37:22,019 INFO (DoPictureTaskServiceImpl.java:236)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-29 13:37:22,578 INFO (DoPictureTaskServiceImpl.java:265)- 执行图片脱敏总时长为: 1秒
2025-07-29 13:37:22,578 INFO (DoPictureTaskServiceImpl.java:266)- 任务ID【22】,执行图片脱敏任务结束
2025-07-29 13:37:22,578 INFO (DoPictureTaskServiceImpl.java:267)- 任务ID【22】,执行完毕
2025-07-29 13:38:07,953 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-29 13:38:08,215 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_input
2025-07-29 13:38:08,216 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-29 13:38:08,216 INFO (DoPictureTaskServiceImpl.java:130)- 根据路径特征判断为目录: \data\joy\p_input
2025-07-29 13:38:08,216 INFO (DoPictureTaskServiceImpl.java:145)- 批处理模式 - inputPath: \data\joy\p_input, isBatch: true
2025-07-29 13:38:08,217 INFO (DockerRunner.java:74)- DockerRunner 参数 - inputPath: \data\joy\p_input, isBatch: true, fileName: null
2025-07-29 13:38:08,217 INFO (DockerRunner.java:78)- 准备执行 Docker 命令:
docker run --rm -v /data/joy/p_input:/app/input -v /data/joy/p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output --batch -target yolov8_license_plate -target yolov8n-face --enable-ocr
2025-07-29 13:38:08,224 ERROR (DockerRunner.java:98)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:81)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:148)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-29 13:38:08,225 WARN (DoPictureTaskServiceImpl.java:161)- Docker 处理失败，退出码: -1
2025-07-29 13:38:08,382 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_output
2025-07-29 13:38:08,383 INFO (DoPictureTaskServiceImpl.java:236)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-29 13:38:08,868 INFO (DoPictureTaskServiceImpl.java:265)- 执行图片脱敏总时长为: 1秒
2025-07-29 13:38:08,868 INFO (DoPictureTaskServiceImpl.java:266)- 任务ID【21】,执行图片脱敏任务结束
2025-07-29 13:38:08,868 INFO (DoPictureTaskServiceImpl.java:267)- 任务ID【21】,执行完毕
