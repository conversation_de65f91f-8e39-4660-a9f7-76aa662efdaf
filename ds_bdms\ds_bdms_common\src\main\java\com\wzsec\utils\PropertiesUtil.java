package com.wzsec.utils;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * properties配置文件加载类
 *
 * <AUTHOR>
 * @date 2020-4-26
 */
public class PropertiesUtil {

    public static Map<String, String> getProperty(String filePath) {
        Map<String, String> map = new HashMap<>();
        try{
            BufferedReader reader = new BufferedReader(new InputStreamReader(FileUtil.getResourceAsStream(filePath, null)));
            Properties p = new Properties();
            p.load(reader);
            for (Map.Entry entry : p.entrySet()) {
                String key = (String) entry.getKey();
                map.put(key, (String) entry.getValue());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return map;
    }

    public static Map<String, String> getProperty(File file) {
        Map<String, String> map = new HashMap<>();
        try (InputStream in = new FileInputStream(file)) {
            Properties p = new Properties();
            p.load(in);
            for (Map.Entry entry : p.entrySet()) {
                String key = (String) entry.getKey();
                map.put(key, (String) entry.getValue());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return map;
    }
}
