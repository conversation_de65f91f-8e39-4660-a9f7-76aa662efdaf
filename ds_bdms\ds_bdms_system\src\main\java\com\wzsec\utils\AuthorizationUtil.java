package com.wzsec.utils;


import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.license.service.LicenseService;
import com.wzsec.modules.license.service.dto.LicenseDto;
import com.wzsec.modules.license.service.dto.LicenseQueryCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.io.UnsupportedEncodingException;
import java.util.List;


/**
 * 权限校验
 */
@Slf4j
public class AuthorizationUtil {

    private static LicenseService licenseService = SpringUtils.getApplicationContext().getBean(LicenseService.class);

    /**
     * 登录天数校验
     *
     * @return boolean
     */
    public static int permissionVerification() {

        String effectiveDate = "";
        //获取数据库中主机秘钥并解析
        LicenseQueryCriteria criteria = new LicenseQueryCriteria();
        List<LicenseDto> licenseDto = licenseService.queryAll(criteria);
        for (LicenseDto dto : licenseDto) {
            effectiveDate = dto.getEffectiveDate();
        }

        String decrypt = VerifyUtil.decrypt(effectiveDate, Const.AUTHORIZATION_AES_SECRET_KEY);

        int count = -1;
        try {
            count = DateUtil.getDayDifference(decrypt);
        } catch (Exception e) {
            throw new BadRequestException(HttpStatus.METHOD_NOT_ALLOWED, "软件授权系统出现异常,请重试!");
        }

        return count;
    }


    /**
     * 数据源限制
     *
     * @return boolean
     */
    public static int findResourceConstraints() {
        String resourceConstraints = "";
        //获取数据库中主机秘钥并解析
        LicenseQueryCriteria criteria = new LicenseQueryCriteria();
        List<LicenseDto> licenseDto = licenseService.queryAll(criteria);
        for (LicenseDto dto : licenseDto) {
            resourceConstraints = dto.getResourceConstraints();
        }
        int decrypt = Integer.parseInt(VerifyUtil.decrypt(resourceConstraints, Const.AUTHORIZATION_AES_SECRET_KEY));
        return decrypt;
    }


    /**
     * 授权码校验
     *
     * @param code 代码
     * @return boolean
     * @throws Exception 异常
     */
    public static boolean verificationAuthorization(String code) throws Exception {
        String effectiveDate = null;
        String resource = null;
        byte[] signature = null;
        Boolean signatureCheck = false;
        try {
            String decrypt = VerifyUtil.decrypt(code, Const.AUTHORIZATION_AES_SECRET_KEY);
            String[] split = decrypt.split(",");
            signature = VerifyUtil.decode(split[0]);
            effectiveDate = split[1];
            resource = split[2];
            byte[] crypto = new byte[0];
            try {
                byte[] bytes = (Const.Authorization + ":" + "WZSEC").getBytes("UTF-8");
                crypto = VerifyUtil.hmacsha1Signature(bytes, Const.AUTHORIZATION_AES_SECRET_KEY.getBytes("UTF-8"));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e.getMessage(), e);
            }
            signatureCheck = VerifyUtil.areEqual(crypto, signature);
        } catch (Exception e) {
            e.printStackTrace();
        }

        int dayDifference = DateUtil.getDayDifference(effectiveDate);

        if (signatureCheck && dayDifference > 0) {
            licenseService.updateAuthorizationDetails(
                    VerifyUtil.encrypt(effectiveDate, Const.AUTHORIZATION_AES_SECRET_KEY),
                    VerifyUtil.encrypt(resource, Const.AUTHORIZATION_AES_SECRET_KEY));
            log.info("授权码校验成功!");
            return true;
        } else {
            log.error("签名校验失败");
            return false;
        }
    }


}
