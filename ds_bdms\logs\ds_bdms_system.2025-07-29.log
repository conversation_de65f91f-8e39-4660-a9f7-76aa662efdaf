2025-07-29 11:24:40,515 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 21356 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 11:24:40,531 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 11:24:45,381 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 11:24:45,385 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 11:24:47,223 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1648 ms. Found 111 JPA repository interfaces.
2025-07-29 11:24:48,463 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 11:24:48,464 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 11:24:48,467 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:24:48,467 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:24:48,471 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:24:48,472 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 11:24:48,476 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 11:24:48,477 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:24:48,477 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:24:49,777 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 11:24:49,802 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 11:24:49,807 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 11:24:50,547 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-29 11:24:50,602 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 11:24:50,609 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-29 11:24:50,610 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-29 11:24:50,697 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.4940281425893153849] which is part of the web application []
2025-07-29 11:24:51,038 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-29 11:24:51,038 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 9729 ms
2025-07-29 11:24:53,691 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 11:24:54,170 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 11:24:54,230 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 11:24:54,231 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 11:24:54,232 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 11:24:54,232 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 11:24:54,233 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 11:24:54,233 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 11:24:54,240 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 11:24:54,247 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 11:24:57,400 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 11:24:58,626 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 11:24:58,947 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 11:24:59,609 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 11:25:00,197 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 11:25:05,999 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 11:25:06,083 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 11:25:09,607 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-29 11:25:15,922 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 11:25:16,056 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 11:25:16,056 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 11:25:16,080 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 11:25:16,086 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 11:25:16,087 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 11:25:16,087 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 11:25:16,089 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@73797a74
2025-07-29 11:25:16,089 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 11:25:30,072 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@ca44cba, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6b4b8163, org.springframework.security.web.context.SecurityContextPersistenceFilter@58100e7d, org.springframework.security.web.header.HeaderWriterFilter@303bcad6, org.springframework.security.web.authentication.logout.LogoutFilter@5f1a2706, org.springframework.web.filter.CorsFilter@c1a74d, com.wzsec.modules.security.security.TokenFilter@5c1b20e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@830b8a5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@74df38c0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33697548, org.springframework.security.web.session.SessionManagementFilter@59d1d820, org.springframework.security.web.access.ExceptionTranslationFilter@12888eb5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@443a9e24]
2025-07-29 11:25:30,173 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 11:25:36,162 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 11:25:36,162 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 11:25:36,163 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 11:25:36,163 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 11:25:36,163 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 11:25:36,163 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 11:25:36,164 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 11:25:36,164 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2b5dfb1b
2025-07-29 11:25:36,423 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 11:25:36,579 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 11:25:36,709 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 11:25:36,716 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 11:25:36,717 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 11:25:36,718 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 11:25:36,718 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 11:25:36,718 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 11:25:36,718 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 11:25:36,719 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 11:25:36,719 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 11:25:36,719 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 11:25:36,728 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 11:25:36,729 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 11:25:36,729 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 11:25:36,743 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-29 11:25:38,321 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-29 11:25:38,545 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-29 11:25:38,875 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-29 11:25:39,103 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-29 11:25:39,365 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-29 11:25:39,366 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-29 11:25:39,378 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 60.291 seconds (JVM running for 65.573)
2025-07-29 11:25:40,513 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-29 11:38:01,736 ERROR (JobRunShell.java:211)- Job DEFAULT.19 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl.executionFromEngine(MaskAuditTaskV1ServiceImpl.java:179)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$FastClassBySpringCGLIB$$a4bd1e4d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$EnhancerBySpringCGLIB$$6b7df789.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskAuditTaskConfigJob.execute(MaskAuditTaskConfigJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-29 11:38:01,743 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.19 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl.executionFromEngine(MaskAuditTaskV1ServiceImpl.java:179)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$FastClassBySpringCGLIB$$a4bd1e4d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$EnhancerBySpringCGLIB$$6b7df789.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskAuditTaskConfigJob.execute(MaskAuditTaskConfigJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-29 11:46:24,604 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 11:46:24,609 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 11:46:24,779 INFO (FrameworkServlet.java:547)- Completed initialization in 170 ms
2025-07-29 13:46:44,548 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 4916 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 13:46:44,555 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 13:46:47,264 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 13:46:47,266 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 13:46:48,623 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1244 ms. Found 111 JPA repository interfaces.
2025-07-29 13:46:49,334 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 13:46:49,335 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 13:46:49,338 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 13:46:49,338 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 13:46:49,341 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 13:46:49,343 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 13:46:49,345 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 13:46:49,346 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 13:46:49,346 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 13:46:50,247 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 13:46:50,268 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 13:46:50,272 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 13:46:50,845 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-29 13:46:50,900 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 13:46:50,907 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-29 13:46:50,907 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-29 13:46:50,995 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.4538627217056041267] which is part of the web application []
2025-07-29 13:46:51,285 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-29 13:46:51,285 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 6118 ms
2025-07-29 13:46:52,973 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 13:46:53,373 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 13:46:53,428 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 13:46:53,429 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 13:46:53,429 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 13:46:53,429 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 13:46:53,430 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 13:46:53,431 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 13:46:53,435 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 13:46:53,439 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 13:46:56,734 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 13:46:57,730 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 13:46:58,006 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 13:46:58,678 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 13:46:59,285 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 13:47:04,790 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 13:47:04,854 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 13:47:07,994 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-29 13:47:12,352 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 13:47:12,527 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 13:47:12,527 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 13:47:12,548 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 13:47:12,553 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 13:47:12,554 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 13:47:12,554 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 13:47:12,555 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@ce1454c
2025-07-29 13:47:12,555 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 13:47:24,406 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@59d1d820, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@549f0e14, org.springframework.security.web.context.SecurityContextPersistenceFilter@3b7edb51, org.springframework.security.web.header.HeaderWriterFilter@75b76c78, org.springframework.security.web.authentication.logout.LogoutFilter@41de8d57, org.springframework.web.filter.CorsFilter@7f77e065, com.wzsec.modules.security.security.TokenFilter@311eb977, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4c8ad614, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@768f6f7e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2dad9e6a, org.springframework.security.web.session.SessionManagementFilter@7106b519, org.springframework.security.web.access.ExceptionTranslationFilter@4f123e4e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@28be98fc]
2025-07-29 13:47:24,475 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 13:47:29,696 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 13:47:29,696 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 13:47:29,697 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 13:47:29,697 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 13:47:29,697 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 13:47:29,697 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 13:47:29,697 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 13:47:29,697 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@399b0d80
2025-07-29 13:47:29,971 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 13:47:30,094 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 13:47:30,204 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 13:47:30,210 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 13:47:30,212 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 13:47:30,212 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 13:47:30,212 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 13:47:30,212 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 13:47:30,213 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 13:47:30,213 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 13:47:30,213 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 13:47:30,213 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 13:47:30,217 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 13:47:30,218 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 13:47:30,218 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 13:47:30,224 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-29 13:47:31,731 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-29 13:47:31,981 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-29 13:47:32,265 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-29 13:47:32,540 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-29 13:47:32,818 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-29 13:47:32,818 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-29 13:47:32,834 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 49.179 seconds (JVM running for 52.157)
2025-07-29 13:47:33,591 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-29 13:51:17,008 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 13:51:17,008 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 13:51:17,012 INFO (FrameworkServlet.java:547)- Completed initialization in 4 ms
2025-07-29 13:58:58,364 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 14:06:27,374 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 12940 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 14:06:27,381 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 14:06:30,197 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 14:06:30,199 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 14:06:31,415 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1112 ms. Found 111 JPA repository interfaces.
2025-07-29 14:06:32,107 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 14:06:32,108 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 14:06:32,111 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 14:06:32,111 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 14:06:32,114 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:06:32,115 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 14:06:32,117 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 14:06:32,117 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:06:32,117 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:06:33,021 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 14:06:33,038 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 14:06:33,040 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 14:06:33,550 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-29 14:06:33,581 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 14:06:33,585 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-29 14:06:33,585 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-29 14:06:33,638 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.6869144576135107789] which is part of the web application []
2025-07-29 14:06:33,923 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-29 14:06:33,924 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5976 ms
2025-07-29 14:06:35,558 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 14:06:35,868 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 14:06:35,899 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 14:06:35,899 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 14:06:35,899 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 14:06:35,899 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 14:06:35,901 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 14:06:35,901 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 14:06:35,904 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 14:06:35,908 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 14:06:38,878 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 14:06:39,999 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 14:06:40,296 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 14:06:41,043 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 14:06:41,749 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 14:06:47,121 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 14:06:47,185 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 14:06:50,595 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-29 14:06:55,336 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 14:06:55,458 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 14:06:55,459 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 14:06:55,483 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 14:06:55,490 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 14:06:55,490 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 14:06:55,490 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 14:06:55,491 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6cc57f26
2025-07-29 14:06:55,492 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 14:07:08,743 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1493c922, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a48e086, org.springframework.security.web.context.SecurityContextPersistenceFilter@761db70f, org.springframework.security.web.header.HeaderWriterFilter@486c3d3d, org.springframework.security.web.authentication.logout.LogoutFilter@1ece8c98, org.springframework.web.filter.CorsFilter@22429a11, com.wzsec.modules.security.security.TokenFilter@5b8b8794, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3ed6437b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7fd1342c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5aaa3740, org.springframework.security.web.session.SessionManagementFilter@5c273b3e, org.springframework.security.web.access.ExceptionTranslationFilter@19608022, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2125bb4e]
2025-07-29 14:07:08,804 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 14:07:20,141 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 14:07:20,142 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 14:07:20,143 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 14:07:20,143 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 14:07:20,143 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 14:07:20,143 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 14:07:20,143 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 14:07:20,143 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@149c942b
2025-07-29 14:07:20,530 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 14:07:20,640 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 14:07:20,866 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 14:07:20,881 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 14:07:20,884 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 14:07:20,884 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 14:07:20,884 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 14:07:20,884 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 14:07:20,885 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 14:07:20,885 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:07:20,885 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 14:07:20,885 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 14:07:20,894 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 14:07:20,894 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 14:07:20,894 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 14:07:20,909 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-29 14:07:22,807 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-29 14:07:23,069 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-29 14:07:23,386 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-29 14:07:23,829 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-29 14:07:24,255 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-29 14:07:24,256 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-29 14:07:24,271 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 57.73 seconds (JVM running for 60.551)
2025-07-29 14:07:25,867 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-29 14:07:45,144 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 14:07:45,144 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 14:07:45,147 INFO (FrameworkServlet.java:547)- Completed initialization in 3 ms
2025-07-29 14:17:20,886 ERROR (GlobalExceptionHandler.java:54)- com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl.executionFromEngine(MaskVideotaskconfigServiceImpl.java:189)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl$$FastClassBySpringCGLIB$$a46b7146.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl$$EnhancerBySpringCGLIB$$d5b27b6d.executionFromEngine(<generated>)
	at com.wzsec.modules.mask.rest.MaskVideotaskconfigController.executionFromEngine(MaskVideotaskconfigController.java:86)
	at com.wzsec.modules.mask.rest.MaskVideotaskconfigController$$FastClassBySpringCGLIB$$74a67986.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.wzsec.aspect.LogAspect.logAround(LogAspect.java:52)
	at sun.reflect.GeneratedMethodAccessor71.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.security.access.intercept.aopalliance.MethodSecurityInterceptor.invoke(MethodSecurityInterceptor.java:67)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.rest.MaskVideotaskconfigController$$EnhancerBySpringCGLIB$$9e9f6d6.executionFromEngine(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:920)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:558)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:352)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:164)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at com.wzsec.modules.security.security.TokenFilter.doFilter(TokenFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1793)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:748)

2025-07-29 14:45:00,944 ERROR (JobRunShell.java:211)- Job DEFAULT.14 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$529a71bf.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-29 14:45:00,960 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.14 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$529a71bf.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-29 14:53:47,776 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 26784 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 14:53:47,782 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 14:53:50,636 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 14:53:50,638 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 14:53:52,093 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1345 ms. Found 111 JPA repository interfaces.
2025-07-29 14:53:53,198 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 14:53:53,199 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 14:53:53,203 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 14:53:53,203 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 14:53:53,208 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:53:53,210 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 14:53:53,212 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 14:53:53,212 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:53:53,212 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:53:54,474 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 14:53:54,501 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 14:53:54,505 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 14:53:55,267 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-29 14:53:55,312 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 14:53:55,319 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-29 14:53:55,319 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-29 14:53:55,391 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.9196930796166673179] which is part of the web application []
2025-07-29 14:53:55,722 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-29 14:53:55,723 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 7361 ms
2025-07-29 14:53:57,790 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 14:53:58,259 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 14:53:58,296 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 14:53:58,296 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 14:53:58,296 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 14:53:58,297 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 14:53:58,297 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 14:53:58,298 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 14:53:58,301 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 14:53:58,305 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 14:54:01,587 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 14:54:02,626 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 14:54:02,919 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 14:54:03,620 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 14:54:04,311 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 14:54:09,592 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 14:54:09,655 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 14:54:13,094 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-29 14:54:17,611 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 14:54:17,727 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 14:54:17,727 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 14:54:17,751 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 14:54:17,756 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 14:54:17,756 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 14:54:17,757 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 14:54:17,758 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@a24e874
2025-07-29 14:54:17,758 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 14:54:30,210 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@64a12381, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@53162e9c, org.springframework.security.web.context.SecurityContextPersistenceFilter@7c609e43, org.springframework.security.web.header.HeaderWriterFilter@6630e32d, org.springframework.security.web.authentication.logout.LogoutFilter@3344bc4, org.springframework.web.filter.CorsFilter@54286339, com.wzsec.modules.security.security.TokenFilter@375575e2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@208ac9b8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4330f93c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16b0a770, org.springframework.security.web.session.SessionManagementFilter@5204a284, org.springframework.security.web.access.ExceptionTranslationFilter@169f4020, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2700b0d9]
2025-07-29 14:54:30,270 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 14:54:36,061 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 14:54:36,062 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 14:54:36,062 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 14:54:36,062 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 14:54:36,062 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 14:54:36,063 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 14:54:36,063 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 14:54:36,063 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5a44b349
2025-07-29 14:54:36,392 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 14:54:36,514 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 14:54:36,637 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 14:54:36,642 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 14:54:36,645 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 14:54:36,645 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 14:54:36,645 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 14:54:36,645 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 14:54:36,645 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 14:54:36,645 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 14:54:36,645 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 14:54:36,645 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 14:54:36,654 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 14:54:36,654 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 14:54:36,654 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 14:54:36,668 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-29 14:54:38,700 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-29 14:54:38,993 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-29 14:54:39,300 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-29 14:54:39,592 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-29 14:54:39,876 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-29 14:54:39,876 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-29 14:54:39,895 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 52.977 seconds (JVM running for 56.526)
2025-07-29 14:54:40,711 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-29 14:55:03,358 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 14:55:03,358 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 14:55:03,360 INFO (FrameworkServlet.java:547)- Completed initialization in 1 ms
2025-07-29 15:05:10,425 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 15:17:15,926 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 25128 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 15:17:15,931 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 15:17:18,780 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 15:17:18,783 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 15:17:20,037 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1232 ms. Found 111 JPA repository interfaces.
2025-07-29 15:17:20,954 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 15:17:20,955 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 15:17:20,958 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 15:17:20,959 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 15:17:20,963 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:17:20,965 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 15:17:20,966 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 15:17:20,968 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:17:20,968 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:17:22,060 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 15:17:22,081 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 15:17:22,085 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 15:17:22,716 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-29 15:17:22,761 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 15:17:22,768 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-29 15:17:22,769 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-29 15:17:22,846 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.2660278178554545158] which is part of the web application []
2025-07-29 15:17:23,149 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-29 15:17:23,149 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 6666 ms
2025-07-29 15:17:25,068 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 15:17:25,367 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 15:17:25,395 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 15:17:25,395 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 15:17:25,396 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 15:17:25,396 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 15:17:25,396 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 15:17:25,397 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 15:17:25,400 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 15:17:25,403 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 15:17:28,247 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 15:17:29,170 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 15:17:29,418 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 15:17:30,027 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 15:17:30,576 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 15:17:34,949 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 15:17:35,005 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 15:17:37,883 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-29 15:17:41,923 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 15:17:42,030 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 15:17:42,030 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 15:17:42,051 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 15:17:42,057 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 15:17:42,057 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 15:17:42,057 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 15:17:42,058 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@746be7ac
2025-07-29 15:17:42,058 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 15:17:53,638 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@645e2cfe, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3f5b2857, org.springframework.security.web.context.SecurityContextPersistenceFilter@226451a2, org.springframework.security.web.header.HeaderWriterFilter@2c542a2a, org.springframework.security.web.authentication.logout.LogoutFilter@6a737ee1, org.springframework.web.filter.CorsFilter@16949dfa, com.wzsec.modules.security.security.TokenFilter@4e1cc48b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@327fe02d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1c2618d8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@530e1f71, org.springframework.security.web.session.SessionManagementFilter@5bca3ba6, org.springframework.security.web.access.ExceptionTranslationFilter@23701ff5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5122088e]
2025-07-29 15:17:53,693 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 15:17:59,123 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 15:17:59,124 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 15:17:59,126 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 15:17:59,126 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 15:17:59,126 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 15:17:59,126 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 15:17:59,126 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 15:17:59,126 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@25ea076d
2025-07-29 15:17:59,362 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 15:17:59,491 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 15:17:59,604 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 15:17:59,609 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 15:17:59,611 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 15:17:59,611 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 15:17:59,611 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 15:17:59,611 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 15:17:59,611 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 15:17:59,611 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:17:59,612 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 15:17:59,612 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 15:17:59,616 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 15:17:59,616 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 15:17:59,616 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 15:17:59,623 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-29 15:18:01,089 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-29 15:18:01,353 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-29 15:18:01,694 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-29 15:18:01,992 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-29 15:18:02,283 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-29 15:18:02,283 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-29 15:18:02,298 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 47.193 seconds (JVM running for 50.696)
2025-07-29 15:18:03,010 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-29 15:18:10,834 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 15:18:10,835 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 15:18:10,840 INFO (FrameworkServlet.java:547)- Completed initialization in 5 ms
2025-07-29 15:30:41,161 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 15:30:42,228 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-29 15:30:42,229 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 15:30:42,229 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 15:30:42,230 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 15:30:42,299 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 15:30:42,299 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 15:30:42,299 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 15:30:42,313 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 15:30:42,380 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-29 15:35:19,174 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 18848 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 15:35:19,181 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 15:35:21,951 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 15:35:21,953 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 15:35:23,163 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1090 ms. Found 111 JPA repository interfaces.
2025-07-29 15:35:23,826 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 15:35:23,828 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 15:35:23,831 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 15:35:23,831 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 15:35:23,834 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:35:23,836 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 15:35:23,838 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 15:35:23,838 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:35:23,838 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:35:24,710 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 15:35:24,728 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 15:35:24,731 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 15:35:25,243 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-29 15:35:25,273 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 15:35:25,277 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-29 15:35:25,278 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-29 15:35:25,327 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.1123371934589381023] which is part of the web application []
2025-07-29 15:35:25,560 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-29 15:35:25,560 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5826 ms
2025-07-29 15:35:27,104 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 15:35:27,413 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 15:35:27,441 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 15:35:27,442 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 15:35:27,442 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 15:35:27,442 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 15:35:27,443 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 15:35:27,443 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 15:35:27,446 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 15:35:27,449 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 15:35:30,298 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 15:35:31,213 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 15:35:31,452 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 15:35:32,036 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 15:35:32,631 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 15:35:37,171 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 15:35:37,226 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 15:35:40,006 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-29 15:35:43,596 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 15:35:43,692 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 15:35:43,692 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 15:35:43,712 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 15:35:43,716 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 15:35:43,717 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 15:35:43,717 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 15:35:43,718 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@5454e13f
2025-07-29 15:35:43,718 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 15:35:54,388 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@26339ea0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@36e8fffc, org.springframework.security.web.context.SecurityContextPersistenceFilter@2d6911a9, org.springframework.security.web.header.HeaderWriterFilter@61a01e85, org.springframework.security.web.authentication.logout.LogoutFilter@601e6724, org.springframework.web.filter.CorsFilter@573870cb, com.wzsec.modules.security.security.TokenFilter@36c8c89, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@59d1d820, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@726f2d26, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@47be1b38, org.springframework.security.web.session.SessionManagementFilter@128f4b59, org.springframework.security.web.access.ExceptionTranslationFilter@6d0ba9b7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@b0b6227]
2025-07-29 15:35:54,443 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 15:35:59,513 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 15:35:59,514 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 15:35:59,514 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 15:35:59,514 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 15:35:59,514 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 15:35:59,514 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 15:35:59,514 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 15:35:59,515 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1389e498
2025-07-29 15:35:59,741 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 15:35:59,839 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 15:35:59,953 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 15:35:59,961 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 15:35:59,963 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 15:35:59,963 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 15:35:59,963 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 15:35:59,963 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 15:35:59,963 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 15:35:59,963 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 15:35:59,964 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 15:35:59,964 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 15:35:59,969 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 15:35:59,969 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 15:35:59,969 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 15:35:59,976 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-29 15:36:01,407 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-29 15:36:01,653 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-29 15:36:01,952 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-29 15:36:02,194 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-29 15:36:02,469 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-29 15:36:02,469 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-29 15:36:02,480 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 44.197 seconds (JVM running for 47.401)
2025-07-29 15:36:03,171 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-29 15:47:09,421 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 15:47:09,433 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 15:47:09,700 INFO (FrameworkServlet.java:547)- Completed initialization in 264 ms
2025-07-29 16:11:01,351 ERROR (JobRunShell.java:211)- Job DEFAULT.131 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$a10831c0.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-29 16:11:01,358 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.131 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$a10831c0.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-29 17:04:27,487 ERROR (JobRunShell.java:211)- Job DEFAULT.174 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$9d5594e7.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-29 17:04:27,489 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.174 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$9d5594e7.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-29 17:24:26,130 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 27932 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-29 17:24:26,136 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-29 17:24:29,594 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 17:24:29,597 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 17:24:31,461 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1709 ms. Found 111 JPA repository interfaces.
2025-07-29 17:24:32,483 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-29 17:24:32,484 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 17:24:32,488 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 17:24:32,489 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 17:24:32,494 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 17:24:32,497 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-29 17:24:32,499 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-29 17:24:32,500 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 17:24:32,500 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 17:24:34,053 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-29 17:24:34,079 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-29 17:24:34,083 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-29 17:24:35,034 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-29 17:24:35,121 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 17:24:35,132 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-29 17:24:35,132 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-29 17:24:35,240 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.59176457298872374] which is part of the web application []
2025-07-29 17:24:35,648 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-29 17:24:35,648 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 8920 ms
2025-07-29 17:24:38,870 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-29 17:24:39,492 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-29 17:24:39,559 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-29 17:24:39,560 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-29 17:24:39,561 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-29 17:24:39,561 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-29 17:24:39,563 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-29 17:24:39,564 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-29 17:24:39,571 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-29 17:24:39,578 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-29 17:24:43,412 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-29 17:24:44,615 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 17:24:44,966 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-29 17:24:45,834 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 17:24:46,729 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-29 17:24:53,136 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 17:24:53,217 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 17:24:57,932 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-29 17:25:03,385 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 17:25:03,576 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 17:25:03,576 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 17:25:03,601 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 17:25:03,608 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 17:25:03,609 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-29 17:25:03,609 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 17:25:03,611 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@e8d54fb
2025-07-29 17:25:03,611 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 17:25:19,034 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@26339ea0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@36e8fffc, org.springframework.security.web.context.SecurityContextPersistenceFilter@2ad4ade7, org.springframework.security.web.header.HeaderWriterFilter@35b71e90, org.springframework.security.web.authentication.logout.LogoutFilter@1fb33520, org.springframework.web.filter.CorsFilter@3dec79f8, com.wzsec.modules.security.security.TokenFilter@36c8c89, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@74bfa2f8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@50d4b0eb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@47be1b38, org.springframework.security.web.session.SessionManagementFilter@58100e7d, org.springframework.security.web.access.ExceptionTranslationFilter@128f4b59, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@b0b6227]
2025-07-29 17:25:19,110 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-29 17:25:27,889 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-29 17:25:27,894 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 17:25:27,896 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-29 17:25:27,896 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-29 17:25:27,896 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 17:25:27,896 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 17:25:27,897 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-29 17:25:27,897 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@44db06d9
2025-07-29 17:25:28,285 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 17:25:28,398 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-29 17:25:28,513 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 17:25:28,521 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-29 17:25:28,523 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-29 17:25:28,523 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-29 17:25:28,523 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-29 17:25:28,524 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 17:25:28,524 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-29 17:25:28,524 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-29 17:25:28,524 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-29 17:25:28,524 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-29 17:25:28,530 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-29 17:25:28,530 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-29 17:25:28,530 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 17:25:28,541 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-29 17:25:30,342 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-29 17:25:30,617 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-29 17:25:30,936 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-29 17:25:31,225 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-29 17:25:31,510 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-29 17:25:31,510 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-29 17:25:31,525 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 66.312 seconds (JVM running for 69.863)
2025-07-29 17:25:32,341 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-29 17:27:32,358 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 17:27:32,358 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-29 17:27:32,363 INFO (FrameworkServlet.java:547)- Completed initialization in 5 ms
2025-07-29 17:29:01,224 ERROR (JobRunShell.java:211)- Job DEFAULT.27 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl.executionFromEngine(FileTaskConfigServiceImpl.java:234)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$FastClassBySpringCGLIB$$5968d26b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$2c57888b.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.FileTaskConfigJob.execute(FileTaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-29 17:29:01,235 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.27 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl.executionFromEngine(FileTaskConfigServiceImpl.java:234)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$FastClassBySpringCGLIB$$5968d26b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$2c57888b.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.FileTaskConfigJob.execute(FileTaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-29 17:36:27,451 ERROR (JobRunShell.java:211)- Job DEFAULT.175 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$3c355f75.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-29 17:36:27,453 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.175 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$3c355f75.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
