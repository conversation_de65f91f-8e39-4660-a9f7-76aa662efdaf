package com.wzsec.dotask.mask.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.wzsec.dotask.mask.service.DoHiveTaskService;
import com.wzsec.modules.alarm.config.MonitorRiskAlarmData;
import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.mask.domain.EngineServer;
import com.wzsec.modules.mask.domain.HiveTaskResult;
import com.wzsec.modules.mask.repository.EngineServerRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.HiveTaskConfigDto;
import com.wzsec.modules.mask.service.dto.HiveTaskResultDto;
import com.wzsec.modules.mask.service.mapper.HiveTaskConfigMapper;
import com.wzsec.modules.mask.service.mapper.HiveTaskResultMapper;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.domain.Datasource;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.*;
import com.wzsec.utils.database.HiveUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hive.jdbc.HiveStatement;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.util.*;
import java.util.Date;


// 默认不使用缓存
//import org.springframework.cache.annotation.CacheConfig;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR>
 * @date 2021-01-26
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "DoHiveTaskServiceImpl")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoHiveTaskServiceImpl implements DoHiveTaskService {

    private final AlgorithmService algorithmService;

    private final HiveTaskConfigService hiveTaskConfigService;

    private final HiveTaskConfigMapper hiveTaskConfigMapper;

    private final HiveTaskResultService hiveTaskResultService;

    private final HiveTaskResultMapper hiveTaskResultMapper;

    private final DatasourceService datasourceService;

    private final DmAlarmdisposalService dmAlarmdisposalService;

    private final MaskTaskresultrecordsService maskTaskresultrecordsService;

    private final EngineServerRepository engineServerRepository;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    private final UserService userService;
    public static String msg = "";

    @Value("${spring.profiles.active}")
    private String active;


    public DoHiveTaskServiceImpl(AlgorithmService algorithmService,
                                 HiveTaskConfigService hiveTaskConfigService,
                                 HiveTaskConfigMapper hiveTaskConfigMapper,
                                 HiveTaskResultService hiveTaskResultService,
                                 HiveTaskResultMapper hiveTaskResultMapper,
                                 DatasourceService datasourceService,
                                 UserService userService,
                                 DmAlarmdisposalService dmAlarmdisposalService,
                                 MaskTaskresultrecordsService maskTaskresultrecordsService,
                                 EngineServerRepository engineServerRepository,
                                 SdkOperationrecordRepository sdkOperationrecordRepository,
                                 SdkApplyconfigRepository sdkApplyconfigRepository) {
        this.algorithmService = algorithmService;
        this.hiveTaskConfigService = hiveTaskConfigService;
        this.hiveTaskConfigMapper = hiveTaskConfigMapper;
        this.hiveTaskResultService = hiveTaskResultService;
        this.hiveTaskResultMapper = hiveTaskResultMapper;
        this.datasourceService = datasourceService;
        this.userService = userService;
        this.dmAlarmdisposalService = dmAlarmdisposalService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.engineServerRepository = engineServerRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }

    @Async//异步执行
    @Override
    public void execution(Integer id, UserDto userDto) {
        log.info("开始执行Hive脱敏任务");

        //根据任务id查询任务
        HiveTaskConfigDto hiveTask = hiveTaskConfigService.findById(id);
        boolean isSuccess = false;

        //结果状态
        String startTime = null;
        String endTime = null;
        String totalTime = null;

        String hiveUser = "";
        String hivePassword = "";
        String hiveDirverName = "";
        String hiveUrl = "";
        String account = "";
        int lineCount = 0;
        String queueName = "";
        String dataBase = "";
        String tableName = "";
        String partitionInfos = "";
        String strExtractFieldNames = "";
        String fieldmaskinfo = "";
        String datarows = "";
        String outHdfsDir = "";
        String compressFormat = "";
        String dataSplit = "";
        String maskfieldnames = "";
        String outputtype = "";
        String outputdbname = "";
        String actontable = "";
        String strFieldAlgoParam = "";
        String jarpath = "";
        String sparefield1="";
        String sparefield2="";

        msg = Const.HIVE_TASK_EXECUTESTATE_EXECUTE_SUCCESS;
        Integer resultid = 0;

        String engine = hiveTask.getSparefield1();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String eip = ipPort[0];
        String eport = ipPort[1];

        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(eip+":"+eport);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        try {

            //TODO 将所使用的引擎，任务负载数+1
            if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
                engineServerRepository.addKingbaseCountByIpPort(eip, eport);
            }else{
                engineServerRepository.addCountByIpPort(eip, eport);
            }

            startTime = DateUtil.getNowTime();
            log.info(String.format(Const.STRMASKLOGFLAG + "Hive脱敏任务开始时间:%s", startTime));

            //查询数据库，得到数据源hive中的用户名,密码
            DatasourceDto datasourceDto = datasourceService.findById(hiveTask.getSourceid().longValue());
            hiveUser = datasourceDto.getUsername();
            hivePassword = datasourceDto.getPassword();
            hiveDirverName = datasourceDto.getDriverprogram();
            hiveUrl = datasourceDto.getSrcurl();
            queueName = hiveTask.getQueuename();
            dataBase = hiveTask.getDbname();
            tableName = hiveTask.getTablename();
            partitionInfos = hiveTask.getPartitioninfo();
            strExtractFieldNames = hiveTask.getExtractfields();
            fieldmaskinfo = hiveTask.getMaskstrategystr();
            datarows = hiveTask.getDatarows();
            outHdfsDir = hiveTask.getOutputdir();
            compressFormat = hiveTask.getFileformat();
            dataSplit = hiveTask.getDatasplit();
            maskfieldnames = hiveTask.getMaskfieldnames();
            outputtype = hiveTask.getOutputtype();
            outputdbname = hiveTask.getOutputdbname();
            actontable = hiveTask.getMaskedtable();
            jarpath = hiveTask.getJarpath();

            if (null != maskfieldnames && !"".equals(maskfieldnames)) {
                List<Map<String, String>> fieldMaskLstMap = StringUtils.jsonStringToList(fieldmaskinfo);
                StringBuilder sbFieldMaskInfo = new StringBuilder();
                for (Map<String, String> fieldMaskMap : fieldMaskLstMap) {
                    String fieldname = fieldMaskMap.get("fieldname");
                    String strAlgoInfo = hiveTaskConfigService.getMaskAlgoInfoByAlgoCname(fieldname, hiveTask.getStrategyid());
                    sbFieldMaskInfo.append(fieldname + "|" + strAlgoInfo);
                    sbFieldMaskInfo.append(";");
                }
                strFieldAlgoParam = sbFieldMaskInfo.toString();
                if (strFieldAlgoParam.endsWith(";")) {
                    strFieldAlgoParam = strFieldAlgoParam.substring(0, strFieldAlgoParam.lastIndexOf(";"));
                }
            }

            HashMap<String, String> algofuncnameAndFuncnamepathMap = algorithmService.getAlgofuncnameAndFuncnamepathMap();
            //判断作用于表是否为空
            if (null != actontable && !"".equals(actontable)) {
                tableName = actontable;
            }
            log.info("使用表：" + tableName);
            System.out.println("使用表：" + tableName);
            if (null != datarows && !"-1".equals(datarows) && !"".equals(datarows)) {
                lineCount = Integer.parseInt(datarows);
            }

            String hiveTaskName = hiveTask.getTaskname();

            HiveTaskResult saveHiveJob = new HiveTaskResult();
            saveHiveJob.setTaskname(hiveTaskName);
            saveHiveJob.setDbname(dataBase);
            saveHiveJob.setTablename(tableName);
            saveHiveJob.setPartitioninfo(partitionInfos);
            saveHiveJob.setPlatform(Const.DB_HIVE);
            saveHiveJob.setDataoutputpath(outHdfsDir);
            saveHiveJob.setFileformat(compressFormat); // 输出文件格式
            saveHiveJob.setDatasplit(dataSplit); // 输出数据文件分割符
            saveHiveJob.setCreatetime(startTime);
            saveHiveJob.setRemark("");
            saveHiveJob.setJobstatus(Const.HIVE_TASK_EXECUTESTATE_EXECUTING);//执行中
            saveHiveJob.setJobstarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:ss", startTime));
            saveHiveJob.setQueuename(queueName);
            // Hive脱敏结果
            if (userDto != null){
                saveHiveJob.setUsername(userDto.getNickName());
                saveHiveJob.setUserid(String.valueOf(userDto.getId()));
            } else {
                saveHiveJob.setUsername(Const.TASK_SUBMITTYPE_AUTOSUBMIT);
            }
            saveHiveJob.setDatarows(datarows);
            resultid = hiveTaskConfigService.addHiveTaskResult(saveHiveJob);
            log.info(Const.STRMASKLOGFLAG + "提交Hive脱敏任务,任务Id：" + id + ";作业信息已插入数据库,作业Id：" + resultid);

            log.info(Const.STRMASKLOGFLAG + "获取Hive脱敏任务相关参数 hiveUrl:" + hiveUrl + Const.BLANK + "hiveDirverName:"
                    + hiveDirverName + Const.BLANK + "hiveUser:" + hiveUser + Const.BLANK + "hivePassword:"
                    + hivePassword + Const.BLANK + Const.BLANK + "queueName:" + queueName
                    + Const.BLANK + "dataBase:" + dataBase + Const.BLANK + "tableName:" + tableName + Const.BLANK
                    + "partitionInfos:" + partitionInfos + Const.BLANK + "strExtractFieldNames:" + strExtractFieldNames
                    + Const.BLANK + "strFieldAlgoParam:" + strFieldAlgoParam + Const.BLANK + "lineCount:" + lineCount
                    + Const.BLANK + "outHdfsDir:" + outHdfsDir + Const.BLANK + "compressFormat:" + compressFormat
                    + Const.BLANK + "dataSplit:" + dataSplit + Const.BLANK + "hiveTaskName:" + hiveTaskName
                    + Const.BLANK + "outputtype:" + outputtype + Const.BLANK + "outputdbname:" + outputdbname
                    + Const.BLANK + "jarpath:" + jarpath);
            //脱敏后表名
            List<String> maskEndTableName = new ArrayList<>();
            isSuccess = doHiveDataMasking(hiveUrl, hiveDirverName, hiveUser, hivePassword, account,
                    queueName, dataBase, tableName, partitionInfos, strExtractFieldNames, strFieldAlgoParam, lineCount,
                    outHdfsDir, compressFormat, dataSplit, hiveTaskName, outputtype, outputdbname,
                    algofuncnameAndFuncnamepathMap, jarpath,maskEndTableName);
            log.info(Const.STRMASKLOGFLAG + "Hive脱敏执行是否成功:" + isSuccess);
            // System.out.println("Hive脱敏执行是否成功:" + isSuccess);
            //保存脱敏前5条数据和脱敏后5条数据
            if (isSuccess){
                // 先进行kerberos认证
                try {
                    AuthKrb5.authKrb5();
                } catch (Exception e) {
                    log.error("kerberos认证失败");
                }
                Connection conn = HiveUtil.getConn(hiveDirverName, hiveUser, hivePassword, hiveUrl, dataBase);
                sparefield1 = HiveUtil.getFiveHiveData(tableName,conn);
                if (ObjectUtil.isNotEmpty(maskEndTableName) && maskEndTableName.size() > 0){
                    if (outputtype.equals(Const.HIVE_FILE)){
                        sparefield2 = maskEndTableName.get(0);
                    }else {
                        sparefield2 = HiveUtil.getFiveHiveData(maskEndTableName.get(0),conn);
                    }
                }
                conn.close();
            }

        } catch (Exception e) {
            if (msg.equals(Const.HIVE_TASK_EXECUTESTATE_EXECUTE_SUCCESS)) {
                msg = Const.HIVE_TASK_EXECUTESTATE_EXECUTE_FAIL;
            }
            sdkOperationrecord.setObjectname(hiveTask.getTaskname());
            sdkOperationrecord.setOperation("Hive脱敏任务执行失败");
            log.error(Const.STRMASKLOGFLAG + "执行Hive脱敏任务失败" + ",异常信息:" + e.getMessage());
//            errorLogService.saveErrorLog(DateUtil.getNowTime(), loginfo, "脱敏执行引擎", loginfo, "高", "信息");
            // System.out.println("Hive脱敏执行任务中出现异常");
            e.printStackTrace();
            isSuccess = false;
        } finally {
            // 任务状态
            String taskstatus = Const.HIVE_TASK_EXECUTESTATE_EXECUTE_FAIL;
            String taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE;
            sdkOperationrecord.setObjectname(hiveTask.getTaskname());
            sdkOperationrecord.setOperation("Hive脱敏任务执行失败");
            if (isSuccess) {
                // 执行成功
                taskstatus = Const.HIVE_TASK_EXECUTESTATE_EXECUTE_SUCCESS;
                taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE;
                sdkOperationrecord.setObjectname(hiveTask.getTaskname());
                sdkOperationrecord.setOperation("Hive脱敏任务执行成功");
            }
            endTime = DateUtil.getNowTime();
            // 执行总时间
            totalTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(startTime, endTime));
            // 更新时间
            String updateTime = DateUtil.getNowTime();
            // 更新Hive任务结果表
            HiveTaskResultDto hiveTaskResultDto = hiveTaskResultService.findById(resultid);
            hiveTaskResultDto.setJobstatus(taskstatus);
            hiveTaskResultDto.setMaskstrategystr(strFieldAlgoParam);
            hiveTaskResultDto.setJobstarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
            hiveTaskResultDto.setJobendtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
            hiveTaskResultDto.setJobtotaltime(totalTime);
            hiveTaskResultDto.setUpdatetime(updateTime);
            hiveTaskResultDto.setSparefield1(sparefield1);
            hiveTaskResultDto.setSparefield2(sparefield2);

            //插入SDK操作记录
            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
            sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_HIVE);
            sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
            sdkOperationrecordRepository.save(sdkOperationrecord);

            boolean updateSuccess = true;
            try {
                hiveTaskResultService.update(hiveTaskResultMapper.toEntity(hiveTaskResultDto));
                MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
                maskTaskresultrecords.setTaskname(hiveTask.getTaskname());
                maskTaskresultrecords.setTasktype(Const.MASK_TASK_HIVE);
                maskTaskresultrecords.setTaskstatus(taskMessage);
                maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
                maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
                maskTaskresultrecordsService.create(maskTaskresultrecords);
            } catch (Exception e) {
                updateSuccess = false;
            }
            // System.out.println("更新Hive任务结果表是否成功:" + isSuccess);
            log.info(Const.STRMASKLOGFLAG + "更新Hive任务结果表是否成功:" + updateSuccess);

            if(Const.TASK_EXECUTESTATE_EXECUTE_FAIL.equals(taskstatus)){
                //将执行HIVE脱敏任务失败告警推送至syslog
                Datasource datasource = datasourceService.findByDataSourceId(Long.valueOf(hiveTask.getSourceid()));

                    DmAlarmdisposal dmAlarmdisposal = new DmAlarmdisposal();
                    // TODO 事件详情定义
                    String str = "数据脱敏：任务ID：{},数据源名称：{},表名：{},库名：{},执行时间：{}，Hive数据脱敏任务执行失败";
                    String eventDetails = cn.hutool.core.util.StrUtil.format(str, hiveTaskResultDto.getTaskname(),datasource.getSrcname(),hiveTaskResultDto.getTablename(),hiveTaskResultDto.getDbname(),startTime);
                    dmAlarmdisposal.setCircumstantiality(eventDetails); //事件详情
                    dmAlarmdisposal.setChecktime(cn.hutool.core.date.DateUtil.now());
                    dmAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED); //处置状态
                    dmAlarmdisposal.setEventrule(Const.DICT_HIVE_EXECUTION_TASK_ERROR);//规则
                    String url = datasource.getSrcurl();
                    String[] hostNameAndPort1 = url.split("//");
                    String[] ips = hostNameAndPort1[1].split(":");
                    String ip = ips[0];
                    String port = ips[1];
                    dmAlarmdisposal.setSourceip(ip);//源ip
                    dmAlarmdisposal.setSourceport(port);//源端口
                    dmAlarmdisposal.setAccount(hiveTaskResultDto.getUsername());//事件相关用户名
                    dmAlarmdisposal.setReservefield2(datasource.getSparefield3());
                    dmAlarmdisposal.setReservefield3("2");
                    String outUrl = hiveTaskResultDto.getDataoutputpath();
                    if(com.wzsec.utils.StringUtils.isNotEmpty(outUrl)){
                        String hostNameAndPort = com.wzsec.utils.StringUtils.substringBetween(outUrl,"//","/");
                        String[] outIps = hostNameAndPort.split(":");
                        String outIp = outIps[0];
                        String outPort = outIps[1];
                        dmAlarmdisposal.setDestinationip(outIp);//目标IP
                        dmAlarmdisposal.setDestinationport(outPort);//目标端口
                    }else {
                        dmAlarmdisposal.setDestinationip(" ");//目标IP
                        dmAlarmdisposal.setDestinationport(" ");//目标端口
                    }
                    dmAlarmdisposalService.create(dmAlarmdisposal);
                    //告警推送syslog
                    new MonitorRiskAlarmData().sendDmExample(dmAlarmdisposal);
            }

            if (null != id) {
                // 更新Hive任务配置表
                hiveTask.setStatus(taskstatus);
                try {
                    hiveTaskConfigService.update(hiveTaskConfigMapper.toEntity(hiveTask));
                    updateSuccess = true;
                } catch (Exception e) {
                    updateSuccess = false;
                }
                // System.out.println("更新Hive任务配置表是否成功:" + isSuccess);
                log.info(Const.STRMASKLOGFLAG + "更新Hive任务配置表是否成功:" + updateSuccess);
            }

            //TODO 将所使用的引擎，任务负载数-1
            if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
                engineServerRepository.reduceKingbaseCountByIpPort(eip, eport);
            }else {
                engineServerRepository.reduceCountByIpPort(eip, eport);
            }

            log.info(String.format(Const.STRMASKLOGFLAG + "Hive脱敏任务结束时间:%s,任务用时:%s秒", endTime, totalTime));
            log.info(Const.STRMASKLOGFLAG + "结束执行Hive脱敏任务,任务Id:" + id);
        }
    }


    /**
     * @Description:执行Hive数据脱敏,在Hive客户端执行nohup hive --service hiveserver2 &
     * http://blog.csdn.net/gamer_gyt/article/details/********
     * <AUTHOR> by xiongpf
     * @date 2019-01-03
     */
    public static boolean doHiveDataMasking(String hiveUrl, String hiveDirverName, String hiveUser, String hivePassword,
                                            String account, String queueName, String dataBase, String tableName,
                                            String partitionInfos, String strExtractFieldNames, String strFieldAlgoParam, int lineCount,
                                            String outHdfsDir, String compressFormat, String dataSplit, String hiveTaskName, String outputtype,
                                            String outputdbname, HashMap<String, String> algofuncnameAndFuncnamepathMap, String jarpath,List<String> maskEndTableName) {
        log.info(String.format(
                Const.STRMASKLOGFLAG
                        + "传递Hive脱敏任务参数信息:hiveUrl:%s, hiveDirverName:%s, hiveUser:%s, hivePassword:%s, account:%s, queueName:%s, dataBase:%s, tableName:%s, partitionInfos:%s, strExtractFieldNames:%s,"
                        + " strFieldAlgoParam:%s, lineCount:%s, outHdfsDir:%s, compressFormat:%s, dataSplit:%s, outputtype:%s, outputdbname:%s",
                hiveUrl, hiveDirverName, hiveUser, hivePassword, account, queueName, dataBase, tableName,
                partitionInfos, strExtractFieldNames, strFieldAlgoParam, lineCount, outHdfsDir, compressFormat,
                dataSplit, outputtype, outputdbname));
        boolean flag = true;
        Connection conn = null;
        HiveStatement stmt = null;
        try {
            /*
             * String hiveUser = ConstEngine.sddEngineConfs.get("hiveUser");
             * String hivePassword = "";
             */
            log.info(Const.STRMASKLOGFLAG + "开始连接Hive数据源");

            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            // conn = HiveUtil.getTestConn(hiveUrl, hiveDirverName, hiveUser, hivePassword, queueName, dataBase);// 非kerberos认证
            // conn = HiveUtil.getConn_v1(hiveDirverName, hiveUser, hivePassword,hiveUrl,dataBase,queueName); // kerberos认证
            conn = HiveUtil.getConn(hiveDirverName,hiveUser,hivePassword,hiveUrl,dataBase);
            stmt = HiveUtil.getStmt(conn);
            if (null == stmt) {
                log.info(Const.STRMASKLOGFLAG + "Hive脱敏获取stmt对象为空");
                msg = "连接hive环境失败";
                return false;
            }
            log.info(Const.STRMASKLOGFLAG + "连接Hive数据源成功");
            // log.info("调用HiveUtil.getConn()后时间:"+System.currentTimeMillis());

            // 1.使用队列，这样配置不生效
            if (null != queueName && !queueName.equals("")) {
                String strSetQueue = "SET mapreduce.job.queuename=" + queueName;
                stmt.execute(strSetQueue);
            }

            // 2、使用Hive库
            if (null != dataBase && !dataBase.equals("")) {
                String strUseBase = "use " + dataBase;
                stmt.execute(strUseBase);
            }
            log.info(Const.STRMASKLOGFLAG + "Hive脱敏使用Hive库:" + dataBase);
            // 3.判断是否是脱敏，还是只抽取
            if (strFieldAlgoParam != null && !strFieldAlgoParam.equals("")) {

                // 获取依赖包local路径和要上传的hdfs路径
                /*
                 * String hiveMaskLibLocalPath =
                 * ConstEngine.sddEngineConfs.get("hiveMaskLibLocalPath").trim
                 * (); String hiveMaskLibHdfsDir =
                 * ConstEngine.sddEngineConfs.get("hiveMaskLibHdfsDir").trim()
                 * ; if(!"".equals(hiveMaskLibLocalPath) &&
                 * !"".equals(hiveMaskLibHdfsDir)){ //创建hdfs依赖包目录
                 * CmdUtil.exeCmd("hadoop fs -mkdir -p "+hiveMaskLibHdfsDir);
                 * //上次依赖包到hdfs目录上
                 * CmdUtil.exeCmd("hadoop fs -put "+hiveMaskLibLocalPath+" "
                 * +hiveMaskLibHdfsDir); }
                 */
                // 3、Hive环境中添加依赖包
				/*String hiveMaskLibPath = ConstEngine.sddEngineConfs.get("hiveMaskLibPath");
				if (null != hiveMaskLibPath && !hiveMaskLibPath.equals("")) {
					String[] hivemaskjararrs = hiveMaskLibPath.split(";");
					for (String hivemaskjar : hivemaskjararrs) {
						String strAddJar = "add jar " + hivemaskjar;
						stmt.execute(strAddJar);
						log.info(Const.STRMASKLOGFLAG + "Hive脱敏添加jar包路径:" + strAddJar);
					}
				}*/
                if (!"".equals(jarpath)) {
                    String strAddJarStr = "add jar " + jarpath;
                    stmt.execute(strAddJarStr);
                    log.info(Const.STRMASKLOGFLAG + "Hive脱敏添加页面jar包路径:" + strAddJarStr);
                }
				/*if (jarpathlist != null && jarpathlist.size() > 0) {
					for (String jarpathstr : jarpathlist) {
						String strAddJar = "add jar " + jarpathstr;
						stmt.execute(strAddJar);
						log.info(Const.STRMASKLOGFLAG + "Hive脱敏添加算法包管理jar包路径:" + strAddJar);
					}
				}
				log.info(Const.STRMASKLOGFLAG + "strFieldAlgoParam信息:" + strFieldAlgoParam);*/

                //TODO 宁夏线上需要注释 SET hive.exec.mode.local.auto = true
                String authType = ConstEngine.sddEngineConfs.get("kb.auth.type");
                if (!authType.equalsIgnoreCase(Const.HIVE_PRODUCE_AUTH)){
                    stmt.execute("SET hive.exec.mode.local.auto = true");
                }

                // 4. Hive中创建临时函数
                String[] fieldAlgoInfoConfigArr = strFieldAlgoParam.split(";");
                HashMap<String, String> funcClassMap = algofuncnameAndFuncnamepathMap;// getHiveFuncClassMap();
                // mdn|MD5SALT$#9876543210123456
                for (int i = 0; i < fieldAlgoInfoConfigArr.length; i++) {
                    String maskAlgo = "";
                    if (fieldAlgoInfoConfigArr[i].contains("|") && fieldAlgoInfoConfigArr[i].contains("$")) {
                        maskAlgo = fieldAlgoInfoConfigArr[i].substring(fieldAlgoInfoConfigArr[i].indexOf("|") + 1,
                                fieldAlgoInfoConfigArr[i].indexOf("$"));
                    } else {
                        log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务字段脱敏策略配置为:" + fieldAlgoInfoConfigArr[i]);
                    }

                    if (maskAlgo != null && !maskAlgo.equals("")) {
                        String funcName = maskAlgo.toLowerCase();
                        String classPath = funcClassMap.get(funcName);
                        log.info(Const.STRMASKLOGFLAG + "funcName:" + funcName + "====" + "classPath:" + classPath);
                        System.out.println("funcName:" + funcName + "====" + "classPath:" + classPath);
                        String createFuncSql = "create temporary function " + funcName + " as '" + classPath + "'";
                        log.info(Const.STRMASKLOGFLAG + "创建Hive临时函数:" + createFuncSql);
                        stmt.execute(createFuncSql);
                    }
                }
            } else if (strFieldAlgoParam.equals("")) {
                log.info(String.format(Const.STRMASKLOGFLAG + "Hive脱敏任务strFieldAlgoParam为%s,只抽取不脱敏字段",
                        strFieldAlgoParam));
            }
            // Hive中对字段脱敏并输出到HDFS上
            String lastOutDir = "";
            /*
             * if (outHdfsDir.trim().equals("")) { // 不配置输出路径，默认去生成表 lastOutDir
             * = ""; } else { // 配置输出路径 if (outHdfsDir.endsWith("/")) {
             * lastOutDir = outHdfsDir + tableName + "_" +
             * StrUtils.getNowTimeString("yyyyMMddHHmmss"); } else { lastOutDir
             * = outHdfsDir + "/" + tableName + "_" +
             * StrUtils.getNowTimeString("yyyyMMddHHmmss"); } }
             */
            if (outHdfsDir == null || "".equals(outHdfsDir)) { // 不配置输出路径，默认去生成表
                lastOutDir = "";
            } else { // 配置输出路径
                if (outHdfsDir.endsWith(Const.JOIN_SLASH)) {
                    lastOutDir = outHdfsDir.trim() + hiveTaskName + Const.JOIN_SLASH + DateUtil.formatDate(new Date()) + Const.JOIN_SLASH + tableName;
                } else {
                    lastOutDir = outHdfsDir.trim() + Const.JOIN_SLASH + hiveTaskName + Const.JOIN_SLASH + DateUtil.formatDate(new Date()) + Const.JOIN_SLASH + tableName;
                }
            }
            String origPartitionInfoStr = "";
            String newPartitionInfoSQLStr = "";
            // prov_id=811/month_id=201707/day_id=20170717;prov_id=811/month_id=201707/day_id=20170717
            if (null != partitionInfos && !"".equals(partitionInfos)) {
                //5. 解析分区信息
                if (partitionInfos.contains(";")) {
                    String[] partitionInfoArr = partitionInfos.split(";");
                    for (String partitionInfo : partitionInfoArr) {
                        if (StringUtils.isNotEmpty(partitionInfo)) {
                            origPartitionInfoStr = partitionInfo; // 原始格式的分区信息
                            log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务origPartitionInfoStr: " + origPartitionInfoStr);

                            if (partitionInfo.startsWith(Const.JOIN_SLASH)) {
                                partitionInfo = partitionInfo.substring(1, partitionInfo.length());
                            }
                            if (partitionInfo.endsWith(Const.JOIN_SLASH)) {
                                partitionInfo = partitionInfo.substring(0, partitionInfo.length());
                            }

                            // 将"prov_id=811/day_id=20171212/net_type=3g"转变为
                            // prov_id='811' and day_id='20171212' and net_type='3g'
                            // Hive 3g 数字字母组成的必须使用引号括起来
                            String[] strPartitionInfoArr = partitionInfo.split(Const.JOIN_SLASH); // 拆开
                            String[] newStrPartitionInfoArr = new String[strPartitionInfoArr.length]; // 放重组后分区信息
                            for (int i = 0; i < strPartitionInfoArr.length; i++) {
                                if (strPartitionInfoArr[i].contains(Const.JOIN_EQUALS_SIGN)) {
                                    String[] strKeyValueArr = strPartitionInfoArr[i].split(Const.JOIN_EQUALS_SIGN);
                                    newStrPartitionInfoArr[i] = strKeyValueArr[0] + Const.JOIN_EQUALS_SIGN
                                            + Const.JOIN_ONESIDE_SINGLEQUOTE + strKeyValueArr[1]
                                            + Const.JOIN_ONESIDE_SINGLEQUOTE;
                                }
                            }
                            newPartitionInfoSQLStr = org.apache.commons.lang.StringUtils.join(newStrPartitionInfoArr,
                                    Const.JOIN_BLANK + Const.JOIN_AND + Const.JOIN_BLANK);
                            log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务newPartitionInfoSQLStr: " + newPartitionInfoSQLStr);
                        }

                        /*
                         * String provId =
                         * BdmsApiController.getProviceId(strPartitionInfoArr);
                         * String monthId =
                         * BdmsApiController.getMonthId(strPartitionInfoArr); String
                         * dayId = BdmsApiController.getDayId(strPartitionInfoArr);
                         * String tag =
                         * BdmsApiController.getTag(strPartitionInfoArr);
                         */

                        // 6. 压缩格式
                        if (compressFormat != null && !compressFormat.equals("")) {
                            // 设置压缩格式
                            if (Const.LZO.equals(compressFormat)) {
                                String outputcompressFormatOpen = "set hive.exec.compress.output=true";
                                stmt.execute(outputcompressFormatOpen); // 设置开启压缩

                                String outputcompressFormat = "set mapreduce.output.fileoutputformat.compress.codec=com.hadoop.compression.lzo.LzopCodec";
                                stmt.execute(outputcompressFormat); // 设置压缩格式
                            }
                            if (Const.GZ.equals(compressFormat)) {
                                String outputcompressFormatOpen = "set hive.exec.compress.output=true";
                                stmt.execute(outputcompressFormatOpen); // 设置开启压缩
                                String outputcompressFormat = "set mapreduce.output.fileoutputformat.compress.codec=org.apache.hadoop.io.compress.GzipCodec";
                                stmt.execute(outputcompressFormat); // 设置压缩格式
                            }
                            // 其它格式 textfile 不做处理
                            log.info(Const.STRMASKLOGFLAG + "Hive脱敏设置压缩格式:" + compressFormat);
                        }

                        // 7. 设置Hive任务名，格式:dm-060320190330002_01-20190330083025
                        /*
                            TODO 宁夏集群环境 不允许设置
                            String setHiveTaskName = "set mapred.job.name=" + hiveTaskName;
                            stmt.execute(setHiveTaskName);
                            log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务名:" + setHiveTaskName);
                         */
                        String setHiveTaskName = "set mapred.job.name=" + hiveTaskName;
                        stmt.execute(setHiveTaskName);
                        log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务名:" + setHiveTaskName);

                        // 8. 执行Hive脱敏
                        String strDoHiveMask = "";
                        if (strFieldAlgoParam != null && !strFieldAlgoParam.equals("")) {
                            strDoHiveMask = getDoHiveMaskSQLStr(tableName, origPartitionInfoStr, newPartitionInfoSQLStr,
                                    strExtractFieldNames, strFieldAlgoParam, lineCount, lastOutDir, dataSplit, outputtype,
                                    outputdbname,maskEndTableName);
                        } else if (strFieldAlgoParam.equals("")) {
                            strDoHiveMask = getDoHiveExtractSQLStr(tableName, origPartitionInfoStr, newPartitionInfoSQLStr,
                                    strExtractFieldNames, lineCount, lastOutDir, dataSplit, outputtype, outputdbname,maskEndTableName);
                        }
                        System.out.println("执行Hive脱敏语句:" + strDoHiveMask);
                        log.info(Const.STRMASKLOGFLAG + "执行Hive脱敏语句:" + strDoHiveMask);
                        log.info("执行Hive脱敏语句 时间:" + System.currentTimeMillis());
                        stmt.execute(strDoHiveMask); // 执行Hive脱敏反馈结果
                        if (outputtype.equals(Const.HIVE_FILE)){
                            saveMask(strDoHiveMask,stmt,maskEndTableName);
                        }

                        log.info(Const.STRMASKLOGFLAG + "Hive对表" + tableName + "分区" + partitionInfo + "脱敏执行成功");
                        log.info("完成执行Hive脱敏语句 时间:" + System.currentTimeMillis());
                    }
                }else {
                    log.info("分区信息输入格式有误");
                    throw new RuntimeException("分区信息输入格式有误");
                }
            } else {
                // 6. 压缩格式
                if (compressFormat != null && !compressFormat.equals("")) {
                    // 设置压缩格式
                    if (Const.LZO.equals(compressFormat)) {
                        String outputcompressFormatOpen = "set hive.exec.compress.output=true";
                        stmt.execute(outputcompressFormatOpen); // 设置开启压缩

                        String outputcompressFormat = "set mapreduce.output.fileoutputformat.compress.codec=com.hadoop.compression.lzo.LzopCodec";
                        stmt.execute(outputcompressFormat); // 设置压缩格式
                    }
                    if (Const.GZ.equals(compressFormat)) {
                        String outputcompressFormatOpen = "set hive.exec.compress.output=true";
                        stmt.execute(outputcompressFormatOpen); // 设置开启压缩
                        String outputcompressFormat = "set mapreduce.output.fileoutputformat.compress.codec=org.apache.hadoop.io.compress.GzipCodec";
                        stmt.execute(outputcompressFormat); // 设置压缩格式
                    }
                    // 其它格式 textfile 不做处理
                    log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务设置压缩格式:" + compressFormat);
                }

                // 7. 设置Hive任务名，格式:dm-060320190330002_01-20190330083025
                String setHiveTaskName = "set mapred.job.name=" + hiveTaskName;
                stmt.execute(setHiveTaskName);
                log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务名:" + setHiveTaskName);

                // 8. 执行Hive脱敏
                String strDoHiveMask = "";
                if (strFieldAlgoParam != null && !strFieldAlgoParam.equals("")) {
                    strDoHiveMask = getDoHiveMaskSQLStr(tableName, origPartitionInfoStr, newPartitionInfoSQLStr,
                            strExtractFieldNames, strFieldAlgoParam, lineCount, lastOutDir, dataSplit, outputtype,
                            outputdbname,maskEndTableName);
                } else if (strFieldAlgoParam.equals("")) {
                    strDoHiveMask = getDoHiveExtractSQLStr(tableName, origPartitionInfoStr, newPartitionInfoSQLStr,
                            strExtractFieldNames, lineCount, lastOutDir, dataSplit, outputtype, outputdbname,maskEndTableName);
                }
                System.out.println("执行Hive脱敏语句:" + strDoHiveMask);
                log.info(Const.STRMASKLOGFLAG + "执行Hive脱敏语句:" + strDoHiveMask);
                log.info("执行Hive脱敏语句 时间:" + System.currentTimeMillis());
                System.err.println(strDoHiveMask);
                stmt.execute(strDoHiveMask); // 执行Hive脱敏反馈结果
                if (outputtype.equals(Const.HIVE_FILE)){
                    saveMask(strDoHiveMask,stmt,maskEndTableName);
                }

                log.info(Const.STRMASKLOGFLAG + "Hive对表" + tableName + "脱敏执行成功");
                log.info("完成执行Hive脱敏语句 时间:" + System.currentTimeMillis());
            }

        } catch (Exception e) {
            e.printStackTrace();
            flag = false;
            log.info(Const.STRMASKLOGFLAG + "Hive对表" + tableName + "脱敏执行失败,错误信息:" + e.getMessage());
            msg = "Hive对表" + tableName + "脱敏执行失败,错误信息:" + e.getMessage();
        } finally {
            HiveUtil.closeStmt(stmt);
            HiveUtil.closeConn(conn);
        }

        return flag;
    }


    /**
     * @Description: 获取Hive脱敏SQL字符串
     * <AUTHOR>
     * @date 2018年5月23日
     */
    public static String getDoHiveMaskSQLStr(String tableName, String origPartitionInfoStr,
                                             String newPartitionInfoSQLStr, String strExtractFieldNames, String strFieldAlgoParam, int lineCount,
                                             String lastOutDir, String dataSplit, String outputtype, String outputdbname,List<String> maskEndTableName) {
        StringBuilder maskStrBuilder = new StringBuilder();
        log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务lastOutDir:" + lastOutDir + Const.JOIN_SLASH + origPartitionInfoStr);
        if (!"1".equals(outputtype)) {
            maskStrBuilder.append("insert overwrite directory "); // 写入输出目录
            // 分区路径
            /*
             * StringBuilder pathStrBuilder = new StringBuilder(); if (provId !=
             * null && !provId.isEmpty()) { pathStrBuilder.append(Const.provId +
             * "=" + provId + "/"); } if (monthId != null && !monthId.isEmpty())
             * { pathStrBuilder.append(Const.monthId + "=" + monthId + "/"); }
             * if (dayId != null && !dayId.isEmpty()) {
             * pathStrBuilder.append(Const.dayId + "=" + dayId + "/"); } if (tag
             * != null && !tag.isEmpty()) { pathStrBuilder.append(Const.tag +
             * "=" + tag + "/"); } String pathStr = pathStrBuilder.toString();
             */
            String allPathStr = lastOutDir + Const.JOIN_SLASH + origPartitionInfoStr;
            // 目录/批次号/分区号/数据
            maskStrBuilder.append("'" + allPathStr + "'");

            // 处理转义分隔符问题
            String strDataSplit = "";
            if (dataSplit.trim().equals("\u0005") || dataSplit.trim().equals("\\u0005")) {
                strDataSplit = "'\\u0005'";
            } else if (dataSplit.trim().equals("\t") || dataSplit.trim().equals("\\t")) {
                strDataSplit = "'\\t'";
            } else if (dataSplit.trim().equals(",")) {
                strDataSplit = "','";
            } else if (dataSplit.trim().equals("|")) {
                strDataSplit = "'\\|'";
            } else {
                strDataSplit = "'\\001'"; // 采用Hive默认分割符
            }

            maskStrBuilder.append(" row format delimited fields terminated by " + strDataSplit);

        } else {
            // 存放到创建的临时表里面
            String tableMaskName = tableName + "_" + ConstEngine.sddEngineConfs.get("hiveoutputtablenameprefix") + "_"
                    + DateUtil.formatDate(new Date()) ;
            maskEndTableName.add(tableMaskName);
            if (null == outputdbname || "".equals(outputdbname)) {
                maskStrBuilder.append("create table " + tableMaskName + " as");
            } else {
                maskStrBuilder.append("create table " + outputdbname + "." + tableMaskName + " as");
            }
        }

        maskStrBuilder.append(" select ");
        // 解析抽取字段
        List<String> extractFieldNamesList = new ArrayList<String>();
        if (strExtractFieldNames.contains(",")) {
            String[] extractFieldNamesArr = strExtractFieldNames.split(",");
            for (String strFieldName : extractFieldNamesArr) {
                extractFieldNamesList.add(strFieldName);
            }
        } else {
            extractFieldNamesList.add(strExtractFieldNames);
        }

        // 需要最终获取的字段、脱敏算法、脱敏参数
        // mdn|MD5SALT$9876543210123456;other_party|PHONE_ENC$138573453edsa678
        String[] fieldAlgoParamArr = strFieldAlgoParam.split(";");

        HashMap<String, String> maskFieldAlgoConfigMap = new HashMap<String, String>();
        for (int i = 0; i < fieldAlgoParamArr.length; i++) {
            String strField = fieldAlgoParamArr[i].substring(0, fieldAlgoParamArr[i].indexOf("|"));
            String strAlgoConfig = fieldAlgoParamArr[i].substring(fieldAlgoParamArr[i].indexOf("|") + 1,
                    fieldAlgoParamArr[i].length());
            maskFieldAlgoConfigMap.put(strField, strAlgoConfig);
        }

        String[] resultFieldsArr = new String[extractFieldNamesList.size()];
        for (int i = 0; i < extractFieldNamesList.size(); i++) {
            String resultFieldName = extractFieldNamesList.get(i);
            if (maskFieldAlgoConfigMap.containsKey(resultFieldName)) {
                resultFieldsArr[i] = resultFieldName + "|" + maskFieldAlgoConfigMap.get(resultFieldName);
            } else {
                resultFieldsArr[i] = resultFieldName;
            }
        }

        for (int i = 0; i < resultFieldsArr.length; i++) {
            String strField = "";
            String strAlgo = "";
            String strParam = "";
            if (resultFieldsArr[i].contains("|")) {
                strField = resultFieldsArr[i].substring(0, resultFieldsArr[i].indexOf("|"));
                strAlgo = resultFieldsArr[i]
                        .substring(resultFieldsArr[i].indexOf("|") + 1, resultFieldsArr[i].indexOf("$")).toLowerCase();
                strParam = resultFieldsArr[i].substring(resultFieldsArr[i].indexOf("$") + 1);
            } else {
                strField = resultFieldsArr[i];
                strAlgo = "";
                strParam = "";
            }

            if (strAlgo.equals("")) { // 没有脱敏算法，表示是抽取字段
                maskStrBuilder.append("`" + strField + "`");
                maskStrBuilder.append(", ");
            } else { // 否则是需要加密脱敏
                String alias = "enc_" + strField;
                if (strParam.equals("")) {
                    maskStrBuilder.append(strAlgo + "(`" + strField + "`)" + " as " + alias);
                    maskStrBuilder.append(", ");
                } else if (strParam.contains(",")) {
                    String[] paramArr = strParam.split(",");
                    if (paramArr.length == 2) { // 算法2个参数
                        String maskFieldMethod = strAlgo + "(`" + strField + "`," + "\'" + paramArr[0] + "\'" + "," + "\'"
                                + paramArr[1] + "\'" + ")" + " as " + alias;
                        maskStrBuilder.append(maskFieldMethod);
                        maskStrBuilder.append(", ");
                    } else if (paramArr.length == 3) { // 算法3个参数
                        String maskFieldMethod = strAlgo + "(`" + strField + "`," + "\'" + paramArr[0] + "\'" + "," + "\'"
                                + paramArr[1] + "\'" + "," + "\'" + paramArr[2] + "\'" + ")" + " as " + alias;
                        maskStrBuilder.append(maskFieldMethod);
                        maskStrBuilder.append(", ");
                    }
                } else {
                    maskStrBuilder.append(strAlgo + "(`" + strField + "`,'" + strParam + "')" + " as " + alias);
                    maskStrBuilder.append(", ");
                }
            }
        }

        String strFrontMaskStrBuilder = maskStrBuilder.toString();
        if (strFrontMaskStrBuilder.endsWith(", ")) {
            strFrontMaskStrBuilder = strFrontMaskStrBuilder.substring(0, strFrontMaskStrBuilder.length() - 2);
        }

        StringBuilder fromMaskStrBuilder = new StringBuilder();
        fromMaskStrBuilder.append(" from " + tableName);
        fromMaskStrBuilder.append(" where 1=1");
        /*
         * if (provId != null && !provId.equals("")) {
         * fromMaskStrBuilder.append(" and prov_id='" + provId + "'"); } if
         * (monthId != null && !monthId.equals("")) {
         * fromMaskStrBuilder.append(" and month_id='" + monthId + "'"); } if
         * (dayId != null && !dayId.equals("")) {
         * fromMaskStrBuilder.append(" and day_id='" + dayId + "'"); } if (tag
         * != null && !tag.equals("")) { fromMaskStrBuilder.append(" and tag='"
         * + tag + "'"); }
         */
        if (!"".equals(newPartitionInfoSQLStr)) {
            fromMaskStrBuilder.append(" and " + newPartitionInfoSQLStr);
        }
        if (lineCount != 0) {
            fromMaskStrBuilder.append(" limit " + lineCount + "");
        }

        return strFrontMaskStrBuilder + fromMaskStrBuilder.toString();
    }

    /**
     * @Description: 获取Hive抽取字段SQL字符串
     * <AUTHOR>
     * @date 2018年9月19日
     */
    public static String getDoHiveExtractSQLStr(String tableName, String origPartitionInfoStr,
                                                String newPartitionInfoSQLStr, String strExtractFieldNames, int lineCount, String lastOutDir,
                                                String dataSplit, String outputtype, String outputdbname,List<String> maskEndTableName) {
        StringBuilder extractStrBuilder = new StringBuilder();
        log.info(Const.STRMASKLOGFLAG + "Hive脱敏任务lastOutDir:" + lastOutDir + Const.JOIN_SLASH + origPartitionInfoStr);
        if ("2".equals(outputtype)) {
            extractStrBuilder.append("insert overwrite directory "); // 写入输出目录
            // 分区路径
            /*
             * StringBuilder pathStrBuilder = new StringBuilder(); if (provId !=
             * null && !provId.isEmpty()) { pathStrBuilder.append(Const.provId +
             * "=" + provId + "/"); } if (monthId != null && !monthId.isEmpty())
             * { pathStrBuilder.append(Const.monthId + "=" + monthId + "/"); }
             * if (dayId != null && !dayId.isEmpty()) {
             * pathStrBuilder.append(Const.dayId + "=" + dayId + "/"); } if (tag
             * != null && !tag.isEmpty()) { pathStrBuilder.append(Const.tag +
             * "=" + tag + "/"); } String pathStr = pathStrBuilder.toString();
             */

            String allPathStr = lastOutDir + Const.JOIN_SLASH + origPartitionInfoStr;
            // 目录/批次号/分区号/数据
            extractStrBuilder.append("'" + allPathStr + "'");

            // 处理转义分隔符问题
            String strDataSplit = "";
            if (dataSplit.trim().equals("\u0005") || dataSplit.trim().equals("\\u0005")) {
                strDataSplit = "'\\u0005'";
            } else if (dataSplit.trim().equals("\t") || dataSplit.trim().equals("\\t")) {
                strDataSplit = "'\\t'";
            } else if (dataSplit.trim().equals(",")) {
                strDataSplit = "','";
            } else if (dataSplit.trim().equals("|")) {
                strDataSplit = "'\\|'";
            } else {
                strDataSplit = "'\\001'"; // 采用Hive默认分割符
            }

            extractStrBuilder.append(" row format delimited fields terminated by " + strDataSplit);

        } else {
            // 存放到创建的临时表里面
            String tableMaskName = tableName + "_" + ConstEngine.sddEngineConfs.get("hiveoutputtablenameprefix") + "_"
                    + DateUtil.formatDate(new Date()) ;
            maskEndTableName.add(tableMaskName);
            if (null == outputdbname || "".equals(outputdbname)) {
                extractStrBuilder.append("create table " + tableMaskName + " as");
            } else {
                extractStrBuilder.append("create table " + outputdbname + "." + tableMaskName + " as");
            }
        }

        extractStrBuilder.append(" select ");
        // 解析抽取字段
        List<String> extractFieldNamesList = new ArrayList<String>();
        if (strExtractFieldNames.contains(",")) {
            String[] extractFieldNamesArr = strExtractFieldNames.split(",");
            for (String strFieldName : extractFieldNamesArr) {
                extractFieldNamesList.add(strFieldName);
            }
        } else {
            extractFieldNamesList.add(strExtractFieldNames);
        }

        for (int i = 0; i < extractFieldNamesList.size(); i++) {
            extractStrBuilder.append(extractFieldNamesList.get(i));
            extractStrBuilder.append(", ");
        }

        String strFrontMaskStrBuilder = extractStrBuilder.toString();
        if (strFrontMaskStrBuilder.endsWith(", ")) {
            strFrontMaskStrBuilder = strFrontMaskStrBuilder.substring(0, strFrontMaskStrBuilder.length() - 2);
        }

        StringBuilder fromMaskStrBuilder = new StringBuilder();
        fromMaskStrBuilder.append(" from " + tableName);
        fromMaskStrBuilder.append(" where 1=1");

        /*
         * if (provId != null && !provId.equals("")) {
         * fromMaskStrBuilder.append(" and prov_id='" + provId + "'"); } if
         * (monthId != null && !monthId.equals("")) {
         * fromMaskStrBuilder.append(" and month_id='" + monthId + "'"); } if
         * (dayId != null && !dayId.equals("")) {
         * fromMaskStrBuilder.append(" and day_id='" + dayId + "'"); } if (tag
         * != null && !tag.equals("")) { fromMaskStrBuilder.append(" and tag='"
         * + tag + "'"); }
         */

        if (!"".equals(newPartitionInfoSQLStr)) {
            fromMaskStrBuilder.append(" and " + newPartitionInfoSQLStr);
        }

        if (lineCount != 0) {
            fromMaskStrBuilder.append(" limit " + lineCount + "");
        }

        return strFrontMaskStrBuilder + fromMaskStrBuilder.toString();
    }

    /***
     * 保存脱敏的hive文件数据
     * @param: strDoHiveMask
     * @param: stmt
     * @param: maskEndTableName
     * <AUTHOR>
     * @date: 2024/12/23 19:20
     * @return:
     */
    private static void saveMask(String strDoHiveMask,HiveStatement stmt,List<String> maskEndTableName) throws SQLException {
        String sql = extractSelectClause(strDoHiveMask);
        ResultSet rs = stmt.executeQuery(sql);
        String hiveDate = "";
        String hiveDateList = "";
        //处理结果
        int rowCount = 0;
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        // 打印查询结果
        while (rs.next() && rowCount < 5){
            for (int i = 1; i <= columnCount; i++) {
                Object columnValue = rs.getObject(i);
                String value = columnValue == null?"":String.valueOf(columnValue);
                if (org.apache.commons.lang.StringUtils.isEmpty(hiveDate)){
                    if (rowCount != 0){
                        hiveDate = "\n" + value;
                    }else {
                        hiveDate = value;
                    }
                }else {
                    hiveDate = hiveDate + ";" +value;
                }
            }
            hiveDateList = hiveDateList + hiveDate;
            hiveDate = "";
            rowCount++;
        }
        maskEndTableName.add(hiveDateList);
    }

    /***
     * 方法提取 SELECT 子句及后续内容
     * @param: sql
     * <AUTHOR>
     * @date: 2024/12/23 19:20
     * @return:
     */
    public static String extractSelectClause(String sql) {
        // 查找 SELECT 的起始位置
        int selectIndex = sql.toLowerCase().indexOf("select");
        if (selectIndex == -1) {
            return null; // 如果没有找到 select, 返回 null
        }
        // 从 SELECT 开始到字符串末尾提取内容
        return sql.substring(selectIndex).trim();
    }
}
