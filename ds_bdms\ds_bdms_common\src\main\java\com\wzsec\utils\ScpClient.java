package com.wzsec.utils;

import ch.ethz.ssh2.*;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <dependency>
 * <groupId>ch.ethz.ganymed</groupId>
 * <artifactId>ganymed-ssh2</artifactId>
 * <version>build210</version>
 * </dependency>
 * 基于SCP进行文件传输
 *
 * <AUTHOR>
 * @date 2020-05-22
 */
public class ScpClient {

    private static ScpClient instance = null;
    private String linuxSeparator = "/";
    private Connection conn = null;

    private ScpClient(String ip, Integer port, String username, String password) {
        this.conn = login(ip, port, username, password);
    }

    /**
     * 获取实例对象
     *
     * @param ip
     * @param port
     * @param username
     * @param password
     * @return
     * <AUTHOR>
     * @date 2020-05-22
     */
    public static ScpClient getInstance(String ip, Integer port, String username, String password) {
        return new ScpClient(ip, port, username, password);
    }

    /**
     * 登陆并返回连接
     *
     * @param ip
     * @param port
     * @param username
     * @param password
     * @return
     * <AUTHOR>
     * @date 2020-05-22
     */
    public static Connection login(String ip, Integer port, String username, String password) {
        //创建远程连接，默认连接端口为22，如果不使用默认，可以使用方法
        //new Connection(ip, port)创建对象
        Connection conn = null;
        try {
            if (port != null)
                conn = new Connection(ip, port);
            else
                conn = new Connection(ip);
            //连接远程服务器
            conn.connect();
            //使用用户名和密码登录
            if (!conn.authenticateWithPassword(username, password)) {
                conn = null;
            }
        } catch (IOException e) {
            conn = null;
            e.printStackTrace();
        }
        return conn;
    }

    /**
     * 上传本地文件到服务器目录下
     *
     * @param localFilePath 本地文件
     * @param remotePath    服务器目录
     * @param mode          模式
     * <AUTHOR> Kunxiang
     * @date 2020-05-22
     */
    public void uploadFile(String localFilePath, String remotePath, String mode) {
        try {
            SCPClient sc = new SCPClient(conn);
            //将本地文件放到远程服务器指定目录下，默认的文件模式为 0600，即 rw，
            //如要更改模式，可调用方法 put(fileName, remotePath, mode),模式须是4位数字且以0开头
            if ((mode == null) || (mode.length() == 0)) {
                mode = "0600";
            }
            File file = new File(localFilePath);
            sc.put(localFilePath, file.getName(), remotePath, mode);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 下载服务器文件到本地目录
     *
     * @param remoteFilePath 服务器文件
     * @param localFilePath  本地文件路径（路径不存在会自动创建）
     * <AUTHOR> Kunxiang
     * @date 2020-05-22
     */
    public boolean downloadFile(String remoteFilePath, String remoteFileName, String localFilePath) {
        boolean result = false;
        try {
            SCPClient sc = new SCPClient(conn);
            File newFile = new File(localFilePath + linuxSeparator + remoteFileName);
            if (!newFile.getParentFile().exists()) {
                newFile.getParentFile().mkdirs();
            }
            sc.get(remoteFilePath + linuxSeparator + remoteFileName, localFilePath);
            result = true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 在远程LINUX服务器上，在指定目录下，获取文件各个属性
     *
     * @param remotePath         远程主机的指定目录
     * @param remoteFilePathList 文件完整路径集合
     * <AUTHOR> Kunxiang
     * @date 2020-05-22
     */
    public void getAllFilePathByRemotePath(String remotePath, List<String> remoteFilePathList) {
        try {
            if (!remotePath.endsWith(linuxSeparator)) {
                remotePath += linuxSeparator;
            }
            SFTPv3Client sft = new SFTPv3Client(conn);
            List<SFTPv3DirectoryEntry> sList = sft.ls(remotePath);
            for (SFTPv3DirectoryEntry s : sList) {
                if (!".".equals(s.filename) && !"..".equals(s.filename)) {
                    String fileOrDirectoryPath = remotePath + s.filename;
                    if (s.attributes.isDirectory()) {
                        getAllFilePathByRemotePath(fileOrDirectoryPath, remoteFilePathList);
                    } else {
                        remoteFilePathList.add(fileOrDirectoryPath);
                    }
                }
            }
            sft.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 在远程LINUX服务器上，在指定目录下，删除指定文件
     *
     * @param remotePath 远程主机的指定目录
     * @param fileName   文件名
     * @return
     * <AUTHOR> Kunxiang
     * @date 2020-05-22
     */
    public void delFile(String remotePath, String fileName) {
        try {
            SFTPv3Client sft = new SFTPv3Client(conn);
            //获取远程目录下文件列表
            List<SFTPv3DirectoryEntry> sList = sft.ls(remotePath);
            for (SFTPv3DirectoryEntry s : sList) {
                //判断列表中文件是否与指定文件名相同
                if (s.filename.equals(fileName)) {
                    //rm()方法中，须是文件绝对路径+文件名称
                    sft.rm(remotePath + s.filename);
                }
                sft.close();
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }


    /**
     * 执行脚本
     *
     * @param cmds 要在linux上执行的指令
     * <AUTHOR> Kunxiang
     * @date 2020-05-22
     */
    public int exec(String cmds) {
        InputStream stdOut = null;
        InputStream stdErr = null;
        int ret = -1;
        try {
            //在connection中打开一个新的会话
            Session session = conn.openSession();
            //在远程服务器上执行linux指令
            session.execCommand(cmds);
            //指令执行结束后的输出
            stdOut = new StreamGobbler(session.getStdout());
            //指令执行结束后的错误
            stdErr = new StreamGobbler(session.getStderr());
            //等待指令执行结束
            session.waitForCondition(ChannelCondition.EXIT_STATUS, 2000);
            //取得指令执行结束后的状态
            ret = session.getExitStatus();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ret;
    }


    public static void main(String[] args) {

        long startTime = System.currentTimeMillis();
        ScpClient scpClient = ScpClient.getInstance("192.168.1.105", 22, "root", "wz001");
        long endTime = System.currentTimeMillis();
        System.out.println("连接登录用时：" + (endTime - startTime));

        startTime = System.currentTimeMillis();
        scpClient.uploadFile("D:\\CtyunWork\\scp\\put\\01.txt", "/root/zkx/2020-5-21", null);
        endTime = System.currentTimeMillis();
        System.out.println("上传用时：" + (endTime - startTime));


        startTime = System.currentTimeMillis();
        scpClient.downloadFile("/root/zkx/2020-5-21/", "01.txt", "D:\\CtyunWork\\scp\\get");
        endTime = System.currentTimeMillis();
        System.out.println("下载用时：" + (endTime - startTime));


        List<String> remoteFilePathList = new ArrayList<>();
        startTime = System.currentTimeMillis();
        scpClient.getAllFilePathByRemotePath("/root/zkx/2020-5-21", remoteFilePathList);
        endTime = System.currentTimeMillis();
        System.out.println("获取所有文件完整路径用时：" + (endTime - startTime));
        for (String remoteFilePath : remoteFilePathList) {
            System.out.println(remoteFilePath);
        }
    }
}