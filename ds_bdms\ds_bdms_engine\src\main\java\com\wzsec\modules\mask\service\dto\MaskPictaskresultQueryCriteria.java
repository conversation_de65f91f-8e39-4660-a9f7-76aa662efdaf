package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2022-04-20
*/
@Data
public class MaskPictaskresultQueryCriteria{

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String taskname;
    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<String> createtime;

    @Query(blurry = "taskname")
    private String blurry;
}
