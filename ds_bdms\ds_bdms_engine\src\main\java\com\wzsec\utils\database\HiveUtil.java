package com.wzsec.utils.database;

import cn.hutool.core.util.StrUtil;
import com.wzsec.utils.*;
import org.apache.hive.jdbc.HiveStatement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.concurrent.*;

/**
 * 创建hive表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description 通过java List<Map<String,
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2020年3月4日
 */
public class HiveUtil extends DatabaseUtil {

    private final static Logger log = LoggerFactory.getLogger(DatabaseUtil.class);

    // hive的jdbc驱动类
    private static String HiveDirverName = "org.apache.hive.jdbc.HiveDriver";

    private static final String JDBC_URL = ConstEngine.sddEngineConfs.get("hive.kerberos.jdbc.url"); // ********************************,hdp04.bonc.com:2181,hdp05.bonc.com:2181/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2
    private static final String KERBEROS_CONF_PATH = ConstEngine.sddEngineConfs.get("hive.kerberos.conf.path"); // /data/software/ddms/system/krb5.conf
    private static final String KERBEROS_KEYTAB = ConstEngine.sddEngineConfs.get("hive.kerberos.keytab"); // /data/software/ddms/system/hadoop.keytab

    private static final String KERBEROS_USER = ConstEngine.sddEngineConfs.get("hive.kerberos.jdbc.user"); // <EMAIL>
    private static final String KERBEROS_JAAS_FILE_PATH = ConstEngine.sddEngineConfs.get("hive.kerberos.jdbc.jaasFilePath"); // /jaas.conf


    /**
     * @Title: getInsert2TableSqlAndPatams
     * @Description: TODO
     * <AUTHOR>
     * @date 2020年3月4日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Description:经管平台传参方式建立连接
     * <AUTHOR> by xiongpf
     * @date 2018-01-03
     */
    public static Connection getTestConn(String hiveUrl, String hiveDirverName, String hiveUser, String hivePassword,
                                         String hiveQueuename, String hiveBase) {
        Connection conn = null;
        System.out.println(hiveDirverName);
        try {
            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            // 连接HiveServer2配置信息
            Class.forName(hiveDirverName);

            String hiveAllUrl = hiveUrl;
            if (hiveUrl.endsWith("/")) {
                hiveAllUrl = hiveUrl + hiveBase;
            } else if (!hiveUrl.contains(";")) {
                hiveAllUrl = hiveUrl + "/" + hiveBase;
            }
            log.info("hiveAllUrl信息:" + hiveAllUrl);

            conn = DriverManager.getConnection(hiveAllUrl);
            log.info("连接Hive Conn信息:" + conn.toString());

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.info("ClassNotFoundException:" + e.toString());
        } catch (SQLException e) {
            e.printStackTrace();
            log.info("SQLException:" + e.toString());
        }
        return conn;
    }

    /**
     * @Description:传参方式建立连接(105测试)
     * <AUTHOR> by dongs
     * @date 2019-07-22
     */
    public static Connection getTestConn(String hiveUser, String hivePassword, String hiveQueuename, String hiveBase) {
        Connection conn = null;
        try {
            // 先进行kerberos认证
            // AuthKrb5.authKrb5();
            // 连接HiveServer2配置信息
            Class.forName(ConstEngine.sddEngineConfs.get("hiveDirverName"));

            /*
             * Properties prop = new Properties(); prop.setProperty("user",
             * hiveUser); prop.setProperty("password", hivePassword);
             */
            // prop.setProperty("hiveconf:mapreduce.job.queuename",
            // hiveQueuename);

            // JDBC连接HiveServer2
            String hiveUrl = ConstEngine.sddEngineConfs.get("hiveUrl").trim();
            // String hiveUrlPrincipal = "principal=" +
            // ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim();

            String hiveAllUrl = "";
            if (hiveUrl.endsWith("/")) {
                hiveAllUrl = hiveUrl + hiveBase;
            } else {
                hiveAllUrl = hiveUrl + "/" + hiveBase;
            }
            log.info("hiveAllUrl信息:" + hiveAllUrl);

            conn = DriverManager.getConnection(hiveAllUrl);
            log.info("连接Hive Conn信息:" + conn.toString());

        } catch (Exception e) {
            log.info("连接Hive出现异常:" + e.toString());
            e.printStackTrace();
        } /*
         * catch (ClassNotFoundException e) { e.printStackTrace();
         * log.info("ClassNotFoundException:" + e.toString()); } catch
         * (SQLException e) { e.printStackTrace(); log.info("SQLException:"
         * + e.toString()); }
         */
        return conn;
    }


    /**
     * @Description:建立hive连接
     * <AUTHOR> by dongs
     * @date 2020-02-25
     */
    public static Connection getConn(String hiveDirverName, String hiveUser, String hivePassword, String hiveurl) {
        Connection conn = null;
        try {
            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            // 连接HiveServer2配置信息
            Class.forName(hiveDirverName);

            /*
             * Properties prop = new Properties(); prop.setProperty("user",
             * hiveUser); prop.setProperty("password", hivePassword);
             */
            // prop.setProperty("hiveconf:mapreduce.job.queuename",
            // hiveQueuename);
            // JDBC连接HiveServer2
            String hiveUrl = hiveurl;
            /*
             * String hiveUrlPrincipal = "principal=" +
             * ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim();
             *
             * String hiveAllUrl = ""; if (hiveUrl.endsWith("/")) { hiveAllUrl =
             * hiveUrl + hiveBase + ";" + hiveUrlPrincipal; } else { hiveAllUrl
             * = hiveUrl + "/" + hiveBase + ";" + hiveUrlPrincipal; }
             */
            log.info("hiveAllUrl信息:" + hiveUrl);

            conn = DriverManager.getConnection(hiveUrl);
            log.info("连接Hive Conn信息:" + conn.toString());

        } catch (Exception e) {
            log.info("连接Hive出现异常:" + e.toString());
            e.printStackTrace();
        } /*
         * catch (ClassNotFoundException e) { e.printStackTrace();
         * log.info("ClassNotFoundException:" + e.toString()); } catch
         * (SQLException e) { e.printStackTrace(); log.info("SQLException:"
         * + e.toString()); }
         */
        return conn;
    }


    /**
     * @Description:建立hive连接
     * <AUTHOR> by dongs
     * @date 2020-02-25
     */
    public static Connection getConn(String hiveDirverName, String hiveUser, String hivePassword, String hiveUrl, String hiveBase) {
        Connection conn = null;
        try {
            String queueName = ConstEngine.sddEngineConfs.get("hiveQueuename");
            log.info("hiveDirverName:" + hiveDirverName + " hiveUrl:" + hiveUrl + " hiveUser:" + hiveUser + " hivePassword:"
                    + hivePassword + " hiveBase:" + hiveBase + " queueName:" + queueName);
            System.out.println("hiveDirverName:" + hiveDirverName + " hiveUrl:" + hiveUrl + " hiveUser:" + hiveUser + " hivePassword:"
                    + hivePassword + " hiveBase:" + hiveBase + " queueName:" + queueName);
            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            // 连接HiveServer2配置信息
            Class.forName(hiveDirverName);

            /*Properties prop = new Properties();
            prop.setProperty("user", null == hiveUser ? "" : hiveUser);
            prop.setProperty("password", null == hivePassword ? "" : hivePassword);

            if (null != queueName && !"".equals(queueName)) {
                prop.setProperty("hiveconf:mapreduce.job.queuename", queueName);
            }
            // JDBC连接HiveServer2
            String hiveUrlPrincipal = ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim();
            if (null != hiveUrlPrincipal && !"".equals(hiveUrlPrincipal)) {
                hiveUrlPrincipal = ";principal=" + hiveUrlPrincipal;
            }

            String hiveAllUrl = hiveUrl;
            if (hiveUrl.endsWith("/")) {
                hiveAllUrl = hiveUrl + hiveBase + ";" + hiveUrlPrincipal;
            } else if (!hiveUrl.contains(";")) {
                hiveAllUrl = hiveUrl + "/" + hiveBase + ";" + hiveUrlPrincipal;
            }*/

            log.info("hiveAllUrl信息:" + hiveUrl);
            System.out.println("hiveAllUrl信息:" + hiveUrl);
            conn = DriverManager.getConnection(hiveUrl, hiveUser, hivePassword);
            log.info("连接Hive Conn信息:" + conn.toString());
        } catch (Exception e) {
            log.info("连接Hive出现异常:" + e.toString());
            e.printStackTrace();
        }
        return conn;
    }

    /**
     * @Description:经管平台传参方式建立连接
     * <AUTHOR> by xiongpf
     * @date 2018-01-03
     */
    public static Connection getConns(String hiveUrl, String hiveDirverName, String hiveUser, String hivePassword,
                                      String hiveQueuename, String hiveBase) {
        Connection conn = null;
        System.out.println(hiveDirverName);
        try {
            // 先进行kerberos认证
            /*
             * if(ConstEngine.sddEngineConfs.get("krb5Path").trim()!=null &&
             * !ConstEngine.sddEngineConfs.get("krb5Path").trim().equals("")){
             * AuthKrb5.authKrb5(); }
             */
            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            // 连接HiveServer2配置信息
            Class.forName(hiveDirverName);

            /*
             * Properties prop = new Properties(); prop.setProperty("user",
             * hiveUser); prop.setProperty("password", hivePassword);
             * if(!"".equals(hiveQueuename)){
             * prop.setProperty("hiveconf:mapreduce.job.queuename",
             * hiveQueuename); }
             */

            // JDBC连接HiveServer2
            // String hiveUrl =
            // ConstEngine.sddEngineConfs.get("hiveUrl").trim();
            String hiveUrlPrincipal = "";
            String hiveAllUrl = "";
            /*
             * if(ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim()!=
             * null &&
             * !ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim().
             * equals("")){ hiveUrlPrincipal = ";principal=" +
             * ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim(); } if
             * (hiveUrl.endsWith("/")) { hiveAllUrl = hiveUrl + hiveBase +
             * hiveUrlPrincipal; } else { hiveAllUrl = hiveUrl + "/" + hiveBase
             * + hiveUrlPrincipal; }
             */
            hiveAllUrl = hiveUrl + "/" + hiveBase;
            log.info("hiveAllUrl信息:" + hiveAllUrl);

            conn = DriverManager.getConnection(hiveAllUrl);
            log.info("连接Hive Conn信息:" + conn.toString());

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.info("ClassNotFoundException:" + e.toString());
        } catch (SQLException e) {
            e.printStackTrace();
            log.info("SQLException:" + e.toString());
        }
        return conn;
    }


    /**
     * @Description:经管平台传参方式建立连接
     * <AUTHOR> by xiongpf
     * @date 2018-01-03
     */
    public static Connection getConn(String hiveDirverName, String hiveUrl, String hiveUser, String hivePassword,
                                     String hiveQueuename, String hiveBase) {
        Connection conn = null;
        try {
            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            // 连接HiveServer2配置信息
            Class.forName(hiveDirverName);
            log.info("hiveDirverName:" + hiveDirverName + " hiveUrl:" + hiveUrl + " hiveUser:" + hiveUser
                    + " hivePassword:" + hivePassword + " hiveQueuename:" + hiveQueuename + " hiveBase:" + hiveBase);
            Properties prop = new Properties();
            prop.setProperty("user", hiveUser);
            prop.setProperty("password", hivePassword);
            // prop.setProperty("hiveconf:mapreduce.job.queuename",
            // hiveQueuename);
            prop.setProperty("hiveconf:mapreduce.job.queuename", "ia_jf_pro");

            // JDBC连接HiveServer2

            /*
             * String hiveUrlPrincipal = "principal=" +
             * ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim();
             *
             * String hiveAllUrl = ""; if (hiveUrl.endsWith("/")) { hiveAllUrl =
             * hiveUrl + hiveBase + ";" + hiveUrlPrincipal; } else { hiveAllUrl
             * = hiveUrl + "/" + hiveBase + ";" + hiveUrlPrincipal; }
             */
            log.info("hiveAllUrl信息:" + hiveUrl);

            conn = DriverManager.getConnection(hiveUrl, prop);
            log.info("连接Hive Conn信息:" + conn.toString());

        } catch (Exception e) {
            log.info("连接Hive出现异常:" + e.toString());
            e.printStackTrace();
        } /*
         * catch (ClassNotFoundException e) { e.printStackTrace();
         * log.info("ClassNotFoundException:" + e.toString()); } catch
         * (SQLException e) { e.printStackTrace(); log.info("SQLException:"
         * + e.toString()); }
         */
        return conn;
    }

    /**
     * @Description:建立hive连接（87机器tuomin用户hive环境）
     * <AUTHOR> by dongs
     * @date 2020-02-25
     */
    public static Connection getConn_v1(String hiveDirverName, String hiveUser, String hivePassword, String hiveurl,
                                        String hiveBase, String queueName) {
        Connection conn = null;
        try {
            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }
            // 连接HiveServer2配置信息
            Class.forName(hiveDirverName);

            Properties prop = new Properties();
            prop.setProperty("user", hiveUser);
            prop.setProperty("password", hivePassword);

            if (null != queueName && !"".equals(queueName)) {
                prop.setProperty("hiveconf:mapreduce.job.queuename", queueName);
            }
            // JDBC连接HiveServer2
            String hiveUrl = hiveurl;

            String hiveUrlPrincipal = ConstEngine.sddEngineConfs.get("hiveUrlPrincipal").trim();
            if (null != hiveUrlPrincipal && !"".equals(hiveUrlPrincipal)) {
                hiveUrlPrincipal = ";principal=" + hiveUrlPrincipal;
            }

            String hiveAllUrl = hiveUrl;
            if (hiveUrl.endsWith("/")) {
                hiveAllUrl = hiveUrl + hiveBase + ";" + hiveUrlPrincipal;
            } else if (!hiveUrl.contains(";")) {
                hiveAllUrl = hiveUrl + "/" + hiveBase + ";" + hiveUrlPrincipal;
            }

            log.info("hiveAllUrl信息:" + hiveAllUrl);
            conn = DriverManager.getConnection(hiveAllUrl, prop);
            log.info("连接Hive Conn信息:" + conn.toString());
            /*
             * conn = DriverManager.getConnection(hiveUrl);
             * log.info("连接Hive Conn信息:" + conn.toString());
             */

        } catch (Exception e) {
            log.info("连接Hive出现异常:" + e.toString());
            e.printStackTrace();
        } /*
         * catch (ClassNotFoundException e) { e.printStackTrace();
         * log.info("ClassNotFoundException:" + e.toString()); } catch
         * (SQLException e) { e.printStackTrace(); log.info("SQLException:"
         * + e.toString()); }
         */
        return conn;
    }

    /**
     * @Description:执行命令
     * <AUTHOR> by dongs
     * @date 2020-02-25
     */
    public static HiveStatement getStmt(final Connection conn) throws SQLException {
        if (conn == null) {
            System.out.println("conn is null");
        }
        ExecutorService exec = Executors.newFixedThreadPool(1);
        Statement createStatement = null;
        try {

            Callable<Statement> call = new Callable<Statement>() {
                public Statement call() throws Exception {
                    return conn.createStatement();
                }
            };
            try {
                Future<Statement> future = exec.submit(call);
                String HiveConnectionTimeout = ConstEngine.sddEngineConfs.get("hiveConnectionTimeout");
                System.out.println(Integer.parseInt(HiveConnectionTimeout));
                createStatement = future.get(Integer.parseInt(HiveConnectionTimeout), TimeUnit.MILLISECONDS); // 任务处理超时时间
            } catch (TimeoutException ex) {
                System.out.println("连接hive超时!");
            } catch (Exception e) {
                System.out.println("处理连接hive超时失败");
                e.printStackTrace();
            }
            // 关闭线程池
            exec.shutdown();

        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
        return (HiveStatement) createStatement;
    }

    /**
     * @Description:关闭连接
     * <AUTHOR> by dongs
     * @date 2020-02-25
     */
    public static void closeConn(Connection conn) {
        try {
            conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * @Description:关闭命令
     * <AUTHOR> by dongs
     * @date 2020-02-25
     */
    public static void closeStmt(HiveStatement stmt) {
        try {
            stmt.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * @Title: getTableFieldInfo
     * @Description: TODO
     * <AUTHOR>
     * @date 2020年3月4日
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        Connection conn = null;
        HiveStatement stmt = null;
        ResultSet res = null;
        List<Map<String, String>> fieldInfoList = null;
        try {
            conn = getConn(HiveDirverName, username, password, dburl);
            stmt = getStmt(conn);
            String strUseBase = "show tables";
            stmt.execute(strUseBase);
            String strSql = "CREATE TABLE IF NOT EXISTS " + " employee ( eid int, name String, "
                    + " salary String, destignation String)" + " COMMENT ‘Employee details’" + " ROW FORMAT DELIMITED"
                    + " FIELDS TERMINATED BY ‘\t’" + " LINES TERMINATED BY ‘\n’" + " STORED AS TEXTFILE;";

            res = stmt.executeQuery(strSql);
            while (res.next()) {
                Map<String, String> fieldInfoMap = new HashMap<>();
                fieldInfoMap.put("Field", res.getString("Field"));
                fieldInfoMap.put("Type", res.getString("Type"));
                fieldInfoMap.put("Null", res.getString("Null"));
                fieldInfoMap.put("Key", res.getString("Key"));
                fieldInfoMap.put("Default", res.getString("Default"));
                fieldInfoMap.put("Extra", res.getString("Extra"));
                fieldInfoMap.put("Privileges", res.getString("Privileges"));
                fieldInfoMap.put("Comment", res.getString("Comment"));
                fieldInfoList.add(fieldInfoMap);
            }
        } catch (Exception ex) {
            // log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeStmt(stmt);
            closeConn(conn);
        }
        return fieldInfoList;
    }

    /**
     * @Title: getInsert2TableSqlAndPatams
     * @Description: TODO
     * <AUTHOR>
     * @date 2020年3月4日
     */
    //@Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Title: getInsert2TableSqlAndPatams
     * @Description: TODO
     * <AUTHOR>
     * @date 2020年3月4日
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @Title: getCreateTableSql
     * @Description: TODO
     * <AUTHOR>
     * @date 2020年3月4日
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE IF NOT EXISTS `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("Field"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("Field")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("Field"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" String");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r) ROW FORMAT DELIMITED  FIELDS TERMINATED BY ','  STORED AS TEXTFILE;";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE IF NOT EXISTS `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("Field"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("Field")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("Field"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" String");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r) ROW FORMAT DELIMITED  FIELDS TERMINATED BY ','  STORED AS TEXTFILE;";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }


    /**
     * @Description:通过配置文件建立连接
     * <AUTHOR> by xiongpf
     * @date 2018-01-03
     */
    public static Connection getConn() {
        Connection conn = null;
        try {
            // 先进行kerberos认证
            try {
                AuthKrb5.authKrb5();
            } catch (Exception e) {
                log.error("kerberos认证失败");
            }

            // 连接HiveServer2配置信息
            Class.forName(ConstEngine.sddEngineConfs.get("hiveDirverName"));

            /*
             * Properties prop = new Properties(); prop.setProperty("user",
             * ConstEngine.sddEngineConfs.get("hiveUser"));
             * prop.setProperty("password",
             * ConstEngine.sddEngineConfs.get("hivePassword"));
             * prop.setProperty("hiveconf:mapreduce.job.queuename",
             * ConstEngine.sddEngineConfs.get("hiveQueuename"));
             */

            // JDBC连接HiveServer2
            conn = DriverManager.getConnection(ConstEngine.sddEngineConfs.get("hiveUrl"));
            log.info("Conn:" + conn.toString());

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.info("ClassNotFoundException:" + e.toString());
        } catch (SQLException e) {
            e.printStackTrace();
            log.info("SQLException:" + e.toString());
        }
        return conn;
    }


    // hive的jdbc驱动类
    // private static String HiveDirverName = "org.apache.hive.jdbc.HiveDriver";
    // private static String HiveDirverName =
    // "org.apache.derby.jdbc.EmbeddedDriver";

    // 连接hive的URL hive1.2.2版本需要的是jdbc:hive2，而不是 jdbc:hive
    // HiveServer2支持多客户端的并发和认证
    // master为Hive客户端主机名，10000为hiveServer的默认端口，mask为Hive库
    // public static String HiveUrl = "******************************";
    // 登录linux的用户名 一般会给权限大一点的用户，否则无法进行事务操作
    // public static String HiveUser = "root";
    // 登录linux的密码
    // public static String HivePassword = "xiong001";

    /**
     * @Description:获取数据库连接
     * <AUTHOR>
     * @date 2020-4-22
     */
    public static Connection getConn_v2(String url, String username, String password, String dbname) {
        Connection conn = null;
        try {
            conn = getConn(HiveDirverName, username, password, url, dbname);
        } catch (Exception ex) {
            System.out.println("获取数据库连接出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        }
        return conn;
    }

    /**
     * @Description: 获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020年2月25日
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbname) {
        HiveStatement stmt = null;
        ResultSet res = null;
        Map<String, String> dbTabMap = null;
        try {
            stmt = getStmt(conn);
            dbTabMap = new TreeMap<String, String>();
            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                stmt.execute(strUseBase);
                String strShowTable = "show tables";
                res = stmt.executeQuery(strShowTable);
                while (res.next()) {
                    if (dbTabMap.containsKey(dbname)) {
                        dbTabMap.put(dbname, dbTabMap.get(dbname) + "," + res.getString(1));
                    } else {
                        dbTabMap.put(dbname, res.getString(1));
                    }
                }
            } else {
                String strShowBase = "show databases";
                res = stmt.executeQuery(strShowBase);
                while (res.next()) {
                    String dbName = res.getString(1);
                    String strUseBase = "use " + dbName;
                    stmt.execute(strUseBase);
                    String strShowTable = "show tables";
                    res = stmt.executeQuery(strShowTable);
                    while (res.next()) {
                        if (dbTabMap.containsKey(dbName)) {
                            dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + res.getString(1));
                        } else {
                            dbTabMap.put(dbName, res.getString(1));
                        }
                    }

                }
            }

        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(res, stmt, null);
        }
        return dbTabMap;
    }

    /**
     * @Description: 判断该表是否为分区表
     * <AUTHOR>
     * @date 2019年7月22日
     */
    protected static boolean getIsPartitionTable(HiveStatement stmt, String tablename) {
        boolean flag = false;
        ResultSet res;
        try {
            String strShowFilenames = "desc " + tablename;
            res = stmt.executeQuery(strShowFilenames);
            while (res.next()) {
                String fieldname = res.getString(1);
                if ("".equals(fieldname)) {
                    flag = true;
                    break;
                }
            }
        } catch (SQLException e) {
            String loginfo = "hive数仓表字段信息判断分区表失败";
            log.error(loginfo + ",异常：" + e.getMessage());
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * @Description: 获取表分区信息
     * <AUTHOR>
     * @date 2019年7月22日
     */
    protected static String getTablePartitionInfo(HiveStatement stmt, String tablename) {
        String Partition = "";
        ResultSet res;
        try {
            String strShowPartitions = "show Partitions " + tablename;
            res = stmt.executeQuery(strShowPartitions);
            while (res.next()) {
                Partition = res.getString(1);
                // fieldlist.add(res.getString(2));
            }
        } catch (SQLException e) {
            String loginfo = "hive数仓表字段信息获取表分区失败";
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return Partition;

    }

    /**
     * @Description: 获取数据库表中数据
     * <AUTHOR>
     * @date 2020年2月25日
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname,
                                                String tabname, Integer lineNum) {

        HiveStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            stmt = getStmt(conn);
            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                stmt.execute(strUseBase);
            }
            String strSql = "set hive.limit.optimize.enable=true";
            stmt.execute(strSql);
            strSql = "set hive.limit.row.max.size=100";
            stmt.execute(strSql);
            strSql = "set hive.limit.optimize.limit.file=10";
            stmt.execute(strSql);

            String strSQL = "select * from " + tabname;

            boolean flag = getIsPartitionTable(stmt, tabname);
            if (flag) {
                String partition = getTablePartitionInfo(stmt, tabname);
                System.out.println("partition:" + partition);
                if (null != partition && !"".equals(partition)) {
                    String[] partitionArr = partition.split("/");
                    if (partitionArr.length > 0) {
                        strSQL += " where";
                        for (String partitionstr : partitionArr) {
                            if (!StrUtil.endWith(strSQL, "where")) {
                                strSQL += " and ";
                            }
                            System.out.println("partitionstr:" + partitionstr);
                            String[] split = partitionstr.split("=");
                            if (split.length > 1) {
                                strSQL += " ";
                                strSQL += split[0];
                                strSQL += " = ";
                                strSQL += " '";
                                strSQL += split[1];
                                strSQL += "'";
                            }
                        }
                    }
                }
            }
            if (lineNum != null && lineNum != 0) {
                strSQL += " LIMIT " + lineNum;
            }
            System.out.println("strSQL: " + strSQL);
            rs = stmt.executeQuery(strSQL);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (rs.next()) {
                //                StringBuffer sub = new StringBuffer();
//                for (int i = 1; i <= columnCount; i++) {
//                    if (i > 1) {
//                        sub.append(";;");
//                    }
//                    sub.append(rs.getObject(i));
//                }
//                tabDataList.add(sub.toString());

                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库表中数据出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            ex.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }


    /**
     * @Description: 获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020年2月25日
     */
    public static Map<String, String> getAllDbAndTabMap(String url, String username, String password, String dbname) {
        Connection conn = null;
        HiveStatement stmt = null;
        ResultSet res = null;
        Map<String, String> dbTabMap = null;
        try {
            conn = getConn(HiveDirverName, username, password, url);
            stmt = getStmt(conn);
            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                stmt.execute(strUseBase);
                String strShowTable = "show tables";
                res = stmt.executeQuery(strShowTable);
                while (res.next()) {
                    if (dbTabMap.containsKey(dbname)) {
                        dbTabMap.put(dbname, dbTabMap.get(dbname) + "," + res.getString(1));
                    } else {
                        dbTabMap.put(dbname, res.getString(1));
                    }
                }
            } else {
                String strShowBase = "show databases";
                res = stmt.executeQuery(strShowBase);
                while (res.next()) {
                    String dbName = res.getString(1);
                    String strUseBase = "use " + dbName;
                    stmt.execute(strUseBase);
                    String strShowTable = "show tables";
                    res = stmt.executeQuery(strShowTable);
                    while (res.next()) {
                        if (dbTabMap.containsKey(dbName)) {
                            dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + res.getString(1));
                        } else {
                            dbTabMap.put(dbName, res.getString(1));
                        }
                    }

                }
            }

        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            // log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeStmt(stmt);
            closeConn(conn);
        }
        return dbTabMap;
    }


    /**
     * @Description: 获取数据库某个字段非空数据
     * <AUTHOR>
     * @date 2021-03-12
     */
    public static List<String[]> getFieldDataList(Connection conn, String dbname, String tabname, String field, Integer lineNum) {

        HiveStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            stmt = getStmt(conn);
            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                stmt.execute(strUseBase);
            }
            String strSql = "set hive.limit.optimize.enable=true";
            stmt.execute(strSql);
            strSql = "set hive.limit.row.max.size=100";
            stmt.execute(strSql);
            strSql = "set hive.limit.optimize.limit.file=10";
            stmt.execute(strSql);

            String strSQL = "select " + field + " from " + tabname;

            boolean flag = getIsPartitionTable(stmt, tabname);
            if (flag) {
                String partition = getTablePartitionInfo(stmt, tabname);
                System.out.println("partition:" + partition);
                if (null != partition && !"".equals(partition)) {
                    String[] partitionArr = partition.split("/");
                    if (partitionArr.length > 0) {
                        strSQL += " where";
                        for (String partitionstr : partitionArr) {
                            if (!StrUtil.endWith(strSQL, "where")) {
                                strSQL += " and ";
                            }
                            System.out.println("partitionstr:" + partitionstr);
                            String[] split = partitionstr.split("=");
                            if (split.length > 1) {
                                strSQL += " ";
                                strSQL += split[0];
                                strSQL += " = ";
                                strSQL += " '";
                                strSQL += split[1];
                                strSQL += "'";
                            }
                        }
                    }
                }
            }
            if (strSQL.indexOf("where") > 0) {
                if (lineNum != null && lineNum != 0) {
                    strSQL += " and " + field + " is not null LIMIT " + lineNum;
                }
            } else {
                if (lineNum != null && lineNum != 0) {
                    strSQL += " where " + field + " is not null LIMIT " + lineNum;
                }
            }

            System.out.println("strSQL: " + strSQL);
            rs = stmt.executeQuery(strSQL);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库表中数据出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            ex.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }


    /**
     * @Description: 获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020年2月25日
     */
    public static List<String> getTabDataList(String url, String username, String password, String dbname,
                                              String tabname) {
        Connection conn = null;
        HiveStatement stmt = null;
        ResultSet rs = null;
        List<String> tabDataList = new ArrayList<String>();
        try {
            conn = getConn(HiveDirverName, username, password, url);
            stmt = getStmt(conn);
            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                stmt.execute(strUseBase);
            }
            String strSQL = "select * from " + tabname + " LIMIT 100 ";
            rs = stmt.executeQuery(strSQL);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (rs.next()) {
                StringBuffer sub = new StringBuffer();
                for (int i = 1; i <= columnCount; i++) {
                    if (i > 1) {
                        sub.append(Const.DB_TAB_DATA_JOIN);
                    }
                    sub.append(rs.getObject(i));
                }
                tabDataList.add(sub.toString());
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            // log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeStmt(stmt);
            closeConn(conn);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020年2月25日
     */
    public static List<String> getFieldNameList(Connection conn, String dbname,
                                                String tabname) {
        HiveStatement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            stmt = getStmt(conn);
            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                stmt.execute(strUseBase);
            }
            String strSQL = "desc " + tabname;
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String fieldname = rs.getString(1);
                if ("".equals(fieldname)) {
                    break;
                }
                list.add(fieldname);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("获取数据库表中所有字段名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return list;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020年2月25日
     */
    public static List<String> getFieldNameList(String url, String username, String password, String dbname,
                                                String tabname) {
        Connection conn = null;
        HiveStatement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            conn = getConn(HiveDirverName, username, password, url);
            stmt = getStmt(conn);
            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                stmt.execute(strUseBase);
            }
            String strSQL = "desc " + tabname;
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String fieldname = rs.getString(1);
                if ("".equals(fieldname)) {
                    break;
                }
                list.add(fieldname);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("获取数据库中所有的库名表名出现异常");
            // log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeStmt(stmt);
            closeConn(conn);
        }
        return list;
    }


    /**
     * @Description: 获取数据库表数据数量
     * <AUTHOR>
     * @date 2020年2月25日
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) {
        HiveStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            stmt = getStmt(conn);

            if (null != dbname && !dbname.equals("")) {
                String strUseBase = "use " + dbname;
                System.out.println("strUseBase: " + strUseBase);
                stmt.execute(strUseBase);
            }

            //TODO 宁夏线上需要注释 SET hive.exec.mode.local.auto = true
            String authType = ConstEngine.sddEngineConfs.get("kb.auth.type");
            if (!authType.equalsIgnoreCase(Const.HIVE_PRODUCE_AUTH)){
                stmt.execute("SET hive.exec.mode.local.auto = true");
            }
            //sql = "SET mapred.job.tracker=local";
            //stmt.execute(sql);
            String strSQL = "select count(*) from " + tabname;
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println("获取数据库表数据数量出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeStmt(stmt);
        }
        return count;
    }


    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = getStmt(conn);
            if (null != dbName && !dbName.equals("")) {
                String strUseBase = "use " + dbName;
                stmt.execute(strUseBase);
            }
            //stmt = conn.createStatement();
            String strSQL = "desc " + tableName;
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                String fieldname = rs.getString(1);
                if ("".equals(fieldname)) {
                    break;
                }
                Map<String, String> fieldInfoMap = new HashMap<>();
                fieldInfoMap.put("fieldName", fieldname);
                fieldInfoMap.put("fieldCName", "");
                fieldInfoList.add(fieldInfoMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表信息
     * @Description 查询表信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:56
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        tableInfoMap.put("tableName", tableName);
        tableInfoMap.put("tableCName", "");
       /* Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(HiveDirverName, username, password, dburl,"");
            stmt = conn.createStatement();// 执行创建表
            String sql = "SELECT TABLE_NAME,TABLE_COMMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'";
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            if (rs != null) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);*/
        return tableInfoMap;
    }

    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 17:01
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        // 1. 从输入参数中获取数据库连接信息
        String driverProgram = map.get("driverProgram");
        String username = map.get("username");
        String password = map.get("password");
        // 如果密码是加密的，进行解密
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }
        String dbname = map.get("dbname");
        String hiveUrl = map.get("hiveUrl");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 加载Hive JDBC驱动
            Class.forName(driverProgram);

            // 2. 创建Hive连接
            connection = DriverManager.getConnection(hiveUrl, username, password);

            // 3. 检查数据库是否存在
            statement = connection.createStatement();
            resultSet = statement.executeQuery("SHOW DATABASES");
            boolean databaseExists = false;
            while (resultSet.next()) {
                String databaseName = resultSet.getString(1);
                if (dbname.equalsIgnoreCase(databaseName)) { // Hive不区分大小写
                    databaseExists = true;
                    break;
                }
            }

            // 4. 如果数据库不存在，则创建它
            if (!databaseExists) {
                // 使用CREATE DATABASE语句创建数据库
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }

        } catch (Exception e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return msgMap;
    }

    /***
     * 获取hive数据库前5条数据
     * @param: tableName  表名
     * @param: conn       驱动
     * <AUTHOR>
     * @date: 2024/12/23 11:02
     * @return:
     */
    public static String getFiveHiveData(String tableName,Connection conn) {
        Statement stmt = null;
        ResultSet rs = null;

        String hiveDate = "";
        String hiveDateList = "";
        try {
            // 创建 Statement
            stmt = conn.createStatement();
            //执行sql
            String sql = "select * from "+tableName+" limit 5";
            rs = stmt.executeQuery(sql);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            //处理结果
            int rowCount = 0;
            while (rs.next() && rowCount <= 5){
                for (int i = 1; i <= columnCount; i++) {
                    Object columnValue = rs.getObject(i);
                    String value = columnValue == null?"":String.valueOf(columnValue);
                    if (org.apache.commons.lang.StringUtils.isEmpty(hiveDate)){
                        if (rowCount != 0){
                            hiveDate = "\n" + value;
                        }else {
                            hiveDate = value;
                        }
                    }else {
                        hiveDate = hiveDate + ";" +value;
                    }
                }
                hiveDateList = hiveDateList + hiveDate;
                hiveDate = "";
                rowCount++;
            }
        } catch (SQLException e) {
            throw new RuntimeException("未找到Hive JDBC驱动程序 :" + e.getMessage(), e);
        } finally {
            closeCon(rs,stmt,null);
        }
        return hiveDateList;
    }
}
