package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import com.wzsec.modules.mask.service.MaskPictaskconfigService;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigDto;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigQueryCriteria;
import com.wzsec.modules.quartz.config.MaskPicScanConfig;
import com.wzsec.utils.Const;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022-04-18
 */
// @Api(tags = "文件图片脱敏管理")
@RestController
@RequestMapping("/api/maskPictaskconfig")
public class MaskPictaskconfigController {

    private final MaskPictaskconfigService maskPictaskconfigService;

    private final MaskPicScanConfig scanConfig;

    public MaskPictaskconfigController(MaskPictaskconfigService maskPictaskconfigService, MaskPicScanConfig scanConfig) {
        this.maskPictaskconfigService = maskPictaskconfigService;
        this.scanConfig = scanConfig;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskPictaskconfig:list')")
    public void download(HttpServletResponse response, MaskPictaskconfigQueryCriteria criteria) throws IOException {
        maskPictaskconfigService.download(maskPictaskconfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询图片脱敏任务")
    @PreAuthorize("@el.check('maskPictaskconfig:list')")
    public ResponseEntity<Object> getMaskPictaskconfigs(MaskPictaskconfigQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(maskPictaskconfigService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增图片脱敏任务")
    @PreAuthorize("@el.check('maskPictaskconfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskPictaskconfig resources) {
        MaskPictaskconfigDto maskPictaskconfigDto = maskPictaskconfigService.create(resources);

        if (maskPictaskconfigDto != null && Const.TASK_STATE_USE.equals(maskPictaskconfigDto.getState()) && Const.TASK_SUBMITTYPE_AUTO.equals(maskPictaskconfigDto.getSparefield2())) {
            scanConfig.addJob(maskPictaskconfigDto);
        }

        return new ResponseEntity<>(maskPictaskconfigDto, HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改图片脱敏任务")
    @PreAuthorize("@el.check('maskPictaskconfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskPictaskconfig resources) {

        //提交方式改为手动提交,将定时时间置空
        if (resources.getSparefield2().equals(Const.TASK_SUBMITTYPE_HAND)) {  //手动执行
            resources.setSparefield2("");
        }

        maskPictaskconfigService.update(resources);

        MaskPictaskconfigDto MaskPictaskconfigDto = maskPictaskconfigService.findById(resources.getId());
        if (MaskPictaskconfigDto != null && Const.TASK_STATE_USE.equals(MaskPictaskconfigDto.getState()) && Const.TASK_SUBMITTYPE_AUTO.equals(MaskPictaskconfigDto.getSparefield2())) {
            scanConfig.updateJob(MaskPictaskconfigDto);
        }

        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除图片脱敏任务")
    @PreAuthorize("@el.check('maskPictaskconfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        for (Integer id : ids) {
            MaskPictaskconfigDto MaskPictaskconfigDto = maskPictaskconfigService.findById(id);
            scanConfig.deleteJob(MaskPictaskconfigDto);
        }
        maskPictaskconfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取图片脱敏任务新增任务号")
    @PreAuthorize("@el.check('fileTaskConfig:add')")
    @GetMapping(value = "/getTaskNo")
    public ResponseEntity<Object> getTaskNo() {
        return new ResponseEntity<>(maskPictaskconfigService.getMAXTaskNo(), HttpStatus.CREATED);
    }

    @Log("执行图片脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    @PreAuthorize("@el.check('fileTaskConfig:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable int id, HttpServletRequest request) {
        maskPictaskconfigService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
