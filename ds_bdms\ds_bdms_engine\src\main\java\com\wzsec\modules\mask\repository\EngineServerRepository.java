package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.EngineServer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2022-06-13
 */
public interface EngineServerRepository extends JpaRepository<EngineServer, Long>, JpaSpecificationExecutor<EngineServer> {


    /**
     * 由id查询引擎配置
     *
     * @param id id
     * @return {@link EngineServer}
     */
    @Query(value = "select * from sdd_engine_server where id=?", nativeQuery = true)
    EngineServer queryByIdForEngine(Long id);

    /**
     * 通过id、端口 查询引擎配置
     * @param ip
     * @param port
     */
    @Query(value = "select * from sdd_engine_server where ip=? and port=?", nativeQuery = true)
    EngineServer findByIpPort(String ip, String port);

    @Query(value = "SELECT * FROM sdd_engine_server where isvalid = '0' and state = '2' order by id desc limit 1", nativeQuery = true)
    EngineServer findLatestData();

    @Modifying
    @Transactional
    @Query(value = "UPDATE sdd_engine_server SET application = CAST(COALESCE(application, '0') AS INTEGER) + 1 WHERE ip=?1 and port=?2", nativeQuery = true)
    void addKingbaseCountByIpPort(String ip, String port);

    @Modifying
    @Transactional
    @Query(value = "UPDATE sdd_engine_server SET application = CAST(COALESCE(application, '0') AS INTEGER) - 1 WHERE ip=?1 and port=?2 ", nativeQuery = true)
    void reduceKingbaseCountByIpPort(String ip, String port);

    @Modifying
    @Transactional
    @Query(value = "UPDATE sdd_engine_server SET application = CAST(COALESCE(application, '0') AS SIGNED) + 1 WHERE ip=?1 and port=?2 ", nativeQuery = true)
    void addCountByIpPort(String ip, String port);

    @Modifying
    @Transactional
    @Query(value = "UPDATE sdd_engine_server SET application = CAST(COALESCE(application, '0') AS SIGNED) - 1 WHERE ip=?1 and port=?2 ", nativeQuery = true)
    void reduceCountByIpPort(String ip, String port);
}
