package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.Maskrule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
* <AUTHOR>
* @date 2020-11-06
*/
public interface MaskruleRepository extends JpaRepository<Maskrule, Integer>, JpaSpecificationExecutor<Maskrule> {

    /**
     * 根据规则名称查询规则数量
     * @param algorithmname 规则名称
     */
    @Query(value = "select count(*) from sdd_mask_rule where rulename = ?1",nativeQuery = true)
    int findRuleCountByRuleEName(String algorithmname);

    /**
     * 根据规则中文名称查询规则数量
     * @param algenglishname 规则中文名
     */
    @Query(value = "select count(*) from sdd_mask_rule where rulecname = ?1",nativeQuery = true)
    int findRuleCountByRuleCName(String algenglishname);

    /**
     * 查询静态脱敏规则
     */
    @Query(value = "SELECT * FROM `sdd_mask_rule`", nativeQuery = true)
    List<Maskrule> findStaticDesensitizationRules();

    @Query(value = "SELECT * FROM sdd_mask_rule where rulename = ?1", nativeQuery = true)
    Maskrule findRuleName(String maskRuleName);
}
