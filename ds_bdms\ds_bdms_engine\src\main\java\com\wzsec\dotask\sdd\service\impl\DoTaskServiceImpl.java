package com.wzsec.dotask.sdd.service.impl;

import com.wzsec.dotask.sdd.service.DoTaskService;
import com.wzsec.config.Log;
import com.wzsec.modules.alarm.config.MonitorRiskAlarmData;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.mask.domain.EngineServer;
import com.wzsec.modules.mask.domain.MaskTablestructure;
import com.wzsec.modules.mask.repository.EngineServerRepository;
import com.wzsec.modules.sdd.basefield.domain.Basefield;
import com.wzsec.modules.sdd.basefield.service.BasefieldService;
import com.wzsec.modules.sdd.category.service.CategoryService;
import com.wzsec.modules.sdd.category.service.dto.CategoryDto;
import com.wzsec.modules.sdd.discover.domain.Task;
import com.wzsec.modules.sdd.discover.repository.TaskRepository;
import com.wzsec.modules.sdd.discover.service.DetailresultService;
import com.wzsec.modules.sdd.metadata.repository.MetaFieldRepository;
import com.wzsec.modules.sdd.metadata.service.MetaFieldService;
import com.wzsec.modules.sdd.metadata.service.MetaTableService;
import com.wzsec.modules.sdd.metadata.service.MetadataService;
import com.wzsec.modules.sdd.discover.service.OutlineresultService;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.domain.Datasource;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.sdd.strategy.service.StrategyService;
import com.wzsec.modules.sdd.strategy.service.dto.StrategyDto;
import com.wzsec.dotask.sdd.service.excute.db.DBSensitiveDataDiscovery;
import com.wzsec.dotask.sdd.service.excute.file.FileSensitiveDataDiscovery_v1;
import com.wzsec.modules.statistics.repository.StatisticstaskDetailRepository;
import com.wzsec.modules.statistics.repository.TasksynrecordDetailRepository;
import com.wzsec.modules.statistics.service.StatisticstaskDetailService;
import com.wzsec.modules.statistics.service.StatisticstaskOutlineService;
import com.wzsec.modules.statistics.service.TasksynrecordDetailService;
import com.wzsec.modules.statistics.service.TasksynrecordOutlineService;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCallback;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

// 默认不使用缓存
//import org.springframework.cache.annotation.CacheConfig;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR> Kunxiang
 * @date 2020-04-03
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "task")
//Propagation.SUPPORTS如果其他bean调用这个方法,在其他bean中声明事务,那就用事务.如果其他bean没有声明事务,那就不用事务.
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoTaskServiceImpl implements DoTaskService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final TaskRepository taskRepository;

    private final RuleService ruleService;

    private final DatasourceService datasourceService;

    private final OutlineresultService outlineresultService;

    private final DetailresultService detailresultService;

    private final StrategyService strategyService;

    private final CategoryService categoryService;

    private final MetadataService metadataService;

    private final BasefieldService basefieldService;

    private final MetaTableService metaTableService;

    private final MetaFieldService metaFieldService;

    private final DmAlarmdisposalService dmAlarmdisposalService;

    private final TasksynrecordOutlineService tasksynrecordOutlineService;

    private final TasksynrecordDetailService tasksynrecordDetailService;

    private final EngineServerRepository engineServerRepository;

    private final StatisticstaskOutlineService statisticstaskOutlineService;

    private final StatisticstaskDetailService statisticstaskDetailService;

    private final MetaFieldRepository metaFieldRepository;

    private final TasksynrecordDetailRepository tasksynrecordDetailRepository;
    private final StatisticstaskDetailRepository statisticstaskDetailRepository;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    private final DictDetailService dictDetailService;

    @Value("${spring.profiles.active}")
    private String active;

    public DoTaskServiceImpl(TaskRepository taskRepository,
                             RuleService ruleService,
                             DatasourceService datasourceService,
                             OutlineresultService outlineresultService,
                             DetailresultService detailresultService,
                             StrategyService strategyService,
                             CategoryService categoryService,
                             MetadataService metadataService,
                             BasefieldService basefieldService,
                             MetaTableService metaTableService,
                             MetaFieldService metaFieldService,
                             DmAlarmdisposalService dmAlarmdisposalService,
                             TasksynrecordOutlineService tasksynrecordOutlineService,
                             TasksynrecordDetailService tasksynrecordDetailService,
                             EngineServerRepository engineServerRepository,
                             StatisticstaskOutlineService statisticstaskOutlineService,
                             StatisticstaskDetailService statisticstaskDetailService,
                             MetaFieldRepository metaFieldRepository,
                             TasksynrecordDetailRepository tasksynrecordDetailRepository,
                             StatisticstaskDetailRepository statisticstaskDetailRepository,
                             SdkOperationrecordRepository sdkOperationrecordRepository,
                             SdkApplyconfigRepository sdkApplyconfigRepository,
                             DictDetailService dictDetailService) {
        this.taskRepository = taskRepository;
        this.ruleService = ruleService;
        this.datasourceService = datasourceService;
        this.outlineresultService = outlineresultService;
        this.detailresultService = detailresultService;
        this.strategyService = strategyService;
        this.categoryService = categoryService;
        this.metadataService = metadataService;
        this.basefieldService = basefieldService;
        this.metaTableService = metaTableService;
        this.metaFieldService = metaFieldService;
        this.dmAlarmdisposalService = dmAlarmdisposalService;
        this.tasksynrecordDetailService = tasksynrecordDetailService;
        this.tasksynrecordOutlineService = tasksynrecordOutlineService;
        this.engineServerRepository = engineServerRepository;
        this.statisticstaskOutlineService = statisticstaskOutlineService;
        this.statisticstaskDetailService = statisticstaskDetailService;
        this.metaFieldRepository = metaFieldRepository;
        this.tasksynrecordDetailRepository = tasksynrecordDetailRepository;
        this.statisticstaskDetailRepository = statisticstaskDetailRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.dictDetailService = dictDetailService;
    }

    @Async//异步执行
    @Override
    public void execution(Long id, String submituser) {
        // 获取敏感数据任务配置
        Task task = taskRepository.findById(id).orElseGet(Task::new);
        execution(task, submituser);
    }

    /**
     * @param task：任务
     * <AUTHOR>
     * @date 2020-4-22
     */
    @Transactional(rollbackFor = Exception.class)
    public void execution(Task task, String submituser) {
        log.info("开始执行自动识别敏感数据类型任务");
        long startTime = System.currentTimeMillis();
        String tasknumber = DateUtil.formatDate(new Date());
        //任务执行状态（0未提交，1,提交失败，2执行中，3执行成功，4执行失败）
        String executionstate = Const.TASK_EXECUTESTATE_EXECUTING;//执行中

        task.setExecutestate(executionstate);
        taskRepository.save(task);//更新任务执行状态
        String engine = task.getSparefield4();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String ip = ipPort[0];
        String port = ipPort[1];

        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(ip+":"+port);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        boolean flag = true;
        try {
            //TODO 将所使用的引擎，任务负载数+1
            if (Const.DB_KINGBASE8.equalsIgnoreCase(active)) {
                engineServerRepository.addKingbaseCountByIpPort(ip, port);
            }else {
                engineServerRepository.addCountByIpPort(ip, port);
            }

            String taskType = StringUtils.isNotEmpty(task.getSparefield2()) ? task.getSparefield2() : Const.DATA_SCAN_TASK_ALL;

            List<RuleDto> ruleList = new ArrayList<>();
            List<CategoryDto> categoryList = new ArrayList<>();
            Map<String, Basefield> baseFieldAllMap = new HashMap<>();
            if (!Const.DATA_SCAN_TASK_METADATA.equals(taskType)){
                String strategyids = task.getStrategyids();
                if (StringUtils.isEmpty(task.getStrategyids())) {
                    throw new Exception("未选择策略");
                }
                // 1.获取敏感数据策略
                List<StrategyDto> strategyDtoList = strategyService.findAllByIdsStr(strategyids);//未考虑规则禁用启用，只要任务中选了就可以使用
                // 从策略中获取规则id并去重
                Set<Long> ruleidsSet = new HashSet<>();//用于去重
                for (StrategyDto strategyDto : strategyDtoList) {
                    String[] ruleids = strategyDto.getRuleids().split(",");
                    ruleidsSet.addAll(Arrays.asList(ArrayUtil.stringToLong(ruleids)));
                }
                String ruleidsStr = StringUtils.join(ruleidsSet, ",");

                // 2.获取敏感数据规则
                ruleList = ruleService.findAllByIdsStr(ruleidsStr);//未考虑规则禁用启用，只要策略中选了就可以使用

                // 获取姓名白名单
                Const.nameWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.NAME_SIGN, Const.whiteList_SIGN);
                Const.addreWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.ADDRESS_SIGN, Const.whiteList_SIGN);
                Const.phoneWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.PHONE_SIGN, Const.whiteList_SIGN);

                // 3.获取所有类别
                categoryList = categoryService.queryAll();

                // 4.获取基础字段
                baseFieldAllMap = basefieldService.findBaseFieldAll();

            }


            String datatype = task.getDatatype();
            String taskname = task.getTaskname();

            // 5.查数据源配置
            Long dataSourceId = task.getDatasourceid();
            DatasourceDto datasourceDto = datasourceService.findById(dataSourceId);
            String srcip = datasourceDto.getSrcip();
            String srcport = datasourceDto.getSrcport();
            String srcname = null, driver = null, type = null, srcurl = null, username = null, password = null, dbnames = null, datastructure = null;
            if (dataSourceId != null) {//无数据源，本地文件检测
                //AES解密
                String decryptPassword = "";
                if (StringUtils.isNotEmpty(datasourceDto.getPassword())) {
                    decryptPassword = AES.decrypt(datasourceDto.getPassword(), Const.AES_SECRET_KEY);
                }
                srcname = datasourceDto.getSrcname();
                driver = datasourceDto.getDriverprogram();
                type = datasourceDto.getType();
                srcurl = datasourceDto.getSrcurl();
                username = datasourceDto.getUsername();
                password = decryptPassword;
                dbnames = datasourceDto.getDbname();
                datastructure = datasourceDto.getSparefield2();
            }
            if (type.equalsIgnoreCase(Const.DB_REDIS)){
                datatype = Const.TASK_DATATYPE_NODB;
            }
            // 6.扫描数据源检测
            if (Const.TASK_DATATYPE_DB.equals(datatype)) {//1.库表检测（数据库源）
                DBSensitiveDataDiscovery dbSensitiveDataDiscovery = new DBSensitiveDataDiscovery(outlineresultService,
                        detailresultService, metadataService, metaTableService, metaFieldService,
                        tasksynrecordOutlineService, tasksynrecordDetailService,
                        statisticstaskOutlineService,statisticstaskDetailService,
                        metaFieldRepository,tasksynrecordDetailRepository,
                        statisticstaskDetailRepository,dictDetailService);
                flag = dbSensitiveDataDiscovery.doDBSensitiveDataDiscovery(taskname, dataSourceId, submituser, srcname, type, srcurl,
                        username, password, dbnames, ruleList, categoryList, baseFieldAllMap, tasknumber, taskType,srcip,srcport);
            } else if (Const.TASK_DATATYPE_FILE.equals(datatype)) {//2.文件（文件源）
                String datapath = task.getDatapath();
                //全部下载再检测，检测完删除全部
//                FileSensitiveDataDiscovery fileSensitiveDataDiscovery = new FileSensitiveDataDiscovery(outlineresultService, detailresultService);
                //单个下载、单个检测、单个删除
                FileSensitiveDataDiscovery_v1 fileSensitiveDataDiscovery = new FileSensitiveDataDiscovery_v1(outlineresultService, detailresultService);
                flag = fileSensitiveDataDiscovery.doFileSensitiveDataDiscovery(taskname, dataSourceId, submituser, srcname, type, srcurl, username, password, datapath, ruleList, datastructure,srcip,srcport);
            } else if(Const.TASK_DATATYPE_BIGDATA.equals(datatype)) {//3.大数据组件检测（大数据组件源）
                DBSensitiveDataDiscovery dbSensitiveDataDiscovery = new DBSensitiveDataDiscovery(outlineresultService,
                        detailresultService, metadataService, metaTableService, metaFieldService,
                        tasksynrecordOutlineService, tasksynrecordDetailService,
                        statisticstaskOutlineService,statisticstaskDetailService,
                        metaFieldRepository,tasksynrecordDetailRepository,
                        statisticstaskDetailRepository,dictDetailService);
                // TODO hbase
                if (type.equals("HBase")) {
                    flag = dbSensitiveDataDiscovery.doHbaseSensitiveDataDiscovery(taskname, dataSourceId, submituser, srcname, type, srcurl,
                            username, password, dbnames, ruleList, categoryList, baseFieldAllMap, tasknumber,srcip,srcport);
                }else {
                    flag = dbSensitiveDataDiscovery.doDBSensitiveDataDiscovery(taskname, dataSourceId, submituser, srcname, type, srcurl,
                            username, password, dbnames, ruleList, categoryList, baseFieldAllMap, tasknumber, taskType,srcip,srcport);
                }
            } else if (Const.TASK_DATATYPE_NODB.equals(datatype)) {//4.非格式化数据库
                DBSensitiveDataDiscovery dbSensitiveDataDiscovery = new DBSensitiveDataDiscovery(outlineresultService,
                        detailresultService, metadataService, metaTableService, metaFieldService,
                        tasksynrecordOutlineService, tasksynrecordDetailService,
                        statisticstaskOutlineService,statisticstaskDetailService,
                        metaFieldRepository,tasksynrecordDetailRepository,
                        statisticstaskDetailRepository,dictDetailService);
                //自动识别敏感数据类型
                flag = dbSensitiveDataDiscovery.doNODBSensitiveDataDiscovery(taskname, dataSourceId, submituser, srcname, type, srcurl,
                        username, password, dbnames, ruleList, categoryList, baseFieldAllMap, tasknumber, taskType,srcip,srcport);
            }
            executionstate = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS;//执行成功
            sdkOperationrecord.setObjectname(taskname);
            sdkOperationrecord.setOperation("敏感数据发现任务执行成功");
            if (!flag) {
                sdkOperationrecord.setObjectname(taskname);
                sdkOperationrecord.setOperation("敏感数据发现任务执行失败");
                throw new Exception("最终结果执行失败");
            }
        } catch (Exception e) {
            executionstate = Const.TASK_EXECUTESTATE_EXECUTE_FAIL;//执行失败
            sdkOperationrecord.setObjectname(task.getTaskname());
            sdkOperationrecord.setOperation("敏感数据发现任务执行失败");
            log.error("自动发现敏感数据任务执行失败", e);

            //TODO 该处为测试数据,相关取值为固定值
            Log log = new Log();
            log.setUsername("admin");
            log.setDescription("检测的数据源出现异常");
            log.setMethod("com.wzsec.dotask.sdd.service.excute.db.DBSensitiveDataDiscovery.doDBSensitiveDataDiscovery()");
            String taskname = task.getTaskname();
            String parameters = "{taskname:" + taskname + "}";
            log.setParams(parameters);
            log.setLogType("ERROR");
            log.setRequestIp("***************");
            log.setAddress("中国|北京|北京市|联通");
            log.setBrowser("Chrome 10");
            //log.setSystematics("高");
            //log.setAlarmInformation("元数据扫描任务，数据源出现异常");
            long endTime = System.currentTimeMillis();
            int time = (int) ((endTime - startTime) / 1000);
            log.setTime((long) time);
            log.setCreateTime(new Timestamp(System.currentTimeMillis()));
            log.setExceptionDetail("com.wzsec.exception.BadRequestException: 元数据扫描任务，数据源出现异常！\n" +
                    "\tat");
            saveLogs(log);


        } finally {
            long endTime = System.currentTimeMillis();
            int time = (int) ((endTime - startTime) / 1000);
            log.info("结束执行自动识别敏感数据类型任务，共执行了：" + time + "秒！");

            if(Const.TASK_EXECUTESTATE_EXECUTE_FAIL.equals(executionstate)){
                Datasource datasource = datasourceService.findByDataSourceId(task.getDatasourceid());
                // TODO 事件详情定义
                String str = "数据脱敏：数据源名称：{},数据源url：{},数据源库名：{},执行时间：{},数据自动扫描任务执行失败";
                MonitorRiskAlarmData.InsertDmAlarmData(str,datasource.getSrcname(),datasource.getSrcurl(),datasource.getDbname(),datasource.getUpdatetime(),task.getCreateuser(),task.getDatatype(),Const.DICT_DATA_AUTMOATIC_SCAN_TASK_FAIL,dmAlarmdisposalService);
            }
            task.setExecutestate(executionstate);
            taskRepository.save(task);//更新任务执行状态

            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
            sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_SENSITIVE);
            sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
            sdkOperationrecordRepository.save(sdkOperationrecord);

            //TODO 将所使用的引擎，任务负载数-1
            if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
                engineServerRepository.reduceKingbaseCountByIpPort(ip, port);
            }else {
                engineServerRepository.reduceCountByIpPort(ip, port);
            }
        }
    }


    /**
     * 保存日志文件
     */
    public boolean saveLogs(Log logs) {
        boolean success = false;
        success = jdbcTemplate.execute("INSERT INTO log (create_time,description,exception_detail,log_type,method,params,request_ip,time,address,browser,alarm_information,alarm_threshold,systematics,username) " +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?);", new PreparedStatementCallback<Boolean>() {
            @Override
            public Boolean doInPreparedStatement(PreparedStatement preparedStatement) throws SQLException, DataAccessException {
                preparedStatement.setTimestamp(1, logs.getCreateTime());
                preparedStatement.setString(2, logs.getDescription());
                preparedStatement.setString(3, logs.getExceptionDetail());
                preparedStatement.setString(4, logs.getLogType());
                preparedStatement.setString(5, logs.getMethod());
                preparedStatement.setString(6, logs.getParams());
                preparedStatement.setString(7, logs.getRequestIp());
                preparedStatement.setLong(8, logs.getTime());
                preparedStatement.setString(9, logs.getAddress());
                preparedStatement.setString(10, logs.getBrowser());
                preparedStatement.setString(11, logs.getAlarmInformation());
                preparedStatement.setString(12, logs.getAlarmThreshold());
                preparedStatement.setString(13, logs.getSystematics());
                preparedStatement.setString(14, logs.getUsername());
                return preparedStatement.executeUpdate() > 0;
            }
        });

        return success;
    }

}
