/*********************************************************************
 *
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 *
 *  [2015] - [2020] China Telecom Corporation Limited,
 *  All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 **********************************************************************/
/**
 *@Title: HBase2Hdfs.java
 *@Description: TODO
 *<AUTHOR>
 *@date 2020年1月16日
 */

package com.wzsec.dotask.mask.service.excute.hbase;

import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.io.ImmutableBytesWritable;
import org.apache.hadoop.hbase.mapreduce.TableReducer;
import org.apache.hadoop.io.Text;
import org.apache.log4j.Logger;

import java.io.IOException;

/**
 *@Description: HBaseDataMask2TableReducer (读取hbase表数据加密输出到table)
 *<AUTHOR>
 *@date 2020-1-16
 */
public class HBaseDataMask2TableReducer extends TableReducer<Text, Put, ImmutableBytesWritable> {

	private static Logger log = Logger.getLogger(HBaseDataMask2TableReducer.class);

    @Override
    public void reduce(Text key, Iterable<Put> values,
            Context context)
            throws IOException, InterruptedException {
        // 从得到的put中得到数据
        for (Put put : values) {
            // 往外写数据
            context.write(null, put);
        }
    }

}

