package com.wzsec.modules.api.rule;

import com.wzsec.modules.api.rule.utils.*;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> by dongs
 * @Description:通过程序进行检测
 * @date 2019-1-16
 */
class ProRule {

    private static final String[] arrayOne = {"有限", "无限", "集团", "合作", "会社", "合伙", "企业"};
    private static final String[] arrayTwo = {"厂", "公司", "行", "社", "中心", "网吧", "院", "部", "处", "股", "队", "委员会", "合作",
            "库", "局", "村", "团", "站", "店", "所", "段", "厅", "组", "工作室", "研究中心", "办公室", "商场", "大学"};
    private static final String[] arrayThree = {"Co.", "Limited", "LIMITED", "LTD.", "INC.", "LLC."};
    private static final String[] arrayException1 = {"委派代表"};
    private static final String[] arrayException2 = {"财政部", "国务院", "国资委"};
    private static final String[] arrayGuFen = {"国家股", "人股", "流通股", "公众股", "工股", "股东", "A股", "H股", "B股", "上市股",
            "基本社员 ", "集体股", "合作股", "募集股"};

    private static final String[] companyNameEnd = {"有限", "有限责任", "集团", "传媒", "无限", "股份", "合资"};

    /**
     * @Description:通过程序检测imsi
     * <AUTHOR> by dongs
     * @date 2019-7-9
     */
    public static boolean p_checkImsi(String Str) {
        if (Str.startsWith("460") && Str.length() == 15) {
            for (int i = 0; i < Str.length(); i++) {
                if (!Character.isDigit(Str.charAt(i))) { // 用char包装类中的判断数字的方法判断每一个字符
                    return false;
                }
            }
            return true;
        } else {
            return false;
        }

    }

    /**
     * @Description:通过程序检测手机号码
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkPhoneNumber(String parameter) {
        if (parameter.startsWith("1") && parameter.length() == 11) {
            if (checkingRule(parameter, "^1(3[0-9]|4[5-9]|5[0-3,5-9]|6[5,6]|7[0-8]|8[0-9]|9[1,8,9])\\d{8}$")) {
                return true;// 符合手机有问题,不符合手机号正确
            }
        }
        return false;
    }

    /**
     * @Description:通过程序检测移动手机号码
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkMobileNumber(String Str) {
        if (Str.startsWith("1") && Str.length() == 11) {
            if (checkingRule(Str, "^(13[4-9]|147|15[0-2,7-9]|178|18[2-4,7-8]|198)\\d{8}$")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }

    }

    /**
     * @Description:通过程序检测联通手机号码
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkUnicomNumber(String Str) {
        if (Str.startsWith("1") && Str.length() == 11) {
            if (checkingRule(Str, "^(13[0-2]|145|15[5-6]|166|17[156]|18[5-6])\\d{8}$")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }

    }

    /**
     * @Description:通过程序检测电信手机号码
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkTelecomNumber(String Str) {
        if (Str.startsWith("1") && Str.length() == 11) {
            if (checkingRule(Str, "^(133|149|153|17[37]|18[019]|199)\\d{8}$")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }

    }

    /**
     * @Description:通过程序检测固话
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkFixphone(String Str) {
        if (Str.startsWith("0") && (Str.length() > 9 || Str.length() < 14)) {
            if (checkingRule(Str, "^[0]{1}[0-9]{2,3}[-]?[0-9]{7,8}$")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }

    }

    /**
     * @Description:通过程序检测身份证号
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkIdcode(String Str) {
        if (Str.length() == 15 || Str.length() == 18) {
            if (checkingRule(Str, "^(^\\d{18}$)|(^\\d{17}(\\d|X|x))$")) {
                String IdCard = Str.toUpperCase();
                if (IdCardVerification.IDCardValidate(IdCard)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }

    }

    /**
     * @Description:通过程序检测地址
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkAddress(String strData) {
        if (strData.contains("区") || strData.contains("县")) {
            if (strData.contains("乡") || strData.contains("镇") || strData.contains("村") || strData.contains("组")
                    || strData.contains("号") || strData.contains("路")) {
                String address = String.valueOf(strData.charAt(0));
                if (address.equals("区") || address.equals("县") || address.equals("乡") || address.equals("镇")
                        || address.equals("村") || address.equals("组") || address.equals("号") || address.equals("路")) {
                    return false;
                } else {
                    /*
                     * for (String s : Const.whiteAddreList) {
                     * if(strData.contains(s)){ return false; } }
                     */
                    return true;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:通过程序检测姓名
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkName(String strData) {
        if (strData.length() > 1 && strData.length() < 5) {
            if (checkingRule(strData, "^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,4}$")) {
                /*
                 * if (Const.whiteNameList.contains(strData)) { // 在白名单中，返回false
                 * return false; } else if
                 * (Const.addreNameList.contains(strData)) { // 在地址名称列表中，返回false
                 * return false; } else{
                 */
                if (strData.length() < 4) {
                    /*
                     * List<String> nameList = Arrays.asList(Dict.firstName); //
                     * 百家姓 List<String> whiteNameList =
                     * Arrays.asList(Dict.nameWhiteStr); // 姓名白名单 List<String>
                     * addreNameList = Arrays.asList(Dict.adressChar); // 地址
                     */ // 判断首字是否是在百家姓中
                    if (Arrays.asList(Dict.firstName).contains(String.valueOf(strData.charAt(0)))) {
                        if (strData.endsWith("省") || strData.endsWith("市") || strData.endsWith("县")
                                || strData.endsWith("区") || strData.endsWith("镇") || strData.endsWith("乡")
                                || strData.endsWith("村") || strData.endsWith("路")) { // 结尾
                            // 以省、市、县、区、镇、乡、村、路结尾, 截取后在地址列表中
                            return false;
                            /*
                             * if
                             * (Const.addreNameList.contains(strData.substring(
                             * 0, strData.length() - 1))) { return false; } else
                             * { return true; }
                             */
                        } else {
                            return true;
                        }
                    } else {
                        List<String> doublesurnameList = Arrays.asList(Dict.doubleSurName);
                        if (doublesurnameList.contains(strData.substring(0, 2))) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                } else {
                    List<String> doublesurnameList = Arrays.asList(Dict.doubleSurName);
                    if (doublesurnameList.contains(strData.substring(0, 2))) {
                        return true;
                    } else {
                        return false;
                    }
                }
                // }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:通过程序检测邮箱
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkEmail(String Str) {
        if (Str.contains("@") && Str.contains(".")) {
            if (checkingRule(Str, "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:通过程序检测AES
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkAES(String Str) {
        if (Str.length() == 32) {
            if (isContainLetterDigit(Str, "1")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:通过程序检测3DES
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_check3DES(String Str) {
        // 12和24位 最后一位是= 数字和大小写字母必须有 不包含汉字 没有|和,和分号
        // 32位 数字和大小写字母 不包含汉字 没有|和,和分号
        boolean isRight = false;// 定义一个boolean值，用来表示最后的结果
        boolean isDigit = false;// 定义一个boolean值，用来表示是否包含数字
        boolean isUpperCase = false;// 定义一个boolean值，用来表示是否包含大写字母
        boolean isLowerCase = false;// 定义一个boolean值，用来表示是否包含小写字母
        if (Str.length() == 12 || Str.length() == 24) {
            if (!"=".equals(Str.substring(Str.length() - 1))) {
                return false;
            }
            for (int i = 0; i < Str.length(); i++) {
                char s = Str.charAt(i);
                if (Character.isDigit(s)) { // 用char包装类中的判断数字的方法判断每一个字符
                    isDigit = true;
                } else if (Character.isUpperCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isUpperCase = true;
                } else if (Character.isLowerCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isLowerCase = true;
                } else if (String.valueOf(s).matches("[\u4e00-\u9fa5]")) { // 判断不包含汉字
                    return false;
                } else if ("|".equals(String.valueOf(s)) || ",".equals(String.valueOf(s))
                        || ";".equals(String.valueOf(s))) {
                    return false;
                } else if (Str.contains(" ")) {
                    return false;
                }
            }
            isRight = isDigit && isUpperCase && isLowerCase;
        } else if (Str.length() == 32) {
            for (int i = 0; i < Str.length(); i++) {
                char s = Str.charAt(i);
                if (Character.isDigit(s)) { // 用char包装类中的判断数字的方法判断每一个字符
                    isDigit = true;
                } else if (Character.isUpperCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isUpperCase = true;
                } else if (Character.isLowerCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isLowerCase = true;
                } else if (String.valueOf(s).matches("[\u4e00-\u9fa5]")) { // 判断不包含汉字
                    return false;
                } else if ("|".equals(String.valueOf(s)) || ",".equals(String.valueOf(s))
                        || ";".equals(String.valueOf(s))) { //
                    return false;
                } else if (Str.contains(" ")) {
                    return false;
                }
            }
            isRight = isDigit && isUpperCase && isLowerCase;
        } else {
            return false;
        }

        return isRight;
    }

    /**
     * @Description:通过程序检测 MD5
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkMD5(String Str) {
        if (Str.length() == 32) {
            if (isContainLetterDigit(Str, "2")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:通过程序检测 sha256
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkSHA256(String Str) {
        if (Str.length() == 64) {
            if (isContainLetterDigit(Str, "2")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description 校验是否是真实IMEI
     * <AUTHOR>
     * @version 下午5:36:38
     */
    public static boolean p_checkIMEI(String strData) {
        if (strData.matches("^\\d{15}$")) {
            String str14Bit = strData.substring(0, 14); // 截取前14位
            String str15th = strData.substring(14, 15); // 截取第15位
            // 计算出第15位检验位
            String checkBit = DeviceUtil.getIMEI15(str14Bit);
            if (str15th.equals(checkBit)) {
                return true; // 校验通过
            } else {
                return false; // 校验不通过
            }
        } else if (strData.matches("^\\d{14}$") || strData.matches("^\\d{16}$")) {
            return true; // 无法校验，先怀疑
        } else {
            return false;
        }

    }

    /**
     * @Description 校验是否是真实MEID
     * <AUTHOR>
     * @version 下午5:36:38
     */
    public static boolean p_checkMEID(String strData) {
        if (strData.matches("^(A0|A1|A2|97|98|99)[A-F0-9]{13}$")) {
            String str14Bit = strData.substring(0, 14); // 截取前14位
            String str15th = strData.substring(14, 15); // 截取第15位
            // 计算出第15位检验位
            String checkBit = DeviceUtil.getMEID15(str14Bit);
            if (str15th.equals(checkBit)) {
                return true; // 校验通过
            } else {
                return false; // 校验不通过
            }
        } else if (strData.matches("^(A0|A1|A2|97|98|99)[A-F0-9]{12}$")
                || strData.matches("^(A0|A1|A2|97|98|99)[A-F0-9]{14}$")) {
            int checkStrLength = strData.length();
            int checkStrReplace0Later = strData.replace("0", "").length();
            // 过滤去除包含一半字符都是“0”的或包含连续4个都是“0”的疑似MEID
            if ((checkStrLength - checkStrReplace0Later) >= (strData.length() / 2) || strData.contains("0000")) {
                return false;
            }
            return true; // 可能是14位、16位，有可能是，先怀疑
        } else {
            return false;
        }
    }

    /**
     * @param passport
     * @return boolean
     * @Description 开户许可证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午6:16:00
     */
    public static boolean p_checkAccountOpeningPermitNo(String accountOpeningPermitNo) {
        if (StringUtils.isEmpty(accountOpeningPermitNo))
            return false;
        // 由1位英文字母+13位数字组成，编码规则为：英文字母（J基本户、L临时户、Z专用户）+XXXX（地区代码）+ XXXXXXX（顺序号）+
        // XX（版本号）；最后2位表示第几个版本：这个要解释一下，比如你新开，那么最后2位就是01；。
        // 核准号:J4693000413701
        String regex = "^[JLZ]{1}\\d{4}\\d{7}\\d{2}$";
        return accountOpeningPermitNo.matches(regex);
    }

    /**
     * @param passport：护照号
     * @return boolean
     * @Description 护照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:59:01
     */
    public static boolean p_checkPassPortCard(String passport) {
        if (StringUtils.isEmpty(passport))
            return false;
        // 护照
        // 规则： 14/15开头 + 7位数字, G + 8位数字, P + 7位数字, S/D + 7或8位数字,等
        // 样本： *********, G12345678, P1234567
        String regex = "^((1[45]\\d{7})|(G\\d{8})|(P\\d{7})|(S\\d{7,8}))?$";
        return passport.matches(regex);
    }

    /**
     * @param officerNo：军官证号码
     * @return boolean
     * @Description 军官证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午5:00:16
     */
    public static boolean p_checkOfficerCard(String officerNo) {
        if (StringUtils.isEmpty(officerNo))
            return false;
        // 军官证
        // 规则： 军/兵/士/文/职/广/（其他中文） + "字第" + 4到8位字母或数字 + "号"
        // 样本： 军字第2001988号, 士字第P011816X号
        String regex = "^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$";
        return officerNo.matches(regex);
    }

    /**
     * @param HMPassCheck：港澳通行证号码
     * @return boolean
     * @Description 港澳通行证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午5:08:41
     */
    public static boolean p_checkHMPassCheck(String HMPassCheck) {
        if (StringUtils.isEmpty(HMPassCheck))
            return false;
        // 港澳居民来往内地通行证
        // 规则： H/M + 10位或8位数字
        // 样本： H1234567890
        String regex = "^[HMhm]{1}([0-9]{10}|[0-9]{8})$";
        return HMPassCheck.matches(regex);
    }

    /**
     * @param taxationNo：税务登记证号码
     * @return boolean
     * @Description 税务登记证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午5:10:11
     */
    public static boolean p_checkTaxationNo(String taxationNo) {
        if (StringUtils.isEmpty(taxationNo))
            return false;
        // 税务登记号编码规则是:纳税人识别号是指税务登记号（分组织机构代码和身份证注册）：
        // 1、税务登记证号由六位行政区划代码加九位组织机构代码组成。
        // 组织机构代码是质量技术监督局发放的组织机构代码证上的九位数字与大写拉丁字母，这个组合是唯一的。
        // 如：110116553110947
        // 2、个体经营者办理税务登记证的由：旧的身份证15位码加5个0或新的身份证18位码加2个0；
        // 如果同一身份证办多户税务登记的，则第二户的税务登记证后两位改为“01”，第三户改为“02”。
        // 如：34222187060625900001，34222119870606259601
        if (taxationNo.length() == 15) {
            String addressNo = taxationNo.substring(0, 6);
            String regex = "\\d{6}";
            if (addressNo.matches(regex)) {
                String code = taxationNo.substring(6, 14) + "-" + taxationNo.substring(14);
                return p_checkValidEntpCode(code);
            }
            return false;
        } else if (taxationNo.length() == 20) {
            String idcard = taxationNo.substring(0, 18);
            String endStr = "";
            String regex = "";
            try {
                if (IdCardVerification.IDCardValidate(idcard)) {
                    endStr = taxationNo.substring(18, 20);
                    regex = "\\d{2}";
                    return endStr.matches(regex);
                } else {
                    idcard = taxationNo.substring(0, 15);
                    if (IdCardVerification.IDCardValidate(idcard)) {
                        endStr = taxationNo.substring(15, 20);
                        regex = "\\d{5}";
                        return endStr.matches(regex);
                    } else {
                        return false;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return false;
    }

    /**
     * @param code：组织机构代码，如：78305975-3,79013765-X
     * @return boolean
     * @Description 组织机构代码校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:17:49
     */
    public static final boolean p_checkValidEntpCode(String code) {
        // 由8位数字（或大写字母）和1位数字（或大写字母）组成

        // 1.全国组织机构代码由八位数字（或大写拉丁字母）本体代码和一位数字（或大写拉丁字母）校验码组成。
        // 本体代码采用系列（即分区段）顺序编码方法。校验码按照以下公式计算：
        // C9=11-MOD(∑Ci(i=1→8)×Wi,11)
        // 式中： MOD——代表求余函数；
        // i——代表代码字符从左至右位置序号；
        // Ci——代表第i位上的代码字符的值（具体代码字符见附表）；
        // C9——代表校验码；
        // Wi——代表第i位上的加权因子；
        // 当C9的值为10时，校验码应用大写的拉丁字母X表示；当C9的值为11时校验码用0表示。
        // 2.代码的表示形式
        // 为便于人工识别，应使用一个连字符“—”分隔本体代码与校验码。机读时，连字符省略。表示形式为：
        // xxxxxxxx—X
        // 3.自定义区
        // 为满足各系统管理上的特殊需要，规定本体代码PDY00001至PDY99999为自定义区，供各系统编制内部组织机构代码使用。自定义区内编制的组织机构代码不作为个系统之间信息交换的依据。
        if (StringUtils.isEmpty(code))
            return false;
        int[] ws = {3, 7, 9, 10, 5, 8, 4, 2};
        String str = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String regex = "^([0-9A-Z]){8}-[0-9|X]$";
        if (!code.matches(regex)) {
            return false;
        }
        int sum = 0;
        for (int i = 0; i < 8; i++) {
            sum += str.indexOf(String.valueOf(code.charAt(i))) * ws[i];
        }
        // System.out.println("sum is " + sum);
        // System.out.println("sum % 11 is " + sum % 11);
        int c9 = 11 - (sum % 11);
        String sc9 = String.valueOf(c9);
        if (11 == c9) {
            sc9 = "0";
        } else if (10 == c9) {
            sc9 = "X";
        }
        // System.out.println("sc9 is " + sc9);
        return sc9.equals(String.valueOf(code.charAt(9)));
    }

    /**
     * @param carnumber：车牌号
     * @return boolean
     * @Description 车牌号校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:27:41
     */
    public static boolean p_checkCarnumberNO(String carnumber) {
        /*
         * 一、
         * 1.传统车牌。第1位为省份简称（汉字），第二位为发牌机关代号（A-Z的字母）第3到第7位为序号（由字母或数字组成，但不存在字母I和O，
         * 防止和数字1、0混淆，另外最后一位可能是“挂学警港澳使领”中的一个汉字）;
         * 2.新能源车牌。第1位和第2位与传统车牌一致，第3到第8位为序号（比传统车牌多一位）。新能源车牌的序号规则如下：
         * 小型车：第1位只能是字母D或F，第2为可以是数字或字母，第3到6位必须是数字。 大型车：第1位到第5位必须是数字，第6位只能是字母D或F。
         * 粤B12345，粤Z1234港
         *
         * 二、 1.常规车牌号：仅允许以汉字开头，后面可录入六个字符，由大写英文字母和阿拉伯数字组成。如：粤B12345；
         * 2.武警车牌：允许前两位为大写英文字母，后面可录入五个或六个字符，由大写英文字母和阿拉伯数字组成，
         * 其中第三位可录汉字也可录大写英文字母及阿拉伯数字，第三位也可空，如：WJ警00081、WJ京1234J、WJ1234X。
         * 3.最后一个为汉字的车牌：允许以汉字开头，后面可录入六个字符，前五位字符，由大写英文字母和阿拉伯数字组成，而最后一个字符为汉字，汉字包括“
         * 挂”、“学”、“警”、“军”、“港”、“澳”。如：粤Z1234港。
         * 4.新军车牌：以两位为大写英文字母开头，后面以5位阿拉伯数字组成。如：BA12345。
         */
        // 一
        String PlateNumMatch = "^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$";
        // 二
        // String carnumRegex =
        // "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[警京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{0,1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$";
        if (StringUtils.isEmpty(carnumber))
            return false;
        else
            return carnumber.matches(PlateNumMatch);
    }

    /**
     * @param ip：IP
     * @return boolean
     * @Description IP校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:29:18
     */
    public static boolean p_checkIP(String ip) {
        if (StringUtils.isEmpty(ip))
            return false;
        String pattern = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";
        return ip.matches(pattern);
    }

    /**
     * @param bankCard：银行卡号
     * @return boolean
     * @Description 银行卡校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:32:16
     */
    public static boolean p_checkBankCard(String bankCard) {
        /*
         * 校验过程： 1、从卡号最后一位数字开始，逆向将奇数位(1、3、5等等)相加。
         * 2、从卡号最后一位数字开始，逆向将偶数位数字，先乘以2（如果乘积为两位数，将个位十位数字相加，即将其减去9），再求和。
         * 3、将奇数位总和加上偶数位总和，结果应该可以被10整除。
         */
        if (StringUtils.isEmpty(bankCard))
            return false;
        if (bankCard.length() < 15 || bankCard.length() > 19) {
            return false;
        }
        char bit = getBankCardCheckCode(bankCard.substring(0, bankCard.length() - 1));
        if (bit == 'N') {
            return false;
        }
        return bankCard.charAt(bankCard.length() - 1) == bit;
    }

    /**
     * 从不含校验位的银行卡卡号采用 Luhm校验算法获得校验位
     *
     * @param nonCheckCodeBankCard
     * @return
     */
    private static char getBankCardCheckCode(String nonCheckCodeBankCard) {
        if (nonCheckCodeBankCard == null || nonCheckCodeBankCard.trim().length() == 0
                || !nonCheckCodeBankCard.matches("\\d+")) {
            // 如果传的不是数据返回N
            return 'N';
        }
        char[] chs = nonCheckCodeBankCard.trim().toCharArray();
        int luhmSum = 0;
        for (int i = chs.length - 1, j = 0; i >= 0; i--, j++) {
            int k = chs[i] - '0';
            if (j % 2 == 0) {
                k *= 2;
                k = k / 10 + k % 10;
            }
            luhmSum += k;
        }
        return (luhmSum % 10 == 0) ? '0' : (char) ((10 - luhmSum % 10) + '0');
    }

    /**
     * @param businesslicense： 营业执照15位或18位
     * @return boolean
     * @Description 营业执照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:43:22
     */
    public static boolean p_checkBusinesslicense(String businesslicense) {

        boolean result = false;
        try {
            if (StringUtils.isEmpty(businesslicense))
                return false;
            if (businesslicense.length() == 15) {
                // 代码结构工商注册号由14位数字本体码和1位数字校验码组成，
                // 其中本体码从左至右依次为：6位首次登记机关码、8位顺序码，1位数字校验码组成。
                // 110108000000016
                result = p_checkBusinesslicense15(businesslicense);
            } else if (businesslicense.length() == 18) {
                // 统一代码由十八位阿拉伯数字或大写英文字母（不使用I、O、Z、S、V）组成，
                // 包括第1位登记管理部门代码、第2位机构类别代码、第3位~第8位登记管理机关行政区划码、第9位~第17位主体标识码（组织机构代码）、第18位校验码五个部门。
                // 913301097909487005
                result = p_checkBusinesslicense18(businesslicense);
            }
            return result;
        } catch (Exception e) {
            // TODO Auto-generated catch block
            return result;
        }
    }

    /**
     * @param businesslicense
     * @return boolean
     * @Description 15位营业执照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:44:23
     */
    private static boolean p_checkBusinesslicense15(String businesslicense) {
        if ("".equals(businesslicense) || " ".equals(businesslicense)) {
            return false;
        } else if (businesslicense.length() != 15) {
            return false;
        }
        // 代码结构工商注册号由14位数字本体码和1位数字校验码组成，其中本体码从左至右依次为：6位首次登记机关码、8位顺序码，1位数字校验码组成。
        String businesslicensePrex14 = businesslicense.substring(0, 14);// 获取营业执照注册号前14位数字用来计算校验码
        String businesslicense15 = businesslicense.substring(14, businesslicense.length());// 获取营业执照号的校验码
        char[] chars = businesslicensePrex14.toCharArray();
        int[] ints = new int[chars.length];
        for (int i = 0; i < chars.length; i++) {
            ints[i] = Integer.parseInt(String.valueOf(chars[i]));
        }
        getCheckCode(ints);
        if (businesslicense15.equals(getCheckCode(ints) + "")) {// 比较
            return true;
        }
        return false;
    }

    /**
     * @param ints
     * @return int
     * @Description 获取 营业执照注册号的校验码
     * <AUTHOR>
     * @date 2019年11月14日 下午4:44:02
     */
    private static int getCheckCode(int[] ints) {
        if (null != ints && ints.length > 1) {
            int ti = 0;
            int si = 0;// pi|11+ti
            int cj = 0;// （si||10==0？10：si||10）*2
            int pj = 10;// pj=cj|11==0?10:cj|11
            for (int i = 0; i < ints.length; i++) {
                ti = ints[i];
                pj = (cj % 11) == 0 ? 10 : (cj % 11);
                si = pj + ti;
                cj = (0 == si % 10 ? 10 : si % 10) * 2;
                if (i == ints.length - 1) {
                    pj = (cj % 11) == 0 ? 10 : (cj % 11);
                    return pj == 1 ? 1 : 11 - pj;
                }
            }
        }
        return -1;

    }

    /**
     * @param code
     * @return boolean
     * @Description 18位营业执照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:43:07
     */
    private static boolean p_checkBusinesslicense18(String code) {

        // 第一部分（第1位）：登记管理部门代码，使用阿拉伯数字或英文字母表示。例如，机构编制、民政、工商三个登记管理部门分别使用1、5、9表示，
        // 其他登记管理部门可使用相应阿拉伯数字或英文字母表示。
        // 第二部分（第2位）：机构类别代码，使用阿拉伯数字或英文字母表示。登记管理部门根据管理职能，确定在本部门登记的机构类别编码。
        // 例如，机构编制部门可用1表示机关单位，2表示事业单位，3表示由中央编办直接管理机构编制的群众团体；
        // 民政部门可用1表示社会团体，2表示民办非企业单位，3表示基金会；工商部门可用1表示企业，2表示个体工商户，3表示农民专业合作社。
        // 第三部分（第3—8位）：登记管理机关行政区划码，使用阿拉伯数字表示。
        // 例如，国家用100000，北京用110000，注册登记时由系统自动生成，体现法人和其他组织注册登记及其登记管理机关所在地，
        // 既满足登记管理部门按地区管理需求，也便于社会对注册登记主体所在区域进行识别。
        // （参照《中华人民共和国行政区划代码》„GB/T 2260—2007‟）
        // 第四部分（第9—17位）：主体标识码（组织机构代码），使用阿拉伯数字或英文字母表示。（参照《全国组织机构代码编制规则》„GB11714—1997‟）
        // 第五部分（第18位）：校验码，使用阿拉伯数字或英文字母表示。
        String str = "0123456789ABCDEFGHJKLMNPQRTUWXY";
        int[] ws = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28};
        String[] codes = new String[2];
        codes[0] = code.substring(0, code.length() - 1);
        codes[1] = code.substring(code.length() - 1, code.length());
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += str.indexOf(codes[0].charAt(i)) * ws[i];
        }
        int c18 = 31 - (sum % 31);
        if (c18 == 30) {
            // System.out.println("第18位 == 30");
            c18 = 'Y';
        } else if (c18 == 31) {
            // System.out.println("第18位 == 31");
            c18 = '0';
        }
        if (!codes[1].equals("" + c18)) {
            // System.out.println("社会信用代码有误！" + c18);
            return false;
        }
        return true;
    }

    /**
     * @param data：社会统一信用代码
     * @return boolean
     * @Description 社会统一信用代码校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:49:03
     */
    public static boolean p_checkUnifiedCreditCode(String unifiedCreditCode) {
        if (StringUtils.isEmpty(unifiedCreditCode))
            return false;
        // 统一代码由十八位的阿拉伯数字或大写英文字母（不使用I、O、Z、S、V）组成，
        // 包括第1位登记管理部门代码、
        // 第2位机构类别代码、
        // 第3位～第8位登记管理机关行政区划码、
        // 第9位～第17位主体标识码（组织机构代码）、
        // 第18位校验码五个部分。
        // 91110108MA00E1PU0C
        return UnifiedCreditCodeUtils.validateUnifiedCreditCode(unifiedCreditCode);
    }

    /**
     * 必须包含大小写字母及数字
     *
     * @param str
     * @param type 1为大写字母 ，2为小写字母 ,3为大小写字母
     * @return
     */
    public static boolean isContainLetterDigit(String str, String type) {
        boolean isRight = false;// 定义一个boolean值，用来表示最后的结果
        boolean isDigit = false;// 定义一个boolean值，用来表示是否包含数字
        boolean isUpperCase = false;// 定义一个boolean值，用来表示是否包含大写字母
        boolean isLowerCase = false;// 定义一个boolean值，用来表示是否包含小写字母
        for (int i = 0; i < str.length(); i++) {
            if (Character.isDigit(str.charAt(i))) { // 用char包装类中的判断数字的方法判断每一个字符
                isDigit = true;
            } else if (Character.isUpperCase(str.charAt(i))) { // 用char包装类中的判断字母的方法判断每一个字符
                isUpperCase = true;
            } else if (Character.isLowerCase(str.charAt(i))) { // 用char包装类中的判断字母的方法判断每一个字符
                isLowerCase = true;
            } else {
                return isRight;
            }
        }
        if (type.equals("1")) {
            isRight = isDigit && isUpperCase;
        } else if (type.equals("2")) {
            isRight = isDigit && isLowerCase;
        } else {
            isRight = isDigit && isUpperCase && isLowerCase;
        }
        return isRight;
    }

    /**
     * @Description 经度校验 经度longitude: -180.0000~180.000000000000000 范围内
     * <AUTHOR> by zhangkx
     * @date 2019-8-2
     */
    public static boolean p_checkLongitude(String longitude) {
        if (!longitude.contains("."))
            return false;
        String regla = "-?(([0-9]|([1-9][0-9])|(1[0-7][0-9])).(\\d{4}|\\d{15}))|-?((180.0000)|(180.000000000000000))";
        longitude = longitude.trim();
        return longitude.matches(regla);
    }

    /**
     * @Description 经纬度校验 纬度latitude： -90.0000~90.000000000000000 范围内
     * <AUTHOR> by zhangkx
     * @date 2019-8-2
     */
    public static boolean p_checkLatitude(String latitude) {
        if (!latitude.contains("."))
            return false;
        String reglo = "-?(([0-9]|([1-8][0-9])).(\\d{4}|\\d{15}))|-?((90.0000)|(90.000000000000000))";
        latitude = latitude.trim();
        return latitude.matches(reglo);
    }

    /**
     * @Description 经纬度校验 纬度latitude： -90.0000~90.000000000000000 范围内
     * <AUTHOR> by zhangkx
     * @date 2019-8-2
     */
    public static boolean p_checkLongitudeAndLatitude(String longitudeAndLatitude) {
        String[] array = null;
        boolean flag = false;
        if (longitudeAndLatitude != null && !"".equals(longitudeAndLatitude)) {
            if (longitudeAndLatitude.contains(":")) {
                array = longitudeAndLatitude.split(":");
            } else if (longitudeAndLatitude.contains(",")) {
                array = longitudeAndLatitude.split(",");
            } else if (longitudeAndLatitude.contains(" ")) {
                array = longitudeAndLatitude.split(" ");
            }

            if (array == null) {
                flag = p_checkLongitude(longitudeAndLatitude);
                if (flag) {
                    flag = p_checkLatitude(longitudeAndLatitude);
                }
            } else if (array.length == 2) {
                flag = p_checkLongitude(array[0]);
                if (flag) {
                    flag = p_checkLatitude(array[1]);
                }
            }
        }
        return flag;
    }

    /**
     * @Description 验证日期格式
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static boolean p_checkDate(String strDate) {
        Pattern pattern = Pattern.compile(
                "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1-2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$");
        Matcher m = pattern.matcher(strDate);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @Description 验证公司或姓名
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static boolean p_validateCompanyOrName(String companyName) {

        /*
         * Map<String, String> map = new HashMap<>(); if(companyName==null){
         * map.put("code", "0002"); map.put("msg", "字符串为空，无法判断"); return map; }
         */
        // 根据字符串判断，是公司 还是 个人

        boolean flag = false;
        if (companyName != null && !"".equals(companyName)) {
            if (companyName.length() > 4) {
                for (int i = 0; i < arrayOne.length; i++) {
                    if (companyName.contains(arrayOne[i])) {
                        flag = true;
                        break;
                    }
                }

                if (!flag) {
                    for (int i = 0; i < arrayTwo.length; i++) {
                        if (companyName.contains(arrayTwo[i])) {
                            flag = true;
                            break;
                        }
                    }
                }

                if (!flag) {
                    for (int i = 0; i < arrayThree.length; i++) {
                        if (companyName.contains(arrayThree[i])) {
                            flag = true;
                            break;
                        }
                    }
                }

                if (!flag) {
                    for (int i = 0; i < arrayException1.length; i++) {
                        if (companyName.contains(arrayException1[i])) {
                            flag = true;
                            break;
                        }
                    }
                }

                if (!flag) {
                    for (int i = 0; i < arrayGuFen.length; i++) {
                        if (companyName.contains(arrayGuFen[i])) {
                            flag = true;
                            break;
                        }
                    }
                }
            }

            if (!flag) {
                for (int i = 0; i < arrayException2.length; i++) {
                    if (companyName.contains(arrayException2[i])) {
                        flag = true;
                        break;
                    }
                }
            }

            if (!flag) {
                if (companyName.length() == 4 && companyName.contains("大学")) {
                    flag = true;
                }
            }
        }
        /*
         * //是公司 if(flag){ map.put("type", "ent"); map.put("code", "0000");
         * map.put("msg", "企业"); }else{ map.put("type", "person");
         * map.put("code", "0001"); map.put("msg", "是人"); }
         */

        return flag;
    }

    /**
     * @Description 公司名称校验
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static boolean p_checkCompanyName(String companyName) {
        if (companyName.endsWith("公司")) {
            for (int i = 0; i < companyNameEnd.length; i++) {
                if (companyName.contains(companyNameEnd[i])) {
                    return true;
                }
            }
        }
        return false;
    }

    /***
     * @Description 判断格式是否正确
     * @param rsContent
     * @return
     * @throws Exception
     */
    public static boolean checkingRule(String parameter, String regexps) {
        boolean paraValid = false;
        Pattern pattern = Pattern.compile(regexps);
        Matcher matcher = pattern.matcher(parameter.trim());
        // 字符串是否与正则表达式相匹配
        if (matcher.matches()) {
            paraValid = true;
        }
        return paraValid;
    }

    /***
     * @Description 通过程序检测imsi
     * @param parameter
     * @return
     * @throws Exception
     */
    public static boolean p_checkIMSI(String parameter) {
        if (parameter.startsWith("460") && parameter.length() == 15) {
            if (checkingRule(parameter, "^460[0-9]{12}$")) {
                return true;// 符合imsi有问题,不符合imsi号正确
            }
        }
        return false;
    }

    /**
     * @Description:省份校验
     * <AUTHOR>
     * @date 2020年5月20日18:05:44
     */
    public static boolean p_checkProvinces(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        return data.matches(
                "^(北京市|天津市|上海市|重庆市|河北省|山西省|辽宁省|吉林省|黑龙江省|江苏省|浙江省|安徽省|福建省|江西省|山东省|河南省|湖北省|湖南省|广东省|海南省|四川省|贵州省|云南省|陕西省|甘肃省|青海省|台湾省|内蒙古自治区|广西壮族自治区|西藏自治区|宁夏回族自治区|新疆维吾尔自治区|香港特别行政区|澳门特别行政区)$");
    }

    /**
     * @Description:民族校验
     * <AUTHOR>
     * @date 2020-5-20 17:47:22
     */
    public static boolean p_checkNational(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        return data.matches(
                "^((汉|满|蒙古|回|藏|维吾尔|苗|彝|壮|布依|侗|瑶|白|土家|哈尼|哈萨克|傣|黎|傈僳|佤|畲|高山|拉祜|水|东乡|纳西|景颇|柯尔克孜|土|达斡尔|仫佬|羌|布朗|撒拉|毛南|仡佬|锡伯|阿昌|普米|朝鲜|塔吉克|怒|乌孜别克|俄罗斯|鄂温克|德昂|保安|裕固|京|塔塔尔|独龙|鄂伦春|赫哲|门巴|珞巴|基诺)|(汉族|满族|蒙古族|回族|藏族|维吾尔族|苗族|彝族|壮族|布依族|侗族|瑶族|白族|土家族|哈尼族|哈萨克族|傣族|黎族|傈僳族|佤族|畲族|高山族|拉祜族|水族|东乡族|纳西族|景颇族|柯尔克孜族|土族|达斡尔族|仫佬族|羌族|布朗族|撒拉族|毛南族|仡佬族|锡伯族|阿昌族|普米族|朝鲜族|塔吉克族|怒族|乌孜别克族|俄罗斯族|鄂温克族|德昂族|保安族|裕固族|京族|塔塔尔族|独龙族|鄂伦春族|赫哲族|门巴族|珞巴族|基诺族))$");
    }

    /**
     * @Description:企业名称校验
     * <AUTHOR>
     * @date 2019-4-28
     */
    public static boolean p_checkEnterpriseName(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.matches("^\\w+$"))
            return false;
        if (data.endsWith("公司"))
            return true;
        if (data.endsWith("办事处"))
            return true;
        if (data.endsWith("代表处")) {
            return true;
        }
        return data.endsWith("厂") && ((data.indexOf("市") > 0) || (data.indexOf("乡") > 0) || (data.indexOf("区") > 0)
                || (data.indexOf("村") > 0) || (data.indexOf("县") > 0) || (data.indexOf("省") > 0)
                || (data.indexOf("盟") > 0) || (data.indexOf("旗") > 0));
    }

    /**
     * @Description:QQ号校验
     * <AUTHOR>
     * @date 2020-5-20 17:35:33
     */
    public static boolean p_checkQQ(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        // 5-11位纯数字，且不能0开头
        if (data.length() >= 5 && data.length() <= 11) {
            return data.matches("[1-9]([0-9]{4,10})");
        }
        return false;
    }

    /**
     * @Description:车辆识别号码校验
     * <AUTHOR>
     * @date 2020-5-20 17:50:19
     */
    public static boolean p_checkVIN(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        return VinUtil.isValidVin(data);
    }

    /**
     * @return boolean
     * @Description IPv6校验(未测试)
     * <AUTHOR>
     * @date 2020-5-20 17:43:16
     */
    public static boolean p_checkIPv6(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        String regex = "^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?\\s*$";
        return data.matches(regex);
    }

    /**
     * @param data 数据
     * @return boolean
     * @description: 通用检测, 不对字段进行校验
     * @author: JOY
     * @date: 2022-02-23
     */
    public static boolean p_general(String data) {
        return true;
    }

}
