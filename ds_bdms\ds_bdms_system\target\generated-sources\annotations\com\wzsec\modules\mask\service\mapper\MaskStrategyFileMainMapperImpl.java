package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyFileMain;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileMainDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:01+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskStrategyFileMainMapperImpl implements MaskStrategyFileMainMapper {

    @Override
    public MaskStrategyFileMainDto toDto(MaskStrategyFileMain entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFileMainDto maskStrategyFileMainDto = new MaskStrategyFileMainDto();

        maskStrategyFileMainDto.setCreatetime( entity.getCreatetime() );
        maskStrategyFileMainDto.setCreateuser( entity.getCreateuser() );
        maskStrategyFileMainDto.setEnabled( entity.getEnabled() );
        maskStrategyFileMainDto.setExtractcolum( entity.getExtractcolum() );
        maskStrategyFileMainDto.setId( entity.getId() );
        maskStrategyFileMainDto.setRemark( entity.getRemark() );
        maskStrategyFileMainDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFileMainDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFileMainDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFileMainDto.setSparefield4( entity.getSparefield4() );
        maskStrategyFileMainDto.setStatus( entity.getStatus() );
        maskStrategyFileMainDto.setStrategydesc( entity.getStrategydesc() );
        maskStrategyFileMainDto.setStrategyname( entity.getStrategyname() );
        maskStrategyFileMainDto.setStrategytype( entity.getStrategytype() );
        maskStrategyFileMainDto.setTotalcolumn( entity.getTotalcolumn() );
        maskStrategyFileMainDto.setUpdatetime( entity.getUpdatetime() );
        maskStrategyFileMainDto.setUpdateuser( entity.getUpdateuser() );

        return maskStrategyFileMainDto;
    }

    @Override
    public List<MaskStrategyFileMainDto> toDto(List<MaskStrategyFileMain> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFileMainDto> list = new ArrayList<MaskStrategyFileMainDto>( entityList.size() );
        for ( MaskStrategyFileMain maskStrategyFileMain : entityList ) {
            list.add( toDto( maskStrategyFileMain ) );
        }

        return list;
    }

    @Override
    public MaskStrategyFileMain toEntity(MaskStrategyFileMainDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyFileMain maskStrategyFileMain = new MaskStrategyFileMain();

        maskStrategyFileMain.setCreatetime( dto.getCreatetime() );
        maskStrategyFileMain.setCreateuser( dto.getCreateuser() );
        maskStrategyFileMain.setEnabled( dto.getEnabled() );
        maskStrategyFileMain.setExtractcolum( dto.getExtractcolum() );
        maskStrategyFileMain.setId( dto.getId() );
        maskStrategyFileMain.setRemark( dto.getRemark() );
        maskStrategyFileMain.setSparefield1( dto.getSparefield1() );
        maskStrategyFileMain.setSparefield2( dto.getSparefield2() );
        maskStrategyFileMain.setSparefield3( dto.getSparefield3() );
        maskStrategyFileMain.setSparefield4( dto.getSparefield4() );
        maskStrategyFileMain.setStatus( dto.getStatus() );
        maskStrategyFileMain.setStrategydesc( dto.getStrategydesc() );
        maskStrategyFileMain.setStrategyname( dto.getStrategyname() );
        maskStrategyFileMain.setStrategytype( dto.getStrategytype() );
        maskStrategyFileMain.setTotalcolumn( dto.getTotalcolumn() );
        maskStrategyFileMain.setUpdatetime( dto.getUpdatetime() );
        maskStrategyFileMain.setUpdateuser( dto.getUpdateuser() );

        return maskStrategyFileMain;
    }

    @Override
    public List<MaskStrategyFileMain> toEntity(List<MaskStrategyFileMainDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyFileMain> list = new ArrayList<MaskStrategyFileMain>( dtoList.size() );
        for ( MaskStrategyFileMainDto maskStrategyFileMainDto : dtoList ) {
            list.add( toEntity( maskStrategyFileMainDto ) );
        }

        return list;
    }
}
