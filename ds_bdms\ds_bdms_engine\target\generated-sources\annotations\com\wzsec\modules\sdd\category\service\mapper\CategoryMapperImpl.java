package com.wzsec.modules.sdd.category.service.mapper;

import com.wzsec.modules.sdd.category.domain.Category;
import com.wzsec.modules.sdd.category.service.dto.CategoryDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CategoryMapperImpl implements CategoryMapper {

    @Override
    public CategoryDto toDto(Category entity) {
        if ( entity == null ) {
            return null;
        }

        CategoryDto categoryDto = new CategoryDto();

        categoryDto.setCategory( entity.getCategory() );
        categoryDto.setCategoryname( entity.getCategoryname() );
        categoryDto.setCreatetime( entity.getCreatetime() );
        categoryDto.setCreateuser( entity.getCreateuser() );
        categoryDto.setData( entity.getData() );
        categoryDto.setExample( entity.getExample() );
        categoryDto.setId( entity.getId() );
        categoryDto.setPid( entity.getPid() );
        categoryDto.setRuleid( entity.getRuleid() );
        categoryDto.setSparefield1( entity.getSparefield1() );
        categoryDto.setSparefield2( entity.getSparefield2() );
        categoryDto.setSparefield3( entity.getSparefield3() );
        categoryDto.setSparefield4( entity.getSparefield4() );
        categoryDto.setSparefield5( entity.getSparefield5() );
        categoryDto.setType( entity.getType() );
        categoryDto.setUpdatetime( entity.getUpdatetime() );
        categoryDto.setUpdateuser( entity.getUpdateuser() );

        return categoryDto;
    }

    @Override
    public List<CategoryDto> toDto(List<Category> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CategoryDto> list = new ArrayList<CategoryDto>( entityList.size() );
        for ( Category category : entityList ) {
            list.add( toDto( category ) );
        }

        return list;
    }

    @Override
    public Category toEntity(CategoryDto dto) {
        if ( dto == null ) {
            return null;
        }

        Category category = new Category();

        category.setCategory( dto.getCategory() );
        category.setCategoryname( dto.getCategoryname() );
        category.setCreatetime( dto.getCreatetime() );
        category.setCreateuser( dto.getCreateuser() );
        category.setData( dto.getData() );
        category.setExample( dto.getExample() );
        category.setId( dto.getId() );
        category.setPid( dto.getPid() );
        category.setRuleid( dto.getRuleid() );
        category.setSparefield1( dto.getSparefield1() );
        category.setSparefield2( dto.getSparefield2() );
        category.setSparefield3( dto.getSparefield3() );
        category.setSparefield4( dto.getSparefield4() );
        category.setSparefield5( dto.getSparefield5() );
        category.setType( dto.getType() );
        category.setUpdatetime( dto.getUpdatetime() );
        category.setUpdateuser( dto.getUpdateuser() );

        return category;
    }

    @Override
    public List<Category> toEntity(List<CategoryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Category> list = new ArrayList<Category>( dtoList.size() );
        for ( CategoryDto categoryDto : dtoList ) {
            list.add( toEntity( categoryDto ) );
        }

        return list;
    }
}
