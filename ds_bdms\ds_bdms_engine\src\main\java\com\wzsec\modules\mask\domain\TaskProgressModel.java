package com.wzsec.modules.mask.domain;

import lombok.Data;
import javax.persistence.Entity;

/**
 * @Description
 * <AUTHOR>
 * @date 2021年4月15日
 */
@Data
public class TaskProgressModel {

	/**
	 * 任务名
	 */
	private int taskid;

	/**
	 * 已完成行数
	 */
	private int finishedcount;

	/**
	 * 总行数
	 */
	private int totalcount;

	/**
	 * 进度
	 */
	private String progress;

	/**
	 * 开始时间
	 */
	private String starttime;

	/**
	 * 预计完成时间
	 */
	private String estimatedtime;

}
