package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
//import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2022-04-18
*/
@Entity
@Data
@Table(name="sdd_mask_pictaskconfig")
public class MaskPictaskconfig implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 原始图片目录 */
    @Column(name = "inputdirectory")
    //@NotBlank
    private String inputdirectory;

    /** 输入文件格式 */
    @Column(name = "inputfileformat")
    private String inputfileformat;

    /** 计算资源 */
    @Column(name = "computeresources")
    //@NotBlank
    private String computeresources;

    /** 脱敏对象 */
    @Column(name = "maskobject")
    //@NotBlank
    private String maskobject;

    /** 任务状态(0禁用,1启用) */
    @Column(name = "state")
    //@NotBlank
    private String state;

    /** 输出文件格式 */
    @Column(name = "outputfileformat")
    //@NotBlank
    private String outputfileformat;

    /** 脱敏后图片目录 */
    @Column(name = "outputdirectory")
    //@NotBlank
    private String outputdirectory;

    /** 执行状态：0 :未提交1:执行中2:执行成功3:执行失败4:提交失败 */
    @Column(name = "executionstate")
    private String executionstate;

    /** 执行方式（1手动提交，2定时执行） */
    @Column(name = "submitmethod")
    private String submitmethod;

    /** 执行时间cron表达式 */
    @Column(name = "cron")
    private String cron;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(MaskPictaskconfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}