package com.wzsec.modules.sdd.category.service.mapper;

import com.wzsec.modules.sdd.category.domain.Level;
import com.wzsec.modules.sdd.category.service.dto.LevelDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class LevelMapperImpl implements LevelMapper {

    @Override
    public LevelDto toDto(Level entity) {
        if ( entity == null ) {
            return null;
        }

        LevelDto levelDto = new LevelDto();

        levelDto.setCreatetime( entity.getCreatetime() );
        levelDto.setCreateuser( entity.getCreateuser() );
        levelDto.setId( entity.getId() );
        levelDto.setLevelcode( entity.getLevelcode() );
        levelDto.setLevelmeaning( entity.getLevelmeaning() );
        levelDto.setSparefield1( entity.getSparefield1() );
        levelDto.setSparefield2( entity.getSparefield2() );
        levelDto.setSparefield3( entity.getSparefield3() );
        levelDto.setSparefield4( entity.getSparefield4() );
        levelDto.setSparefield5( entity.getSparefield5() );
        levelDto.setUpdatetime( entity.getUpdatetime() );
        levelDto.setUpdateuser( entity.getUpdateuser() );

        return levelDto;
    }

    @Override
    public List<LevelDto> toDto(List<Level> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LevelDto> list = new ArrayList<LevelDto>( entityList.size() );
        for ( Level level : entityList ) {
            list.add( toDto( level ) );
        }

        return list;
    }

    @Override
    public Level toEntity(LevelDto dto) {
        if ( dto == null ) {
            return null;
        }

        Level level = new Level();

        level.setCreatetime( dto.getCreatetime() );
        level.setCreateuser( dto.getCreateuser() );
        level.setId( dto.getId() );
        level.setLevelcode( dto.getLevelcode() );
        level.setLevelmeaning( dto.getLevelmeaning() );
        level.setSparefield1( dto.getSparefield1() );
        level.setSparefield2( dto.getSparefield2() );
        level.setSparefield3( dto.getSparefield3() );
        level.setSparefield4( dto.getSparefield4() );
        level.setSparefield5( dto.getSparefield5() );
        level.setUpdatetime( dto.getUpdatetime() );
        level.setUpdateuser( dto.getUpdateuser() );

        return level;
    }

    @Override
    public List<Level> toEntity(List<LevelDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Level> list = new ArrayList<Level>( dtoList.size() );
        for ( LevelDto levelDto : dtoList ) {
            list.add( toEntity( levelDto ) );
        }

        return list;
    }
}
