package com.wzsec.modules.strategy.service.mapper;

import com.wzsec.modules.strategy.domain.Strategy;
import com.wzsec.modules.strategy.service.dto.StrategyDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:20+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class StrategyMapperImpl implements StrategyMapper {

    @Override
    public Strategy toEntity(StrategyDto dto) {
        if ( dto == null ) {
            return null;
        }

        Strategy strategy = new Strategy();

        strategy.setId( dto.getId() );
        strategy.setCname( dto.getCname() );
        strategy.setEname( dto.getEname() );
        strategy.setRuleids( dto.getRuleids() );
        strategy.setState( dto.getState() );
        strategy.setDes( dto.getDes() );
        strategy.setNote( dto.getNote() );
        strategy.setCreateuser( dto.getCreateuser() );
        strategy.setCreatetime( dto.getCreatetime() );
        strategy.setUpdateuser( dto.getUpdateuser() );
        strategy.setUpdatetime( dto.getUpdatetime() );
        strategy.setSparefield1( dto.getSparefield1() );
        strategy.setSparefield2( dto.getSparefield2() );
        strategy.setSparefield3( dto.getSparefield3() );
        strategy.setSparefield4( dto.getSparefield4() );
        strategy.setSparefield5( dto.getSparefield5() );

        return strategy;
    }

    @Override
    public StrategyDto toDto(Strategy entity) {
        if ( entity == null ) {
            return null;
        }

        StrategyDto strategyDto = new StrategyDto();

        strategyDto.setId( entity.getId() );
        strategyDto.setCname( entity.getCname() );
        strategyDto.setEname( entity.getEname() );
        strategyDto.setRuleids( entity.getRuleids() );
        strategyDto.setState( entity.getState() );
        strategyDto.setDes( entity.getDes() );
        strategyDto.setNote( entity.getNote() );
        strategyDto.setCreateuser( entity.getCreateuser() );
        strategyDto.setCreatetime( entity.getCreatetime() );
        strategyDto.setUpdateuser( entity.getUpdateuser() );
        strategyDto.setUpdatetime( entity.getUpdatetime() );
        strategyDto.setSparefield1( entity.getSparefield1() );
        strategyDto.setSparefield2( entity.getSparefield2() );
        strategyDto.setSparefield3( entity.getSparefield3() );
        strategyDto.setSparefield4( entity.getSparefield4() );
        strategyDto.setSparefield5( entity.getSparefield5() );

        return strategyDto;
    }

    @Override
    public List<Strategy> toEntity(List<StrategyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Strategy> list = new ArrayList<Strategy>( dtoList.size() );
        for ( StrategyDto strategyDto : dtoList ) {
            list.add( toEntity( strategyDto ) );
        }

        return list;
    }

    @Override
    public List<StrategyDto> toDto(List<Strategy> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<StrategyDto> list = new ArrayList<StrategyDto>( entityList.size() );
        for ( Strategy strategy : entityList ) {
            list.add( toDto( strategy ) );
        }

        return list;
    }
}
