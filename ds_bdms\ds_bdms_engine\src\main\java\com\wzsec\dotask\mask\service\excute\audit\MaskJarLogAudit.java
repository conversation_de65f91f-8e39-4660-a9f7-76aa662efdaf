package com.wzsec.dotask.mask.service.excute.audit;

import com.alibaba.fastjson.JSONObject;
import com.wzsec.modules.mask.domain.MaskAuditJarLogResult;
import com.wzsec.modules.mask.service.MaskAuditJarLogResultService;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1Dto;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Title: MaskJarLogAudit
 * Decription:
 *
 * <AUTHOR>
 * @date 2021/4/15
 */
@Slf4j
public class MaskJarLogAudit {

    private final DatasourceService datasourceService;
    private final MaskAuditJarLogResultService maskAuditJarLogResultService;

    public MaskJarLogAudit(DatasourceService datasourceService, MaskAuditJarLogResultService maskAuditJarLogResultService) {
        this.datasourceService = datasourceService;
        this.maskAuditJarLogResultService = maskAuditJarLogResultService;
    }

    /**
     * 审计脱敏Jar日志
     *
     * @param maskAuditTaskDto
     * @param checkTime
     * @param submituser
     */
    public boolean maskAuditForJarLog(MaskAuditTaskV1Dto maskAuditTaskDto, String checkTime, String submituser) {
        log.info("开始执行脱敏Jar日志审计任务");
        boolean resultStatus = false;
        try {
            String intervalDay = ConstEngine.sddEngineConfs.get("maskaudit.jarlog.interval.day").trim();
            String checkDate = ConstEngine.sddEngineConfs.get("maskaudit.jarlog.date").trim();
            if (StringUtils.isEmpty(checkDate)) {
                checkDate = TimeUtils.getYesterdayByCalendar("yyyyMMdd", Integer.parseInt(intervalDay));
            }

            DatasourceDto datasource = datasourceService.findById(maskAuditTaskDto.getOutsourceid().longValue());
            String type = datasource.getType();
            if (Const.FILE_LOCAL.equals(type)) {
                // 本地磁盘检测
                List<String> filePathList = new ArrayList<>();
                FileUtil.getFilePathList(maskAuditTaskDto.getOutfilepath() + File.separator + checkDate, filePathList);
                HashMap<String, String> resultMap = new HashMap<>();
                for (String filePath : filePathList) {
                    String logDate = TimeUtils.strToFormatStr("yyyyMMdd", "yyyy-MM-dd", checkDate);
                    // 检测日志
                    resultMap = dealSingleFile(filePath, logDate, checkTime, maskAuditTaskDto.getTaskname());
                }
                if (!resultMap.containsKey("containMaskLog")) {
                    MaskAuditJarLogResult maskAuditJarLogResult = new MaskAuditJarLogResult();
                    maskAuditJarLogResult.setChecktime(checkTime);
                    maskAuditJarLogResult.setNote("未检测到数据脱敏相关操作");
                    maskAuditJarLogResultService.create(maskAuditJarLogResult);
                    log.info("未检测到数据脱敏相关操作");
                }
                resultStatus = true;
            }
            log.info("脱敏Jar日志审计成功");
        } catch (Exception e) {
            log.info("脱敏Jar日志审计失败");
            resultStatus = false;
            e.printStackTrace();
        }
        return resultStatus;
    }


    /**
     * 处理清洗过后的单行数据
     *
     * @param line
     */
    private HashMap<String, String> dealCleanedSingleLine(String line, String logDate, String checkTime, String taskName, HashMap<String, String> taskInfoMap, HashMap<String, String> algMap) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(line);
        String message = jsonObject.get("message").toString();
        String upperLine = message.toUpperCase();
        // 一个message中包含多条INFO信息，根据日期切分每条,数据中的日期应该与检测的checkDate日期相同
        String[] dataInfos = upperLine.split(logDate);
        for (String dataInfo : dataInfos) {
            if (dataInfo.contains(Const.MASKAUDIT_JARLOG_CREATR_TMP_FUN) && dataInfo.contains(Const.MASKAUDIT_JARLOG_AS)){
                // 包含 create temporary function 和 as
                // 日志格式：create temporary function aessm3 as 'cn.ctyun.mask.alglib.udf.enc.H_AESSM3'
                // 截取别名
                String funAlias = dataInfo.substring(dataInfo.indexOf(Const.MASKAUDIT_JARLOG_CREATR_TMP_FUN) + Const.MASKAUDIT_JARLOG_CREATR_TMP_FUN.length(), dataInfo.indexOf(Const.MASKAUDIT_JARLOG_AS));
                if (dataInfo.contains("'")){
                    // 算法绝对路径
                    String algAbsPath = dataInfo.substring(dataInfo.indexOf("'") + 1, dataInfo.lastIndexOf("'"));
                    // 算法名称
                    String algName = algAbsPath.substring(algAbsPath.lastIndexOf(".") + 1);
                    algMap.put(funAlias,algName);
                }
                // 跳出本次循环
                continue;
            }
            if (dataInfo.contains(Const.MASKAUDIT_JARLOG_HOSTNAME) &&
                    dataInfo.contains(Const.MASKAUDIT_JARLOG_HOSTIP) &&
                    dataInfo.contains(Const.MASKAUDIT_JARLOG_ALGPACKAGE)) {
                // 包含主机名、主机IP、调用算法包
                // 日志格式：enc.SM3V0: 主机名:wz001-pc,主机IP:null,调用算法包:maskalglib-v2.1.10,算法:cn.ctyun.mask.alglib.java.enc.SM3V0
                String operLine = dataInfo.substring(dataInfo.indexOf(Const.MASKAUDIT_JARLOG_HOSTNAME));
                String[] infos = operLine.split(",");
                for (String info : infos) {
                    String[] value = info.split(":");
                    if (Const.MASKAUDIT_JARLOG_HOSTNAME.equals(value[0])){
                        taskInfoMap.put(Const.MASKAUDIT_JARLOG_HOSTNAME,value[1]);
                    }
                    if (Const.MASKAUDIT_JARLOG_HOSTIP.equals(value[0])){
                        taskInfoMap.put(Const.MASKAUDIT_JARLOG_HOSTIP,value[1]);
                    }
                    if (Const.MASKAUDIT_JARLOG_ALGPACKAGE.equals(value[0])){
                        taskInfoMap.put(Const.MASKAUDIT_JARLOG_ALGPACKAGE,value[1]);
                    }
                }
                // 跳出本次循环
                continue;
            }
            if (dataInfo.contains(Const.MASKAUDIT_JARLOG_SELECT)) {
                // 判断是否调用了算法包
                if (taskInfoMap.size() != 0) {
                    // 判断SQL语句中是否使用了创建临时函数的函数名称
                    for (String fun : algMap.keySet()) {
                        if (dataInfo.contains(fun)) {
                            StringBuilder strategyStr = new StringBuilder();
                            StringBuilder algNameStr = new StringBuilder();
                            String paramLine = "";
                            String lineNum = "";
                            // 包含SQL语句
                            // 格式 select id, md5(loginname) as enc_loginname, md5salt(cname,'0P)3zJ%xF28l1b76') as enc_cname from ...
                            if (dataInfo.contains(Const.MASKAUDIT_JARLOG_FROM)) {
                                paramLine = dataInfo.substring(dataInfo.indexOf(Const.MASKAUDIT_JARLOG_SELECT), dataInfo.indexOf(Const.MASKAUDIT_JARLOG_FROM));
                            } else {
                                paramLine = dataInfo.substring(dataInfo.indexOf(Const.MASKAUDIT_JARLOG_SELECT));
                            }
                            List<String> params = null;

                            try {
                                params = JSQLParseUtil.selectItems(paramLine);
                                for (String param : params) {
                                    // param : md5salt(cname,'0P)3zJ%xF28l1b76')
                                    String field = "";
                                    String funAlias = "";
                                    if (param.contains("(")) {
                                        funAlias = param.trim().substring(0, param.indexOf("("));
                                        String funParam = param.substring(param.indexOf("(") + 1, param.indexOf(")"));
                                        if (funParam.contains(",")) {
                                            field = funParam.split(",")[0];
                                        } else {
                                            field = funParam;
                                        }
                                    } else {
                                        if (param.contains(Const.MASKAUDIT_JARLOG_AS)) {
                                            field = param.trim().substring(0, param.indexOf(Const.MASKAUDIT_JARLOG_AS));
                                        } else {
                                            field = param;
                                        }
                                    }
                                    strategyStr.append(field);
                                    strategyStr.append("|");
                                    if (algMap.containsKey(funAlias)) {
                                        String algName = algMap.get(funAlias);
                                        strategyStr.append(algName);
                                        algNameStr.append(algName);
                                        algNameStr.append(",");
                                    } else {
                                        log.info("未识别到脱敏函数名称：" + funAlias + "对应的算法！");
                                    }
                                    strategyStr.append(";");

                                }

                                if (dataInfo.contains(Const.MASKAUDIT_JARLOG_LIMIT)) {
                                    lineNum = dataInfo.substring(dataInfo.indexOf(Const.MASKAUDIT_JARLOG_LIMIT) + Const.MASKAUDIT_JARLOG_LIMIT.length());
                                    try {
                                        Long.parseLong(lineNum);
                                    }catch (NumberFormatException e){
                                        log.info("脱敏行数截取异常");
                                        System.out.println(dataInfo);
                                        lineNum = "";
                                    }
                                    if (lineNum.endsWith(";")) {
                                        lineNum = lineNum.split(";")[0];
                                    }
                                }

                                MaskAuditJarLogResult maskAuditJarLogResult = new MaskAuditJarLogResult();
                                maskAuditJarLogResult.setTaskname(taskName);
                                maskAuditJarLogResult.setStrategy(strategyStr.toString());
                                if (taskInfoMap.containsKey(Const.MASKAUDIT_JARLOG_ALGPACKAGE)) {
                                    maskAuditJarLogResult.setAlgorithmpackagename(taskInfoMap.get(Const.MASKAUDIT_JARLOG_ALGPACKAGE).toLowerCase());
                                }
                                if (StringUtils.isNotEmpty(algNameStr)) {
                                    maskAuditJarLogResult.setAlgorithmname(algNameStr.toString().substring(0,algNameStr.toString().length()-1));
                                }
                                maskAuditJarLogResult.setIpaddress(taskInfoMap.get(Const.MASKAUDIT_JARLOG_HOSTIP));
                                maskAuditJarLogResult.setAccount(taskInfoMap.get(Const.MASKAUDIT_JARLOG_HOSTNAME));
                                maskAuditJarLogResult.setStarttime("");
                                maskAuditJarLogResult.setEndtime("");
                                maskAuditJarLogResult.setLinenumber(lineNum);
                                maskAuditJarLogResult.setIssuccessful("");
                                maskAuditJarLogResult.setChecktime(checkTime);
                                maskAuditJarLogResultService.create(maskAuditJarLogResult);

                                // 用于判断此次任务是否检测到脱敏日志
                                taskInfoMap.put("containMaskLog","true");
                            }   catch (JSQLParserException e){
                                log.info("SQL格式暂不支持：" + paramLine);
                            }
                        }
                    }
                }
            }
        }
        return taskInfoMap;
    }


    /**
     * @Description:处理单个文件
     * <AUTHOR> by wangqi
     * @date 2021-01-19
     */
    public HashMap<String, String> dealSingleFile(String strInput,String logDate,String checkTime,String taskName) throws Exception{
        BufferedReader br = null;
        try {
            String f_name = "";
            File f_Source = new File(strInput);
            f_name = f_Source.getName();
            br = new BufferedReader(new FileReader(f_Source));

            String sline = null;
            long startTime = System.currentTimeMillis();
            HashMap<String, String> taskInfoMap = new HashMap<>();//暂存一下
            HashMap<String, String> algMap = new HashMap<>();//暂存一下
            while ((sline = br.readLine()) != null) {
                // System.out.println("处理行：" + sline);
                taskInfoMap = dealCleanedSingleLine(sline, logDate, checkTime, taskName, taskInfoMap, algMap);
            }
            long endTime = System.currentTimeMillis();
            long useTime = endTime - startTime;
            System.out.println("处理文件'" + f_name + "'用时:" + useTime + "ms");
            return taskInfoMap;
        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                br.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}
