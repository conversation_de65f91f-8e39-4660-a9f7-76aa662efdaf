package com.wzsec.modules.api.algo;

import com.wzsec.utils.ConfigurationManager;
import org.apache.commons.lang.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 改写结果脱敏算法_AES算法加密密钥(数据包有限制,暂按截取5位进行显示)
 */
public class AESMask {

    /**
     * AES加密 (参数:无需填写)
     *
     * @param strData str数据
     */
    public static String encrypt(String strData) throws Exception {
        String strKey = ConfigurationManager.getProperty("AES_SECRET_KEY");
        String returnstr = "";
        if (strData != null && !strData.equals("")) {
            byte[] byteRe = enCrypt(strData, strKey);
            returnstr = parseByte2HexStr(byteRe).substring(0,5);
//            returnstr = parseByte2HexStr(byteRe);

            String[] strMaskResultArr = returnstr.split("");
            return StringUtils.join(strMaskResultArr, "");
        }
        return null;
    }


    public static byte[] enCrypt(String content, String strKey) throws Exception {

        //实例化加密算法对象
        Cipher cipher = Cipher.getInstance("AES");
        //使用密钥初始化
        cipher.init(Cipher.ENCRYPT_MODE, initKeyForAES(strKey));
        byte[] cByte = cipher.doFinal(content.getBytes("UTF-8"));
        return cByte;
    }


    public static String parseByte2HexStr(byte[] buf) {
        StringBuffer sb = new StringBuffer();

        for (int i = 0; i < buf.length; ++i) {
            String hex = Integer.toHexString(buf[i] & 255);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    private static Key initKeyForAES(String key) throws NoSuchAlgorithmException {
        if (null == key || key.length() == 0) {
            throw new NullPointerException("key not is null");
        }
        SecretKeySpec key2 = null;
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        random.setSeed(key.getBytes());
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128, random);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            key2 = new SecretKeySpec(enCodeFormat, "AES");
        } catch (NoSuchAlgorithmException ex) {
            throw new NoSuchAlgorithmException();
        }
        return key2;
    }


    public static void main(String[] args) {
        try {
            System.out.println(encrypt("123456"));
            System.out.println(encrypt("root"));
            System.out.println(encrypt("wz001"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
