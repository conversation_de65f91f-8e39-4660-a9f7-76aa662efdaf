package com.wzsec.modules.mask.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

/**
* <AUTHOR>
* @date 2022-04-18
*/
@Data
public class MaskPictaskconfigQueryCriteria {

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String taskname;

    /** 精确 */
    @Query
    private String state;

    /** 精确 */
    @Query
    private String executionstate;

    @Query(blurry = "tasknamestateexecutionstate")
    private String blurry;
}
