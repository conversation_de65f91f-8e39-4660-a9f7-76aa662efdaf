2025-07-28 13:46:50,683 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 24100 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 13:46:50,691 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 13:46:54,030 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 13:46:54,032 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 13:46:56,619 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 2413 ms. Found 111 JPA repository interfaces.
2025-07-28 13:46:58,179 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 13:46:58,215 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 13:46:58,233 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:46:58,234 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:46:58,251 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:46:58,268 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 13:46:58,276 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 13:46:58,280 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:46:58,282 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:47:02,811 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 13:47:02,853 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 13:47:02,859 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 13:47:04,135 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-28 13:47:04,200 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-28 13:47:04,209 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-28 13:47:04,209 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-28 13:47:04,313 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.3552866530403609385] which is part of the web application []
2025-07-28 13:47:04,758 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-28 13:47:04,759 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 13480 ms
2025-07-28 13:47:07,376 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 13:47:08,065 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 13:47:08,117 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 13:47:08,118 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 13:47:08,118 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 13:47:08,119 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 13:47:08,119 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 13:47:08,120 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 13:47:08,124 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 13:47:08,129 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 13:47:11,673 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 13:47:13,666 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 13:47:14,342 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 13:47:15,672 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 13:47:16,691 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 13:47:27,413 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 13:47:27,531 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 13:47:30,974 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-28 13:47:39,089 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 13:47:39,202 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 13:47:39,203 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 13:47:39,226 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 13:47:39,233 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 13:47:39,234 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 13:47:39,234 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 13:47:39,235 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@55b0c0a6
2025-07-28 13:47:39,235 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 13:47:53,034 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@104f120c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@349cb607, org.springframework.security.web.context.SecurityContextPersistenceFilter@3d8ead6, org.springframework.security.web.header.HeaderWriterFilter@5bb556ab, org.springframework.security.web.authentication.logout.LogoutFilter@e6093da, org.springframework.web.filter.CorsFilter@6acb45c1, com.wzsec.modules.security.security.TokenFilter@3d9e6e12, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3e4c4911, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25bdcf6e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@aa1f0b5, org.springframework.security.web.session.SessionManagementFilter@2e57c15e, org.springframework.security.web.access.ExceptionTranslationFilter@48e8de7b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4a9296a3]
2025-07-28 13:47:53,096 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 13:48:00,415 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 13:48:00,416 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 13:48:00,416 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 13:48:00,417 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 13:48:00,417 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 13:48:00,417 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 13:48:00,417 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 13:48:00,417 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@65fa344
2025-07-28 13:48:00,812 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-28 13:48:00,991 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 13:48:01,161 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-28 13:48:01,175 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 13:48:01,177 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 13:48:01,177 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 13:48:01,177 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 13:48:01,177 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 13:48:01,178 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 13:48:01,178 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:48:01,178 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 13:48:01,178 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:48:01,188 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 13:48:01,188 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 13:48:01,188 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 13:48:01,198 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-28 13:48:03,315 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-28 13:48:03,583 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-28 13:48:03,900 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-28 13:48:04,241 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-28 13:48:04,542 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-28 13:48:04,543 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-28 13:48:04,554 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 74.755 seconds (JVM running for 79.271)
2025-07-28 13:48:06,116 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-28 13:50:02,850 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 13:54:18,076 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 21048 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 13:54:18,082 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 13:54:21,238 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 13:54:21,240 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 13:54:22,672 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1328 ms. Found 111 JPA repository interfaces.
2025-07-28 13:54:23,470 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 13:54:23,472 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 13:54:23,475 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:54:23,475 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:54:23,480 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:54:23,482 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 13:54:23,485 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 13:54:23,486 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:54:23,486 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:54:25,012 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 13:54:25,050 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 13:54:25,053 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 13:54:25,636 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-28 13:54:25,679 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-28 13:54:25,688 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-28 13:54:25,688 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-28 13:54:25,756 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.3611144260776723636] which is part of the web application []
2025-07-28 13:54:26,030 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-28 13:54:26,030 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 7227 ms
2025-07-28 13:54:27,847 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 13:54:28,170 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 13:54:28,201 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 13:54:28,202 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 13:54:28,202 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 13:54:28,202 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 13:54:28,203 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 13:54:28,203 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 13:54:28,206 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 13:54:28,209 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 13:54:30,872 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 13:54:31,857 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 13:54:32,130 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 13:54:32,826 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 13:54:33,723 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 13:54:40,486 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 13:54:40,616 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 13:54:44,545 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-28 13:54:48,782 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 13:54:48,907 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 13:54:48,908 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 13:54:48,931 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 13:54:48,937 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 13:54:48,937 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 13:54:48,937 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 13:54:48,938 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6b18bdf7
2025-07-28 13:54:48,939 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 13:55:01,492 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@50d293a2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@637f8b01, org.springframework.security.web.context.SecurityContextPersistenceFilter@886adc3, org.springframework.security.web.header.HeaderWriterFilter@761db70f, org.springframework.security.web.authentication.logout.LogoutFilter@64d05e02, org.springframework.web.filter.CorsFilter@35524549, com.wzsec.modules.security.security.TokenFilter@24ae5dd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1b826a22, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@10a4dc72, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@44e8d9dc, org.springframework.security.web.session.SessionManagementFilter@210d8f51, org.springframework.security.web.access.ExceptionTranslationFilter@698b0a1c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7a48e086]
2025-07-28 13:55:01,600 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 13:55:14,624 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 13:55:14,625 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 13:55:14,625 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 13:55:14,626 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 13:55:14,626 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 13:55:14,626 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 13:55:14,626 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 13:55:14,626 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6395cf5a
2025-07-28 13:55:15,338 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-28 13:55:15,807 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 13:55:16,331 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-28 13:55:16,357 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 13:55:16,360 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 13:55:16,361 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 13:55:16,361 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 13:55:16,361 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 13:55:16,361 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 13:55:16,361 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:55:16,361 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 13:55:16,361 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:55:16,375 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 13:55:16,376 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 13:55:16,376 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 13:55:16,392 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-28 13:55:19,216 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-28 13:55:19,463 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-28 13:55:19,749 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-28 13:55:19,989 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-28 13:55:20,224 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-28 13:55:20,224 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-28 13:55:20,236 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 63.249 seconds (JVM running for 66.696)
2025-07-28 13:55:20,992 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-28 13:55:48,867 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 13:55:48,867 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 13:55:48,871 INFO (FrameworkServlet.java:547)- Completed initialization in 4 ms
2025-07-28 14:45:01,383 ERROR (JobRunShell.java:211)- Job DEFAULT.14 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$d4ad966e.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 14:45:01,401 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.14 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$d4ad966e.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 14:53:33,313 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 23148 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:53:33,321 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:53:36,320 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:53:36,322 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:53:37,673 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1236 ms. Found 111 JPA repository interfaces.
2025-07-28 14:53:38,544 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:53:38,545 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:53:38,548 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:53:38,549 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:53:38,553 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:53:38,555 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:53:38,557 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:53:38,557 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:53:38,558 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:53:39,619 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:53:39,642 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:53:39,647 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:53:40,320 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-28 14:53:40,370 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-28 14:53:40,376 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-28 14:53:40,376 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-28 14:53:40,450 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.2920909267146775523] which is part of the web application []
2025-07-28 14:53:40,740 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-28 14:53:40,740 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 6596 ms
2025-07-28 14:53:42,730 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:53:43,200 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:53:43,243 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:53:43,244 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:53:43,245 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:53:43,245 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:53:43,245 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:53:43,246 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:53:43,250 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:53:43,254 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:53:46,448 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:53:47,602 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:53:47,875 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:53:48,559 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:53:49,248 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:53:55,183 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:53:55,265 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:53:59,671 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-28 14:54:05,135 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:54:05,240 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:54:05,240 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:54:05,262 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:54:05,267 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:54:05,268 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:54:05,268 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:54:05,269 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@272f0840
2025-07-28 14:54:05,269 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:54:16,522 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@b562753, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@71a7ce25, org.springframework.security.web.context.SecurityContextPersistenceFilter@5dfeb2e3, org.springframework.security.web.header.HeaderWriterFilter@25684391, org.springframework.security.web.authentication.logout.LogoutFilter@50829172, org.springframework.web.filter.CorsFilter@4f525e13, com.wzsec.modules.security.security.TokenFilter@77c70f9a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2700b0d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3b4340f8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@40d3f93d, org.springframework.security.web.session.SessionManagementFilter@22e5665f, org.springframework.security.web.access.ExceptionTranslationFilter@95602c9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@81a48a8]
2025-07-28 14:54:16,578 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:54:21,676 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:54:21,677 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:54:21,678 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:54:21,678 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:54:21,678 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:54:21,678 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:54:21,678 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:54:21,678 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4d38f8b8
2025-07-28 14:54:21,953 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-28 14:54:22,065 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:54:22,168 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-28 14:54:22,180 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:54:22,182 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:54:22,182 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:54:22,182 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:54:22,182 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:54:22,182 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:54:22,183 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:54:22,183 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:54:22,183 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:54:22,187 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:54:22,188 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:54:22,188 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:54:22,195 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-28 14:54:23,609 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-28 14:54:25,428 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-28 14:54:28,174 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-28 14:54:28,461 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-28 14:54:28,759 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-28 14:54:28,760 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-28 14:54:28,786 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 56.48 seconds (JVM running for 59.575)
2025-07-28 14:54:29,582 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-28 14:54:35,120 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 14:54:35,120 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:54:35,123 INFO (FrameworkServlet.java:547)- Completed initialization in 3 ms
2025-07-28 15:12:22,664 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 15:52:54,123 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 13912 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 15:52:54,130 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 15:52:57,551 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 15:52:57,554 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 15:52:59,066 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1390 ms. Found 111 JPA repository interfaces.
2025-07-28 15:52:59,911 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 15:52:59,912 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 15:52:59,916 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:52:59,916 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:52:59,920 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:52:59,922 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 15:52:59,924 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 15:52:59,924 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:52:59,924 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:53:01,053 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 15:53:01,077 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 15:53:01,081 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 15:53:01,719 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-28 15:53:01,757 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-28 15:53:01,763 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-28 15:53:01,763 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-28 15:53:01,842 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.6636529263801664138] which is part of the web application []
2025-07-28 15:53:02,130 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-28 15:53:02,131 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 7400 ms
2025-07-28 15:53:04,109 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 15:53:04,521 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 15:53:04,560 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 15:53:04,561 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 15:53:04,561 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 15:53:04,562 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 15:53:04,562 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 15:53:04,563 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 15:53:04,569 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 15:53:04,573 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 15:53:07,769 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 15:53:08,834 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 15:53:09,124 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 15:53:09,827 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 15:53:10,530 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 15:53:15,790 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 15:53:15,861 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 15:53:19,105 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-28 15:53:23,503 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 15:53:23,619 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 15:53:23,620 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 15:53:23,642 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 15:53:23,646 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 15:53:23,647 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 15:53:23,647 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 15:53:23,649 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@45c2fa2
2025-07-28 15:53:23,650 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 15:53:35,734 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5aff837f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f1c19ad, org.springframework.security.web.context.SecurityContextPersistenceFilter@70c81870, org.springframework.security.web.header.HeaderWriterFilter@144a7ad5, org.springframework.security.web.authentication.logout.LogoutFilter@62fe5aca, org.springframework.web.filter.CorsFilter@43b4ec0c, com.wzsec.modules.security.security.TokenFilter@2020083c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@affa948, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b366254, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@29295f4e, org.springframework.security.web.session.SessionManagementFilter@4c2aa4db, org.springframework.security.web.access.ExceptionTranslationFilter@612bfb57, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@95602c9]
2025-07-28 15:53:35,799 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 15:53:41,541 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 15:53:41,543 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 15:53:41,543 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 15:53:41,543 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 15:53:41,543 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 15:53:41,543 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 15:53:41,544 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 15:53:41,544 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5d7ca8ce
2025-07-28 15:53:41,909 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-28 15:53:42,068 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 15:53:42,192 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-28 15:53:42,206 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 15:53:42,209 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 15:53:42,209 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 15:53:42,209 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 15:53:42,209 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 15:53:42,210 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 15:53:42,210 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:53:42,210 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 15:53:42,210 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:53:42,215 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 15:53:42,215 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 15:53:42,216 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 15:53:42,223 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-28 15:53:44,290 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-28 15:53:44,546 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-28 15:53:44,838 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-28 15:53:45,084 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-28 15:53:45,339 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-28 15:53:45,339 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-28 15:53:45,350 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 52.224 seconds (JVM running for 57.445)
2025-07-28 15:53:46,142 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-28 15:57:35,207 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 15:57:35,217 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 15:57:35,478 INFO (FrameworkServlet.java:547)- Completed initialization in 258 ms
2025-07-28 16:11:01,714 ERROR (JobRunShell.java:211)- Job DEFAULT.131 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 16:11:01,731 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.131 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 17:04:27,142 ERROR (JobRunShell.java:211)- Job DEFAULT.174 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$9519a89d.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 17:04:27,144 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.174 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$9519a89d.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 17:29:04,628 ERROR (JobRunShell.java:211)- Job DEFAULT.27 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl.executionFromEngine(FileTaskConfigServiceImpl.java:234)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$FastClassBySpringCGLIB$$5968d26b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$853bd1b3.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.FileTaskConfigJob.execute(FileTaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 17:29:04,634 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.27 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl.executionFromEngine(FileTaskConfigServiceImpl.java:234)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$FastClassBySpringCGLIB$$5968d26b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$853bd1b3.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.FileTaskConfigJob.execute(FileTaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 17:36:27,228 ERROR (JobRunShell.java:211)- Job DEFAULT.175 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$9519a89d.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 17:36:27,229 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.175 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$9519a89d.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 17:54:22,670 ERROR (JobRunShell.java:211)- Job DEFAULT.258 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 17:54:22,673 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.258 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 17:54:22,852 ERROR (JobRunShell.java:211)- Job DEFAULT.256 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 17:54:22,854 ERROR (JobRunShell.java:211)- Job DEFAULT.257 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-28 17:54:22,855 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.257 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 17:54:22,855 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.256 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$98cc4576.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-28 18:14:21,652 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 18:14:22,687 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 18:14:22,687 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 18:14:22,687 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 18:14:22,687 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 18:14:22,810 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 18:14:22,810 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 18:14:22,814 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 18:14:22,834 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 18:14:22,917 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
