package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskHbasetaskresult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
* <AUTHOR>
* @date 2024-10-14
*/
public interface MaskHbasetaskresultRepository extends JpaRepository<MaskHbasetaskresult, Integer>, JpaSpecificationExecutor<MaskHbasetaskresult> {
}