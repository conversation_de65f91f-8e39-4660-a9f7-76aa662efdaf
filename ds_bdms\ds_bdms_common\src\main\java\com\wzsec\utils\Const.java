package com.wzsec.utils;

import com.wzsec.utils.rule.Dict;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 常量类
 *
 * <AUTHOR>
 * @date 2020-4-23
 */
public class Const {

    /*通用常量start*/

//    public final static Map<String, String> sddConfs = PropertiesUtil.getProperty("sdd.properties");
    //public final static Map<String, String> sddEngineConfs = PropertiesUtil.getProperty("sdd-engine.properties");

    public final static String Authorization = "Authorization";//header中token的key

    public final static String INIT_PASSWORD = "Ct$Ad@20";//初始用户密码

    public final static String HTTP_method_GET = "GET";//HTTP请求类型
    public final static String HTTP_method_POST = "POST";
    public final static String HTTP_method_PUT = "PUT";
    public final static String HTTP_method_DELETE = "DELETE";

    public final static String HTTP = "HTTP";
    public final static String HTTPS = "HTTPS";

    public final static List<String> firstNameList = Arrays.asList(Dict.firstName);//单姓
    public final static List<String> doubleSurNameList = Arrays.asList(Dict.doubleSurName);//复姓
    public final static List<String> addressCharList = Arrays.asList(Dict.addressChar);//地级单位或省市
    public final static List<String> countryNameList = Arrays.asList(Dict.countryName);//国家


    public final static String PHONE_SIGN = "手机号码"; //手机号码标识
    public final static String ADDRESS_SIGN = "地址"; //地址标识
    public final static String IDENTITY_SIGN = "身份证号码";//身份证号码标识
    public final static String EMAIL_SIGN = "邮箱";//邮箱标识
    public final static String IMSI_SIGN = "IMSI";//IMSI标识
    public final static String MEID_SIGN = "MEID";//MEID标识
    public final static String IMEI_SIGN = "IMEI";//IMEI标识
    public final static String NAME_SIGN = "姓名";//姓名标识
    public final static String ITUDE_SIGN = "经纬度";//经纬度标识


    public final static String STATE_ON = "0";//状态：0启用
    public final static String STATE_OFF = "1";//状态：1禁用

    public final static String ISVALID_DISABLED = "0";//是否可用：0不可用
    public final static String ISVALID_AVAILABLE = "1";//是否可用：1可用

    //数据库表数据分隔符
    public final static String DB_TAB_DATA_JOIN = ";_;_;_;";
    public final static String SPECIAL_AUDIT_SPLIT_JOIN = "&#&"; // 特殊审计分隔符

    public final static String DB_MYSQL = "MySQL";//数据库：mysql

    public final static String DB_UNIDB = "unidb";//数据库：unidb
    public final static String DB_MARIADB = "MariaDB";//数据库：MariaDB
    public final static String DB_RDS_MYSQL = "RDS-MySQL";//数据库：RDS-MySQL
    public final static String DB_ORACLE = "Oracle";//数据库：oracle
    public final static String DB_GBASE = "GBase";//数据库：gbase
    public final static String DB_GBASE8A = "GBase8a";//数据库：gbase
    public final static String DB_GBASE8S = "GBase8s";//数据库：gbase
    public final static String DB_SQLSERVER = "SQLServer";//数据库：sqlserver
    public final static String DB_DB2 = "DB2";//数据库：db2
    public final static String DB_HIVE = "Hive";//数据库：hive
    public final static String DB_HBASE = "HBase";//数据库：hbase
    public final static String DB_PSOTGRESQL = "PostgreSql";//数据库：PostgreSql
    public final static String DB_RDS_PSOTGRESQL = "RDS-PostgreSql";//数据库：RDS-PostgreSql
    public final static String DB_DM = "DM";//数据库：DM
    public final static String DB_MONGODB = "MongoDB";//数据库：MongoDB
    public final static String DB_REDIS = "redis";//数据库：Redis
    public final static String DB_INFORMIX = "Informix";//数据库：Informix
    public final static String DB_TERADATA = "Teradata";//数据库：Teradata
    public final static String DB_ODPS = "ODPS";//数据库：odps
    public final static String DB_DRDS = "DRDS";//数据库：DRDS
    public final static String DB_GREENPLUM = "DRDS";//数据库：Greenplum
    public final static String DB_HYBRIDDB = "HybridDB";//数据库：HybridDB
    public final static String DB_BYTEHOUSE = "ByteHouse";//数据库：ByteHouse
    public final static String DB_KINGBASE8 = "KingBase8";//数据库：KingBase8

    public final static String FILE_HDFS = "HDFS";//文件源：hdfs
    public final static String FILE_FTP = "FTP";//文件源：ftp
    public final static String FILE_LINUX = "Linux-Host";//文件源：linux主机
    public final static String FILE_LOCAL = "Local-Host";//文件源：本地主机
    public final static String ES = "Elasticsearch";//ES
    public final static String HBASE = "HBASE";//HBASE

    public final static String SEPARATOR_LINUX = "/";//linux环境路径分隔符
    public final static String SEPARATOR_WINDOWS = "\\";//windows环境分隔符
    public final static String SEPARATOR_CURRENT = File.separator;//当前环境分隔符

    public final static String STRING_EMPTY = "";//空字符串

    public final static String LOCALHOST = "127.0.0.1";//本地IP

    //AES加密密钥
    public final static String AES_SECRET_KEY = "AESCt$Ad@20";

    public final static String FILETASK_FORMAT_FILE = "格式化文件";//格式化文件

    public final static String EXCELIMPORT = "Excel导入";// Excel导入

    public static final String DEFAULT_DATABASE = "admin";//默认数据库
    public static final String ENABLE = "0";//启用
    public static final String PAUSE = "1";//暂停
    public static final String CUSTOM_ALGORITHM = "3"; //自定义算法

    /*通用常量end*/

    /*黑白名单常量start*/
    public static List<String> nameWhiteList = null; // 姓名白名单
    public static List<String> addreWhiteList = null; // 地址白名单
    public static List<String> phoneWhiteList = null;// 手机号白名单

    public final static String whiteList_SIGN = "0"; // 白名单标识
    public final static String blackList_SIGN = "1"; // 黑名单标识

    /*黑白名单常量end*/


    /*字典表常量start*/
    public final static String DICT_RULE_TYPE = "rule_type";//规则类型
    public final static String DICT_RULE_STATUS = "rule_status";//规则状态
    public final static String DICT_DATASOURCE_CODE = "datasource_code";//数据源字符编码集
    public final static String DICT_DATASOURCE_TYPE = "datasource_type";//数据源类型
    public final static String DICT_DATASOURCE_STATUS = "datasource_status";//数据源状态
    public final static String DICT_TASK_INPUTTYPE = "task_inputtype";//任务输入文件类型
    public final static String DICT_TASK_SUBMITTYPE = "task_submittype";//任务执行方式
    public final static String DICT_TASK_EXECUTESTATUS = "task_executestatus";//任务执行状态
    public final static String DICT_TASK_STATUS = "task_status";//任务状态
    public final static String DICT_OUTLINERESULT_SOURCETYPE = "outlineresult_sourcetype";//概要结果数据来源
    public final static String DICT_DETAILRESULT_SOURCETYPE = "detailresult_sourcetype";//详情结果数据来源
    public final static String DICT_DATASOURCE_DRIVERPROGRAM = "datasource_driverprogram";//数据源驱动程序
    public final static String DICT_STRATEGY_STATUS = "strategy_status";//策略状态
    public final static String DICT_CATEGORY_TYPE = "category_type";//类别层次
    public final static String DICT_SENSITIVE_LEVELV = "sensitive_levelv";//敏感级别
    public final static String DICT_WP_WEAKPWD_ENCTYPE = "wp_weakpwd_enctype";//弱密码加密类型
    public final static String DICT_WP_TASK_SUBMITTYPE = "wp_task_submittype";//弱密码任务执行方式
    public final static String DICT_WP_TASK_STATUS = "wp_task_status";//弱密码任务状态
    public final static String DICT_WP_TASK_EXECUTESTATE = "wp_task_executestate";//弱密码任务执行状态
    public final static String DICT_WP_RESULT_TASK_STATUS = "wp_result_task_status";//弱密码结果任务级别结果状态

    /*字典表常量end*/

    /*规则表常量start*/
    public final static String RULE_RULETYPE_REGEXPS = "1";//规则类型：1正则
    public final static String RULE_RULETYPE_ALGORITHM_PROGRAM = "2";//规则类型：2程序算法
    public final static String RULE_RULETYPE_ALGORITHM_CUSTOM = "3";//规则类型：3自定义算法

    /*规则表常量end*/

    /*类别表常量start*/
    public final static String CATEGORY_TYPE_1 = "1";//类别层级：1类别----大类
    public final static String CATEGORY_TYPE_2 = "2";//类别层级：2大类----大类范围
    public final static String CATEGORY_TYPE_3 = "3";//类别层级：3子类----子类
    public final static String CATEGORY_TYPE_4 = "4";//类别层级：4----子类范围
//    public final static String CATEGORY_TYPE_5 = "5";//类别层级：5----子类细分范围

    public final static String CATEGORY_TYPE_1_NAME = "大类";//类别层级：类别----大类
    public final static String CATEGORY_TYPE_2_NAME = "大类范围";//类别层级：大类----大类范围
    public final static String CATEGORY_TYPE_3_NAME = "子类";//类别层级：子类----子类
    public final static String CATEGORY_TYPE_4_NAME = "子类范围";//类别层级：----子类范围
//    public final static String CATEGORY_TYPE_5_NAME = "子类细分范围";//类别层级：----子类细分范围

    public final static String CATEGORY_TYPERANK = "typerank";//类别层级
    public final static String CATEGORY_CATEGORY = "category";//类别标识
    public final static String CATEGORY_CATEGORYNAME = "categoryname";//类别名称
    public final static String CATEGORY_DATA = "data";//对应数据

    /*类别表常量end*/

    /*任务表常量start*/
    public final static String TASK_DATATYPE_DB = "1";//输入文件类型：1数据库
    public final static String TASK_DATATYPE_FILE = "2";//输入文件类型：2文件

    public final static String TASK_SUBMITTYPE_HAND = "1";//执行方式：1手动提交
    public final static String TASK_SUBMITTYPE_AUTO = "2";//执行方式：2定时执行

    public final static String TASK_STATE_USE = "0";//状态：0启用
    public final static String TASK_STATE_OFF = "1";//状态：1禁用

    public final static String TASK_EXECUTESTATE_NOT_SUBMIT = "0";//任务执行状态：0未提交
    public final static String TASK_EXECUTESTATE_SUBMIT_FAIL = "1";//任务执行状态：1提交失败
    public final static String TASK_EXECUTESTATE_EXECUTING = "2";//任务执行状态：2执行中
    public final static String TASK_EXECUTESTATE_EXECUTE_SUCCESS = "3";//任务执行状态：3执行成功
    public final static String TASK_EXECUTESTATE_EXECUTE_FAIL = "4";//任务执行状态：4执行失败

    public final static String TASK_RESULT_EXECUTING = "1";//任务结果状态：1执行中
    public final static String TASK_RESULT_EXECUTE_SUCCESS = "2";//任务结果状态：2执行成功
    public final static String TASK_RESULT_EXECUTE_FAIL = "3";//任务结果状态：3执行失败

    public final static String TASK_SUBMITTYPE_AUTOSUBMIT = "系统定时任务";//定时任务

    /*任务表常量end*/


    /*结果概要表常量start*/
    public final static String OUTLINERESULT_DATASOURCETYPE_DB = "1";//数据来源：1库表
    public final static String OUTLINERESULT_DATASOURCETYPE_FILE = "2";//数据来源：2文件

    /*结果概要表常量end*/


    /*结果详情表常量start*/
    public final static String DETAILRESULT_SOURCETYPE_DB = "1";//数据源类型：1库表
    public final static String DETAILRESULT_SOURCETYPE_FILE = "2";//数据源类型：2文件

    /*结果详情表常量end*/


    /*弱密码库表常量start*/
    public final static String WPPWD_ENCTYPE_NOT = "0";//不加密
    public final static String WPPWD_ENCTYPE_MD5 = "1";//MD5
    public final static String WPPWD_ENCTYPE_MYSQLSHA1 = "2";//MySQLSha1

    /*弱密码库表常量end*/

    /*弱密码主任务表常量start*/
    public final static String WPTASK_SUBMITTYPE_HAND = "1";//执行方式：1手动提交
    public final static String WPTASK_SUBMITTYPE_AUTO = "2";//执行方式：2定时执行

    public final static String WPTASK_EXECUTESTATE_NOT_SUBMIT = "0";//任务执行状态：0未提交
    public final static String WPTASK_EXECUTESTATE_SUBMIT_FAIL = "1";//任务执行状态：1提交失败
    public final static String WPTASK_EXECUTESTATE_EXECUTING = "2";//任务执行状态：2执行中
    public final static String WPTASK_EXECUTESTATE_EXECUTE_SUCCESS = "3";//任务执行状态：3执行成功
    public final static String WPTASK_EXECUTESTATE_EXECUTE_FAIL = "4";//任务执行状态：4执行失败

    /*弱密码主任务表常量end*/

    /*弱密码结果任务级别结果表常量start*/
    public final static String WPRESULT_TASK_STATE_SUCCESS = "0";//结果状态：0成功
    public final static String WPRESULT_TASK_STATE_FAIL = "1";//结果状态：1失败

    /*弱密码结果任务级别结果表常量end*/

    /*弱密码结果表级别结果表常量start*/
    public final static String WPRESULT_TASK_TABLE_RESULTTYPE_WEAKPWD_LABLE = "弱密码";//弱密码
    public final static String WPRESULT_TASK_TABLE_RESULTTYPE_STRONGPWD_LABLE = "强密码";//强密码
    public final static String WPRESULT_TASK_TABLE_RESULTTYPE_NOTSUPPORT_LABLE = "无法判断";//无法判断

    /*弱密码结果表级别结果表常量end*/

    /*弱密码子任务加密算法常量start*/
    public final static String WPRESULT_SUBTASK_WEAKPWD = "0";//弱密码库
    public final static String WPRESULT_SUBTASK_ENCRYALG_MD5MD5 = "1";//加密算法_MD5(MD5)
    public final static String WPRESULT_SUBTASK_ENCRYALG_MD5_32 = "2";//加密算法_MD5_32
    public final static String WPRESULT_SUBTASK_ENCRYALG_SHA256 = "3";//加密算法_SHA256
    public final static String WPRESULT_SUBTASK_ENCRYALG_MD5_16 = "4";//加密算法_MD5_16

    /*弱密码子任务加密算法常量end*/

    /*弱密码存储库常量start*/
    public final static String WPRESULT_DB_MYSQL = "mysql";
    public final static String WPRESULT_DB_HBASE = "hbase";

    /*弱密码存储库常量end*/

    /*工作空间范围常量start*/
    public final static String WORKSPACE_SCOPE_SDD = "1";//敏感数据发现
    public final static String WORKSPACE_SCOPE_WPD = "2";//弱密码发现

    /*工作空间范围常量end*/

    /*数据源常量start*/
    public final static String SOURCE_STRUCTURED = "0";//结构化
    public final static String SOURCE_SEMI_STRUCTURED = "1";// 半结构化
    public final static String SOURCE__NOT_STRUCTURED = "2";//非结构化

    public final static String SOURCE_DB = "1";//数据源：1数据库
    public final static String SOURCE_FILE = "2";//数据源：2文件
    public final static String SOURCE_BIGDATA = "3";//数据源：3大数据组件
    /*数据源常量end*/

    /*敏感数据发现行数常量start*/
    public final static long SOURCE_FILE_LINENUM = 10000;
    /*敏感数据发现行数常量end*/

    /*数据分类分级评估状态常量start*/
    public final static String EVALUATIONSTATUS_NOASSESS = "0";//未评估
    public final static String EVALUATIONSTATUS_STAYASSESS = "1";//待评估
    public final static String EVALUATIONSTATUS_STAYAUDIT = "2";//待审核
    public final static String EVALUATIONSTATUS_REPEATASSESS = "3";//重评估
    public final static String EVALUATIONSTATUS_HASASSESS = "4";//已评估

    public final static String META_TABLE_SHOW = "0";//显示
    public final static String META_TABLE_HIDE = "1";//隐藏

    /*数据分类分级评估状态常量end*/

    /*数据分类分级策略常量start*/
    public final static String MASK_STRATEGY_EXTRACT_FIELD = "0";//抽取字段
    public final static String MASK_STRATEGY_NOT_EXTRACT_FIELD = "1";//未抽取字段
    /*数据分类分级策略常量end*/

    /*数据分类数据库脱敏常量start*/
    public final static String DB_TASK_SUBMITTYPE_HAND = "1";//执行方式：1手动提交
    public final static String DB_TASK_SUBMITTYPE_AUTO = "2";//执行方式：2定时执行

    public final static String DB_TASK_OUTPUTTYPE_DB = "1";//库表
    public final static String DB_TASK_OUTPUTTYPE_EXCEL = "2";//excel
    public final static String DB_TASK_OUTPUTTYPE_TXT = "3";//txt
    public final static String DB_TASK_OUTPUTTYPE_CSV = "4";//csv

    public final static String DB_TASK_EXECUTESTATE_NOT_SUBMIT = "0";//任务执行状态：0未提交
    public final static String DB_TASK_EXECUTESTATE_SUBMIT_FAIL = "1";//任务执行状态：1提交失败
    public final static String DB_TASK_EXECUTESTATE_EXECUTING = "2";//任务执行状态：2执行中
    public final static String DB_TASK_EXECUTESTATE_EXECUTE_SUCCESS = "3";//任务执行状态：3执行成功
    public final static String DB_TASK_EXECUTESTATE_EXECUTE_FAIL = "4";//任务执行状态：4执行失败
    /*数据分类数据库脱敏常量end*/

    /*格式化文件脱敏任务表常量start*/
    public final static String FILETASK_SUBMITTYPE_HAND = "1";//执行方式：1手动提交
    public final static String FILETASK_SUBMITTYPE_AUTO = "2";//执行方式：2定时执行

    public final static String FILETASK_EXECUTESTATE_NOT_SUBMIT = "0";//任务执行状态：0未提交
    public final static String FILETASK_EXECUTESTATE_SUBMIT_FAIL = "1";//任务执行状态：1提交失败
    public final static String FILETASK_EXECUTESTATE_EXECUTING = "2";//任务执行状态：2执行中
    public final static String FILETASK_EXECUTESTATE_EXECUTE_SUCCESS = "3";//任务执行状态：3执行成功
    public final static String FILETASK_EXECUTESTATE_EXECUTE_FAIL = "4";//任务执行状态：4执行失败

    public final static String FILETASK_OUT_TYPE_TXT = "1";//任务执行状态：0未提交
    public final static String FILETASK_OUT_TYPE_EXCEL = "2";//任务执行状态：0未提交
    public final static String FILETASK_OUT_TYPE_CSV = "3";//任务执行状态：0未提交
    /*格式化文件脱敏任务表常量end*/


    /*HDFS文件脱敏常量start*/
    public final static String HDFS_TASK_EXECUTESTATE_NOT_SUBMIT = "0";//任务执行状态：0未提交
    public final static String HDFS_TASK_EXECUTESTATE_SUBMIT_FAIL = "1";//任务执行状态：1提交失败
    public final static String HDFS_TASK_EXECUTESTATE_EXECUTING = "2";//任务执行状态：2执行中
    public final static String HDFS_TASK_EXECUTESTATE_EXECUTE_SUCCESS = "3";//任务执行状态：3执行成功
    public final static String HDFS_TASK_EXECUTESTATE_EXECUTE_FAIL = "4";//任务执行状态：4执行失败
    public final static String STRMASKLOGFLAG = "【脱敏审计日志】";
    public final static String PLATFORM_HADOOP = "Hadoop";
    public final static String BLANK = " ";

    /*HDFS文件脱敏常量end*/

    /*Hive脱敏常量start*/
    public final static String HIVE_TASK_EXECUTESTATE_NOT_SUBMIT = "0";//任务执行状态：0未提交
    public final static String HIVE_TASK_EXECUTESTATE_SUBMIT_FAIL = "1";//任务执行状态：1提交失败
    public final static String HIVE_TASK_EXECUTESTATE_EXECUTING = "2";//任务执行状态：2执行中
    public final static String HIVE_TASK_EXECUTESTATE_EXECUTE_SUCCESS = "3";//任务执行状态：3执行成功
    public final static String HIVE_TASK_EXECUTESTATE_EXECUTE_FAIL = "4";//任务执行状态：4执行失败
    public final static String JOIN_SLASH = "/";
    public final static String JOIN_EQUALS_SIGN = "=";
    public final static String JOIN_ONESIDE_SINGLEQUOTE = "'";
    public final static String JOIN_AND = "and";
    public final static String JOIN_BLANK = " ";
    public final static String LZO = "lzo";
    public final static String GZ = "gz";
    /*Hive脱敏常量end*/

    /*脱敏审计常量start*/
    public final static String MASKAUDIT_SUBMITTYPE_HAND = "1";//执行方式：1手动提交
    public final static String MASKAUDIT_SUBMITTYPE_AUTO = "2";//执行方式：2定时执行
    public final static String AUDIT_SPLIT_JOIN = "-";
    public final static String MASKAUDIT_TASK_EXECUTESTATE_NOT_SUBMIT = "0";//任务执行状态：0未提交
    public final static String MASKAUDIT_TASK_EXECUTESTATE_SUBMIT_FAIL = "1";//任务执行状态：1提交失败
    public final static String MASKAUDIT_TASK_EXECUTESTATE_EXECUTING = "2";//任务执行状态：2执行中
    public final static String MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_SUCCESS = "3";//任务执行状态：3执行成功
    public final static String MASKAUDIT_TASK_EXECUTESTATE_EXECUTE_FAIL = "4";//任务执行状态：4执行失败
    public final static String MASKAUDIT_RESULT_HIGH = "3";
    public final static String MASKAUDIT_RESULT_MIDDLE = "2";
    public final static String MASKAUDIT_RESULT_LOW = "1";
    public final static String MASKAUDIT_RESULT_NO = "0";


    public final static String MASKAUDIT_JARLOG_TASKFLAG = "脱敏jar包任务执行日志";
    public final static String MASKAUDIT_JARLOG_CREATR_TMP_FUN = "CREATE TEMPORARY FUNCTION ";//注意此处有空格
    public final static String MASKAUDIT_JARLOG_AS = " AS ";//注意此处有空格
    public final static String MASKAUDIT_JARLOG_SELECT = "SELECT ";//注意此处有空格
    public final static String MASKAUDIT_JARLOG_FROM = "FROM ";//注意此处有空格
    public final static String MASKAUDIT_JARLOG_LIMIT = "LIMIT ";//注意此处有空格
    public final static String MASKAUDIT_JARLOG_HOSTNAME = "主机名";
    public final static String MASKAUDIT_JARLOG_HOSTIP = "主机IP";
    public final static String MASKAUDIT_JARLOG_ALGPACKAGE = "调用算法包";

    public final static String MASKAUDIT_CHECKTYPE_DB = "1";// 任务类型：库表
    public final static String MASKAUDIT_CHECKTYPE_FILE = "2"; // 任务类型：文件
    public final static String MASKAUDIT_CHECKTYPE_LOG = "3"; // 任务类型：日志
    public final static String MASKAUDIT_CHECKTYPE_JARLOG = "4"; // 任务类型：脱敏Jar包
    public final static String MASKAUDIT_CHECKTYPE_PRO_JARLOG = "5"; // 任务类型：脱敏程序Jar包

    public final static String MASKAUDIT_FILETYPE_TXT = "1";// 文件类型：txt
    public final static String MASKAUDIT_FILETYPE_EXCEL = "2"; // 文件类型：excel
    public final static String MASKAUDIT_FILETYPE_CSV = "3"; // 文件类型：csv

    public final static String MASKAUDIT_STRATEGY_TRUE = "0"; //符合策略
    public final static String MASKAUDIT_STRATEGY_FALSE = "1"; //不符合策略
    public final static String MASKAUDIT_RESULTTYPE_NORMAL = "正常"; //不符合策略
    /*脱敏审计常量end*/

    /*过滤敏感数据结果条件表常量start*/
    public final static String SENSITIVEDATA_QUERY_CONDITION_true = "是";//是
    public final static String SENSITIVEDATA_QUERY_CONDITION_false = "否";//否
    /*过滤敏感数据结果条件表常量end*/

    /*Kafka脱敏常量start*/
    public final static String KAFKA_TASK_STATUS_START = "0"; // Kafka任务状态初始创建
    public final static String KAFKA_TASK_STATUS_EXECUTION = "1"; // Kafka任务状态执行中
    public final static String KAFKA_TASK_STATUS_KILL = "2";// Kafka任务状态停止
    public final static String KAFKA_TASK_STATUS_SUBMIT_FAIL = "3";// Kafka任务状态提交失败

    public final static String KAFKA_MESSAGE_PREFIX = "{";// Kafka数据前缀分隔符
    public final static String KAFKA_MESSAGE_NEWLINE = "\n";// Kafka数据换行符
    public final static String KAFKA_REDISKEY_0 = "0";// Kafka Rediskey 0 "yyyyMMdd-HH"
    public final static String KAFKA_REDISKEY_1 = "1";// Kafka Rediskey 0 "yyyyMMdd-HHmm"
    /*Kafka脱敏常量end*/

    /*脱敏算法常量start*/
    public final static String BLOWFISH_ENC = "BLOWFISH_ENC";
    public final static String BLOWFISH_DEC = "BLOWFISH_DEC";
    public final static String BASE64_ENC = "BASE64_ENC";
    public final static String BASE64_DEC = "BASE64_DEC";
    public final static String AES_ENC = "AES_ENC";
    public final static String AES_DEC = "AES_DEC";
    public final static String DES_ENC = "DES_ENC";
    public final static String DES_DEC = "DES_DEC";
    public final static String THREEDES_ENC = "THREEDES_ENC";
    public final static String THREEDES_DEC = "THREEDES_DEC";
    public final static String MD5 = "MD5";
    public final static String MD5SALT = "MD5SALT";
    public final static String SHA1 = "SHA1";
    public final static String SHA1SALT = "SHA1SALT";
    public final static String TRUNCATEDMD5 = "TRUNCATEDMD5";
    public final static String SM3 = "SM3";
    public final static String SM4_ENC = "SM4_ENC";
    public final static String SM4_DEC = "SM4_DEC";
    public final static String AESMD5 = "AESMD5";
    public final static String AESSHA256 = "AESSHA256";
    public final static String AESSM3 = "AESSM3";

    public final static String TRUNCATED = "TRUNCATED";
    public final static String IDCARD_ENC = "IDCARD_ENC";
    public final static String IDCARD_DEC = "IDCARD_DEC";
    public final static String IDRESET_ENC = "IDRESET_ENC";
    public final static String IDRESET_DEC = "IDRESET_DEC";
    public final static String IMSI_ENC = "IMSI_ENC";
    public final static String IMSI_DEC = "IMSI_DEC";
    public final static String MEID = "MEID";
    public final static String PHONE_ENC = "PHONE_ENC";
    public final static String PHONE_DEC = "PHONE_DEC";
    public final static String CNAME_ENC = "CNAME_ENC";
    public final static String CNAME_DEC = "CNAME_DEC";
    public final static String ADDRESS = "ADDRESS";
    public final static String DATEMOVEROUND = "DATEMOVEROUND";
    public final static String ACCOUNT_ENC = "ACCOUNT_ENC";
    public final static String ACCOUNT_DEC = "ACCOUNT_DEC";
    public final static String REPLACE_ENC = "REPLACE_ENC";
    public final static String REPLACE_DEC = "REPLACE_DEC";
    public final static String IDMASK = "IDMASK";
    public final static String MD5AES = "MD5AES";
    public final static String SHA256 = "SHA256";
    public final static String SM4CBC_ENC = "SM4CBC_ENC";
    public final static String SM4CBC_DEC = "SM4CBC_DEC";
    public final static String SM4ECB_ENC = "SM4ECB_ENC";
    public final static String SM4ECB_DEC = "SM4ECB_DEC";
    public final static String FIXEDMAPPING_ENC = "FIXEDMAPPING_ENC";
    public final static String FIXEDMAPPING_DEC = "FIXEDMAPPING_DEC";
    public final static String ORGNAMEMASK = "ORGNAMEMASK";
    public static final String FIXREPLACE_ENC = "FIXREPLACE_ENC";
    public static final String FIXREPLACE_DEC = "FIXREPLACE_DEC";
    public static final String PHONEFIXREPLACEKEEPSEGMENT = "PHONEFIXREPLACEKEEPSEGMENT";

    /*脱敏算法常量end*/

    /*接口日志检测常量start*/
    public final static String REGEXP_RULE = "r_";
    public final static String PRO_RULE = "p_";
    public final static String CLEAETEXT_SUFFIX = "_cleartext.log";//保存明文数据，文件名后缀
    public final static String NOT_STANDARD = "不符合接口规范";
    public final static String STANDARD = "符合接口规范";
    public final static String RISK_NOT = "0";// 风险程度无
    public final static String RISK_LOW = "1";// 风险程度低
    public final static String RISK_MIDDLE = "2";// 风险程度中
    public final static String RISK_HIGH = "3";// 风险程度高
    /*接口日志检测常量end*/

    /*数据操作审计常量start*/
    public final static String OPERATION_CHECK_TYPE_HIVE = "Hive";
    public final static String OPERATION_CHECK_TYPE_MYSQL = "MySQL";
    public final static String OPERATION_CHECK_TYPE_HDFS = "HDFS";
    public final static String OPERATION_CHECK_TYPE_LINUX = "Linux";
    public final static String OPERATION_CHECK_TYPE_4A = "4A";
    public final static String OPERATION_LOG_PRE_HIVE = "【Hive操作日志未匹配命令】:";
    public final static String OPERATION_LOG_PRE_MYSQL = "【MySQL操作日志未匹配命令】:";
    public final static String OPERATION_LOG_PRE_HDFS = "【HDFS操作日志未匹配命令】:";
    public final static String OPERATION_LOG_PRE_LINUX = "【Linux操作日志未匹配命令】:";
    public final static String OPERATION_LOG_PRE_4A = "【4A操作日志未匹配命令】:";
    /*数据操作审计常量end*/

    /*任务号标识前缀start*/
    public final static String MASK_HIVE_TASKNUM_PREFIX = "0101";// Hive脱敏任务号前缀
    public final static String MASK_HDFS_TASKNUM_PREFIX = "0102";// HDFS脱敏任务号前缀
    public final static String MASK_KAFKA_TASKNUM_PREFIX = "0103";// Kafka脱敏任务号前缀
    public final static String MASK_DB_TASKNUM_PREFIX = "0104";// 数据库脱敏任务号前缀
    public final static String MASK_FILE_TASKNUM_PREFIX = "0105";// 文件脱敏任务号前缀
    public final static String MASK_FILE_PICTURE_PREFIX = "0106";// 图片脱敏任务号前缀
    public final static String MASK_FILE_VIDEO_PREFIX = "0107";// 视频脱敏任务号前缀
    public final static String MASK_DB_BATCH_TASKNUM_PREFIX = "0108";// 批量脱敏任务号前缀
    public final static String MASK_AUDIT_TASKNUM_PREFIX = "0201";// 脱敏审计任务号前缀
    public final static String MASK_HBASE_TASKNUM_PREFIX = "0203";// HBASE脱敏任务号前缀
    public final static String MASK_ANONYMIZATION_TASKNUM_PREFIX = "0204";// 匿名化脱敏任务号前缀
    public final static String WP_TASKNUM_PREFIX = "0301";// 弱密码任务号前缀
    /*任务号标识前缀end*/

    /*文件审计常量start*/
    //public final static String FA_TASK_TYPE_KEYWORD = "关键字检测";
    public final static String FA_TASK_TYPE_SENSITIVITY_DATA = "敏感数据检测";
    /*文件审计常量end*/

    /*接口审计常量start*/
    public final static String[] IC_TASK_RULE_TYPE = {"正则表达式", "数据标识符", "程序算法"};//接口审计检测规则
    /*接口审计常量end*/

    /*JDBC驱动SQL记录常量start*/
    public final static String JDBC_SQLRECORD_REWRITESTATE_SUCCESS = "改写成功";//改写成功
    public final static String JDBC_SQLRECORD_REWRITESTATE_FAIL = "改写失败";//改写失败
    public final static String JDBC_SQLRECORD_REWRITESTATE_DEFAULT = "未改写";//未改写
    public final static String JDBC_SQLRECORD_HANDLESTATE_RELEASE = "放行";//放行
    public final static String JDBC_SQLRECORD_HANDLESTATE_INTERCEPT = "拦截";//拦截
    public final static String JDBC_SQLRECORD_EXECSTATE_SUCCESS = "执行成功";//
    public final static String JDBC_SQLRECORD_EXECSTATE_FAIL = "执行失败";//
    /*JDBC驱动SQL记录常量start*/

    /*Proxy代理常量start*/
    // 数据库编码
    public final static String ORACLE_CHARSET_GBK = "GBK";
    public final static String ORACLE_CHARSET_UTF8 = "UTF8";

    // 脱敏掩码符号*对应的字节，UTF-8时为42，UTF-16时为0 42
    public final static byte MASK_STAR = 0x2a;

    // 操作类型-
    public final static String OPTION_TYPE_QUERY = "查询";
    public final static String OPTION_TYPE_EXPORT = "导出";

    // 脱敏类型
    public final static String MASKSUCCESS_TEXT = "改写SQL";
    public final static String REWRITERESPONSEDATA_TEXT = "改写结果";
    public final static String REWRITERESPONSEDATAERROR_TEXT = "改写结果失败";
    // 黑名单-类型
    public final static String BLACKLIST_TYPE = "UntreatedType_0";
    // 黑名单-返回信息
    public final static String BLACKLIST_MESSAGE = "The user name or IP is on the blacklist";
    // 黑名单-记录日志类型
    public final static String BLACKLIST_TEXT = "黑名单";
    // 不支持语句类型-类型
    public final static String UNSUPPORTEDTYPE_TYPE = "UntreatedType_1";
    // 不支持语句类型-返回信息
    public final static String UNSUPPORTEDTYPE_MESSAGE = "This type is not supported";
    // 不支持语句类型-记录日志类型
    public final static String UNSUPPORTEDTYPE_TEXT = "不支持语句类型";
    // 未配置脱敏策略-类型
    public final static String NOCONFIGSTRATEGY_TYPE = "UntreatedType_2";
    // 未配置脱敏策略-返回信息
    public final static String NOCONFIGSTRATEGY_MESSAGE = "Desensitization strategy is not configured";
    // 未配置脱敏策略-记录日志类型
    public final static String NOCONFIGSTRATEGY_TEXT = "未配置脱敏策略";
    // 未进行用户配置-类型
    public final static String NOCONFIGUSER_TYPE = "UntreatedType_3";
    // 未进行用户配置-返回信息
    public final static String NOCONFIGUSER_MESSAGE = "The user is not configured";
    // 未进行用户配置-记录日志类型
    public final static String NOCONFIGUSER_TEXT = "未进行用户配置";
    // 改写后SQL超长-类型
    public final static String SQLTOOLONG_TYPE = "UntreatedType_4";
    // 改写后SQL超长-返回信息
    public final static String SQLTOOLONG_MESSAGE = "The sql after rewriting is too long";
    // 改写后SQL超长-记录日志类型
    public final static String SQLTOOLONG_TEXT = "改写后SQL超长";
    // 白名单-类型
    public final static String WHITELIST_TYPE = "UntreatedType_5";
    // 白名单-记录日志类型
    public final static String WHITELIST_TEXT = "白名单";

    //动态脱敏算法类型
    public static final String ALGO_TYPE_MASK = "掩码";
    public static final String ALGO_TYPE_MD5 = "散列";
    public static final String ALGO_TYPE_FIXED_REPLACE = "固定替换";
    public static final String ALGO_TYPE_CLEARTEXT = "明文";
    public static final String ALGO_TYPE_REPLACE = "替换";
    public static final String ALGO_TYPE_CONTAIN_TRUNCATE = "包含指定字符截断";
    public static final String ALGO_TYPE_UNCONTAIN_TRUNCATE = "不包含指定字符截断";
    public static final String ALGO_TYPE_DATE_RANGE_RANDOM = "时间范围内随机";
    public static final String ALGO_TYPE_NUM_RANGE_RANDOM = "数字范围内随机";
    public static final String ALGO_TYPE_CUTOUT = "截取";
    public static final String ALGO_TYPE_EXCISION = "截除";
    public static final String ALGO_TYPE_SETZERO = "归零";
    public static final String ALGO_TYPE_NUM_FLOAT = "数字浮动";
    public static final String ALGO_TYPE_SETNULL = "置空";
    public static final String ALGO_TYPE_AGEGROUP = "分档";
    public static final String ALGO_TYPE_RANDOM_REPLACE = "随机替换";
    public static final String ALGO_TYPE_ADD_MASK = "地址掩码";
    public static final String ALGO_TYPE_AES_ENCRYPT = "AES加密";
    public static final String ALGO_TYPE_AES_DECRYPT = "AES解密";
    public static final String ALGO_TYPE_DES_ENCRYPT = "DES加密";
    public static final String ALGO_TYPE_DES_DECRYPT = "DES解密";

    //动态脱敏字段类型
    public static final String FIELD_TYPE_ADDRESS = "地址";
    public static final String FIELD_TYPE_EMAIL = "电子邮箱地址";
    public static final String FIELD_TYPE_MOBILENUMBER = "手机号码";
    public static final String FIELD_TYPE_FIXPHONENUMBER = "固话号码";
    public static final String FIELD_TYPE_DATE = "时间";

    //动态脱敏改写结果算法类型
    public static final String RESULTALGO_TYPE_MASK = "掩码";
    public static final String RESULTALGO_TYPE_ADD_MASK = "地址掩码";
    public static final String RESULTALGO_TYPE_NUMBERROUND = "数字取整";

    //API动态脱敏常量
    public static final String API_ISMASK_NO = "0";
    public static final String API_ISMASK_YES = "1";
    public static final String API_ISJSON_NO = "0";
    public static final String API_ISJSON_YES = "1";
    public static final String API_MASKSTATUS_SUCCESS = "1";
    public static final String API_MASKSTATUS_FAIL = "2";


    //SQL类型常量start
    public final static String SQL_TYPE_S001 = "S_001";// S_开头表示单表
    public final static String SQL_TYPE_M001 = "M_001";// M_开头表示连接表查询
    public final static String SQL_TYPE_SQ001 = "SQ_001";// SQ_开头表示子查询
    public final static String SQL_TYPE_SQ002 = "SQ_002";
    public final static String SQL_TYPE_U001 = "U_001";// U_开头表示Union查询
    public final static String SQL_TYPE_U002 = "U_002";

    //数据库关键字常量start
    public final static String DB_KEYWORD_SELECT = "SELECT";
    public final static String DB_KEYWORD_FROM = "FROM";
    public final static String DB_KEYWORD_WHERE = "WHERE";
    public final static String DB_KEYWORD_AS = "AS";
    public final static String DB_KEYWORD_UNION = "UNION";
    public final static String DB_KEYWORD_UNION_ALL = "UNION ALL";
    public final static String DB_KEYWORD_ORDER_BY = "ORDER BY";
    public final static String DB_KEYWORD_DESC = "DESC";
    public final static String DB_KEYWORD_ASC = "ASC";
    public final static String DB_KEYWORD_GROUP_BY = "GROUP BY";
    public final static String DB_KEYWORD_HAVING = "HAVING";
    public final static String DB_KEYWORD_JOIN = "JOIN";
    public final static String DB_KEYWORD_INNER_JOIN = "INNER JOIN";
    public final static String DB_KEYWORD_LEFT_JOIN = "LEFT JOIN";
    public final static String DB_KEYWORD_RIGHT_JOIN = "RIGHT JOIN";
    public final static String DB_KEYWORD_ON = "ON";
    public final static String DB_KEYWORD_LIMIT = "LIMIT";

    //数据源类型常量start
    public final static String MYSQL_SIGN = "MYSQL";
    public final static String ORACLE_SIGN = "ORACLE";
    public final static String RDSMYSQL_SIGN = "RDS-MYSQL";

    // 库名表名字段名拼接符
    public final static String DB_TAB_FIELD_JOIN = "&_&";

    //CTR文件检测类型
    public final static String FILE_BUSINESS_TYPE_CTR = "CTR";


    //ES告警索引前缀(写ES)
    public final static String ES_ALARM_INDEX_PREFIX = "cksjaudit-";

    //ES待检测数据索引前缀(读ES)
    public final static String ES_DETECTED_INDEX_PREFIX = "netflow-";

    public final static String API_ALERT_TYPE = "apicall"; //事件类型
    public final static String API_ALERT_CLASSIFY = "Alert"; //事件分类

    //接口对应的平台或系统
    public final static String API_ALERT_PALTFORM = "共享交换平台";
    public final static String API_ALERT_CLIENT = "客户端";
    public final static String API_ALERT_SERVER = "服务端";

    public final static String CONVERT_TIME_TO_UTC = "yyyy-MM-dd'T'HH:mm:ss.S'Z'";

    //告警级别
    public final static String API_ALERT_SEVERITY_HIGH = "3"; //告警等级 高
    public final static String API_ALERT_SEVERITY_MIDDLE = "2"; //告警等级 中
    public final static String API_ALERT_SEVERITY_LOW = "1"; //告警等级 低
    public final static String API_ALERT_SEVERITY_NOT = "0"; //告警等级 无

    //告警规则分类
    public final static String ALERT_RULE_SORT_API = "api";

    //风险程度
    public final static String SEVERITY_HIGH = "高危"; //风险程度 高危
    public final static String SEVERITY_MIDDLE = "中危"; //风险程度 中危
    public final static String SEVERITY_LOW = "低危"; //风险程度 低危


    //告警结果输出
    public final static String ALERT_DATA_OUTTYPE_ES = "1";//输出到ES
    public final static String ALERT_DATA_OUTTYPE_KAFKA = "2";//输出到Kafka


    //基本常量
    public final static String CLIENT_ERROR_STATUS = "1";  //客户端错误
    public final static String SERVER_ERROR_STATUS = "1";  //服务端错误


    //告警编号及类型
    public final static String API_ALERT_UNEXPECTED_METHOD = " CKNR0001";    //非预期的方法
    public final static String API_ALERT_UNEXPECTED_FIELD_NAME = " CKNR0002";    //非预期的字段名
    public final static String API_ALERT_UNEXPECTED_FIELD_VALUE = " CKNR0003";    //非预期的字段值
    public final static String API_ALERT_PARAMETER_ENUMERATION_ATTACK = " CKNR0004";    //参数枚举攻击
    public final static String API_ALERT_REQUEST_RESPONSE_CORRESPONDING_EXCEPTION = " CKNR0005";    //请求与返回内容对应关系异常
    public final static String API_ALERT_PARAMETER_LENGTH_ABNORMAL = " CKNR0006";    //参数长度异常
    public final static String API_ALERT_EXPOSE_SENSITIVE = " CKNR0007";    //API暴露敏感数据
    public final static String API_ALERT_SENSITIVE_RETURNED_LARGE = " CKNR0008";    //返回敏感数据量偏大
    public final static String API_ALERT_RETURN_SENSITIVE_TYPE_EXCEPTION = " CKNR0009";    //返回敏感数据类型异常
    public final static String API_ALERT_ABNORMAL_CONTENT_FORMAT = " CKNR0010";    //内容格式异常

    //类型(API内容异常检测)
    public final static String API_UNEXPECTED_METHOD = "非预期的方法";    //非预期的方法
    public final static String API_UNEXPECTED_FIELD_NAME = "非预期的字段名";   //非预期的字段名
    public final static String API_UNEXPECTED_FIELD_VALUE = "非预期的字段值";   //非预期的字段值
    public final static String API_PARAMETER_ENUMERATION_ATTACK = "参数枚举攻击";   //参数枚举攻击
    public final static String API_REQUEST_RESPONSE_CORRESPONDING_EXCEPTION = "请求与返回内容对应关系异常";   //请求与返回内容对应关系异常
    public final static String API_PARAMETER_LENGTH_ABNORMAL = "参数长度异常";   //参数长度异常
    public final static String API_EXPOSE_SENSITIVE_DATA = "API暴露敏感数据";   //API暴露敏感数据
    public final static String API_SENSITIVE_RETURNED_LARGE = "返回敏感数据量偏大";   //返回敏感数据量偏大
    public final static String API_RETURN_SENSITIVE_TYPE_EXCEPTION = "返回敏感数据类型异常";   //返回敏感数据类型异常
    public final static String API_ABNORMAL_CONTENT_FORMAT = "内容格式异常";   //内容格式异常

    public final static String API_ALERT_VISITS_ARE_HIGH = "CKXW0001";    //API访问量偏大
    public final static String API_ALERT_USER_VISITS_ARE_HIGH = "CKXW0002";    //用户访问API次数偏大
    public final static String API_ALERT_BURSTS_ACQUIRE_LARGE_DATA = "CKXW0003";    //API突发获取大量数据
    public final static String API_ALERT_VISITS_DURING_ABNORMAL = "CKXW0004";    //非常规时段API出现大量访问
    public final static String API_ALERT_USERS_OBTAINED_SENSITIVE_ABNORMAL = "CKXW0005";    //用户通过API获取的敏感数据量异常
    public final static String API_ALERT_CLIENT_BAD_REQUESTS_HIGH = "CKXW0006";    //用户向API发起的包含客户端错误请求数量偏高
    public final static String API_ALERT_SERVER_BAD_REQUESTS_HIGH = "CKXW0007";    //用户向API发起的包含服务器错误请求数量偏高
    public final static String API_ALERT_NO_CALL_VOLUME = "CKXW0008";    //疑似僵尸API
    public final static String API_ALERT_SUDDEN_ACCESS_LONG_NON_VISITED = "CKXW0009";    //突发访问罕见API
    public final static String API_ALERT_SILENT_ACCOUNT_BURST_ACCESS = "CKXW0010";    //静默账号突发访问API
    public final static String API_ALERT_TRAFFIC_LARGE = "CKXW0011";    //API流量偏大
    public final static String API_ALERT_USER_TRAFFIC_HIGH = "CKXW0012";    //用户访问API流量偏大

    //类型(API调用行为异常检测)
    public final static String API_VISITS_ARE_HIGH = "API访问量偏大";    //API访问量偏大
    public final static String API_USER_VISITS_ARE_HIGH = "用户访问API次数偏大";    //用户访问API次数偏大
    public final static String API_BURSTS_ACQUIRE_LARGE_DATA = "API突发获取大量数据";    //API突发获取大量数据
    public final static String API_VISITS_DURING_ABNORMAL = "非常规时段API出现大量访问";    //非常规时段API出现大量访问
    public final static String API_USERS_OBTAINED_SENSITIVE_ABNORMAL = "用户通过API获取的敏感数据量异常";    //用户通过API获取的敏感数据量异常
    public final static String API_CLIENT_BAD_REQUESTS_HIGH = "用户向API发起的包含客户端错误请求数量偏高";    //用户向API发起的包含客户端错误请求数量偏高
    public final static String API_SERVER_BAD_REQUESTS_HIGH = "用户向API发起的包含服务器错误请求数量偏高";    //用户向API发起的包含服务器错误请求数量偏高
    public final static String API_NO_CALL_VOLUME = "疑似僵尸API";    //疑似僵尸API
    public final static String API_SUDDEN_ACCESS_LONG_NON_VISITED = "突发访问罕见API";    //突发访问罕见API
    public final static String API_SILENT_ACCOUNT_BURST_ACCESS = "静默账号突发访问API";    //静默账号突发访问API
    public final static String API_TRAFFIC_LARGE = "API流量偏大";    //API流量偏大
    public final static String API_USER_TRAFFIC_HIGH = "用户访问API流量偏大";    //用户访问API流量偏大

    //API内容异常告警规则描述
    public final static String API_ALERT_UNEXPECTED_METHOD_DESCRIPTION = "出现了未允许或未出现过的请求方法，可能意味着属于自动化构造的恶意请求包";    //非预期的方法
    public final static String API_ALERT_UNEXPECTED_FIELD_NAME_DESCRIPTION = "基于历史数据包字段名统计，出现了未见过/非常罕见的字段名称，可能为恶意构造的请求包";    //非预期的字段名
    public final static String API_ALERT_UNEXPECTED_FIELD_VALUE_DESCRIPTION = "如果一个字段对应值属于可枚举类型的,突然某一天该请求该字段值出现了枚举值范围以外的值，则认为异常";    //非预期的字段值
    public final static String API_ALERT_PARAMETER_ENUMERATION_ATTACK_DESCRIPTION = "用户对同一个API短时间发起大量请求，请求关键参数值中存在枚举行为，比如短信验证码的枚举、口令枚举等";    //参数枚举攻击
    public final static String API_ALERT_REQUEST_RESPONSE_CORRESPONDING_EXCEPTION_DESCRIPTION = "学习请求和返回内容中抽样的内容映射关系，比如特定url请求必定会返回固定长度TokenID值；或者请求中包含特定字段值时才会返回特定类型内容，如果违反该规律，则告警";    //请求与返回内容对应关系异常
    public final static String API_ALERT_PARAMETER_LENGTH_ABNORMAL_DESCRIPTION = "对API请求和响应包中字段值长度进行统计学习，如果出现偏离参数长度的请求/响应（包括偏大或者偏小），则进行告警";    //参数长度异常
    public final static String API_ALERT_EXPOSE_SENSITIVE_DESCRIPTION = "对请求/响应数据包中内容进行敏感数据类型匹配，发现包含敏感数据的API接口";    //API暴露敏感数据
    public final static String API_ALERT_SENSITIVE_RETURNED_LARGE_DESCRIPTION = "基于历史统计基线，API返回内容中包含的敏感数据量偏大";    //返回敏感数据量偏大
    public final static String API_ALERT_RETURN_SENSITIVE_TYPE_EXCEPTION_DESCRIPTION = "API返回内容中出现了新的类型敏感数据";    //返回敏感数据类型异常
    public final static String API_ALERT_ABNORMAL_CONTENT_FORMAT_DESCRIPTION = "识别请求和响应头内容是否符合协议规范，有没有包含一些显著的攻击payload，比如对于Restful结构，URL格式是否正确；方法是否属于MIME类型，请求参数是否包含SQL注入特征代码";    //内容格式异常

    //API调用行为异常告警规则描述
    public final static String API_ALERT_VISITS_ARE_HIGH_DESCRIPTION = "基于基线模型，判断时间窗口内某API的次数是否超过基线值";    //API访问量偏大
    public final static String API_ALERT_USER_VISITS_ARE_HIGH_DESCRIPTION = "在时间窗口内某用户访问某接口的次数偏大";    //用户访问API次数偏大
    public final static String API_ALERT_BURSTS_ACQUIRE_LARGE_DATA_DESCRIPTION = "在某时间窗口内API响应输出大量数据";    //API突发获取大量数据
    public final static String API_ALERT_VISITS_DURING_ABNORMAL_DESCRIPTION = "针对某一个API接口，识别在非常规时段是否出现大量请求访问";    //非常规时段API出现大量访问
    public final static String API_ALERT_USERS_OBTAINED_SENSITIVE_ABNORMAL_DESCRIPTION = "时间窗口内用户向API获取的敏感数据（比如身份证、银行卡号等）数目超过基线值";    //用户通过API获取的敏感数据量异常
    public final static String API_ALERT_CLIENT_BAD_REQUESTS_HIGH_DESCRIPTION = "时间窗口内用户向API发起的、返回值包含客户端错误的请求次数异常";    //用户向API发起的包含客户端错误请求数量偏高
    public final static String API_ALERT_SERVER_BAD_REQUESTS_HIGH_DESCRIPTION = "时间窗口内用户向API发起的、返回值包含服务器错误的请求次数异常";    //用户向API发起的包含服务器错误请求数量偏高
    public final static String API_ALERT_NO_CALL_VOLUME_DESCRIPTION = "过去30天内API列表中没有访问请求的API接口";    //疑似僵尸API
    public final static String API_ALERT_SUDDEN_ACCESS_LONG_NON_VISITED_DESCRIPTION = "过去一段时间内只有少数IP访问的接口，是否为正常业务需要，是否存在被利用风险";    //突发访问罕见API
    public final static String API_ALERT_SILENT_ACCOUNT_BURST_ACCESS_DESCRIPTION = "过去一段时间内账号处于静默状态，现突发访问API行为";    //静默账号突发访问API
    public final static String API_ALERT_TRAFFIC_LARGE_DESCRIPTION = "基于基线模型，判断时间窗口内某API接收到的访问流量是否超过基线值";    //API流量偏大
    public final static String API_ALERT_USER_TRAFFIC_HIGH_DESCRIPTION = "在时间窗口内某用户访问某接口的流量偏大";  //用户访问API流量偏大


    //引擎配置_引擎服务器工作状态
    //public final static String SERVER_EXECUTION_UNWANTED = "0";  //空闲
    public final static String SERVER_EXECUTION_USING = "1";  //在用
    public final static String SERVER_EXECUTION_AVAILABLE = "2";  //可用
    public final static String SERVER_EXECUTION_UNAVAILABLE = "3";  //不可用
    public final static String SERVER_EXECUTION_UNKNOWN = "4";  //未知

    //服务监控常量
    public final static String SERVICE_MONITORING_STATUS_NORMAL = "正常";
    public final static String SERVICE_MONITORING_STATUS_ABNORMAL = "异常";

    // 系统软件license授权功能常量
    public final static String AUTHORIZATION_SYSTEM_NAME = "数据脱敏系统"; //系统名称
    public final static String AUTHORIZATION_VERSION = "V3.6.13"; //版本
    public final static String AUTHORIZATION_AES_SECRET_KEY = "wzSec@20"; //AES加密秘钥
    public final static String AUTHORIZATION_PERMANENT = "2999-12-30"; //永久授权
    public final static String AUTHORIZATION_UNLIMITED = "9999"; //资源限制


    //静态脱敏-脱敏策略常量
    public final static String STATIC_DESENSITIZATION_STRATEGY_APPROVAL_YES = "0"; //需要审批
    public final static String STATIC_DESENSITIZATION_STRATEGY_APPROVAL_NO = "1"; //不需要审批
    public final static String STRATEGY_APPROVAL_STATUS_NOT_APPROVED = "0"; //未审批
    public final static String STRATEGY_APPROVAL_STATUS_PASS = "1"; //审批通过
    public final static String STRATEGY_APPROVAL_STATUS_NOT_PASS = "2"; //审批不通过

    //策略类型 1单表 2全量
    public final static String STRATEGY_TYPE_SINGLETABLE = "1";
    public final static String STRATEGY_TYPE_ALL = "2";

    public final static Long COMMON_ALL_STRATEGY_ID = 1L;

    //推送syslog标识
    public final static String EVENT_TYPE = "数据脱敏"; //数据脱敏
    public final static String DEVICE_TYPE = "库表"; //库表

    //接口告警处置处理状态
    public final static String INTERFACE_ALARM_DISPOSAL_UNHANDLED = "0"; //未处理
    public final static String INTERFACE_ALARM_DISPOSAL_IGNORED = "1"; //已忽略
    public final static String INTERFACE_ALARM_DISPOSAL_PROCESSED = "2"; //已处理

    //行为
    public final static String DICT_DATASORCE_CONNECTION_FAIL = "数据源连接失败";
    public final static String DICT_DATA_AUTMOATIC_SCAN_TASK_FAIL = "数据自动扫描任务执行失败";
    public final static String DICT_DATABASETABLE_EXECUTION_TASK_ERROR = "库表数据脱敏任务执行失败";
    public final static String DICT_MASK_EXECUTION_ENGINE_SERVER_ERROR = "脱敏执行引擎服务异常";
    public final static String DICT_FILE_EXECUTION_TASK_ERROR = "文件数据脱敏任务执行失败";
    public final static String DICT_HIVE_EXECUTION_TASK_ERROR = "Hive数据脱敏任务执行失败";

    public final static String DICT_DESENSITIZATION_ABNORMAL_DATA_HANDLING = "异常数据脱敏处置";

    public final static String EVENT_IS_SEND_SYSLOG = "is_send_syslog"; //是否写入字典取值标识
    public final static String EVENT_SEND_SYSLOG = "1"; //写入字典取值标识

    //操作新增，修改，删除
    public final static String MASK_ADD = "保存";
    public final static String ADD_MASK = "添加";
    public final static String MASK_UPDATE = "修改";
    public final static String MASK_DELETE = "删除";

    public final static String MASK_TASK_HIVE = "Hive";
    public final static String MASK_TASK_HDFS = "HDFS";
    public final static String MASK_TASK_KAFKA = "Kafka";
    public final static String MASK_TASK_DB = "数据库";
    public final static String MASK_TASK_DB_ONLY = "数据库单表";
    public final static String MASK_TASK_DB_MORE = "数据库多表";
    public final static String MASK_TASK_FILE = "文件";
    public final static String MASK_TASK_PIC = "图片";
    public final static String MASK_TASK_MOVE = "视频";

    // 溯源状态 0初始，1成功，2失败
    public final static String TRACE_STATE_INIT = "0";
    public final static String TRACE_STATE_SUCCESS = "1";
    public final static String TRACE_STATE_FAIL = "2";
    // 溯源数据是否敏感 1敏感，2不敏感
    public final static String TRACE_DATA_SEN = "1";
    public final static String TRACE_DATA_NOT_SEN = "2";
    // 溯源数据是否泄漏 1泄漏，2未泄露
    public final static String TRACE_DATA_LEAKAGE = "1";
    public final static String TRACE_DATA_NOT_LEAKAGE = "2";
    // 水印关键字
    public final static String TRACE_KEYWORD_PROVIDER = "数据提供方:";
    public final static String TRACE_KEYWORD_USE = "数据使用方:";

    //输出类型
    public static final String OUT_TYPE_DB = "1";
    public static final String OUT_TYPE_FILE = "2";

    public final static String ALGORITHM_IS_REVERSIBLE_YES = "1";//算法是否可逆，1是
    public final static String ALGORITHM_IS_REVERSIBLE_NO = "0";//算法是否可逆，0否

    public final static String IS_REWRITE_TAB_YES = "0";//是否基于原表改写，是
    public final static String IS_REWRITE_TAB_NO = "1";//是否基于原表改写，否

    public final static String IS_WATERMARK_YES = "1";//是否添加水印，是
    public final static String IS_WATERMARK_NO = "2";//是否添加水印，否

    public final static String MASK_WARY_ALL = "1";//脱敏方式，全量
    public final static String MASK_WARY_INCREMENT = "2";//脱敏方式，增量

    public final static String MASK_TABLE_NAME_ORIG = "0";//脱敏表名，维持原样
    public final static String MASK_TABLE_NAME_POSTFIX = "1";//脱敏表名，添加后缀

    public final static String IS_REMOVE_NO = "0";//是否删除，否
    public final static String IS_REMOVE_YES = "1";//是否删除，是

    public final static String QUARTZ_JOB_START = "0";//定时任务状态，开始
    public final static String QUARTZ_JOB_SUSPEND = "1";//定时任务状态，暂停

    // 操作类型(1新增，2删除，3修改)
    public final static String OPERATETYPE_ADD = "1";
    public final static String OPERATETYPE_DEL = "2";
    public final static String OPERATETYPE_ALTER = "3";

    //关联状态(1未关联，2已关联)
    public final static String STATE_NOT_CORRELATION = "1";
    public final static String STATE_CORRELATION = "2";

    //表类型(1主表，2从表)
    public final static String TABLETYPE_MASTER = "1";
    public final static String TABLETYPE_SLAVE = "2";

    //算法可用于分段
    public final static String EXTRACT_ALGORITHM = "1";

    //算法配置方式
    public final static String ALG_CONFIG_WAY_ENTIRETY = "0";//整体
    public final static String ALG_CONFIG_WAY_EXTRACT = "1";//分段

    //匿名化
    public final static String ANONYMIZATION_STATE = "1";//匿名化状态
    public final static String ANONYMIZATION_CARRY_STATE = "0";//匿名化任务初始执行状态

    //算法参数秘钥 （分段配置简化配置，参数秘钥写死）
    public final static Map<String,String> algorithmParameterSecretKeyMap(){
        Map<String,String> algorithmParameterSecretKeyMap = new HashMap<>();
        algorithmParameterSecretKeyMap.put("HIDECODE","*,0,0");//默认全部掩掉
        algorithmParameterSecretKeyMap.put("RANDOMMAPPING","");//随机映射不需要参数或秘钥
        return algorithmParameterSecretKeyMap;
    }

    public final static String OPERATIONRECORDPUSH_SYNCHRONIZATION = "operationRecordPushSynchronization";// 操作日志推送获取kafka连接信息

    //sdk插件名称
    public final static String SDK_FILE_NAME = "ds_bdms_engine";

    //下载文件类型
    public final static String DOWNLOAD_SDK_FILE_NAME = "SDK插件";
    public final static String DOWNLOAD_WORD_FILE_NAME = "相关文档";

    //SDK插件压缩包内文件名
    public final static String DOWNLOAD_SDK_ZIP_APPLICATION = "application.yml";
    public final static String DOWNLOAD_SDK_ZIP_APPLICATIONDEV = "application-dev.yml";
    public final static String DOWNLOAD_SDK_ZIP_PROPERTIES = "ds_bdms_engine.properties";
    public final static String DOWNLOAD_SDK_ZIP_JDK = "jdk-8u221-linux-x64.tar.gz";
    public final static String DOWNLOAD_SDK_WORD_TEST = "数据静态脱敏系统插件部署调试文档.docx";
    public final static String DOWNLOAD_SDK_WORD_USE = "数据静态脱敏系统插件使用说明文档.docx";

    //开始端口
    public static final Integer STRAT_PORT = 0;
    //结束端口
    public static final Integer END_PORT = 65535;
}
