package com.wzsec.modules.traceability.service.mapper;

import com.wzsec.modules.traceability.domain.FileMaskTrace;
import com.wzsec.modules.traceability.service.dto.FileMaskTraceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:19+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class FileMaskTraceMapperImpl implements FileMaskTraceMapper {

    @Override
    public FileMaskTrace toEntity(FileMaskTraceDto dto) {
        if ( dto == null ) {
            return null;
        }

        FileMaskTrace fileMaskTrace = new FileMaskTrace();

        fileMaskTrace.setId( dto.getId() );
        fileMaskTrace.setFilename( dto.getFilename() );
        fileMaskTrace.setFilesize( dto.getFilesize() );
        fileMaskTrace.setFilesuffix( dto.getFilesuffix() );
        fileMaskTrace.setDataseparator( dto.getDataseparator() );
        fileMaskTrace.setIssensitive( dto.getIssensitive() );
        fileMaskTrace.setIsleakage( dto.getIsleakage() );
        fileMaskTrace.setDataprovider( dto.getDataprovider() );
        fileMaskTrace.setDatause( dto.getDatause() );
        fileMaskTrace.setState( dto.getState() );
        fileMaskTrace.setOperationuser( dto.getOperationuser() );
        fileMaskTrace.setOperationtime( dto.getOperationtime() );
        fileMaskTrace.setSparefield1( dto.getSparefield1() );
        fileMaskTrace.setSparefield2( dto.getSparefield2() );
        fileMaskTrace.setSparefield3( dto.getSparefield3() );
        fileMaskTrace.setSparefield4( dto.getSparefield4() );
        fileMaskTrace.setSparefield5( dto.getSparefield5() );

        return fileMaskTrace;
    }

    @Override
    public FileMaskTraceDto toDto(FileMaskTrace entity) {
        if ( entity == null ) {
            return null;
        }

        FileMaskTraceDto fileMaskTraceDto = new FileMaskTraceDto();

        fileMaskTraceDto.setId( entity.getId() );
        fileMaskTraceDto.setFilename( entity.getFilename() );
        fileMaskTraceDto.setFilesize( entity.getFilesize() );
        fileMaskTraceDto.setFilesuffix( entity.getFilesuffix() );
        fileMaskTraceDto.setDataseparator( entity.getDataseparator() );
        fileMaskTraceDto.setIssensitive( entity.getIssensitive() );
        fileMaskTraceDto.setIsleakage( entity.getIsleakage() );
        fileMaskTraceDto.setDataprovider( entity.getDataprovider() );
        fileMaskTraceDto.setDatause( entity.getDatause() );
        fileMaskTraceDto.setState( entity.getState() );
        fileMaskTraceDto.setOperationuser( entity.getOperationuser() );
        fileMaskTraceDto.setOperationtime( entity.getOperationtime() );
        fileMaskTraceDto.setSparefield1( entity.getSparefield1() );
        fileMaskTraceDto.setSparefield2( entity.getSparefield2() );
        fileMaskTraceDto.setSparefield3( entity.getSparefield3() );
        fileMaskTraceDto.setSparefield4( entity.getSparefield4() );
        fileMaskTraceDto.setSparefield5( entity.getSparefield5() );

        return fileMaskTraceDto;
    }

    @Override
    public List<FileMaskTrace> toEntity(List<FileMaskTraceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FileMaskTrace> list = new ArrayList<FileMaskTrace>( dtoList.size() );
        for ( FileMaskTraceDto fileMaskTraceDto : dtoList ) {
            list.add( toEntity( fileMaskTraceDto ) );
        }

        return list;
    }

    @Override
    public List<FileMaskTraceDto> toDto(List<FileMaskTrace> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FileMaskTraceDto> list = new ArrayList<FileMaskTraceDto>( entityList.size() );
        for ( FileMaskTrace fileMaskTrace : entityList ) {
            list.add( toDto( fileMaskTrace ) );
        }

        return list;
    }
}
