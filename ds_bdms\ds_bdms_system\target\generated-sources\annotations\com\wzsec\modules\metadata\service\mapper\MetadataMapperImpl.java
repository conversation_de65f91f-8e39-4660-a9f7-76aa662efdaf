package com.wzsec.modules.metadata.service.mapper;

import com.wzsec.modules.metadata.domain.Metadata;
import com.wzsec.modules.metadata.service.dto.MetadataDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MetadataMapperImpl implements MetadataMapper {

    @Override
    public MetadataDto toDto(Metadata entity) {
        if ( entity == null ) {
            return null;
        }

        MetadataDto metadataDto = new MetadataDto();

        metadataDto.setAlgorithmid( entity.getAlgorithmid() );
        metadataDto.setCategory( entity.getCategory() );
        metadataDto.setColumnfamily( entity.getColumnfamily() );
        metadataDto.setConsistency( entity.getConsistency() );
        metadataDto.setCreatetime( entity.getCreatetime() );
        metadataDto.setCreateuser( entity.getCreateuser() );
        metadataDto.setDbid( entity.getDbid() );
        metadataDto.setDbisvalid( entity.getDbisvalid() );
        metadataDto.setDbname( entity.getDbname() );
        metadataDto.setEvaluationmethod( entity.getEvaluationmethod() );
        metadataDto.setEvaluationstate( entity.getEvaluationstate() );
        metadataDto.setExample( entity.getExample() );
        metadataDto.setFieldcname( entity.getFieldcname() );
        metadataDto.setFieldid( entity.getFieldid() );
        metadataDto.setFieldisvalid( entity.getFieldisvalid() );
        metadataDto.setFieldname( entity.getFieldname() );
        metadataDto.setFieldscname( entity.getFieldscname() );
        metadataDto.setFieldsname( entity.getFieldsname() );
        metadataDto.setGrundlagen( entity.getGrundlagen() );
        metadataDto.setId( entity.getId() );
        metadataDto.setLevel( entity.getLevel() );
        metadataDto.setNote( entity.getNote() );
        metadataDto.setSourcetype( entity.getSourcetype() );
        metadataDto.setSparefield1( entity.getSparefield1() );
        metadataDto.setSparefield2( entity.getSparefield2() );
        metadataDto.setSparefield3( entity.getSparefield3() );
        metadataDto.setSparefield4( entity.getSparefield4() );
        metadataDto.setSparefield5( entity.getSparefield5() );
        metadataDto.setStorepath( entity.getStorepath() );
        metadataDto.setSubjectdomain( entity.getSubjectdomain() );
        metadataDto.setTablecname( entity.getTablecname() );
        metadataDto.setTableid( entity.getTableid() );
        metadataDto.setTableisvalid( entity.getTableisvalid() );
        metadataDto.setTablename( entity.getTablename() );
        metadataDto.setTaskname( entity.getTaskname() );
        metadataDto.setUpdatetime( entity.getUpdatetime() );
        metadataDto.setUpdateuser( entity.getUpdateuser() );
        metadataDto.setVeracity( entity.getVeracity() );

        return metadataDto;
    }

    @Override
    public List<MetadataDto> toDto(List<Metadata> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetadataDto> list = new ArrayList<MetadataDto>( entityList.size() );
        for ( Metadata metadata : entityList ) {
            list.add( toDto( metadata ) );
        }

        return list;
    }

    @Override
    public Metadata toEntity(MetadataDto dto) {
        if ( dto == null ) {
            return null;
        }

        Metadata metadata = new Metadata();

        metadata.setAlgorithmid( dto.getAlgorithmid() );
        metadata.setCategory( dto.getCategory() );
        metadata.setColumnfamily( dto.getColumnfamily() );
        metadata.setConsistency( dto.getConsistency() );
        metadata.setCreatetime( dto.getCreatetime() );
        metadata.setCreateuser( dto.getCreateuser() );
        metadata.setDbid( dto.getDbid() );
        metadata.setDbisvalid( dto.getDbisvalid() );
        metadata.setDbname( dto.getDbname() );
        metadata.setEvaluationmethod( dto.getEvaluationmethod() );
        metadata.setEvaluationstate( dto.getEvaluationstate() );
        metadata.setExample( dto.getExample() );
        metadata.setFieldcname( dto.getFieldcname() );
        metadata.setFieldid( dto.getFieldid() );
        metadata.setFieldisvalid( dto.getFieldisvalid() );
        metadata.setFieldname( dto.getFieldname() );
        metadata.setFieldscname( dto.getFieldscname() );
        metadata.setFieldsname( dto.getFieldsname() );
        metadata.setGrundlagen( dto.getGrundlagen() );
        metadata.setId( dto.getId() );
        metadata.setLevel( dto.getLevel() );
        metadata.setNote( dto.getNote() );
        metadata.setSourcetype( dto.getSourcetype() );
        metadata.setSparefield1( dto.getSparefield1() );
        metadata.setSparefield2( dto.getSparefield2() );
        metadata.setSparefield3( dto.getSparefield3() );
        metadata.setSparefield4( dto.getSparefield4() );
        metadata.setSparefield5( dto.getSparefield5() );
        metadata.setStorepath( dto.getStorepath() );
        metadata.setSubjectdomain( dto.getSubjectdomain() );
        metadata.setTablecname( dto.getTablecname() );
        metadata.setTableid( dto.getTableid() );
        metadata.setTableisvalid( dto.getTableisvalid() );
        metadata.setTablename( dto.getTablename() );
        metadata.setTaskname( dto.getTaskname() );
        metadata.setUpdatetime( dto.getUpdatetime() );
        metadata.setUpdateuser( dto.getUpdateuser() );
        metadata.setVeracity( dto.getVeracity() );

        return metadata;
    }

    @Override
    public List<Metadata> toEntity(List<MetadataDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Metadata> list = new ArrayList<Metadata>( dtoList.size() );
        for ( MetadataDto metadataDto : dtoList ) {
            list.add( toEntity( metadataDto ) );
        }

        return list;
    }
}
