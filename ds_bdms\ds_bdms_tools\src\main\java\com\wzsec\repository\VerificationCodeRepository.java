package com.wzsec.repository;

import com.wzsec.domain.VerificationCode;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @date 2018-12-26
 */
public interface VerificationCodeRepository extends JpaRepository<VerificationCode, Long> {

    /**
     * 获取有效的验证码
     * @param scenes 业务场景，如重置密码，重置邮箱等等
     * @param type 类型
     * @param value 值
     * @return VerificationCode
     */
    VerificationCode findByScenesAndTypeAndValueAndStatusIsTrue(String scenes, String type, String value);
}
