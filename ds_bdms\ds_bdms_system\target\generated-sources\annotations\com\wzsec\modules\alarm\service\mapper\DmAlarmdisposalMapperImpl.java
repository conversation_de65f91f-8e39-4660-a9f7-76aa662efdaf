package com.wzsec.modules.alarm.service.mapper;

import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.dto.DmAlarmdisposalDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:20+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DmAlarmdisposalMapperImpl implements DmAlarmdisposalMapper {

    @Override
    public DmAlarmdisposal toEntity(DmAlarmdisposalDto dto) {
        if ( dto == null ) {
            return null;
        }

        DmAlarmdisposal dmAlarmdisposal = new DmAlarmdisposal();

        dmAlarmdisposal.setId( dto.getId() );
        dmAlarmdisposal.setCircumstantiality( dto.getCircumstantiality() );
        dmAlarmdisposal.setChecktime( dto.getChecktime() );
        dmAlarmdisposal.setTreatmentstate( dto.getTreatmentstate() );
        dmAlarmdisposal.setNote( dto.getNote() );
        dmAlarmdisposal.setReservefield1( dto.getReservefield1() );
        dmAlarmdisposal.setReservefield2( dto.getReservefield2() );
        dmAlarmdisposal.setReservefield3( dto.getReservefield3() );
        dmAlarmdisposal.setReservefield4( dto.getReservefield4() );
        dmAlarmdisposal.setIncidenthandler( dto.getIncidenthandler() );
        dmAlarmdisposal.setEventhandlingtime( dto.getEventhandlingtime() );
        dmAlarmdisposal.setSourceip( dto.getSourceip() );
        dmAlarmdisposal.setSourceport( dto.getSourceport() );
        dmAlarmdisposal.setDestinationip( dto.getDestinationip() );
        dmAlarmdisposal.setDestinationport( dto.getDestinationport() );
        dmAlarmdisposal.setAccount( dto.getAccount() );
        dmAlarmdisposal.setEventrule( dto.getEventrule() );
        dmAlarmdisposal.setPushnumber( dto.getPushnumber() );

        return dmAlarmdisposal;
    }

    @Override
    public DmAlarmdisposalDto toDto(DmAlarmdisposal entity) {
        if ( entity == null ) {
            return null;
        }

        DmAlarmdisposalDto dmAlarmdisposalDto = new DmAlarmdisposalDto();

        dmAlarmdisposalDto.setId( entity.getId() );
        dmAlarmdisposalDto.setCircumstantiality( entity.getCircumstantiality() );
        dmAlarmdisposalDto.setChecktime( entity.getChecktime() );
        dmAlarmdisposalDto.setTreatmentstate( entity.getTreatmentstate() );
        dmAlarmdisposalDto.setNote( entity.getNote() );
        dmAlarmdisposalDto.setReservefield1( entity.getReservefield1() );
        dmAlarmdisposalDto.setReservefield2( entity.getReservefield2() );
        dmAlarmdisposalDto.setReservefield3( entity.getReservefield3() );
        dmAlarmdisposalDto.setReservefield4( entity.getReservefield4() );
        dmAlarmdisposalDto.setIncidenthandler( entity.getIncidenthandler() );
        dmAlarmdisposalDto.setEventhandlingtime( entity.getEventhandlingtime() );
        dmAlarmdisposalDto.setSourceip( entity.getSourceip() );
        dmAlarmdisposalDto.setSourceport( entity.getSourceport() );
        dmAlarmdisposalDto.setDestinationip( entity.getDestinationip() );
        dmAlarmdisposalDto.setDestinationport( entity.getDestinationport() );
        dmAlarmdisposalDto.setAccount( entity.getAccount() );
        dmAlarmdisposalDto.setEventrule( entity.getEventrule() );
        dmAlarmdisposalDto.setPushnumber( entity.getPushnumber() );

        return dmAlarmdisposalDto;
    }

    @Override
    public List<DmAlarmdisposal> toEntity(List<DmAlarmdisposalDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DmAlarmdisposal> list = new ArrayList<DmAlarmdisposal>( dtoList.size() );
        for ( DmAlarmdisposalDto dmAlarmdisposalDto : dtoList ) {
            list.add( toEntity( dmAlarmdisposalDto ) );
        }

        return list;
    }

    @Override
    public List<DmAlarmdisposalDto> toDto(List<DmAlarmdisposal> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DmAlarmdisposalDto> list = new ArrayList<DmAlarmdisposalDto>( entityList.size() );
        for ( DmAlarmdisposal dmAlarmdisposal : entityList ) {
            list.add( toDto( dmAlarmdisposal ) );
        }

        return list;
    }
}
