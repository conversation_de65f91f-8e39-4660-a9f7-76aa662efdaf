package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcUser;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcUserSmallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JdbcUserSmallMapperImpl implements JdbcUserSmallMapper {

    @Override
    public JdbcUserSmallDto toDto(JdbcUser entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcUserSmallDto jdbcUserSmallDto = new JdbcUserSmallDto();

        jdbcUserSmallDto.setId( entity.getId() );
        jdbcUserSmallDto.setNickName( entity.getNickName() );
        jdbcUserSmallDto.setUsername( entity.getUsername() );

        return jdbcUserSmallDto;
    }

    @Override
    public List<JdbcUserSmallDto> toDto(List<JdbcUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcUserSmallDto> list = new ArrayList<JdbcUserSmallDto>( entityList.size() );
        for ( JdbcUser jdbcUser : entityList ) {
            list.add( toDto( jdbcUser ) );
        }

        return list;
    }

    @Override
    public JdbcUser toEntity(JdbcUserSmallDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcUser jdbcUser = new JdbcUser();

        jdbcUser.setId( dto.getId() );
        jdbcUser.setNickName( dto.getNickName() );
        jdbcUser.setUsername( dto.getUsername() );

        return jdbcUser;
    }

    @Override
    public List<JdbcUser> toEntity(List<JdbcUserSmallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcUser> list = new ArrayList<JdbcUser>( dtoList.size() );
        for ( JdbcUserSmallDto jdbcUserSmallDto : dtoList ) {
            list.add( toEntity( jdbcUserSmallDto ) );
        }

        return list;
    }
}
