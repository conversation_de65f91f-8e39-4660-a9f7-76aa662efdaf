package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskstrategyIdentifymaskstrategies;
import com.wzsec.modules.mask.service.dto.MaskstrategyIdentifymaskstrategiesDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskstrategyIdentifymaskstrategiesMapperImpl implements MaskstrategyIdentifymaskstrategiesMapper {

    @Override
    public MaskstrategyIdentifymaskstrategiesDto toDto(MaskstrategyIdentifymaskstrategies entity) {
        if ( entity == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategiesDto maskstrategyIdentifymaskstrategiesDto = new MaskstrategyIdentifymaskstrategiesDto();

        maskstrategyIdentifymaskstrategiesDto.setCreatetime( entity.getCreatetime() );
        maskstrategyIdentifymaskstrategiesDto.setCreateuser( entity.getCreateuser() );
        maskstrategyIdentifymaskstrategiesDto.setEnabled( entity.getEnabled() );
        maskstrategyIdentifymaskstrategiesDto.setId( entity.getId() );
        maskstrategyIdentifymaskstrategiesDto.setRemark( entity.getRemark() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield1( entity.getSparefield1() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield2( entity.getSparefield2() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield3( entity.getSparefield3() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield4( entity.getSparefield4() );
        maskstrategyIdentifymaskstrategiesDto.setStatus( entity.getStatus() );
        maskstrategyIdentifymaskstrategiesDto.setStrategydesc( entity.getStrategydesc() );
        maskstrategyIdentifymaskstrategiesDto.setStrategyname( entity.getStrategyname() );
        maskstrategyIdentifymaskstrategiesDto.setUpdatetime( entity.getUpdatetime() );
        maskstrategyIdentifymaskstrategiesDto.setUpdateuser( entity.getUpdateuser() );

        return maskstrategyIdentifymaskstrategiesDto;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategiesDto> toDto(List<MaskstrategyIdentifymaskstrategies> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategiesDto> list = new ArrayList<MaskstrategyIdentifymaskstrategiesDto>( entityList.size() );
        for ( MaskstrategyIdentifymaskstrategies maskstrategyIdentifymaskstrategies : entityList ) {
            list.add( toDto( maskstrategyIdentifymaskstrategies ) );
        }

        return list;
    }

    @Override
    public MaskstrategyIdentifymaskstrategies toEntity(MaskstrategyIdentifymaskstrategiesDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategies maskstrategyIdentifymaskstrategies = new MaskstrategyIdentifymaskstrategies();

        maskstrategyIdentifymaskstrategies.setCreatetime( dto.getCreatetime() );
        maskstrategyIdentifymaskstrategies.setCreateuser( dto.getCreateuser() );
        maskstrategyIdentifymaskstrategies.setEnabled( dto.getEnabled() );
        maskstrategyIdentifymaskstrategies.setId( dto.getId() );
        maskstrategyIdentifymaskstrategies.setRemark( dto.getRemark() );
        maskstrategyIdentifymaskstrategies.setSparefield1( dto.getSparefield1() );
        maskstrategyIdentifymaskstrategies.setSparefield2( dto.getSparefield2() );
        maskstrategyIdentifymaskstrategies.setSparefield3( dto.getSparefield3() );
        maskstrategyIdentifymaskstrategies.setSparefield4( dto.getSparefield4() );
        maskstrategyIdentifymaskstrategies.setStatus( dto.getStatus() );
        maskstrategyIdentifymaskstrategies.setStrategydesc( dto.getStrategydesc() );
        maskstrategyIdentifymaskstrategies.setStrategyname( dto.getStrategyname() );
        maskstrategyIdentifymaskstrategies.setUpdatetime( dto.getUpdatetime() );
        maskstrategyIdentifymaskstrategies.setUpdateuser( dto.getUpdateuser() );

        return maskstrategyIdentifymaskstrategies;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategies> toEntity(List<MaskstrategyIdentifymaskstrategiesDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategies> list = new ArrayList<MaskstrategyIdentifymaskstrategies>( dtoList.size() );
        for ( MaskstrategyIdentifymaskstrategiesDto maskstrategyIdentifymaskstrategiesDto : dtoList ) {
            list.add( toEntity( maskstrategyIdentifymaskstrategiesDto ) );
        }

        return list;
    }
}
