package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2021-06-20
*/
@Data
public class MaskStrategyFileMainDto implements Serializable {

    /** 主键 */
    private Integer id;

    /** 策略名称 */
    private String strategyname;

    /** 策略描述 */
    private String strategydesc;

    /** 策略类型（0：文件，1：非格格式化式化文件） */
    private String strategytype;

    /** 文件总列数（适用于格式化文件） */
    private Integer totalcolumn;

    /** 抽取列（适用于格式化文件） */
    private String extractcolum;

    /** 状态(0：启用，1：禁用) */
    private String status;

    /** 创建用户 */
    private String createuser;

    /** 创建时间 */
    private Timestamp createtime;

    /** 更新用户 */
    private String updateuser;

    /** 更新时间 */
    private Timestamp updatetime;

    /** 备注 */
    private String remark;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;
}
