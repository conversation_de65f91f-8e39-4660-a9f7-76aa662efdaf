package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.TasksynrecordOutline;
import com.wzsec.modules.statistics.service.dto.TasksynrecordOutlineDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:06+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class TasksynrecordOutlineMapperImpl implements TasksynrecordOutlineMapper {

    @Override
    public TasksynrecordOutlineDto toDto(TasksynrecordOutline entity) {
        if ( entity == null ) {
            return null;
        }

        TasksynrecordOutlineDto tasksynrecordOutlineDto = new TasksynrecordOutlineDto();

        tasksynrecordOutlineDto.setCreatetime( entity.getCreatetime() );
        tasksynrecordOutlineDto.setCreateuser( entity.getCreateuser() );
        tasksynrecordOutlineDto.setDbname( entity.getDbname() );
        tasksynrecordOutlineDto.setId( entity.getId() );
        tasksynrecordOutlineDto.setSourcetype( entity.getSourcetype() );
        tasksynrecordOutlineDto.setSparefield1( entity.getSparefield1() );
        tasksynrecordOutlineDto.setSparefield2( entity.getSparefield2() );
        tasksynrecordOutlineDto.setSparefield3( entity.getSparefield3() );
        tasksynrecordOutlineDto.setSrcname( entity.getSrcname() );
        tasksynrecordOutlineDto.setSynoutline( entity.getSynoutline() );
        tasksynrecordOutlineDto.setTaskname( entity.getTaskname() );
        tasksynrecordOutlineDto.setTasknumber( entity.getTasknumber() );

        return tasksynrecordOutlineDto;
    }

    @Override
    public List<TasksynrecordOutlineDto> toDto(List<TasksynrecordOutline> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TasksynrecordOutlineDto> list = new ArrayList<TasksynrecordOutlineDto>( entityList.size() );
        for ( TasksynrecordOutline tasksynrecordOutline : entityList ) {
            list.add( toDto( tasksynrecordOutline ) );
        }

        return list;
    }

    @Override
    public TasksynrecordOutline toEntity(TasksynrecordOutlineDto dto) {
        if ( dto == null ) {
            return null;
        }

        TasksynrecordOutline tasksynrecordOutline = new TasksynrecordOutline();

        tasksynrecordOutline.setCreatetime( dto.getCreatetime() );
        tasksynrecordOutline.setCreateuser( dto.getCreateuser() );
        tasksynrecordOutline.setDbname( dto.getDbname() );
        tasksynrecordOutline.setId( dto.getId() );
        tasksynrecordOutline.setSourcetype( dto.getSourcetype() );
        tasksynrecordOutline.setSparefield1( dto.getSparefield1() );
        tasksynrecordOutline.setSparefield2( dto.getSparefield2() );
        tasksynrecordOutline.setSparefield3( dto.getSparefield3() );
        tasksynrecordOutline.setSrcname( dto.getSrcname() );
        tasksynrecordOutline.setSynoutline( dto.getSynoutline() );
        tasksynrecordOutline.setTaskname( dto.getTaskname() );
        tasksynrecordOutline.setTasknumber( dto.getTasknumber() );

        return tasksynrecordOutline;
    }

    @Override
    public List<TasksynrecordOutline> toEntity(List<TasksynrecordOutlineDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TasksynrecordOutline> list = new ArrayList<TasksynrecordOutline>( dtoList.size() );
        for ( TasksynrecordOutlineDto tasksynrecordOutlineDto : dtoList ) {
            list.add( toEntity( tasksynrecordOutlineDto ) );
        }

        return list;
    }
}
