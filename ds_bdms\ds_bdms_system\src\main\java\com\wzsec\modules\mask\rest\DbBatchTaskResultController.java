package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.DbBatchTaskResult;
import com.wzsec.modules.mask.service.DbBatchTaskResultService;
import com.wzsec.modules.mask.service.dto.DBTaskResultDto;
import com.wzsec.modules.mask.service.dto.DbBatchTaskResultDto;
import com.wzsec.modules.mask.service.dto.DbBatchTaskResultQueryCriteria;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2023-02-07
*/
// @Api(tags = "批量脱敏任务结果管理")
@RestController
@RequestMapping("/api/dbbatchtaskresult")
public class DbBatchTaskResultController {

    private final DbBatchTaskResultService dbbatchtaskresultService;

    public DbBatchTaskResultController(DbBatchTaskResultService dbbatchtaskresultService) {
        this.dbbatchtaskresultService = dbbatchtaskresultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('dbbatchtaskresult:list')")
    public void download(HttpServletResponse response, DbBatchTaskResultQueryCriteria criteria) throws IOException {
        dbbatchtaskresultService.download(dbbatchtaskresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询批量脱敏任务结果")
    // @ApiOperation("查询批量脱敏任务结果")
    @PreAuthorize("@el.check('dbbatchtaskresult:list')")
    public ResponseEntity<Object> getDbbatchtaskresults(DbBatchTaskResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(dbbatchtaskresultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增批量脱敏任务结果")
    // @ApiOperation("新增批量脱敏任务结果")
    @PreAuthorize("@el.check('dbbatchtaskresult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody DbBatchTaskResult resources){
        return new ResponseEntity<>(dbbatchtaskresultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改批量脱敏任务结果")
    // @ApiOperation("修改批量脱敏任务结果")
    @PreAuthorize("@el.check('dbbatchtaskresult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody DbBatchTaskResult resources){
        dbbatchtaskresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除批量脱敏任务结果")
    // @ApiOperation("删除批量脱敏任务结果")
    @PreAuthorize("@el.check('dbbatchtaskresult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        dbbatchtaskresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/getBatchTaskResultDetails")
    @Log("获取批量任务结果详情")
    public ResponseEntity<Object> getBatchTaskResultDetails(@RequestParam Integer id){
        DbBatchTaskResultDto byId = dbbatchtaskresultService.findById(id);
        String beforemaskdata = byId.getBeforemaskdata();
        String[] before = beforemaskdata.split("\\n");
        String aftermaskdata = byId.getAftermaskdata();
        String[] after = aftermaskdata.split("\\n");
        List<DbBatchTaskResultDto> dbBatchTaskResultDtoList = new ArrayList<DbBatchTaskResultDto>();
        if (before.length == after.length) {
            DbBatchTaskResultDto dbBatchTaskResultDto =null;
            for (int i = 0; i < before.length; i++) {
                dbBatchTaskResultDto = new DbBatchTaskResultDto();
                dbBatchTaskResultDto.setBeforemaskdata(before[i]);
                dbBatchTaskResultDto.setAftermaskdata(after[i]);
                dbBatchTaskResultDtoList.add(dbBatchTaskResultDto);
            }
        }
        return new ResponseEntity<>(PageUtil.toPage(dbBatchTaskResultDtoList,dbBatchTaskResultDtoList.size()),HttpStatus.OK);
    }

    @PostMapping("/softDeleteTaskRule")
    @Log("软删除脱敏结果")
    public ResponseEntity<Object> softDeleteTaskRule(String taskName, String outDBName, String outTableName){
        dbbatchtaskresultService.softDeleteTaskRule(taskName,outDBName,outTableName);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("文件下载")
    @GetMapping("/fileDownload")
    public void fileDownload(@RequestParam Integer id, HttpServletRequest request, HttpServletResponse response){
        dbbatchtaskresultService.fileDownload(id,request,response);
    }

    @Log("sftp推送文件")
    @GetMapping("/sftpPush")
    public void sftpPush(@RequestParam Integer id, HttpServletRequest request,HttpServletResponse response){
        dbbatchtaskresultService.sftpPush(id,request,response);
    }

    @Log("ftp推送文件")
    @GetMapping("/ftpPush")
    public void ftpPush(@RequestParam Integer id, HttpServletRequest request,HttpServletResponse response){
        dbbatchtaskresultService.ftpPush(id,request,response);
    }
}
