package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskStrategyFileMain;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* <AUTHOR>
* @date 2021-06-20
*/
public interface MaskStrategyFileMainRepository extends JpaRepository<MaskStrategyFileMain, Integer>, JpaSpecificationExecutor<MaskStrategyFileMain> {
    /**
     * 根据策略名称查询策略数量
     * @param strategyname 策略名称
     */
    @Query(value = "select count(*) from sdd_mask_strategy_file_main where strategyname = ?1",nativeQuery = true)
    int findStrategyCountByStrategyName(String strategyname);

}