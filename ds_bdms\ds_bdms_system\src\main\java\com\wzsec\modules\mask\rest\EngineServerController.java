package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.EngineServer;
import com.wzsec.modules.mask.service.EngineServerService;
import com.wzsec.modules.mask.service.dto.EngineServerQueryCriteria;
import com.wzsec.modules.strategy.service.dto.StrategyQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2022-06-13
*/
// @Api(tags = "引擎服务器配置管理")
@RestController
@RequestMapping("/api/engineServer")
public class EngineServerController {

    private final EngineServerService engineServerService;

    public EngineServerController(EngineServerService engineServerService) {
        this.engineServerService = engineServerService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('engineServer:list')")
    public void download(HttpServletResponse response, EngineServerQueryCriteria criteria) throws IOException {
        engineServerService.download(engineServerService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询引擎服务器配置")
    // @ApiOperation("查询引擎服务器配置")
    @PreAuthorize("@el.check('engineServer:list')")
    public ResponseEntity<Object> getEngineServers(EngineServerQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(engineServerService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增引擎服务器配置")
    // @ApiOperation("新增引擎服务器配置")
    @PreAuthorize("@el.check('engineServer:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody EngineServer resources){
        return new ResponseEntity<>(engineServerService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改引擎服务器配置")
    // @ApiOperation("修改引擎服务器配置")
    @PreAuthorize("@el.check('engineServer:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody EngineServer resources){
        engineServerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除引擎服务器配置")
    // @ApiOperation("删除引擎服务器配置")
    @PreAuthorize("@el.check('engineServer:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        engineServerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @Log("测试引擎连通")
    // @ApiOperation("测试引擎连通")
    @PutMapping(value = "/test/{id}")
    @PreAuthorize("@el.check('datasource:edit')")
    public ResponseEntity<Object> test(@PathVariable Long id, HttpServletRequest request) {
        //new ResponseEntity<>()
        return new ResponseEntity<>(engineServerService.testFromEngine(id, request), HttpStatus.OK);
    }

    // @Log("下拉框查询引擎服务")
    @PostMapping(value = "/getEngineServer")
    public ResponseEntity<Object> getEngineServer(@RequestBody EngineServerQueryCriteria criteria) {
        return new ResponseEntity<>(engineServerService.getEngineServer(criteria), HttpStatus.OK);
    }

}
