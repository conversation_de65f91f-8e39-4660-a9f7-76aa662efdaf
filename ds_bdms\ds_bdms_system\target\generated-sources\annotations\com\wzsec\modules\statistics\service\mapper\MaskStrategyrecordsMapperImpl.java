package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.MaskStrategyrecords;
import com.wzsec.modules.statistics.service.dto.MaskStrategyrecordsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:02+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskStrategyrecordsMapperImpl implements MaskStrategyrecordsMapper {

    @Override
    public MaskStrategyrecordsDto toDto(MaskStrategyrecords entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyrecordsDto maskStrategyrecordsDto = new MaskStrategyrecordsDto();

        maskStrategyrecordsDto.setAlgorithmname( entity.getAlgorithmname() );
        maskStrategyrecordsDto.setCreatetime( entity.getCreatetime() );
        maskStrategyrecordsDto.setCreateuser( entity.getCreateuser() );
        maskStrategyrecordsDto.setDbname( entity.getDbname() );
        maskStrategyrecordsDto.setFieldcname( entity.getFieldcname() );
        maskStrategyrecordsDto.setFieldname( entity.getFieldname() );
        maskStrategyrecordsDto.setId( entity.getId() );
        maskStrategyrecordsDto.setOperation( entity.getOperation() );
        maskStrategyrecordsDto.setRulename( entity.getRulename() );
        maskStrategyrecordsDto.setSparefield1( entity.getSparefield1() );
        maskStrategyrecordsDto.setSparefield2( entity.getSparefield2() );
        maskStrategyrecordsDto.setSparefield3( entity.getSparefield3() );
        maskStrategyrecordsDto.setSparefield4( entity.getSparefield4() );
        maskStrategyrecordsDto.setStrategydesc( entity.getStrategydesc() );
        maskStrategyrecordsDto.setStrategyname( entity.getStrategyname() );
        maskStrategyrecordsDto.setStrategytype( entity.getStrategytype() );
        maskStrategyrecordsDto.setTabename( entity.getTabename() );
        maskStrategyrecordsDto.setTabid( entity.getTabid() );

        return maskStrategyrecordsDto;
    }

    @Override
    public List<MaskStrategyrecordsDto> toDto(List<MaskStrategyrecords> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyrecordsDto> list = new ArrayList<MaskStrategyrecordsDto>( entityList.size() );
        for ( MaskStrategyrecords maskStrategyrecords : entityList ) {
            list.add( toDto( maskStrategyrecords ) );
        }

        return list;
    }

    @Override
    public MaskStrategyrecords toEntity(MaskStrategyrecordsDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyrecords maskStrategyrecords = new MaskStrategyrecords();

        maskStrategyrecords.setAlgorithmname( dto.getAlgorithmname() );
        maskStrategyrecords.setCreatetime( dto.getCreatetime() );
        maskStrategyrecords.setCreateuser( dto.getCreateuser() );
        maskStrategyrecords.setDbname( dto.getDbname() );
        maskStrategyrecords.setFieldcname( dto.getFieldcname() );
        maskStrategyrecords.setFieldname( dto.getFieldname() );
        maskStrategyrecords.setId( dto.getId() );
        maskStrategyrecords.setOperation( dto.getOperation() );
        maskStrategyrecords.setRulename( dto.getRulename() );
        maskStrategyrecords.setSparefield1( dto.getSparefield1() );
        maskStrategyrecords.setSparefield2( dto.getSparefield2() );
        maskStrategyrecords.setSparefield3( dto.getSparefield3() );
        maskStrategyrecords.setSparefield4( dto.getSparefield4() );
        maskStrategyrecords.setStrategydesc( dto.getStrategydesc() );
        maskStrategyrecords.setStrategyname( dto.getStrategyname() );
        maskStrategyrecords.setStrategytype( dto.getStrategytype() );
        maskStrategyrecords.setTabename( dto.getTabename() );
        maskStrategyrecords.setTabid( dto.getTabid() );

        return maskStrategyrecords;
    }

    @Override
    public List<MaskStrategyrecords> toEntity(List<MaskStrategyrecordsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyrecords> list = new ArrayList<MaskStrategyrecords>( dtoList.size() );
        for ( MaskStrategyrecordsDto maskStrategyrecordsDto : dtoList ) {
            list.add( toEntity( maskStrategyrecordsDto ) );
        }

        return list;
    }
}
