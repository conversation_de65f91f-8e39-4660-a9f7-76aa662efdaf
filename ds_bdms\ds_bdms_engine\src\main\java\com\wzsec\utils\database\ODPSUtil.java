package com.wzsec.utils.database;

import com.aliyun.odps.*;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description odps操作
 * <AUTHOR>
 * @date 2020-07-13
 */
public class ODPSUtil {

	private static Logger log = Logger.getLogger(ODPSUtil.class);

	// private static String driverName = "com.aliyun.odps.jdbc.OdpsDriver";
	private static final String accessId = "";
	private static final String accessKey = "";
	private static final String endPoint = "http://service.odps.aliyun.com/api";
	private static final String project = "";

	/**
	 * @Description:建立odps连接
	 * <AUTHOR> by dongs
	 * @date 2020-07-13
	 */
	public static Odps getConn(String url, String accessId, String accessKey, String project) {
		Account account = new AliyunAccount(accessId, accessKey);
		Odps odps = new Odps(account);
		odps.setEndpoint(url);
		odps.setDefaultProject(project);
		return odps;
	}

	/**
	 * 获取所有表名
	 * @param conn
	 * @param dbnames
	 * @return
	 */
	public static Map<String, String> getAllDbAndTabMap(Odps conn, String dbnames) {
		Map<String, String> dbTabMap = new HashMap<>();
		try {
			Tables tables = conn.tables();
			for (Table table : tables) {
				String tableName = table.getName();
				// 表中文名
				// String remarks = table.getComment();
				if (dbTabMap.containsKey(dbnames)) {
					dbTabMap.put(dbnames, dbTabMap.get(dbnames) + "," + tableName);
				} else {
					dbTabMap.put(dbnames, tableName);
				}
			}
		} catch (Exception e) {
			String loginfo = "自动化同步ODPS数仓表字段信息获取表信息失败";
			log.error(loginfo + ",异常：" + e.getMessage());
			throw e;
		}
		return dbTabMap;
	}

	/**
	 * 获取表数据
	 * @param conn
	 * @param dbname
	 * @param tabname
	 * @param lineNum
	 * @return
	 */
	public static List<String[]> getTabDataList(Odps conn, String dbname, String tabname, Integer lineNum) throws Exception {
		List<String []> tabDataList = new ArrayList<>();
		Instance i;
		try {
			String sql = "select * from " + tabname + " limit " + lineNum + ";";
			i = SQLTask.run(conn, sql);
			i.waitForSuccess();
			List<Record> records = SQLTask.getResult(i);
			for (Record r : records) {
				Column[] columns = r.getColumns();
				String[] row = new String[columns.length];
				for (int j = 0; j < columns.length; j++) {
					String example = r.getString(columns[j].getName());
					row[j] = example == null ? "" : example;
				}
				tabDataList.add(row);
			}
		} catch (Exception e) {
			String loginfo = "自动化同步ODPS数仓表字段信息获取表字段信息失败，表名：" + tabname;
			log.error(loginfo);
			e.printStackTrace();
			throw e;
		}
		return tabDataList;
	}

	/**
	 * 获取表字段
	 * @param conn
	 * @param dbname
	 * @param tabname
	 * @return
	 * @throws Exception
	 */
	public static List<String> getFieldNameList(Odps conn, String dbname, String tabname) throws Exception {
		List<String> tableFieldlist = new ArrayList<>();
		try {
			if (null != tabname && !tabname.equals("")) {
				Table t = conn.tables().get(tabname);
				t.reload();
				TableSchema schema = t.getSchema();
				List<Column> columns = schema.getColumns();
				for (Column column : columns) {
					tableFieldlist.add(column.getName());
				}
			}
		} catch (Exception e) {
			String loginfo = "自动化同步ODPS数仓表字段信息获取表信息失败,表名：" + tabname;
			log.error(loginfo);
			throw e;
		}
		return tableFieldlist;
	}

	/**
	 * 查询表信息
	 * @param dbName
	 * @param tableName
	 * @param conn
	 * @return
	 */
	public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Odps conn) {
		Map<String, String> tableInfoMap = new HashMap<>();
		try {
			Tables tables = conn.tables();
			Table table = tables.get(tableName);
			tableInfoMap.put("tableName", table.getName());
			tableInfoMap.put("tableCName", table.getComment());
		} catch (Exception e) {
			throw e;
		}
		return tableInfoMap;
	}

	/**
	 * 获取字段信息
	 * @param dbName
	 * @param tableName
	 * @param conn
	 * @return
	 */
	public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Odps conn) {
		List<Map<String, String>> fieldInfoList =  new ArrayList<>();
		try {
			Tables tables = conn.tables();
			Table table = tables.get(tableName);
			TableSchema schema = table.getSchema();
			List<Column> columns = schema.getColumns();
			for (Column column : columns) {
				Map<String, String> fieldInfoMap = new HashMap<>();
				fieldInfoMap.put("fieldName",column.getName());
				fieldInfoMap.put("fieldCName",column.getComment());
				fieldInfoList.add(fieldInfoMap);
			}
		} catch (Exception e) {
			throw e;
		}
		return fieldInfoList;
	}
}
