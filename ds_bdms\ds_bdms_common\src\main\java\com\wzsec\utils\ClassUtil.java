package com.wzsec.utils;

import java.lang.reflect.Method;

/**
 * JAVA类反射
 *
 * <AUTHOR>
 * @date 2020-4-22
 */
public class ClassUtil {

    /**
     * 通过调用指定class方法获取处理结果值
     * 指定类要求：给一个要检测的文本数据内容，返回的是（true）（false）
     *
     * @param className
     * @param methodName
     * @param rags
     * @return
     * @throws Exception
     */
    public static boolean getClassReturnBoolean(String className, String methodName, Object... rags) throws Exception {
        return Boolean.parseBoolean(getClassReturnObject(className, methodName, rags).toString());
    }

    /**
     * 通过调用指定class方法获取处理结果值
     *
     * @param className
     * @param methodName
     * @param rags
     * @return
     * @throws Exception
     */
    public static Object getClassReturnObject(String className, String methodName, Object... rags) throws Exception {
        //使用反射获取算法类
        Class classz = Class.forName(className);
        //根据类名，获取spring中注册好的类
        Object obj = classz.newInstance();
        //根据方法名获取方法，并制指定传入的参数
        Method m = classz.getDeclaredMethod(methodName, String.class);
        return m.invoke(obj, rags);
    }
}
