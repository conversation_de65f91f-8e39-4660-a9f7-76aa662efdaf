package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2022-04-20
*/
@Data
public class MaskPictaskresultDto implements Serializable {

    /** ID */
    private Integer id;

    /** 任务名 */
    private String taskname;

    /** 任务结果状态 */
    private String taskstatus;

    /** 原始图片目录 */
    private String inputdirectory;

    /** 原图片格式 */
    private String inputfileformat;

    /** 原图片数 */
    private String beforepicamount;

    /** 脱敏对象 */
    private String maskobject;

    /** 脱敏后图片目录 */
    private String outputdirectory;

    /** 输出文件格式 */
    private String outputfileformat;

    /** 脱敏成功图片数 */
    private String afterpicamount;

    /** 任务耗时 */
    private String jobtotaltime;

    /** 任务结束时间 */
    private String jobendtime;

    /** 备注 */
    private String remark;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;
}