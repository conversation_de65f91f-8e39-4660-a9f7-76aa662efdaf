package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-09
*/
@Entity
@Getter
@Setter
@Table(name="sdd_mask_strategy_table")
public class MaskStrategyTable implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 策略名称 */
    @Column(name = "strategyname")
    //@NotBlank
    private String strategyname;

    /** 策略描述 */
    @Column(name = "strategydesc")
    private String strategydesc;

    /** 策略类型 */
    @Column(name = "strategytype")
    private String strategytype;

    /** 状态 */
    @Column(name = "status")
    private String status;

    /** 状态 */
    private Boolean enabled;

    /** 表ID */
    @Column(name = "tabid")
    private String tabid;

    /** 表名 */
    @Column(name = "tabename")
    private String tabename;

    /** 表中文名 */
    @Column(name = "tabcname")
    private String tabcname;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 数据源类型 */
    @Column(name = "sourcetype")
    private String sourcetype;

    /** 来源平台 */
    @Column(name = "level_val")
    private String levelVal;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "memo")
    private String memo;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;


    public void copy(MaskStrategyTable source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
