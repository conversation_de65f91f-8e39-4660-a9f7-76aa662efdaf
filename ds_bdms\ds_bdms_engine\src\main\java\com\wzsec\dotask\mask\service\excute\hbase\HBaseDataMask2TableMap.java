/*********************************************************************
 *
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 *
 *  [2015] - [2020] China Telecom Corporation Limited,
 *  All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 **********************************************************************/
/**
 *@Title: HBase2Hdfs.java
 *@Description: TODO
 *<AUTHOR>
 *@date 2020年1月16日
 */

package com.wzsec.dotask.mask.service.excute.hbase;

import cn.god.mask.common.MaskAlgFactory;
import com.wzsec.dotask.mask.service.excute.hbase.model.HBaseMaskingConfigInfo;
import com.wzsec.modules.mask.domain.Algorithm;
import com.wzsec.modules.mask.repository.AlgorithmRepository;
import com.wzsec.utils.AlgorithmUtils;
import com.wzsec.utils.Const;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.io.ImmutableBytesWritable;
import org.apache.hadoop.hbase.mapreduce.TableMapper;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.io.Text;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 *@Description: HBaseDataMask2HdfsMap (读取hbase表数据加密输出到hdfs)
 *<AUTHOR>
 *@date 2020-1-16
 */
public class HBaseDataMask2TableMap extends TableMapper<Text, Put> {

	private static Logger log = Logger.getLogger(HBaseDataMask2TableMap.class);

	private String count;
	private String splitStr;
	private HashMap<String, HBaseMaskingConfigInfo> maskConfigInfoMap;

	private static HashMap<String, String> maskfrontbackdataMap;

	private static int totalcount;

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public String getSplitStr() {
		return splitStr;
	}

	public void setSplitStr(String splitStr) {
		this.splitStr = splitStr;
	}

	public HashMap<String, HBaseMaskingConfigInfo> maskConfigInfoMap() {
		return maskConfigInfoMap;
	}

	public void setMaskConfigInfoArr(HashMap<String, HBaseMaskingConfigInfo> maskConfigInfoMap) {
		this.maskConfigInfoMap = maskConfigInfoMap;
	}

	public static HashMap<String, String> getMaskfrontbackdataMap() {
		return maskfrontbackdataMap;
	}

	public static void setMaskfrontbackdataMap(HashMap<String, String> MaskfrontbackdataMap) {
		maskfrontbackdataMap = MaskfrontbackdataMap;
	}

	@Autowired
	private AlgorithmRepository algorithmRepository;

	/**
	 *@Description: 脱敏Map前数据初始化
	 *<AUTHOR>
	 *@date 2020-1-16
	 */
	@Override
	protected void setup(Context context) throws IOException, InterruptedException {
		super.setup(context);

		Configuration conf = context.getConfiguration();

		//获取行数
		/*this.count = conf.get("count");
		log.info("Map Receive count Str:"+ conf.get("count"));*/

		//获取分割符
		this.splitStr = conf.get("splitStr");
		log.info("Map Receive Split Str:"+ conf.get("splitStr"));

		//获取脱敏配置信息
		String maskingConfigInfoStr = conf.get("maskingConfigInfoStr");
		log.info("Map Receive Masking Config Info:"+ maskingConfigInfoStr);
		this.maskConfigInfoMap = getMaskingConfigInfoHashMap(maskingConfigInfoStr);

		this.maskfrontbackdataMap = new HashMap<String,String>();
	}

	/**
	 *@Description: 脱敏Map
	 *<AUTHOR>
	 *@date 2020-1-16
	 */
	@Override
	protected void map(ImmutableBytesWritable key, Result columns,
                       Context context)
			throws IOException, InterruptedException {
		StringBuffer datasb = new StringBuffer();
		StringBuffer maskdatasb = new StringBuffer();
		//int count = Integer.parseInt(this.count);
		String split = this.splitStr;
		HashMap<String, HBaseMaskingConfigInfo> maskConfigInfoMap = this.maskConfigInfoMap; //脱敏配置信息

		String rowKey = Bytes.toString(key.get());

		Put put= new Put(Bytes.toBytes(rowKey));
        // 迭代以获取cell数据
        for (Cell kv : columns.rawCells()) {
        	//String rowkey = Bytes.toString(CellUtil.cloneRow(kv));
			String value = Bytes.toString(CellUtil.cloneValue(kv));
			String family = Bytes.toString(CellUtil.cloneFamily(kv));
			String Qualifier = Bytes.toString(CellUtil.cloneQualifier(kv));

			if(!"".equals(datasb.toString())){
				datasb.append(",");
			}
			datasb.append(value);

			if(maskConfigInfoMap.containsKey(family+"|"+Qualifier)){
				HBaseMaskingConfigInfo hbaseMaskingConfigInfo = maskConfigInfoMap.get(family+"|"+Qualifier);
				String algoPara = hbaseMaskingConfigInfo.getAlgoParameter();
				if(!"".equals(maskdatasb.toString())){
					maskdatasb.append(",");
				}
				try {
					//log.info(value+" "+hbaseMaskingConfigInfo.getAlgoName()+" "+algoPara);
					String algoName = hbaseMaskingConfigInfo.getAlgoName();
					//获取当前算法
					Algorithm algorithm = algorithmRepository.findByAlgorithmName(algoName);
					String sparefield1 = algorithm.getSparefield1();
					if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
						value =  MaskAlgFactory.getMaskData(value, algoName,algoPara);
					}else {
						//自定义算法
						String jarname = algorithm.getJarname();//jar包名称
						String funcnamepath = algorithm.getFuncnamepath(); //方法路径
						String methodname = algorithm.getMethodname();//方法名
						Map<String, String> paramMap = new HashMap<>();
						paramMap.put("jarname",jarname);
						paramMap.put("funcnamepath",funcnamepath);
						paramMap.put("methodname",methodname);
						paramMap.put("maskData",value);
						value = AlgorithmUtils.invokeJarMethod(paramMap);
					}
					maskdatasb.append(value);
				} catch (Exception e) {
					e.printStackTrace();
				}
				put.addColumn(Bytes.toBytes(family), Bytes.toBytes(Qualifier), Bytes.toBytes(value));
			}

        }
        if(maskfrontbackdataMap.size()<5){
			maskfrontbackdataMap.put(datasb.toString(), maskdatasb.toString());
		}
        /*if(count!=0 && totalcount<count){
			//4.传递

		}*/
        context.write(new Text(rowKey), put);
		totalcount++;
	}


	/**
	 *@Description: 将脱敏配置信息特定字符串转换为脱敏配置信息数组(cf1|name|;cf1|telephone|MD5;cf2|job|;)
     *<AUTHOR>
	 *@date 2020-1-17
	 */
	public static HashMap<String, HBaseMaskingConfigInfo> getMaskingConfigInfoHashMap(String maskingConfigInfoStr){
		HashMap<String, HBaseMaskingConfigInfo> maskConfigInfoMap = new HashMap<String,HBaseMaskingConfigInfo>();
		if(null!=maskingConfigInfoStr && maskingConfigInfoStr.length()>0){
			if(maskingConfigInfoStr.contains(";")){
				String[] configItemArr = maskingConfigInfoStr.split(";");
				for(int i=0; i<configItemArr.length; i++) {
					String[] postionAlgoParaArr = configItemArr[i].split("\\|");
					String columnfamily = postionAlgoParaArr[0]; //列族
					String column= postionAlgoParaArr[1]; //列

					String algoName = "";  //算法名称
					String algoPara = "";  //算法参数

					if(postionAlgoParaArr.length > 2){
						String algoNamePara = postionAlgoParaArr[2];
						if(algoNamePara.contains("$")){  //MD5$9876543210123456
							String[] algoNameParaArr = algoNamePara.split("\\$");
							algoName = algoNameParaArr[0];
							algoPara = algoNameParaArr[1];
						}
						else{                                 //IDNUMBER
							algoName = algoNamePara;
							algoPara = "";
						}
					}

					HBaseMaskingConfigInfo sMaskConfigInfo = new HBaseMaskingConfigInfo(columnfamily,column,algoName,algoPara);
					maskConfigInfoMap.put(columnfamily+"|"+column, sMaskConfigInfo);
				}
			}
		    else{
				//cf1|name|;
				String[] postionAlgoPara = maskingConfigInfoStr.split("\\|");
				String columnfamily = postionAlgoPara[0];
				String column = postionAlgoPara[1];
				String algoName = "";
				String algoPara = "";
				if(postionAlgoPara[1].contains("$")){
					String[] algoParaArr = postionAlgoPara[1].split("\\$");
					algoName = algoParaArr[0];
					algoPara = algoParaArr[1];
				}
				else{
					algoName = postionAlgoPara[1];
					algoPara = "";
				}
				HBaseMaskingConfigInfo sMaskConfigInfo = new HBaseMaskingConfigInfo(columnfamily,column,algoName,algoPara);
				maskConfigInfoMap.put(columnfamily+"|"+column, sMaskConfigInfo);
			}
		}

		return maskConfigInfoMap;
	}

}

