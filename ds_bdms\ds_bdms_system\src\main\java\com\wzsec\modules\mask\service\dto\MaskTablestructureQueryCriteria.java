package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Data
public class MaskTablestructureQueryCriteria{

    @Query
    private String stategytableid;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createtime;

    @Query(type = Query.Type.INNER_LIKE)
    private String dbname;

    @Query(type = Query.Type.INNER_LIKE)
    private String source;

    @Query(type = Query.Type.INNER_LIKE)
    private String tablename;

    @Query(blurry = "dbname,source,tablename")
    private String blurry;
}
