package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoPictureTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/engine/picture/task")
public class PictureTaskController {

    private final DoPictureTaskService doPictureTaskService;

    private final UserService userService;

    public PictureTaskController(DoPictureTaskService doPictureTaskService, UserService userService) {
        this.doPictureTaskService = doPictureTaskService;
        this.userService = userService;
    }

    @Log("执行图片脱敏任务")
    @PostMapping(value = "/exec/{id}")
    public ResponseEntity<Object> execution(@PathVariable Integer id) {
        System.out.println("执行图片脱敏任务：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doPictureTaskService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


    @Log("定时执行数据库脱敏任务")
    @PostMapping(value = "/timingexec/{id}")
    public ResponseEntity<Object> timingexecution(@PathVariable Integer id) {
        System.out.println("定时执行图片脱敏任务：" + id);
        doPictureTaskService.execution(id, Const.TASK_SUBMITTYPE_AUTOSUBMIT);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
