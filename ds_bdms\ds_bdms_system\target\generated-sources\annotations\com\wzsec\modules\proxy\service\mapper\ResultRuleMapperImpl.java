package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.ResultRule;
import com.wzsec.modules.proxy.service.dto.ResultRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:06+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ResultRuleMapperImpl implements ResultRuleMapper {

    @Override
    public ResultRuleDto toDto(ResultRule entity) {
        if ( entity == null ) {
            return null;
        }

        ResultRuleDto resultRuleDto = new ResultRuleDto();

        resultRuleDto.setAlgorithm( entity.getAlgorithm() );
        resultRuleDto.setCategory( entity.getCategory() );
        resultRuleDto.setCheckrule( entity.getCheckrule() );
        resultRuleDto.setCreatetime( entity.getCreatetime() );
        resultRuleDto.setCreateuser( entity.getCreateuser() );
        resultRuleDto.setDatasource( entity.getDatasource() );
        resultRuleDto.setId( entity.getId() );
        resultRuleDto.setLevel( entity.getLevel() );
        resultRuleDto.setNote( entity.getNote() );
        resultRuleDto.setParam( entity.getParam() );
        resultRuleDto.setRole( entity.getRole() );
        resultRuleDto.setSname( entity.getSname() );
        resultRuleDto.setSparefield1( entity.getSparefield1() );
        resultRuleDto.setSparefield2( entity.getSparefield2() );
        resultRuleDto.setSparefield3( entity.getSparefield3() );
        resultRuleDto.setSparefield4( entity.getSparefield4() );
        resultRuleDto.setSparefield5( entity.getSparefield5() );
        resultRuleDto.setState( entity.getState() );
        resultRuleDto.setUpdatetime( entity.getUpdatetime() );
        resultRuleDto.setUpdateuser( entity.getUpdateuser() );

        return resultRuleDto;
    }

    @Override
    public List<ResultRuleDto> toDto(List<ResultRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultRuleDto> list = new ArrayList<ResultRuleDto>( entityList.size() );
        for ( ResultRule resultRule : entityList ) {
            list.add( toDto( resultRule ) );
        }

        return list;
    }

    @Override
    public ResultRule toEntity(ResultRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultRule resultRule = new ResultRule();

        resultRule.setAlgorithm( dto.getAlgorithm() );
        resultRule.setCategory( dto.getCategory() );
        resultRule.setCheckrule( dto.getCheckrule() );
        resultRule.setCreatetime( dto.getCreatetime() );
        resultRule.setCreateuser( dto.getCreateuser() );
        resultRule.setDatasource( dto.getDatasource() );
        resultRule.setId( dto.getId() );
        resultRule.setLevel( dto.getLevel() );
        resultRule.setNote( dto.getNote() );
        resultRule.setParam( dto.getParam() );
        resultRule.setRole( dto.getRole() );
        resultRule.setSname( dto.getSname() );
        resultRule.setSparefield1( dto.getSparefield1() );
        resultRule.setSparefield2( dto.getSparefield2() );
        resultRule.setSparefield3( dto.getSparefield3() );
        resultRule.setSparefield4( dto.getSparefield4() );
        resultRule.setSparefield5( dto.getSparefield5() );
        resultRule.setState( dto.getState() );
        resultRule.setUpdatetime( dto.getUpdatetime() );
        resultRule.setUpdateuser( dto.getUpdateuser() );

        return resultRule;
    }

    @Override
    public List<ResultRule> toEntity(List<ResultRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultRule> list = new ArrayList<ResultRule>( dtoList.size() );
        for ( ResultRuleDto resultRuleDto : dtoList ) {
            list.add( toEntity( resultRuleDto ) );
        }

        return list;
    }
}
