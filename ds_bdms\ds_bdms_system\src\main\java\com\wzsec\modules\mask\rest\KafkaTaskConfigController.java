package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.KafkaTaskConfig;
import com.wzsec.modules.mask.service.KafkaTaskConfigService;
import com.wzsec.modules.mask.service.dto.KafkaTaskConfigQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-02-26
*/
// @Api(tags = "Kafka脱敏任务配置管理")
@RestController
@RequestMapping("/api/kafkatask")
public class KafkaTaskConfigController {

    private final KafkaTaskConfigService kafkaTaskService;

    public KafkaTaskConfigController(KafkaTaskConfigService kafkaTaskService) {
        this.kafkaTaskService = kafkaTaskService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('kafkatask:list')")
    public void download(HttpServletResponse response, KafkaTaskConfigQueryCriteria criteria) throws IOException {
        kafkaTaskService.download(kafkaTaskService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询Kafka脱敏任务配置")
    // @ApiOperation("查询Kafka脱敏任务配置")
    @PreAuthorize("@el.check('kafkatask:list')")
    public ResponseEntity<Object> getKafkatasks(KafkaTaskConfigQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(kafkaTaskService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增Kafka脱敏任务配置")
    // @ApiOperation("新增Kafka脱敏任务配置")
    @PreAuthorize("@el.check('kafkatask:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody KafkaTaskConfig resources){
        return new ResponseEntity<>(kafkaTaskService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改Kafka脱敏任务配置")
    // @ApiOperation("修改Kafka脱敏任务配置")
    @PreAuthorize("@el.check('kafkatask:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody KafkaTaskConfig resources){
        kafkaTaskService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除Kafka脱敏任务配置")
    // @ApiOperation("删除Kafka脱敏任务配置")
    @PreAuthorize("@el.check('kafkatask:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        kafkaTaskService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @Log("Kafka脱敏数据对应的库名")
    // @ApiOperation("Kafka脱敏数据对应的表名")
    @GetMapping(value = "/getDBname")
    @PreAuthorize("@el.check('kafkatask:list')")
    public ResponseEntity<Object> getDBname() {
        //new ResponseEntity<>()
        return new ResponseEntity<>(kafkaTaskService.getDBname(), HttpStatus.OK);
    }

    @Log("获取Kafka脱敏任务新增任务号")
    // @ApiOperation("获取Kafka脱敏任务新增任务号")
    @PreAuthorize("@el.check('kafkatask:add')")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(kafkaTaskService.getMAXTaskName(), HttpStatus.CREATED);
    }

    @Log("Kafka脱敏数据对应的表名")
    // @ApiOperation("Kafka脱敏数据对应的表名")
    @GetMapping(value = "/getTabnameByDBname/{dbname}")
    @PreAuthorize("@el.check('kafkatask:list')")
    public ResponseEntity<Object> getTabname(@PathVariable String dbname) {
        //new ResponseEntity<>()
        return new ResponseEntity<>(kafkaTaskService.getTabname(dbname), HttpStatus.OK);
    }



    @Log("执行Kafka静态脱敏任务")
    // @ApiOperation("在引擎执行数据库脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    // @PreAuthorize("@el.check('kafkatask:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Long id, HttpServletRequest request) {
        kafkaTaskService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("停止Kafka静态脱敏任务")
    // @ApiOperation("在引擎停止数据库脱敏任务")
    @PutMapping(value = "/stopFromEngine/{id}")
    @PreAuthorize("@el.check('kafkatask:edit')")
    public ResponseEntity<Object> stopFromEngine(@PathVariable Long id, HttpServletRequest request) {
        kafkaTaskService.stop(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


}
