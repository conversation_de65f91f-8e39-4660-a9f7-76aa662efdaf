package com.wzsec.modules.alarm.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-04-07
 */
@Data
public class DmAlarmdisposalDto implements Serializable {

    private Integer id;

    /** 事件详情 */
    private String circumstantiality;

    /** 检测时间 */
    private String checktime;

    /** 处理状态 */
    private String treatmentstate;

    /** 备注 */
    private String note;

    /** 备用字段1 */
    private String reservefield1;

    /** 备用字段2 */
    private String reservefield2;

    /** 备用字段3 */
    private String reservefield3;

    /** 备用字段4 */
    private String reservefield4;

    /** 事件处置人员 */
    private String incidenthandler;

    /** 事件处置时间 */
    private String eventhandlingtime;

    /** 源IP */
    private String sourceip;

    /** 源端口 */
    private String sourceport;

    /** 目标IP */
    private String destinationip;

    /** 目标端口 */
    private String destinationport;

    /** 事件相关用户名 */
    private String account;

    /** 规则 */
    private String eventrule;

    /** 推送次数 */
    private String pushnumber;
}