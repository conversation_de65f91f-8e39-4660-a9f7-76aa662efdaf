package com.wzsec.dotask.sdd.service.excute.common;

import com.wzsec.modules.sdd.category.service.dto.CategoryDto;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.utils.Const;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 敏感数据类别管理类
 *
 * <AUTHOR>
 * @date 2020-6-11
 */
@Slf4j
public class CategoryManager {


    /**
     * 通过适用类型中文名获取检测规则标识(用于数据库元数据分类)
     * 如：通过“手机号”获取A1-3
     * <AUTHOR>
     * @date 2020-4-26
     */
    public static String[] getCategoryAndFieldsnameByApplytypecname(String applytypecname, List<RuleDto> ruleList, List<CategoryDto> categoryList) {
        Long ruleId = null;
        String applytypename = null;
        for (RuleDto ruleDto : ruleList) {
            if (applytypecname.equals(ruleDto.getApplytypecname())) {
                ruleId = ruleDto.getId();
                applytypename = ruleDto.getApplytypename();
                break;
            }
        }
        if (ruleId == null) {
            return null;
        }
        String category = null;
        for (CategoryDto categoryDto : categoryList) {
            if (Const.CATEGORY_TYPE_3.equals(categoryDto.getType())) {
                if (categoryDto.getRuleid() == ruleId) {
                    category = categoryDto.getCategory();
                    break;
                }
            }
        }
        return new String[]{applytypename, category};
    }
}
