package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Data
public class MaskKanonymizationtaskDto implements Serializable {

    private Integer id;

    /** 任务名 */
    private String taskname;

    /** 状态 */
    private String state;

    /** 数据库 */
    private String databasename;

    /** 库名 */
    private String dbname;

    /** 表名 */
    private String tabname;

    /** 数据库连接地址 */
    private String url;

    /** 用户名 */
    private String username;

    /** 密码 */
    private String pass;

    /** 策略 */
    private String strategy;

    /** 文件输出目录 */
    private String outputdirectory;

    /** 异常目录 */
    private String errordirectory;

    /** 创建用户id */
    private Integer createuserid;

    /** 创建时间 */
    private String createtime;

    /** 更新用户id */
    private Integer updateuserid;

    /** 更新时间 */
    private String updatetime;

    /** 备注 */
    private String remark;

    /** K值大小 */
    private String sparefield1;

    /** 抑制度 */
    private String sparefield2;

    /** 任务执行状态(0初始创建,1执行中,2执行成功,3执行失败,4提交失败) */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;
}
