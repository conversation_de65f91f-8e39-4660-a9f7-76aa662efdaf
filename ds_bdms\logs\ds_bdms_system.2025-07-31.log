2025-07-31 09:59:01,020 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 17456 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 09:59:01,026 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 09:59:04,167 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 09:59:04,169 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 09:59:05,470 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1281 ms. Found 111 JPA repository interfaces.
2025-07-31 09:59:06,245 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 09:59:06,246 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 09:59:06,249 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 09:59:06,249 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 09:59:06,253 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 09:59:06,255 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 09:59:06,257 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 09:59:06,257 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 09:59:06,257 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 09:59:08,493 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 09:59:08,531 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 09:59:08,537 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 09:59:09,502 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 09:59:09,563 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 09:59:09,570 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 09:59:09,570 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 09:59:09,682 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.4909133136571971525] which is part of the web application []
2025-07-31 09:59:10,103 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 09:59:10,103 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 8544 ms
2025-07-31 09:59:12,441 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 09:59:12,890 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 09:59:12,937 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 09:59:12,937 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 09:59:12,937 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 09:59:12,937 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 09:59:12,937 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 09:59:12,937 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 09:59:12,937 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 09:59:12,953 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 09:59:17,696 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 09:59:18,846 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 09:59:19,159 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 09:59:19,904 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 09:59:20,676 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 09:59:27,446 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 09:59:27,512 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 09:59:31,485 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 09:59:35,938 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 09:59:36,035 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 09:59:36,037 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 09:59:36,058 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 09:59:36,062 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 09:59:36,062 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 09:59:36,062 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 09:59:36,063 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6376534c
2025-07-31 09:59:36,063 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 09:59:46,905 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4c286590, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@574108a0, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c88b82b, org.springframework.security.web.header.HeaderWriterFilter@66c937dd, org.springframework.security.web.authentication.logout.LogoutFilter@28485eb7, org.springframework.web.filter.CorsFilter@2ca1e49a, com.wzsec.modules.security.security.TokenFilter@7895bad0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@46103135, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1bc7745d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3e59d81b, org.springframework.security.web.session.SessionManagementFilter@3dfbd145, org.springframework.security.web.access.ExceptionTranslationFilter@5d408b09, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@44fc80a0]
2025-07-31 09:59:46,972 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 09:59:52,351 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 09:59:52,352 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 09:59:52,352 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 09:59:52,352 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 09:59:52,355 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 09:59:52,355 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 09:59:52,355 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 09:59:52,355 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@102cb8a2
2025-07-31 09:59:52,895 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 09:59:53,035 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 09:59:53,164 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 09:59:53,173 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 09:59:53,175 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 09:59:53,175 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 09:59:53,175 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 09:59:53,175 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 09:59:53,177 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 09:59:53,177 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 09:59:53,177 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 09:59:53,177 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 09:59:53,182 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 09:59:53,182 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 09:59:53,182 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 09:59:53,190 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-31 09:59:54,815 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 09:59:56,670 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 09:59:57,400 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 09:59:57,874 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 09:59:58,130 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 09:59:58,130 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-31 09:59:58,143 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 57.96 seconds (JVM running for 61.259)
2025-07-31 09:59:59,346 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 10:01:16,146 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:01:16,148 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 10:01:16,172 INFO (FrameworkServlet.java:547)- Completed initialization in 24 ms
2025-07-31 11:05:44,687 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 24364 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:05:44,695 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:05:47,352 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:05:47,352 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:05:48,491 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1120 ms. Found 111 JPA repository interfaces.
2025-07-31 11:05:49,231 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:05:49,233 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:05:49,240 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:05:49,240 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:05:49,248 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:05:49,251 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:05:49,256 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:05:49,257 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:05:49,257 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:05:50,131 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:05:50,147 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:05:50,151 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:05:50,654 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:05:50,681 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:05:50,686 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:05:50,686 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:05:50,741 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.3153750543938877277] which is part of the web application []
2025-07-31 11:05:50,989 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:05:50,989 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5748 ms
2025-07-31 11:05:52,503 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:05:52,805 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:05:52,832 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:05:52,832 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:05:52,832 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:05:52,832 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:05:52,832 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:05:52,832 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:05:52,844 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:05:52,846 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:05:55,319 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:05:56,161 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:05:56,361 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:05:56,877 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:05:57,382 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:06:01,294 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:06:01,337 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:06:04,001 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:06:07,453 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:06:07,545 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:06:07,545 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:06:07,563 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:06:07,567 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:06:07,567 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:06:07,567 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:06:07,567 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@342daa77
2025-07-31 11:06:07,569 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:06:17,304 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@318b3d79, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@793b6e25, org.springframework.security.web.context.SecurityContextPersistenceFilter@16b3794f, org.springframework.security.web.header.HeaderWriterFilter@4eace98f, org.springframework.security.web.authentication.logout.LogoutFilter@136e9a68, org.springframework.web.filter.CorsFilter@5f5a33ed, com.wzsec.modules.security.security.TokenFilter@238e2c4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b6deb26, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2412347c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@419b3cc5, org.springframework.security.web.session.SessionManagementFilter@700e297d, org.springframework.security.web.access.ExceptionTranslationFilter@75b7889d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@62204612]
2025-07-31 11:06:17,382 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:06:21,911 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:06:21,915 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:06:21,915 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:06:21,915 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:06:21,915 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:06:21,915 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:06:21,915 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:06:21,915 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5d1f854f
2025-07-31 11:06:22,096 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 11:06:22,185 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:06:22,280 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 11:06:22,284 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:06:22,286 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:06:22,286 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:06:22,286 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:06:22,286 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:06:22,286 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:06:22,286 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:06:22,286 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:06:22,286 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:06:22,295 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:06:22,295 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:06:22,295 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:06:22,302 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 11:06:23,481 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 11:06:23,705 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 11:06:23,980 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 11:06:24,220 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 11:06:24,450 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 11:06:24,653 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：0
2025-07-31 11:06:24,945 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:06:24,947 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 11:06:24,952 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 41.028 seconds (JVM running for 44.09)
2025-07-31 11:06:25,555 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 11:10:41,609 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:10:42,366 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 11:10:42,368 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:10:42,368 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:10:42,368 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:10:42,404 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:10:42,404 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:10:42,404 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:10:42,411 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:10:42,434 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 11:10:52,854 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 19368 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:10:52,865 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:10:54,852 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:10:54,852 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:10:55,772 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 901 ms. Found 111 JPA repository interfaces.
2025-07-31 11:10:56,289 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:10:56,289 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:10:56,294 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:10:56,294 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:10:56,295 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:10:56,298 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:10:56,298 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:10:56,298 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:10:56,298 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:10:56,891 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:10:56,904 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:10:56,906 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:10:57,309 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:10:57,332 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:10:57,335 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:10:57,335 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:10:57,363 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.2640021498443937387] which is part of the web application []
2025-07-31 11:10:57,529 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:10:57,529 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 4235 ms
2025-07-31 11:10:58,786 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:10:59,094 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:10:59,116 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:10:59,116 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:10:59,116 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:10:59,116 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:10:59,116 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:10:59,119 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:10:59,120 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:10:59,122 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:11:01,430 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:11:02,172 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:11:02,350 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:11:02,783 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:11:03,249 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:11:06,792 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:11:06,835 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:11:09,126 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:11:12,288 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:11:12,381 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:11:12,381 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:11:12,396 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:11:12,396 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:11:12,396 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:11:12,396 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:11:12,396 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@7fa20767
2025-07-31 11:11:12,396 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:11:21,096 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@49be17f6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18e6e845, org.springframework.security.web.context.SecurityContextPersistenceFilter@4e794f68, org.springframework.security.web.header.HeaderWriterFilter@31d23ea5, org.springframework.security.web.authentication.logout.LogoutFilter@77ecce1b, org.springframework.web.filter.CorsFilter@76220ef1, com.wzsec.modules.security.security.TokenFilter@3da82b44, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@59d05dcb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@32babb12, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f21aa2b, org.springframework.security.web.session.SessionManagementFilter@576e46af, org.springframework.security.web.access.ExceptionTranslationFilter@3e59d81b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@630f63ee]
2025-07-31 11:11:21,138 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:11:24,878 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:11:24,878 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:11:24,878 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:11:24,878 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:11:24,878 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:11:24,878 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:11:24,878 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:11:24,882 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2f2a8ce2
2025-07-31 11:11:25,039 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 11:11:25,113 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:11:25,200 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 11:11:25,202 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:11:25,202 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:11:25,202 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:11:25,202 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:11:25,202 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:11:25,202 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:11:25,206 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:11:25,206 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:11:25,206 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:11:25,209 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:11:25,209 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:11:25,209 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:11:25,214 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 11:11:26,280 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 11:11:26,483 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 11:11:26,713 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 11:11:26,917 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 11:11:27,139 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 11:11:27,335 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：0
2025-07-31 11:11:27,598 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:11:27,598 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 11:11:27,603 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 35.362 seconds (JVM running for 37.602)
2025-07-31 11:11:28,164 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 11:11:56,694 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:13:17,074 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 16304 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:13:17,081 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:13:19,205 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:13:19,205 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:13:20,125 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 904 ms. Found 111 JPA repository interfaces.
2025-07-31 11:13:20,630 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:13:20,630 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:13:21,240 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:13:21,254 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:13:21,255 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:13:21,645 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:13:21,682 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:13:21,685 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:13:21,685 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:13:21,722 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.8302355843097060082] which is part of the web application []
2025-07-31 11:13:21,877 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:13:21,877 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 4363 ms
2025-07-31 11:13:23,158 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:13:23,463 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:13:23,502 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:13:23,504 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:13:23,504 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:13:23,505 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:13:23,505 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:13:23,505 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:13:23,508 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:13:23,514 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:13:26,032 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:13:26,830 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:13:27,020 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:13:27,506 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:13:27,975 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:13:31,724 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:13:31,774 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:13:34,100 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:13:37,323 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:13:37,412 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:13:37,412 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:13:37,431 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:13:37,435 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:13:37,435 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:13:37,435 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:13:37,437 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@4cd29de4
2025-07-31 11:13:37,437 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:13:46,860 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@61fa65c7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1b02231b, org.springframework.security.web.context.SecurityContextPersistenceFilter@23ceb383, org.springframework.security.web.header.HeaderWriterFilter@81a48a8, org.springframework.security.web.authentication.logout.LogoutFilter@2125bb4e, org.springframework.web.filter.CorsFilter@3a751535, com.wzsec.modules.security.security.TokenFilter@1d1e0f6e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54dfde5b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@315441a6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7476a621, org.springframework.security.web.session.SessionManagementFilter@7ae87fbe, org.springframework.security.web.access.ExceptionTranslationFilter@6461e6ba, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7b51740b]
2025-07-31 11:13:46,905 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:13:50,969 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:13:50,971 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:13:50,971 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:13:50,971 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:13:50,971 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:13:50,971 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:13:50,971 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:13:50,971 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@212cecfa
2025-07-31 11:13:51,132 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 11:13:51,200 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:13:51,296 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 11:13:51,296 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:13:51,296 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:13:51,296 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:13:51,296 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:13:51,296 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:13:51,296 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:13:51,296 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:13:51,296 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:13:51,296 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:13:51,307 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:13:51,307 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:13:51,307 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:13:51,307 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 11:13:52,418 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 11:13:52,618 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 11:13:52,877 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 11:13:53,093 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 11:13:53,306 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 11:13:53,508 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：0
2025-07-31 11:13:53,771 INFO (MaskVideoScanConfig.java:67)- 视频脱敏任务新增: 16- 0 0 2 * * ? *
2025-07-31 11:13:53,771 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:13:53,771 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 11:13:53,781 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 37.32 seconds (JVM running for 39.597)
2025-07-31 11:13:54,371 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 11:18:35,481 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 11:18:35,481 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 11:18:35,482 INFO (FrameworkServlet.java:547)- Completed initialization in 1 ms
2025-07-31 11:19:24,541 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 31 - 0 20 11 * * ? *
2025-07-31 11:20:00,069 ERROR (JobRunShell.java:211)- Job DEFAULT.PicJob31 threw an unhandled Exception: 
java.lang.NumberFormatException: For input string: "PicJob31"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:580)
	at java.lang.Integer.parseInt(Integer.java:615)
	at com.wzsec.modules.quartz.config.MaskPicConfigJob.execute(MaskPicConfigJob.java:33)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-31 11:20:00,071 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.PicJob31 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NumberFormatException: For input string: "PicJob31"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:580)
	at java.lang.Integer.parseInt(Integer.java:615)
	at com.wzsec.modules.quartz.config.MaskPicConfigJob.execute(MaskPicConfigJob.java:33)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-31 11:21:43,616 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:21:44,371 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 11:21:44,371 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:21:44,371 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:21:44,371 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:21:44,412 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:21:44,412 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:21:44,423 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:21:44,423 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:21:44,464 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 11:21:55,460 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 29816 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:21:55,464 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:21:57,927 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:21:57,927 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:21:59,059 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1109 ms. Found 111 JPA repository interfaces.
2025-07-31 11:21:59,620 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:21:59,620 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:21:59,630 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:21:59,630 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:21:59,633 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:21:59,633 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:21:59,635 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:21:59,635 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:21:59,635 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:22:00,331 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:22:00,345 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:22:00,345 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:22:00,770 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:22:00,797 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:22:00,805 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:22:00,805 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:22:00,856 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.3164380987436494105] which is part of the web application []
2025-07-31 11:22:01,034 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:22:01,034 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5069 ms
2025-07-31 11:22:02,296 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:22:02,565 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:22:02,589 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:22:02,589 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:22:02,589 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:22:02,589 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:22:02,589 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:22:02,591 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:22:02,593 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:22:02,595 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:22:04,931 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:22:05,753 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:22:05,982 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:22:06,481 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:22:06,962 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:22:10,681 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:22:10,730 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:22:13,261 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:22:16,624 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:22:16,712 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:22:16,712 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:22:16,734 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:22:16,737 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:22:16,737 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:22:16,737 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:22:16,737 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@7552c30e
2025-07-31 11:22:16,737 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:22:26,272 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5d408b09, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@47dff7db, org.springframework.security.web.context.SecurityContextPersistenceFilter@3980d430, org.springframework.security.web.header.HeaderWriterFilter@1cd7cd44, org.springframework.security.web.authentication.logout.LogoutFilter@23ceb383, org.springframework.web.filter.CorsFilter@5178345d, com.wzsec.modules.security.security.TokenFilter@66c937dd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@728594e9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c66040f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@13ef9007, org.springframework.security.web.session.SessionManagementFilter@2f544e25, org.springframework.security.web.access.ExceptionTranslationFilter@7106b519, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@74bfa2f8]
2025-07-31 11:22:26,313 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:22:30,239 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:22:30,239 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:22:30,239 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:22:30,239 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:22:30,239 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:22:30,239 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:22:30,239 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:22:30,239 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6704d495
2025-07-31 11:22:30,411 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 11:22:30,495 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:22:30,581 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 11:22:30,586 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:22:30,586 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:22:30,586 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:22:30,586 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:22:30,586 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:22:30,586 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:22:30,586 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:22:30,586 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:22:30,588 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:22:30,590 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:22:30,590 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:22:30,590 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:22:30,596 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 11:22:31,690 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 11:22:31,895 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 11:22:32,149 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 11:22:32,370 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 11:22:32,596 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 11:22:32,866 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 31 - 0 20 11 * * ? *
2025-07-31 11:22:32,866 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:22:33,090 INFO (MaskVideoScanConfig.java:67)- 视频脱敏任务新增: 16- 0 0 2 * * ? *
2025-07-31 11:22:33,090 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:22:33,090 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 11:22:33,099 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 38.398 seconds (JVM running for 40.889)
2025-07-31 11:22:33,690 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 11:22:49,933 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 11:22:49,942 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 11:22:49,945 INFO (FrameworkServlet.java:547)- Completed initialization in 3 ms
2025-07-31 11:23:11,223 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 22 - 0 24 11 * * ? *
2025-07-31 11:23:11,223 INFO (MaskPicScanConfig.java:92)- 图片脱敏任务更新: 22 - 0 24 11 * * ? *
2025-07-31 11:24:00,457 ERROR (JobRunShell.java:211)- Job DEFAULT.PicJob-22 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.MaskPictaskconfigServiceImpl.executionFromEngine(MaskPictaskconfigServiceImpl.java:242)
	at com.wzsec.modules.mask.service.impl.MaskPictaskconfigServiceImpl$$FastClassBySpringCGLIB$$de4457.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskPictaskconfigServiceImpl$$EnhancerBySpringCGLIB$$245fb4dc.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskPicConfigJob.execute(MaskPicConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-31 11:24:00,457 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.PicJob-22 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.MaskPictaskconfigServiceImpl.executionFromEngine(MaskPictaskconfigServiceImpl.java:242)
	at com.wzsec.modules.mask.service.impl.MaskPictaskconfigServiceImpl$$FastClassBySpringCGLIB$$de4457.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskPictaskconfigServiceImpl$$EnhancerBySpringCGLIB$$245fb4dc.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskPicConfigJob.execute(MaskPicConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-31 11:25:02,772 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:25:03,567 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 11:25:03,567 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:25:03,567 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:25:03,567 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:25:03,602 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:25:03,602 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:25:03,602 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:25:03,606 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:25:03,636 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 11:25:14,287 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 17896 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:25:14,293 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:25:16,625 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:25:16,629 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:25:17,814 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1156 ms. Found 111 JPA repository interfaces.
2025-07-31 11:25:18,410 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:25:18,411 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:25:18,414 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:25:18,414 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:25:18,418 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:25:18,419 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:25:18,420 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:25:18,420 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:25:18,420 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:25:19,054 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:25:19,058 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:25:19,058 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:25:19,452 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:25:19,477 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:25:19,479 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:25:19,481 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:25:19,520 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.1987436449227174464] which is part of the web application []
2025-07-31 11:25:19,715 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:25:19,715 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 4975 ms
2025-07-31 11:25:21,004 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:25:21,271 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:25:21,280 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:25:21,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:25:21,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:25:21,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:25:21,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:25:21,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:25:21,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:25:21,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:25:23,757 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:25:24,527 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:25:24,740 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:25:25,241 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:25:25,723 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:25:30,145 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:25:30,186 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:25:32,676 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:25:36,179 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:25:36,270 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:25:36,270 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:25:36,283 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:25:36,292 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:25:36,292 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:25:36,292 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:25:36,292 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@4d0f58a9
2025-07-31 11:25:36,292 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:25:46,420 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4f260159, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6f1bb144, org.springframework.security.web.context.SecurityContextPersistenceFilter@32af7447, org.springframework.security.web.header.HeaderWriterFilter@2ad4ade7, org.springframework.security.web.authentication.logout.LogoutFilter@63fbb062, org.springframework.web.filter.CorsFilter@37364634, com.wzsec.modules.security.security.TokenFilter@5f4e6738, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@40b9d8a2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3dfbd145, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5e1e60d7, org.springframework.security.web.session.SessionManagementFilter@4aae59b0, org.springframework.security.web.access.ExceptionTranslationFilter@7a6a4327, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d862e03]
2025-07-31 11:25:46,470 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:25:50,604 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:25:50,604 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:25:50,604 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:25:50,604 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:25:50,604 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:25:50,604 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:25:50,604 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:25:50,607 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@653cfba0
2025-07-31 11:25:50,790 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 11:25:50,878 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:25:50,969 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 11:25:50,974 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:25:50,975 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:25:50,975 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:25:50,975 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:25:50,975 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:25:50,975 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:25:50,977 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:25:50,977 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:25:50,977 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:25:50,980 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:25:50,980 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:25:50,980 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:25:50,987 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 11:25:52,114 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 11:25:52,308 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 11:25:52,546 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 11:25:52,773 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 11:25:52,996 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 11:25:53,277 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 22 - 0 24 11 * * ? *
2025-07-31 11:25:53,277 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 31 - 0 20 11 * * ? *
2025-07-31 11:25:53,277 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：2
2025-07-31 11:25:53,509 INFO (MaskVideoScanConfig.java:67)- [视频脱敏任务新增]: 16- 0 0 2 * * ? *
2025-07-31 11:25:53,509 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:25:53,509 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 11:25:53,520 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 39.92 seconds (JVM running for 42.478)
2025-07-31 11:25:54,278 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 11:26:03,033 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 11:26:03,033 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 11:26:03,036 INFO (FrameworkServlet.java:547)- Completed initialization in 3 ms
2025-07-31 11:26:20,951 INFO (MaskPicScanConfig.java:92)- 图片脱敏任务更新: 22 - 0 27 11 * * ? *
2025-07-31 11:29:49,182 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:29:49,939 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 11:29:49,939 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:29:49,939 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:29:49,939 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:29:50,002 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 11:29:50,002 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:29:50,002 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 11:29:50,012 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:29:50,058 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 11:29:54,958 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 15332 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:29:54,969 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:29:57,216 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:29:57,218 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:29:58,190 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 954 ms. Found 111 JPA repository interfaces.
2025-07-31 11:29:58,856 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:29:58,856 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:29:58,866 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:29:58,868 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:29:58,870 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:29:58,872 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:29:58,874 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:29:58,874 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:29:58,874 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:29:59,659 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:29:59,674 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:29:59,674 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:30:00,129 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:30:00,154 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:30:00,159 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:30:00,159 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:30:00,201 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.1954829870519897828] which is part of the web application []
2025-07-31 11:30:00,395 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:30:00,395 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 4954 ms
2025-07-31 11:30:01,604 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:30:01,872 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:30:01,895 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:30:01,895 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:30:01,895 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:30:01,896 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:30:01,896 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:30:01,896 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:30:01,897 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:30:01,899 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:30:04,323 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:30:05,175 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:30:05,607 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:30:06,445 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:30:07,086 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:30:12,354 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:30:12,443 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:30:16,409 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:30:44,341 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 5692 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:30:44,347 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:30:46,723 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:30:46,723 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:30:47,745 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1019 ms. Found 111 JPA repository interfaces.
2025-07-31 11:30:48,290 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:30:48,290 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:30:48,290 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:30:48,290 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:30:48,290 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:48,299 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:30:48,299 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:30:48,299 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:48,299 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:48,959 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:30:48,975 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:30:48,976 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:30:49,372 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:30:49,396 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:30:49,399 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:30:49,399 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:30:49,439 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.1686660120870438914] which is part of the web application []
2025-07-31 11:30:49,603 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:30:49,603 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 4728 ms
2025-07-31 11:30:50,838 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:30:51,105 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:30:51,137 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:30:51,137 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:30:51,138 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:30:51,138 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:30:51,138 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:30:51,138 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:30:51,141 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:30:51,141 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:30:53,556 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:30:54,351 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:30:54,532 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:30:54,998 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:30:55,445 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:30:59,030 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:30:59,075 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:31:01,346 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:31:04,619 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:31:04,705 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:31:04,705 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:31:04,723 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:31:04,729 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:31:04,729 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:31:04,729 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:31:04,729 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6e0ae02d
2025-07-31 11:31:04,729 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:31:13,681 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@62204612, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@16dcb0b5, org.springframework.security.web.context.SecurityContextPersistenceFilter@326e8433, org.springframework.security.web.header.HeaderWriterFilter@249a58b6, org.springframework.security.web.authentication.logout.LogoutFilter@5effdc4d, org.springframework.web.filter.CorsFilter@3400d6fa, com.wzsec.modules.security.security.TokenFilter@617d6007, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3afa0f4e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1777ef25, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ec82627, org.springframework.security.web.session.SessionManagementFilter@5c62d0c3, org.springframework.security.web.access.ExceptionTranslationFilter@5f9a024e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@612deab0]
2025-07-31 11:31:13,732 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:31:17,530 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:31:17,530 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:31:17,530 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:31:17,530 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:31:17,530 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:31:17,530 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:31:17,530 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:31:17,530 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7779d208
2025-07-31 11:31:17,695 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 11:31:17,766 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:31:17,855 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 11:31:17,860 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:31:17,861 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:31:17,861 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:31:17,861 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:31:17,861 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:31:17,862 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:31:17,862 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:31:17,862 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:31:17,862 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:31:17,863 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:31:17,863 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:31:17,863 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:31:17,870 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 11:31:18,961 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 11:31:19,203 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 11:31:19,436 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 11:31:19,660 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 11:31:19,876 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 11:31:20,173 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 22 - 0 27 11 * * ? *
2025-07-31 11:31:20,173 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 31 - 0 20 11 * * ? *
2025-07-31 11:31:20,173 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：2
2025-07-31 11:31:20,398 INFO (MaskVideoScanConfig.java:67)- [视频脱敏任务新增]: 16- 0 0 2 * * ? *
2025-07-31 11:31:20,398 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:31:20,398 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 11:31:20,415 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 36.819 seconds (JVM running for 39.595)
2025-07-31 11:31:20,977 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 11:32:56,821 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 11:32:56,826 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 11:32:56,829 INFO (FrameworkServlet.java:547)- Completed initialization in 3 ms
2025-07-31 11:33:52,174 INFO (MaskPicScanConfig.java:92)- 图片脱敏任务更新: 22 - 0 35 11 * * ? *
2025-07-31 11:34:26,759 INFO (MaskVideoScanConfig.java:92)- 视频脱敏任务更新: 16- 0 36 11 * * ? *
2025-07-31 11:36:02,950 ERROR (JobRunShell.java:211)- Job DEFAULT.VideoJob-16 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl.executionFromEngine(MaskVideotaskconfigServiceImpl.java:227)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl$$FastClassBySpringCGLIB$$a46b7146.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl$$EnhancerBySpringCGLIB$$4ea950dd.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskVideoConfigJob.execute(MaskVideoConfigJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-31 11:36:02,951 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.VideoJob-16 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl.executionFromEngine(MaskVideotaskconfigServiceImpl.java:227)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl$$FastClassBySpringCGLIB$$a46b7146.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskVideotaskconfigServiceImpl$$EnhancerBySpringCGLIB$$4ea950dd.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskVideoConfigJob.execute(MaskVideoConfigJob.java:34)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-31 11:36:22,963 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 11:52:56,492 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 4736 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:52:56,501 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:52:59,129 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:52:59,129 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:53:00,213 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1059 ms. Found 111 JPA repository interfaces.
2025-07-31 11:53:00,772 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:53:00,772 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:53:00,772 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:53:00,772 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:53:00,772 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:53:00,772 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:53:00,779 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:53:00,779 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:53:00,779 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:53:01,621 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:53:01,662 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:53:01,668 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:53:02,255 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 11:53:02,291 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 11:53:02,295 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 11:53:02,295 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 11:53:02,334 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.1665750611934883779] which is part of the web application []
2025-07-31 11:53:02,577 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 11:53:02,577 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5446 ms
2025-07-31 11:53:04,030 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:53:04,318 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:53:04,355 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:53:04,357 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:53:04,357 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:53:04,357 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:53:04,358 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:53:04,358 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:53:04,362 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:53:04,366 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:53:07,440 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:53:08,263 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:53:08,461 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:53:08,984 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:53:09,534 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:53:13,704 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:53:13,751 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:53:16,458 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 11:53:20,222 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:53:20,313 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:53:20,313 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:53:20,333 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:53:20,337 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:53:20,337 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:53:20,337 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:53:20,338 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@7606a261
2025-07-31 11:53:20,338 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:53:30,534 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@70c81870, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@695d66ce, org.springframework.security.web.context.SecurityContextPersistenceFilter@78ae72f6, org.springframework.security.web.header.HeaderWriterFilter@4a320414, org.springframework.security.web.authentication.logout.LogoutFilter@25b20814, org.springframework.web.filter.CorsFilter@48aa0813, com.wzsec.modules.security.security.TokenFilter@affa948, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@66c755cc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@106976f4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1e2cf7f9, org.springframework.security.web.session.SessionManagementFilter@486417fc, org.springframework.security.web.access.ExceptionTranslationFilter@73c50bf, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3b02d444]
2025-07-31 11:53:30,587 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:53:36,445 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:53:36,446 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:53:36,446 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:53:36,446 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:53:36,446 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:53:36,448 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:53:36,448 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:53:36,448 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@584c289b
2025-07-31 11:53:36,629 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 11:53:36,740 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:53:36,841 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 11:53:36,845 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:53:36,847 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:53:36,847 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:53:36,847 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:53:36,847 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:53:36,847 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:53:36,847 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:53:36,847 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:53:36,847 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:53:36,849 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:53:36,849 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:53:36,849 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:53:36,856 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 11:53:38,378 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 11:53:38,653 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 11:53:38,973 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 11:53:39,248 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 11:53:39,540 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 11:53:39,877 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 22 - 0 35 11 * * ? *
2025-07-31 11:53:39,878 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 31 - 0 20 11 * * ? *
2025-07-31 11:53:39,878 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：2
2025-07-31 11:53:40,155 INFO (MaskVideoScanConfig.java:67)- [视频脱敏任务新增]: 16- 0 36 11 * * ? *
2025-07-31 11:53:40,155 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 11:53:40,156 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 11:53:40,170 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 44.606 seconds (JVM running for 47.387)
2025-07-31 11:53:40,945 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 11:56:19,197 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 11:56:19,197 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 11:56:19,214 INFO (FrameworkServlet.java:547)- Completed initialization in 17 ms
2025-07-31 12:18:30,678 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 12:18:31,760 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-31 12:18:31,769 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 12:18:31,769 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 12:18:31,771 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 12:18:31,860 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 12:18:31,876 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 12:18:31,877 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 12:18:31,892 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 12:18:31,994 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-31 13:38:34,636 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 8644 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 13:38:34,645 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 13:38:37,378 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 13:38:37,379 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 13:38:38,521 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1124 ms. Found 111 JPA repository interfaces.
2025-07-31 13:38:39,070 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 13:38:39,070 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 13:38:39,074 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 13:38:39,074 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 13:38:39,076 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 13:38:39,079 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 13:38:39,079 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 13:38:39,079 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 13:38:39,081 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 13:38:39,812 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 13:38:39,827 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 13:38:39,827 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 13:38:40,283 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-31 13:38:40,315 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-31 13:38:40,319 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-31 13:38:40,319 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-31 13:38:40,367 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.946045188829337982] which is part of the web application []
2025-07-31 13:38:40,586 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-31 13:38:40,589 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5367 ms
2025-07-31 13:38:42,125 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 13:38:42,409 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 13:38:42,434 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 13:38:42,434 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 13:38:42,434 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 13:38:42,434 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 13:38:42,434 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 13:38:42,436 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 13:38:42,437 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 13:38:42,439 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 13:38:45,561 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 13:38:46,402 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 13:38:46,623 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 13:38:47,171 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 13:38:47,720 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 13:38:51,973 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 13:38:52,047 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 13:38:54,579 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-31 13:38:58,093 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 13:38:58,185 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 13:38:58,187 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 13:38:58,205 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 13:38:58,208 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 13:38:58,208 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 13:38:58,208 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 13:38:58,208 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@34b312d8
2025-07-31 13:38:58,208 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 13:39:08,347 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b4b8163, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c1b20e9, org.springframework.security.web.context.SecurityContextPersistenceFilter@41e8e7d4, org.springframework.security.web.header.HeaderWriterFilter@653408dc, org.springframework.security.web.authentication.logout.LogoutFilter@46103135, org.springframework.web.filter.CorsFilter@2b3b1124, com.wzsec.modules.security.security.TokenFilter@33697548, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ad4ade7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@33953208, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1d862e03, org.springframework.security.web.session.SessionManagementFilter@6525968a, org.springframework.security.web.access.ExceptionTranslationFilter@679d66fa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2412347c]
2025-07-31 13:39:08,387 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 13:39:12,850 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 13:39:12,850 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 13:39:12,850 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 13:39:12,850 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 13:39:12,850 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 13:39:12,852 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 13:39:12,852 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 13:39:12,852 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@8785f98
2025-07-31 13:39:13,027 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-31 13:39:13,101 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 13:39:13,175 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-31 13:39:13,184 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 13:39:13,184 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 13:39:13,184 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 13:39:13,184 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 13:39:13,189 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 13:39:13,189 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 13:39:13,189 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 13:39:13,189 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 13:39:13,189 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 13:39:13,190 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 13:39:13,190 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 13:39:13,190 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 13:39:13,190 INFO (ScanInstantiation.java:38)- 任务初始化---start---
2025-07-31 13:39:14,376 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-31 13:39:14,599 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-31 13:39:14,858 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-31 13:39:15,101 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-31 13:39:15,381 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-31 13:39:15,804 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 22 - 0 35 11 * * ? *
2025-07-31 13:39:15,805 INFO (MaskPicScanConfig.java:67)- 图片脱敏任务新增: 31 - 0 20 11 * * ? *
2025-07-31 13:39:15,805 INFO (MaskPicScanConfig.java:39)- 图片脱敏初始化数：2
2025-07-31 13:39:16,073 INFO (MaskVideoScanConfig.java:67)- [视频脱敏任务新增]: 16- 0 36 11 * * ? *
2025-07-31 13:39:16,073 INFO (MaskVideoScanConfig.java:39)- 图片脱敏初始化数：1
2025-07-31 13:39:16,073 INFO (ScanInstantiation.java:47)- 任务初始化---end---
2025-07-31 13:39:16,082 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 42.217 seconds (JVM running for 45.751)
2025-07-31 13:39:16,686 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-31 13:39:43,110 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 13:39:43,110 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-31 13:39:43,110 INFO (FrameworkServlet.java:547)- Completed initialization in 0 ms
