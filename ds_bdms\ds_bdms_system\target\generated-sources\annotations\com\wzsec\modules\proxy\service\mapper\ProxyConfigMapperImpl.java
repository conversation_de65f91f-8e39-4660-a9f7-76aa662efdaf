package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.ProxyConfig;
import com.wzsec.modules.proxy.service.dto.ProxyConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ProxyConfigMapperImpl implements ProxyConfigMapper {

    @Override
    public ProxyConfigDto toDto(ProxyConfig entity) {
        if ( entity == null ) {
            return null;
        }

        ProxyConfigDto proxyConfigDto = new ProxyConfigDto();

        proxyConfigDto.setCreatetime( entity.getCreatetime() );
        proxyConfigDto.setCreateuser( entity.getCreateuser() );
        proxyConfigDto.setExecutionengine( entity.getExecutionengine() );
        proxyConfigDto.setId( entity.getId() );
        proxyConfigDto.setIsvalid( entity.getIsvalid() );
        proxyConfigDto.setNote( entity.getNote() );
        proxyConfigDto.setProxyport( entity.getProxyport() );
        proxyConfigDto.setSparefield1( entity.getSparefield1() );
        proxyConfigDto.setSparefield2( entity.getSparefield2() );
        proxyConfigDto.setSparefield3( entity.getSparefield3() );
        proxyConfigDto.setSparefield4( entity.getSparefield4() );
        proxyConfigDto.setSparefield5( entity.getSparefield5() );
        proxyConfigDto.setSrcid( entity.getSrcid() );
        proxyConfigDto.setUpdatetime( entity.getUpdatetime() );
        proxyConfigDto.setUpdateuser( entity.getUpdateuser() );

        return proxyConfigDto;
    }

    @Override
    public List<ProxyConfigDto> toDto(List<ProxyConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ProxyConfigDto> list = new ArrayList<ProxyConfigDto>( entityList.size() );
        for ( ProxyConfig proxyConfig : entityList ) {
            list.add( toDto( proxyConfig ) );
        }

        return list;
    }

    @Override
    public ProxyConfig toEntity(ProxyConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        ProxyConfig proxyConfig = new ProxyConfig();

        proxyConfig.setCreatetime( dto.getCreatetime() );
        proxyConfig.setCreateuser( dto.getCreateuser() );
        proxyConfig.setExecutionengine( dto.getExecutionengine() );
        proxyConfig.setId( dto.getId() );
        proxyConfig.setIsvalid( dto.getIsvalid() );
        proxyConfig.setNote( dto.getNote() );
        proxyConfig.setProxyport( dto.getProxyport() );
        proxyConfig.setSparefield1( dto.getSparefield1() );
        proxyConfig.setSparefield2( dto.getSparefield2() );
        proxyConfig.setSparefield3( dto.getSparefield3() );
        proxyConfig.setSparefield4( dto.getSparefield4() );
        proxyConfig.setSparefield5( dto.getSparefield5() );
        proxyConfig.setSrcid( dto.getSrcid() );
        proxyConfig.setUpdatetime( dto.getUpdatetime() );
        proxyConfig.setUpdateuser( dto.getUpdateuser() );

        return proxyConfig;
    }

    @Override
    public List<ProxyConfig> toEntity(List<ProxyConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ProxyConfig> list = new ArrayList<ProxyConfig>( dtoList.size() );
        for ( ProxyConfigDto proxyConfigDto : dtoList ) {
            list.add( toEntity( proxyConfigDto ) );
        }

        return list;
    }
}
