package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
//import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2020-12-11
*/
@Entity
@Data
@Table(name="sdd_mask_hivetaskconfig")
public class HiveTaskConfig implements Serializable {

    /** 自增id */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    //@NotBlank
    private String taskname;

    /** 数据源id */
    @Column(name = "sourceid")
    private Integer sourceid;

    /** Hive库名 */
    @Column(name = "dbname")
    //@NotBlank
    private String dbname;

    /** 表名 */
    @Column(name = "tablename")
    //@NotBlank
    private String tablename;

    /** 被脱敏表 */
    @Column(name = "maskedtable")
    private String maskedtable;

    /** 脱敏策略Id */
    @Column(name = "strategyid")
    private Integer strategyid;

    /** jar包路径 */
    @Column(name = "jarpath")
    //@NotBlank
    private String jarpath;

    /** 执行所用队列 */
    @Column(name = "queuename")
    private String queuename;

    /** 分区信息 */
    @Column(name = "partitioninfo")
    private String partitioninfo;

    /** 数据行数 */
    @Column(name = "datarows")
    private String datarows;

    /** 输出类型1库表、2文件 */
    @Column(name = "outputtype")
    //@NotBlank
    private String outputtype;

    /** 输出库名 */
    @Column(name = "outputdbname")
    private String outputdbname;

    /** 数据输出目录 */
    @Column(name = "outputdir")
    private String outputdir;

    /** 输出文件数据分隔符 */
    @Column(name = "datasplit")
    private String datasplit;

    /** 输出文件数据格式 */
    @Column(name = "fileformat")
    private String fileformat;

    /** 任务状态：(0初始创建,1执行中,2执行成功,3执行失败,4提交失败) */
    @Column(name = "status")
    private String status;

    /** 所有字段 */
    @Column(name = "fieldnames")
    private String fieldnames;

    /** 抽取字段 */
    @Column(name = "extractfields")
    private String extractfields;

    /** 脱敏字段 */
    @Column(name = "maskfieldnames")
    private String maskfieldnames;

    /** 脱敏策略son串 */
    @Column(name = "maskstrategystr")
    private String maskstrategystr;

    /** 创建人 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新人 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(HiveTaskConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
