package com.wzsec.modules.api.algo;

import org.apache.commons.lang.StringUtils;

/**
 * 改写结果脱敏算法_截断(向后截断_0:不保留截断符;1:表示保留截断符)
 */
public class CUTOUTMask {

    /**
     * 截断（参数：截断符，保留标识<0：不保留，1：保留>）
     *
     * @param truncationMark 截断符
     * @param truncationId   保留标识
     */
    public static String encrypt(String strData, String truncationMark, String truncationId) {
        if (strData != null && !strData.equals("")) {
            String strMaskResult = strData;

            if (truncationId.equals("0")) {
                strMaskResult = strData.substring(0, strData.indexOf(truncationMark));
            } else if (truncationId.equals("1")) {
                strMaskResult = strData.substring(0, strData.indexOf(truncationMark) + 1);
            }

            String[] strMaskResultArr = strMaskResult.split("");

            return StringUtils.join(strMaskResultArr, "");
        }
        return null;


//        String hiddenCharacters = null;
//        if (strData != null && !strData.equals("")) {
//            String strMaskResult = strData;
//            if (truncationId == "0") {
//                hiddenCharacters = strData.substring(strData.indexOf(truncationMark), strMaskResult.length());
//            } else {
//                hiddenCharacters = strData.substring(strData.indexOf(truncationMark) + 1, strMaskResult.length());
//            }
//            String[] strMaskResultArr = strMaskResult.split("");
//            for (int i = 0; i < strMaskResultArr.length; i++) {
//                if (hiddenCharacters.contains(strMaskResultArr[i])) {
//                    strMaskResultArr[i] = "";
//                }
//            }
//            return StringUtils.join(strMaskResultArr, "");
//        }
//        return null;
    }

    public static void main(String[] args) {
        String strData = "<EMAIL>";
        System.out.println(encrypt(strData, "@", "0"));
        System.out.println(encrypt(strData, "@", "1"));
        System.out.println(encrypt(strData, ".", "1"));
    }


}
