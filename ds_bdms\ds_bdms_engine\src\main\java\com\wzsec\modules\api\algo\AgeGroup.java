package com.wzsec.modules.api.algo;

/**
 * @description: 年龄分档
 * @author: JOY
 * @date: 2022-03-04
 **/
public class AgeGroup {
    public static String encrypt(String strData) {
        long age = Integer.parseInt(strData);
        if (age < 0) {
            return "DateError";
        } else if (0 <= age && age <= 6) {
            return "0-6";
        } else if (7 <= age && age <= 12) {
            return "7-12";
        } else if (13 <= age && age <= 17) {
            return "13-17";
        } else if (18 <= age && age <= 45) {
            return "18-45";
        } else if (46 <= age && age <= 69) {
            return "46-69";
        } else {
            return "70+";
        }
    }


    public static void main(String[] args) {
        String strData = "22";
        System.out.println(encrypt(strData));
    }

}
