package com.wzsec.dotask.mask.service.excute.hdfs;

import com.alibaba.fastjson.JSON;
import com.wzsec.dotask.mask.service.excute.file.MaskingConfigInfo;
import com.wzsec.modules.mask.domain.HadoopTaskResultModel;
import com.wzsec.utils.AuthKrb5;
import com.wzsec.utils.ConstEngine;
import com.wzsec.utils.DateUtil;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Counter;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Description:Linux下数据脱敏
 * <AUTHOR>
 * @date 2017-1-5
 */
public class DataMaskingOnHadoop {

	private static Logger log = Logger.getLogger(DataMaskingOnHadoop.class);

	/**
	 * @Description:Driver驱动程序
	 * @args id、batchname、fileformat、splitStr、ConfigInfo、inputPath、outputPath
	 * <AUTHOR>
	 * @date 2017-1-4
	 */
	public static boolean doDataMasking(String[] args, HadoopTaskResultModel hadoopTaskResultModel, List<Map<String,Object>> ruleList) throws Exception {
		// 先进行kerberos认证
		AuthKrb5.authKrb5();
		//System.setProperty("hadoop.home.dir", "D:\\winutil\\");
		boolean isSuccess =false;
		String status = "2";
		//job表中用户名、批次号、文件格式、分隔符、脱敏参数、数据输入路径、数据输出路径
		String userName = args[0];    //用户名
		String taskName = args[1]; //job提交时任务号
		//设置Hadoop作业提交用户
		///通过这种方式设置java客户端访问hdfs的身份：会以 userName 的身份访问 hdfs文件系统目录下的路径：/user/userName 的目录
		System.setProperty("HADOOP_USER_NAME", userName);
		Configuration conf = new Configuration();
		String splitStr = args[3];
		if(splitStr.equals("u0005")) {  //特殊字符
		   splitStr = "\u0005";
		}
		conf.set("splitStr", splitStr);
        conf.set("mapreduce.job.jar","HadoopMask.jar");

		/** hadoop集群任一节点拿配置文件到本地 */
		String coreSite = ConstEngine.sddEngineConfs.get("kb.coreSite");
		String hdfsSite = ConstEngine.sddEngineConfs.get("kb.hdfsSite");
		String yarnSite = ConstEngine.sddEngineConfs.get("kb.yarnSite");
		conf.addResource(new Path(coreSite));
		conf.addResource(new Path(hdfsSite));
		conf.addResource(new Path(yarnSite));

		// 提交jar的路径
		//conf.set("mapred.jar", property);
		//设置队列
		String queuename = args[7];
		if (null != queuename && !"".equals(queuename)) {
			conf.set("mapred.job.queue.name", queuename);
		}
		String newStrMaskingConfigInfo = args[4];
		conf.set("maskingConfigInfoStr", newStrMaskingConfigInfo);
		// 策略类型(0:格式化文件,1非格式化文件)
		String strategyType = args[8];
		conf.set("strategyType",strategyType);
		// 敏感识别规则 存JSON字符串格式
		conf.set("ruleList", JSON.toJSONString(ruleList));

		// 设置MR递归子目录
		conf.setBoolean("mapreduce.input.fileinputformat.input.dir.recursive", true);
		try {
			// 设置Hadoop JobName
			Job job = Job.getInstance(conf);
			//指定本次mr用户名
			job.setUser(userName);
			//指定本次mr任务名
			job.setJobName(taskName);
			//指定本次mr运行主类
			job.setJarByClass(DataMaskingOnHadoop.class);
			//指定本次mr运行Mapper类
			job.setMapperClass(DataMaskingMapper.class);
			//指定本次mr运行Reduce任务个数
			job.setNumReduceTasks(0);
			//指定本次mr运行Reduce类
			job.setReducerClass(DataMaskingReducer.class);
			//指定本次mr输出Key类型
			job.setOutputKeyClass(NullWritable.class);
			//指定本次mr输出Value类型
			job.setOutputValueClass(Text.class);
			// 设置输入路径
			String inputPath = args[5];
			//指定本次mr输入的数据路径
			FileInputFormat.addInputPath(job, new Path(inputPath));
			// 设置输出路径
			String outputPath = args[6];
			String endTime = DateUtil.getNowTimeString("yyyyMMddHHmmss");
			if (!outputPath.endsWith(File.separator)) {
				outputPath = outputPath + File.separator;
			}
			//指定本次mr输出数据路径
			FileOutputFormat.setOutputPath(job, new Path(outputPath + taskName + File.separator + endTime));
			//指定本次mr输入文件格式
			/*if(null!=fileformat && fileformat.equalsIgnoreCase("lzo")){
		       job.setInputFormatClass(LzoTextInputFormat.class);
		    }*/
			//指定本次mr输出文件格式
			String outputfileformat =args[2];
			/*if(null!=outputfileformat && outputfileformat.equalsIgnoreCase("lzo")){
				FileOutputFormat.setCompressOutput(job, true);
				FileOutputFormat.setOutputCompressorClass(job, LzopCodec.class);
			}*/
			job.waitForCompletion(true);
			isSuccess = job.isSuccessful();
			//log.info(Const.STRMASKLOGFLAG + "Hadoop脱敏执行是否成功:" + isSuccess);
		    if(isSuccess){
		        Counter findCounter = job.getCounters().findCounter("DataMasking", "DataLines");
				int count = (int) findCounter.getValue();
				hadoopTaskResultModel.setCount(count);
				hadoopTaskResultModel.setUsername(userName);
				hadoopTaskResultModel.setInputpath(inputPath);
				hadoopTaskResultModel.setOutputPath(outputPath+taskName);
		    }
		} catch (Exception ex) {
			throw ex;
		}
		return isSuccess;
	}

	/**
	 * @Description:将脱敏配置信息数组集合转换为特定字符串，如下列格式：
	 * 1|MD5$9876543210123456;2|DATE_EARLY_INT$1,2;3|IDNUMBER
	 * <AUTHOR>
	 * @date 2017-1-3
	 */
	public static String getMaskingConfigInfoStr(MaskingConfigInfo[] maskConfigInfoArr) {
		StringBuilder sbMaskingConfigInfo = new StringBuilder();
		if (null != maskConfigInfoArr && maskConfigInfoArr.length > 0) { // 如果有脱敏配置
			for (int i = 0; i < maskConfigInfoArr.length; i++) {
				sbMaskingConfigInfo.append(maskConfigInfoArr[i].getMaskingPostion() + "|");
				sbMaskingConfigInfo.append(maskConfigInfoArr[i].getAlgoName());
				if (null != maskConfigInfoArr[i].getAlgoParameter()
						&& !maskConfigInfoArr[i].getAlgoParameter().equals("")) {
					sbMaskingConfigInfo.append("$" + maskConfigInfoArr[i].getAlgoParameter());
					sbMaskingConfigInfo.append(";");
				} else {
					sbMaskingConfigInfo.append(";");
				}
			}
		} else {
			sbMaskingConfigInfo.append(""); // 没有脱敏配置，则给空字符串
		}
		return sbMaskingConfigInfo.toString();
	}

	/**
	 * @Description:从脱敏算法配置文件中获取脱敏配置信息数组
	 * <AUTHOR>
	 * @date 2017-1-3
	 */
	public static MaskingConfigInfo[] getMaskingConfigInfoFromXml(Configuration conf, String maskingConfigKey) {
		// String configValue = properties.getProperty(maskingConfigKey);
		String configValue = conf.get(maskingConfigKey); // maskingconfig对应值如1|MD5,2|AES_ENC,3|MD5
		MaskingConfigInfo[] arrMaskingConfigInfo = null;
		if (null != configValue && !configValue.isEmpty()) {
			int itemNum = configValue.split(";").length; // 配置项数
			arrMaskingConfigInfo = new MaskingConfigInfo[itemNum];
			// 将脱敏配置项转换为Map
			HashMap<Integer, String> configMap = getConfigMapByConfigValue(configValue);
			int i = 0;
			// 遍历配置Map
			for (Integer posKey : configMap.keySet()) {
				String algoPara = getParaByAlgoNamePositionFromXml(conf, configMap.get(posKey), posKey.intValue());
				MaskingConfigInfo mcInfo = new MaskingConfigInfo(posKey.intValue(), configMap.get(posKey), algoPara, false, null);
				arrMaskingConfigInfo[i] = mcInfo;
				i = i + 1;
			}
		}
		return arrMaskingConfigInfo;
	}

	/**
	 * @Description: 从脱敏算法配置文件中根据算法名获取脱敏算法参数数组
	 * <AUTHOR>
	 * @date 2017-1-3
	 */
	public static String getParaByAlgoNamePositionFromXml(Configuration conf, String algoName, int position) {
		String algoParaConfigItem = algoName.toUpperCase() + ".parameter";
		String para = "";
		String algoParaConfigValue = "";
		if (null != conf.get(algoParaConfigItem) && !conf.get(algoParaConfigItem).isEmpty()) {
			algoParaConfigValue = conf.get(algoParaConfigItem).trim(); // 截取左右空格
		}

		if (null != algoParaConfigValue && !algoParaConfigValue.isEmpty()) {
			// 有多个字段使用这个脱敏算法,如MD5.parameter=1|9876543210123456;3|9876543210123456;4|9876543210123456
			// 2|3,1
			if (algoParaConfigValue.contains(";")) {
				String[] positionParaArray = algoParaConfigValue.split(";");
				for (int j = 0; j < positionParaArray.length; j++) {
					if (positionParaArray[j].startsWith(String.valueOf(position))) {
						para = positionParaArray[j].substring(positionParaArray[j].indexOf("|") + 1);
					}
				}
			} else {
				// 1|9876543210123456
				if (algoParaConfigValue.startsWith(String.valueOf(position))) {
					para = algoParaConfigValue.substring(algoParaConfigValue.indexOf("|") + 1);
				}
			}
		}

		return para;
	}

	/**
	 * @Description:从脱敏算法配置文件中根据maskingconfig项获取脱敏字段位置及脱敏算法名
	 * <AUTHOR>
	 * @date 2017-1-3
	 */
	public static HashMap<Integer, String> getConfigMapByConfigValue(String configValue) {
		//存放脱敏位置与算法的Map
		HashMap<Integer, String> configMap = new HashMap<Integer, String>();
		//如果类似1|SHA1;2|MD5情形
		if (configValue.contains("|") && configValue.contains(";")) {
			String[] positionTypes = configValue.split(";");
			for (int i = 0; i < positionTypes.length; i++) {
				String[] postionType = positionTypes[i].split("\\|");
				if(postionType.length==2){
					configMap.put(Integer.parseInt(postionType[0]), postionType[1]);
				}
			}
			return configMap;
		}
		//如果类似1:SHA1情形
		else if (configValue.contains("|") && !configValue.contains(";")) {
			String[] postionType = configValue.split("\\|");
			if(postionType.length==2){
				configMap.put(Integer.parseInt(postionType[0]), postionType[1]);
			}
			return configMap;
		} else {
			return null;
		}
	}
}
