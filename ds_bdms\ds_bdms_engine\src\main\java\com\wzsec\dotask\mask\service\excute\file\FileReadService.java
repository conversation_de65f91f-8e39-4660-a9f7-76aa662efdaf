package com.wzsec.dotask.mask.service.excute.file;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import com.wzsec.dotask.sdd.service.excute.common.RuleManager;
import com.wzsec.modules.mask.domain.MaskStrategyFileFormatSub;
import com.wzsec.modules.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.mask.service.AlgorithmService;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import com.wzsec.modules.mask.service.dto.FileTaskConfigDto;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.utils.*;
import com.wzsec.utils.database.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

@Slf4j
@Component
public class FileReadService {

    private static RuleService ruleService;

    private static AlgorithmService algorithmService;

    private static DatasourceService datasourceService;

    public FileReadService(RuleService ruleService, AlgorithmService algorithmService, DatasourceService datasourceService) {
        this.ruleService = ruleService;
        this.algorithmService = algorithmService;
        this.datasourceService = datasourceService;
    }

    /**
     * @Description:读取目录下文件
     * <AUTHOR>
     * @date 2020-11-13
     */
    public static List<HashMap<String, List<String>>> readDirFile(FileTaskConfigDto fileTaskConfigDto, String inputPath) throws Exception {
        List<HashMap<String, List<String>>> lineDataList = new ArrayList<HashMap<String, List<String>>>();
        try {
            List<File> fileList = new ArrayList<File>();
            File file = new File(inputPath); // 任务配置扫描路径
            System.out.println(file.isFile());
            if (file.isFile()) {
                fileList.add(file);
            } else {
                File[] files = file.listFiles(); // 获取目录下的所有文件或文件夹
                if (files == null) { // 如果目录为空，直接退出
                    return null;
                }
                // 遍历，目录下的所有文件
                for (File f : files) {
                    if (!f.isDirectory()) {
                        if (f.isFile()) {
                            if (!f.getName().endsWith(".checked")) {
                                fileList.add(f);
                            } else {
                                continue;
                            }
                        }
                    }
                }
            }


            // 读取解析文本文件
            for (File f1 : fileList) {
                String fileName = f1.getName();
                if (fileName.endsWith("txt") || fileName.endsWith("dat") || fileName.endsWith("csv") ||
                        fileName.endsWith("del") || fileName.endsWith("xls") || fileName.endsWith("xlsx")) {
                    // 读取txt文件
                    if ((fileName.endsWith("txt") && "txt".equals(CheckFileTypeUtil.getFileType(f1.getAbsolutePath()))) ||
                            (fileName.endsWith("dat")) ||
                            (fileName.endsWith("del"))
                    ) {
                        String detect = EncodingDetect.detect(f1.getAbsolutePath());
                        ArrayList<String> readTxtFile = ReadFileService.readTxtFile(f1.getAbsolutePath(), null);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readTxtFile);
                        lineDataList.add(LineDataMap);

                    } else if (fileName.endsWith("csv")) {
                        ArrayList<String> readCsvFile = ReadFileService.readCsvFile(f1.getAbsolutePath(), null);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readCsvFile);
                        lineDataList.add(LineDataMap);
                    } else if (fileName.endsWith("xls")) {
                        ArrayList<String> readXlsFile = ReadFileService.readXlsFile(f1.getAbsolutePath(), fileTaskConfigDto.getSplitstr(), null);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readXlsFile);
                        lineDataList.add(LineDataMap);
                    } else if (fileName.endsWith("xlsx")) {
                        ArrayList<String> readXlsxFile = ReadFileService.readXlsxFile(f1.getAbsolutePath(), fileTaskConfigDto.getSplitstr(), null);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readXlsxFile);
                        lineDataList.add(LineDataMap);
                    }
                } else {
                    System.out.println(inputPath + "目录下 " + f1.getName() + "不处理");
                }
            }
            return lineDataList;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * @Description:读取目录下第一个文件(只读取一个文件)
     * <AUTHOR>
     * @date 2020-11-13
     */
    public static List<HashMap<String, List<String>>> readDirFirstFile(FileTaskConfigDto fileTaskConfigDto, String inputPath) throws Exception {
        List<HashMap<String, List<String>>> lineDataList = new ArrayList<HashMap<String, List<String>>>();
        try {
            List<File> fileList = new ArrayList<File>();
            File file = new File(inputPath); // 任务配置扫描路径
            System.out.println(file.isFile());
            if (file.isFile()) {
                fileList.add(file);
            } else {
                File[] files = file.listFiles(); // 获取目录下的所有文件或文件夹
                if (files == null) { // 如果目录为空，直接退出
                    return null;
                }
                // 遍历，目录下的所有文件
                for (File f : files) {
                    if (!f.isDirectory()) {
                        if (f.isFile()) {
                            if (!f.getName().endsWith(".checked")) {
                                fileList.add(f);
                                break;
                            } else {
                                continue;
                            }
                        }
                    }
                }
            }


            // 读取解析文本文件
            for (File f1 : fileList) {
                String fileName = f1.getName();
                if (fileName.endsWith("txt") || fileName.endsWith("dat") || fileName.endsWith("csv") ||
                        fileName.endsWith("del") || fileName.endsWith("xls") || fileName.endsWith("xlsx")) {
                    // 读取txt文件
                    if ((fileName.endsWith("txt") && "txt".equals(CheckFileTypeUtil.getFileType(f1.getAbsolutePath()))) ||
                            (fileName.endsWith("dat")) ||
                            (fileName.endsWith("del"))
                    ) {
                        String detect = EncodingDetect.detect(f1.getAbsolutePath());
                        ArrayList<String> readTxtFile = ReadFileService.readTxtFile(f1.getAbsolutePath(), 5);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readTxtFile);
                        lineDataList.add(LineDataMap);

                    } else if (fileName.endsWith("csv")) {
                        ArrayList<String> readCsvFile = ReadFileService.readCsvFile(f1.getAbsolutePath(), 5);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readCsvFile);
                        lineDataList.add(LineDataMap);
                    } else if (fileName.endsWith("xls")) {
                        ArrayList<String> readXlsFile = ReadFileService.readXlsFile(f1.getAbsolutePath(), fileTaskConfigDto.getSplitstr(), 5);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readXlsFile);
                        lineDataList.add(LineDataMap);
                    } else if (fileName.endsWith("xlsx")) {
                        ArrayList<String> readXlsxFile = ReadFileService.readXlsxFile(f1.getAbsolutePath(), fileTaskConfigDto.getSplitstr(), 5);
                        HashMap<String, List<String>> LineDataMap = new HashMap<String, List<String>>();
                        LineDataMap.put(fileName, readXlsxFile);
                        lineDataList.add(LineDataMap);
                    }
                } else {
                    System.out.println(inputPath + "目录下 " + f1.getName() + "不处理");
                }
            }
            return lineDataList;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * @Description:读取目录下的sql文件
     * <AUTHOR>
     * @date 2020-11-13
     */
    public static List<SQLFileUtil> readSqlFile(String inputPath, String suffix) throws Exception {
        List<SQLFileUtil> sqlFileUtilList = new ArrayList<>();
        try {
            List<File> fileList = new ArrayList<File>();
            File file = new File(inputPath); // 任务配置扫描路径
            System.out.println(file.isFile());
            if (file.isFile()) {
                fileList.add(file);
            } else {
                File[] files = file.listFiles(); // 获取目录下的所有文件或文件夹
                if (files == null) { // 如果目录为空，直接退出
                    return null;
                }
                // 遍历，目录下的所有文件
                for (File f : files) {
                    if (!f.isDirectory()) {
                        if (f.isFile()) {
                            if (!f.getName().endsWith(".checked")) {
                                fileList.add(f);
                            } else {
                                continue;
                            }
                        }
                    }
                }
            }
            // 读取解析文本文件
            for (File f1 : fileList) {
                String fileName = f1.getName();
                if (fileName.endsWith(suffix)) {
                    SQLFileUtil sqlFileUtil = new SQLFileUtil(f1.getAbsolutePath());
                    sqlFileUtilList.add(sqlFileUtil);
                } else {
                    System.out.println(inputPath + "目录下 " + f1.getName() + "不处理");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            return sqlFileUtilList;
        }
    }

    /**
     * @Description:读取目录下的对应后缀的文件
     * <AUTHOR>
     * @date 2024-06-05
     */
    public static Map<String, String> readFileBySuffix(String inputPath, String suffix) {
        Map<String, String> fileInfoMap = new LinkedHashMap<>();
        try {
            List<File> fileList = new ArrayList<File>();
            File file = new File(inputPath); // 任务配置扫描路径
            System.out.println(file.isFile());
            if (file.isFile()) {
                fileList.add(file);
            } else {
                File[] files = file.listFiles(); // 获取目录下的所有文件或文件夹
                if (files == null) { // 如果目录为空，直接退出
                    return null;
                }
                // 遍历，目录下的所有文件
                for (File f : files) {
                    if (!f.isDirectory()) {
                        if (f.isFile()) {
                            if (!f.getName().endsWith(".checked")) {
                                fileList.add(f);
                            } else {
                                continue;
                            }
                        }
                    }
                }
            }
            // 读取解析文本文件
            for (File f1 : fileList) {
                String fileName = f1.getName();
                if (fileName.endsWith(suffix)) {
                    fileInfoMap.put(f1.getAbsolutePath(), fileName);
                } else {
                    System.out.println(inputPath + "目录下 " + f1.getName() + "不处理");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            return fileInfoMap;
        }
    }

    /**
     * 读取目录及子目录下的对应后缀的文件
     * @param inputPath 根目录路径
     * @param suffix 目标文件后缀（如 ".txt"）
     * @return 键：文件绝对路径，值：文件名（包含后缀）
     */
    public static Map<String, String> readFileBySuffixList(String inputPath, String suffix) {
        Map<String, String> fileInfoMap = new LinkedHashMap<>();
        File rootFile = new File(inputPath);

        // 检查路径是否存在
        if (!rootFile.exists()) {
            System.out.println("路径不存在：" + inputPath);
            return fileInfoMap;
        }

        // 递归收集所有符合条件的文件
        List<File> fileList = new ArrayList<>();
        collectFiles(rootFile, suffix, fileList);

        // 填充结果Map
        for (File file : fileList) {
            fileInfoMap.put(file.getAbsolutePath(), file.getName());
        }

        return fileInfoMap;
    }

    /**
     * 递归收集目录及子目录中符合后缀的文件
     * @param currentFile 当前文件/目录
     * @param suffix 目标后缀
     * @param fileList 收集结果的列表
     */
    private static void collectFiles(File currentFile, String suffix, List<File> fileList) {
        // 如果是文件，检查后缀
        if (currentFile.isFile()) {
            // 跳过.checked后缀的文件，且只保留目标后缀的文件
            if (!currentFile.getName().endsWith(".checked")
                    && currentFile.getName().endsWith(suffix)) {
                fileList.add(currentFile);
            }
            return;
        }

        // 如果是目录，递归遍历子文件
        File[] childFiles = currentFile.listFiles();
        if (childFiles == null) { // 目录为空或无权限
            System.out.println("无法访问目录（可能无权限）：" + currentFile.getAbsolutePath());
            return;
        }

        // 遍历子文件/子目录
        for (File child : childFiles) {
            collectFiles(child, suffix, fileList); // 递归处理
        }
    }

    /**
     * @Description:对抽取字段中脱敏字段的数据进行脱敏
     * <AUTHOR>
     * @date 2019-11-12
     */
    public static List<String> getMaskDataList(List<String> fieldDataList, String maskAlgoInfoMap, String splitstr, HashMap<String, Algorithm> algorithmMap) throws Exception {
        MaskingConfigInfo[] maskingConfigInfoArray = getMaskingConfigInfoArray(maskAlgoInfoMap);
        ArrayList<String> maskDataList = new ArrayList<String>();
        for (String lineData : fieldDataList) {
            String maskLineData = getMaskingLineData(lineData, splitstr, maskingConfigInfoArray, algorithmMap);
            maskDataList.add(maskLineData);
        }
        return maskDataList;
    }

    public static List<String> getMaskDataList(List<String> fieldDataList,
                                               String maskAlgoInfoMap,
                                               String splitstr,
                                               HashMap<String, Algorithm> algorithmMap,
                                               Map<Integer, MaskStrategyFileFormatSub> maskStrategyFileFormatSubMap,
                                               Map<String, String> abnormalHandlingAlarmMap) throws Exception {
        MaskingConfigInfo[] maskingConfigInfoArray = getMaskingConfigInfoArray(maskAlgoInfoMap);
        ArrayList<String> maskDataList = new ArrayList<String>();
        for (String lineData : fieldDataList) {
            String maskLineData = getMaskingLineData(lineData, splitstr, maskingConfigInfoArray, algorithmMap, maskStrategyFileFormatSubMap, abnormalHandlingAlarmMap);
            maskDataList.add(maskLineData);
        }
        return maskDataList;
    }

    /**
     * @Description: 得到脱敏行数据
     * <AUTHOR>
     * @date 2020-11-13
     */
    public static String getMaskingLineData(String lineData, String split, MaskingConfigInfo[] maskingConfigArray, HashMap<String, Algorithm> algorithmMap) throws Exception {

        String maskLineData = "";
        String maskingData = "";
        StringBuilder maskDataSBuilder = new StringBuilder();
        if (null != maskingConfigArray && maskingConfigArray.length > 0) {
            //1.对原始行数据进行处理
            String newLine = MaskingHelper.replaceWhile(lineData, split);
            String[] arrData = StringUtils.split(newLine, split);

            //2.遍历脱敏配置策略
            for (int i = 0; i < maskingConfigArray.length; i++) {
                String tmpMaskData = "";
                int arrDataIndex = maskingConfigArray[i].getMaskingPostion() - 1;
                boolean subsection = maskingConfigArray[i].isSubsection();//是否分段

                String data = arrData[arrDataIndex];
                String algoName = "";
                String algoPara = "";
                try {
                    if (subsection) {
                        String maskData = data;
                        String maskStrategyStr = maskingConfigArray[i].getOriMaskStrategyStr();
                        //TODO 脱敏策略示例： 13|HIDECODE$*,0,0$1-6|RANDOMMAPPING$$15-18;
                        String[] strategyStr = maskStrategyStr.split("\\|");
                        //下标从1开始，下标0是文件实际的列数
                        for (int j = 1; j < strategyStr.length; j++) {
                            String configStr = strategyStr[j];
                            String[] config = configStr.split("\\$");
                            algoName = config[0];
                            algoPara = config[1];
                            String[] indexArr = config[2].split("-");
                            Integer startIndex = Integer.valueOf(indexArr[0]) - 1;
                            Integer endIndex = Integer.valueOf(indexArr[1]);
                            String substring = maskData.substring(startIndex, endIndex);

                            Algorithm algorithm = algorithmMap.get(algoName);
                            String sparefield1 = algorithm.getSparefield1();
                            String middleData = "";
                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                middleData = MaskAlgFactory.getMaskData(substring, algoName, algoPara, algorithmMap);
                            }else {
                                //自定义算法
                                String jarname = algorithm.getJarname();//jar包名称
                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                String methodname = algorithm.getMethodname();//方法名
                                Map<String, String> paramMap = new HashMap<>();
                                paramMap.put("jarname",jarname);
                                paramMap.put("funcnamepath",funcnamepath);
                                paramMap.put("methodname",methodname);
                                paramMap.put("maskData",substring);
                                middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                            }

                            String startData = maskData.substring(0, startIndex);
                            String endData = maskData.substring(endIndex, data.length());
                            maskData = startData + middleData + endData;
                        }
                        tmpMaskData = maskData;
                    } else {
                        algoName = maskingConfigArray[i].getAlgoName(); //脱敏算法
                        algoPara = maskingConfigArray[i].getAlgoParameter(); //脱敏参数

                       /* String[] algoParaArr = new String[5]; //某种脱敏算法参数数组
                        if (algoPara != "") {
                            if (algoPara.contains(",")) {
                                algoParaArr = algoPara.split(",");
                            } else {
                                algoParaArr[0] = algoPara;
                            }
                        }*/
                        Algorithm algorithm = algorithmMap.get(algoName);
                        String sparefield1 = algorithm.getSparefield1();
                        if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                            tmpMaskData = MaskAlgFactory.getMaskData(data, algoName, algoPara, algorithmMap);
                        }else {
                            //自定义算法
                            String jarname = algorithm.getJarname();//jar包名称
                            String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                            String methodname = algorithm.getMethodname();//方法名
                            Map<String, String> paramMap = new HashMap<>();
                            paramMap.put("jarname",jarname);
                            paramMap.put("funcnamepath",funcnamepath);
                            paramMap.put("methodname",methodname);
                            paramMap.put("maskData",data);
                            tmpMaskData = AlgorithmUtils.invokeJarMethod(paramMap);
                        }
                    }
                } catch (Exception e) {
                    if (Const.CUSTOM_EXCEPTION_INFO_ALGORITHM.equals(e.getMessage())) {
                        log.info(e.getMessage() + "：算法出现异常，返回原始数据，请检查该列脱敏规则是否适合用于该列数据");
                    }
                    log.info("数据：" + data + ",脱敏过程中出现错误，使用脱敏算法：" + algoName + ",参数：" + algoPara);
                }

                maskDataSBuilder.append(tmpMaskData).append(split);
                //在指定位置添加字符串，0位字符串前面添加,防止最终结果出现逆序
                //maskDataSBuilder.insert(0,tmpMaskData + split);
            }

            //3.将字符串数组用分隔符连接成字符串
            //String maskingData = MaskingHelper.arrayJoinString(arrData, splitStr, 0, arrData.length-1);
            maskingData = maskDataSBuilder.toString().substring(0, maskDataSBuilder.toString().length() - 1);

            //4.还原成原字符串样式
            if (maskingData.substring(maskingData.length() - 3, maskingData.length()).equalsIgnoreCase("$&$")) {
                maskLineData = StringUtils.replace(maskingData, "$&$", "", 10) + split;
            } else {
                maskLineData = StringUtils.replace(maskingData, "$&$", "", 10);
            }
        } else {
            maskLineData = lineData;
        }
        return maskLineData;
    }

    /**
     * @Description: 得到脱敏行数据
     * <AUTHOR>
     * @date 2020-11-13
     */
    public static String getMaskingLineData(String lineData, String split,
                                            MaskingConfigInfo[] maskingConfigArray,
                                            HashMap<String, Algorithm> algorithmMap,
                                            Map<Integer, MaskStrategyFileFormatSub> maskStrategyFileFormatSubMap,
                                            Map<String, String> abnormalHandlingAlarmMap) {

        String maskLineData = "";
        String maskingData = "";
        StringBuilder maskDataSBuilder = new StringBuilder();
        if (null != maskingConfigArray && maskingConfigArray.length > 0) {
            //1.对原始行数据进行处理
            String newLine = MaskingHelper.replaceWhile(lineData, split);
            String[] arrData = StringUtils.split(newLine, split);

            //2.遍历脱敏配置策略
            for (int i = 0; i < maskingConfigArray.length; i++) {
                String tmpMaskData = "";
                int arrDataIndex = maskingConfigArray[i].getMaskingPostion() - 1;
                boolean subsection = maskingConfigArray[i].isSubsection();//是否分段

                String data = arrData[arrDataIndex];
                String algoName = "";
                String algoPara = "";

                try {
                    if (subsection) {
                        String maskData = data;
                        String maskStrategyStr = maskingConfigArray[i].getOriMaskStrategyStr();
                        //TODO 脱敏策略示例： 13|HIDECODE$*,0,0$1-6|RANDOMMAPPING$$15-18;
                        String[] strategyStr = maskStrategyStr.split("\\|");
                        //下标从1开始，下标0是文件实际的列数
                        for (int j = 1; j < strategyStr.length; j++) {
                            String configStr = strategyStr[j];
                            String[] config = configStr.split("\\$");
                            algoName = config[0];
                            algoPara = config[1];
                            String[] indexArr = config[2].split("-");
                            Integer startIndex = Integer.valueOf(indexArr[0]) - 1;
                            Integer endIndex = Integer.valueOf(indexArr[1]);
                            String substring = maskData.substring(startIndex, endIndex);

                            Algorithm algorithm = algorithmMap.get(algoName);
                            String sparefield1 = algorithm.getSparefield1();
                            String middleData = "";
                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                middleData = MaskAlgFactory.getMaskData(substring, algoName, algoPara, algorithmMap);
                            }else {
                                //自定义算法
                                String jarname = algorithm.getJarname();//jar包名称
                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                String methodname = algorithm.getMethodname();//方法名
                                Map<String, String> paramMap = new HashMap<>();
                                paramMap.put("jarname",jarname);
                                paramMap.put("funcnamepath",funcnamepath);
                                paramMap.put("methodname",methodname);
                                paramMap.put("maskData",substring);
                                middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                            }

                            String startData = maskData.substring(0, startIndex);
                            String endData = maskData.substring(endIndex, data.length());
                            maskData = startData + middleData + endData;
                        }
                        tmpMaskData = maskData;
                    } else {
                        algoName = maskingConfigArray[i].getAlgoName(); //脱敏算法
                        algoPara = maskingConfigArray[i].getAlgoParameter(); //脱敏参数


                        Algorithm algorithm = algorithmMap.get(algoName);
                        String sparefield1 = algorithm.getSparefield1();
                        if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                            tmpMaskData = MaskAlgFactory.getMaskData(data, algoName, algoPara, algorithmMap);
                        }else {
                            //自定义算法
                            String jarname = algorithm.getJarname();//jar包名称
                            String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                            String methodname = algorithm.getMethodname();//方法名
                            Map<String, String> paramMap = new HashMap<>();
                            paramMap.put("jarname",jarname);
                            paramMap.put("funcnamepath",funcnamepath);
                            paramMap.put("methodname",methodname);
                            paramMap.put("maskData",data);
                            tmpMaskData = AlgorithmUtils.invokeJarMethod(paramMap);
                        }
                    }
                } catch (Exception e) {
                    log.error("数据：" + data + ",脱敏过程中出现错误，使用脱敏算法：" + algoName + ",参数：" + algoPara);
                    // TODO 异常数据处置
                    MaskStrategyFileFormatSub maskStrategyFileFormatSub = maskStrategyFileFormatSubMap.get(arrDataIndex);
                    String abnormalDataHandling = maskStrategyFileFormatSub.getSparefield4();
                    String columnDesc = maskStrategyFileFormatSub.getColumndesc();

                    log.warn("异常数据处置算法名为: {}", abnormalDataHandling);
                    tmpMaskData = abnormalDataHandling(columnDesc, data, abnormalDataHandling, abnormalHandlingAlarmMap,algorithmService);
                }
                maskDataSBuilder.append(tmpMaskData).append(split);
            }

            //3.将字符串数组用分隔符连接成字符串
            //String maskingData = MaskingHelper.arrayJoinString(arrData, splitStr, 0, arrData.length-1);
            maskingData = maskDataSBuilder.toString().substring(0, maskDataSBuilder.toString().length() - 1);

            //4.还原成原字符串样式
            if (maskingData.substring(maskingData.length() - 3, maskingData.length()).equalsIgnoreCase("$&$")) {
                maskLineData = StringUtils.replace(maskingData, "$&$", "", 10) + split;
            } else {
                maskLineData = StringUtils.replace(maskingData, "$&$", "", 10);
            }
        } else {
            maskLineData = lineData;
        }
        return maskLineData;
    }

    /**
     * 异常数据处理
     *
     * @param data 数据
     * @return {@link String }
     */
    private static String abnormalDataHandling(String filedName, String data, String abnormalDataHandling,
                                               Map<String, String> abnormalHandlingAlarmMap,AlgorithmService algorithmService) {
        // TODO ---------- 异常数据处理 ----------
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(abnormalDataHandling)) {
                if (!abnormalDataHandling.equalsIgnoreCase("IGNORE")) { //置空,替换
                    com.wzsec.modules.mask.domain.Algorithm algorithm = algorithmService.findByAlgorithmName(abnormalDataHandling);
                    String sparefield1 = algorithm.getSparefield1();
                    if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                        data = MaskAlgFactory.getMaskData(data, abnormalDataHandling, Const.AES_SECRET_KEY, null);
                    }else {
                        //自定义算法
                        String jarname = algorithm.getJarname();//jar包名称
                        String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                        String methodname = algorithm.getMethodname();//方法名
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("jarname",jarname);
                        paramMap.put("funcnamepath",funcnamepath);
                        paramMap.put("methodname",methodname);
                        paramMap.put("maskData",data);
                        data = AlgorithmUtils.invokeJarMethod(paramMap);
                    }

                    String cNameAlgorithm = abnormalDataHandling.equals(Const.ABNORMAL_DATA_HANDLING_EMPTY) ?
                            Const.ABNORMAL_DATA_HANDLING_EMPTY_CNAME : Const.ABNORMAL_DATA_HANDLING_SUBSTITUTION_CNAME;
                    abnormalHandlingAlarmMap.put(filedName, cNameAlgorithm);
                } else { // 忽略
                    abnormalHandlingAlarmMap.put(filedName, Const.ABNORMAL_DATA_HANDLING_IGNORE_CNAME);
                }
            }
        } catch (Exception ex) {
            int length = data.length();
            StringBuilder masked = new StringBuilder();
            for (int i = 0; i < length; i++) {
                masked.append('*');
            }
            data = masked.toString();
        }
        return data;
        // TODO ---------- 异常数据处理 ----------
    }


    /**
     * @Description: 将脱敏配置信息特定字符串转换为脱敏配置信息数组，如下列格式:
     * 1|MD5$9876543210123456;2|DATE_EARLY_INT$1,2;3|IDNUMBER
     * 1 MD5 9876543210123456
     * 2 DATE_EARLY_INT 1,2
     * 3 IDNUMBER
     * <AUTHOR>
     * @date 2020-11-13
     */
    public static MaskingConfigInfo[] getMaskingConfigInfoArray(String maskingConfigInfoStr) throws Exception {
        MaskingConfigInfo[] maskConfigInfoArr = null;
        if (null != maskingConfigInfoStr && maskingConfigInfoStr.length() > 0) {
            String[] algoNameParaArr = null;
            if (maskingConfigInfoStr.contains(";")) {  //1|MD5$9876543210123456;2|DATE_EARLY_INT$1,2;3|IDNUMBER
                String[] configItemArr = maskingConfigInfoStr.split(";");
                maskConfigInfoArr = new MaskingConfigInfo[configItemArr.length];
                for (int i = 0; i < configItemArr.length; i++) {
                    String[] postionAlgoParaArr = configItemArr[i].split("\\|");
                    int postion = Integer.parseInt(postionAlgoParaArr[0]); //脱敏位置
                    String algoName = "";  //算法名称
                    String algoPara = "";  //算法参数
                    boolean isSubsection = false;//是否分段，如果是分段的规则，后续逻辑单独处理
                    if (postionAlgoParaArr.length > 1) {
                        String algoNamePara = postionAlgoParaArr[1];
                        if (algoNamePara.contains("$")) {  //MD5$9876543210123456
                            algoNameParaArr = algoNamePara.split("\\$");
                            if (algoNameParaArr.length > 2) {
                                isSubsection = true;
                            } else if (algoNameParaArr.length > 1) {
                                algoName = algoNameParaArr[0];
                                algoPara = algoNameParaArr[1];
                            }
                        } else {                                 //IDNUMBER
                            algoName = algoNamePara;
                            algoPara = "";
                        }
                    }
                    MaskingConfigInfo nMaskConfigInfo = new MaskingConfigInfo(postion, algoName, algoPara, isSubsection, configItemArr[i]);
                    maskConfigInfoArr[i] = nMaskConfigInfo;
                }
            } else {
                //1|MD5$9876543210123456     3|IDNUMBER
                maskConfigInfoArr = new MaskingConfigInfo[1];
                String[] postionAlgoParaArr = maskingConfigInfoStr.split("\\|");
                int postion = Integer.parseInt(postionAlgoParaArr[0]);
                String algoName = "";  //算法名称
                String algoPara = "";  //算法参数
                boolean isSubsection = false;//是否分段，如果是分段的规则，后续逻辑单独处理
                if (postionAlgoParaArr.length > 1) {
                    String algoNamePara = postionAlgoParaArr[1];
                    if (algoNamePara.contains("$")) {
                        algoNameParaArr = algoNamePara.split("\\$");
                        if (algoNameParaArr.length > 2) {
                            isSubsection = true;
                        } else if (algoNameParaArr.length > 1) {
                            algoName = algoNameParaArr[0];
                            algoPara = algoNameParaArr[1];
                        }
                    } else {
                        algoName = algoNamePara;
                        algoPara = "";
                    }
                }
                MaskingConfigInfo sMaskConfigInfo = new MaskingConfigInfo(postion, algoName, algoPara, isSubsection, maskingConfigInfoStr);
                maskConfigInfoArr[0] = sMaskConfigInfo;
            }
        }

        return maskConfigInfoArr;
    }

    /**
     * @Description: 脱敏数据合并
     * <AUTHOR>
     * @date 2020-11-13
     */
    public static void getMaskDataLists(List<Map<String, String>> maskDataLists, List<String> maskDataList,
                                        String fieldnames, String splitstr) throws Exception {
        for (String maskdata : maskDataList) {
            String[] maskdataarr = maskdata.split(splitstr);
            String[] fieldnamearr = fieldnames.split(",");
            if (fieldnamearr.length == maskdataarr.length) {
                Map<String, String> maskDataMap = new HashMap<String, String>();
                for (int i = 0; i < fieldnamearr.length; i++) {
                    maskDataMap.put(fieldnamearr[i], maskdataarr[i]);
                }
                maskDataLists.add(maskDataMap);
            }
        }
    }

    // 获取脱敏前后数据保存的行数
    public static String listToStr(List<String> list) {
        String res = "";
        long m = 0;
        if (list.size() > 5) {
            m = 5;
        } else {
            m = list.size();
        }
        for (int i = 0; i < m; i++) {
            res += list.get(i) + "\n";
        }
        res = res.substring(0, res.length() - 1);
        return res;
    }

    /**
     * @Description: 创建新表并批量插入数据到新表
     * <AUTHOR>
     * @date 2019年11月14日
     */
    public static boolean insertData2NewTable(List<Map<String, String>> maskDataLists, String dbname, String tablename,
                                              String srcurl, String username, String password, String type, String jdbc_driver, String fieldnames) {
        DatabaseUtil databaseUtil = null;
        String createTableSql = "";
        if (Const.DB_MYSQL.equals(type) || Const.DB_UNIDB.equals(type)) {
            databaseUtil = new MysqlUtil();
            // 1.生成创建表语句
            createTableSql = WirteDbnameService.getMysqlCreateTableSql(tablename, fieldnames);
        } else if (Const.DB_ORACLE.equals(type)) {
            databaseUtil = new OracleUtil();
            createTableSql = WirteDbnameService.getOraclereateTableSql(dbname, tablename, fieldnames);
        } else if (Const.DB_DM.equals(type)) {
            databaseUtil = new DMUtil();
            createTableSql = WirteDbnameService.getDMCreateTableSql(dbname, tablename, fieldnames);
        } else if (Const.DB_INFORMIX.equals(type)) {
            databaseUtil = new InformixUtil();
            createTableSql = WirteDbnameService.getInformixCreateTableSql(dbname, tablename, fieldnames);
        } else {
            log.info("暂不支持该数据源:" + srcurl);
            return false;
        }
        if (createTableSql == null) {
            log.info("1.生成建表SQL语句失败");
            return false;
        }
        log.info("1.生成建表SQL语句成功");

        // 数据库密码解密
        if (StringUtils.isNotEmpty(password)) {
            try {
                String decrypt = AES.decrypt(password, Const.AES_SECRET_KEY);
                password = decrypt;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 2.通过建表SQL语句、数据库连接信息创建表
        Boolean createMysqlTableResult = WirteDbnameService.createTable(createTableSql, jdbc_driver, srcurl, username,
                password);
        if (!createMysqlTableResult) {
            log.info("2.通过建表SQL语句创建表失败");
            return false;
        }
        log.info("2.通过建表SQL语句创建表成功");

        Map<String, Object> sqlAndParams = null;
        int end = 1000;
        for (int i = 0; i < maskDataLists.size(); i += 1000) {
            if (maskDataLists.size() < end) {
                end = maskDataLists.size();
            }
            // 3.生成批量插入语句,批量插入参数
            sqlAndParams = databaseUtil.getInsert2TableSqlAndPatams(i, end, maskDataLists, dbname, tablename, fieldnames);
            if (sqlAndParams == null) {
                log.info("3.生成批量插入语句,批量插入参数失败");
                return false;
            }
            String insert2MysqlTableSql = sqlAndParams.get("sql").toString();
            Object[] insert2MysqlTableParams = (Object[]) sqlAndParams.get("params");
            // 5.通过批量插入语句、数据库连接信息批量插入数据
            Boolean insert2MysqlTableResult = DatabaseUtil.executeUpdate(insert2MysqlTableSql, insert2MysqlTableParams,
                    jdbc_driver, srcurl, username, password);
            if (!insert2MysqlTableResult) {
                log.info("4.通过批量插入SQL语句批量插入数据失败");
                return false;
            }
            end += 1000;
        }
        log.info("3.生成批量插入语句,批量插入参数成功");
        log.info("4.通过批量插入SQL语句批量插入数据成功");
        return true;

    }

    /**
     * 获取非格式化文件的脱敏数据
     *
     * @param lineDataList
     * @param ruleList
     * @param splitstr
     * @return
     * <AUTHOR>
     * @date 2021-06-21
     */
    public static List<String> getMaskDataListForUnformatFile(List<String> lineDataList,
                                                              List<Map<String, Object>> ruleList,
                                                              String splitstr,
                                                              HashMap<String, Algorithm> algorithmMap,
                                                              List<Integer> maskFieldIndex) throws Exception {
        try {
            List<String> maskDataList = new ArrayList<String>();
            for (String lineData : lineDataList) {
                StringBuilder maskDataSBuilder = new StringBuilder();
                String newLine = MaskingHelper.replaceWhile(lineData, splitstr);
                String[] arrData = StringUtils.split(newLine, splitstr);
                String maskingData = "";
                String maskLineData = "";
                if (arrData.length > 0) {
                    for (int i = 0; i < arrData.length; i++) {
                        String data = arrData[i];
                        boolean isStr = false;
                        if (maskFieldIndex != null && StringUtils.isNotEmpty(data) && data.length() > 2) {
                            //最前面一位与最后一位如果是单引号，需要去掉，还原数据
                            String startStr = data.substring(0, 1);
                            String endStr = data.substring(data.length() - 1, data.length());
                            if ("'".equals(startStr) && "'".equals(endStr)) {
                                data = data.substring(1, data.length() - 1);
                                isStr = true;
                            }
                        }
                        String tmpMaskData = "";
                        boolean flag = false;
                        if (ruleList != null) {

                            for (Map<String, Object> ruleMap : ruleList) {

                                RuleDto ruleDto = (RuleDto) ruleMap.get("senRule");
                                //姓名、地址、手机号白名单过滤
                                if (Const.DATA_TYPE_NAME.equals(ruleDto.getApplytypecname()) && Const.nameWhiteList != null && Const.nameWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_ADDRESS.equals(ruleDto.getApplytypecname()) || Const.DATA_TYPE_DETAIL_ADDRESS.equals(ruleDto.getApplytypecname()) && Const.addreWhiteList != null && Const.addreWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_PHONE_NUMBER.equals(ruleDto.getApplytypecname()) && Const.phoneWhiteList != null && Const.phoneWhiteList.contains(data)) {
                                    continue;
                                }
                                boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
                                if (checkSensResult) {
                                    String maskData = data;
                                    String algoName = "";
                                    String algoPara = "";
                                    try {
                                        Object maskStrategyStr = ruleMap.get("maskstrategystr");
                                        if (maskStrategyStr != null) {
                                            //TODO 脱敏策略示例： HIDECODE$*,0,0$1-6|RANDOMMAPPING$$15-18HIDECODE$*,0,0$1-6RANDOMMAPPING$$15-18
                                            String[] strategyStr = maskStrategyStr.toString().split("\\|");
                                            //下标从1开始，下标0数据为空
                                            for (int j = 1; j < strategyStr.length; j++) {
                                                String configStr = strategyStr[j];
                                                String[] config = configStr.split("\\$");
                                                algoName = config[0];
                                                algoPara = config[1];
                                                String[] indexArr = config[2].split("-");
                                                Integer startIndex = Integer.valueOf(indexArr[0]) - 1;
                                                Integer endIndex = Integer.valueOf(indexArr[1]);
                                                String substring = maskData.substring(startIndex, endIndex);

                                                Algorithm algorithm = algorithmMap.get(algoName);
                                                String sparefield1 = algorithm.getSparefield1();
                                                String middleData = "";
                                                if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                                    middleData = MaskAlgFactory.getMaskData(substring, algoName, algoPara, algorithmMap);
                                                }else {
                                                    //自定义算法
                                                    String jarname = algorithm.getJarname();//jar包名称
                                                    String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                                    String methodname = algorithm.getMethodname();//方法名
                                                    Map<String, String> paramMap = new HashMap<>();
                                                    paramMap.put("jarname",jarname);
                                                    paramMap.put("funcnamepath",funcnamepath);
                                                    paramMap.put("methodname",methodname);
                                                    paramMap.put("maskData",substring);
                                                    middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                                                }

                                                String startData = maskData.substring(0, startIndex);
                                                String endData = maskData.substring(endIndex, data.length());
                                                maskData = startData + middleData + endData;
                                            }
                                            tmpMaskData = maskData;
                                        } else {
                                            // 该数据为敏感数据
                                            // 脱敏算法
                                            AlgorithmDto algorithmDto = (AlgorithmDto) ruleMap.get("algorithm");
                                            algoName = algorithmDto.getAlgenglishname();
                                            StringBuilder parStringBuilder = new StringBuilder();
                                            String param = ruleMap.get("param") == null ? "" : ruleMap.get("param").toString();
                                            String secretkey = ruleMap.get("secretkey") == null ? "" : ruleMap.get("secretkey").toString();
                                            if (StringUtils.isNotEmpty(param)) {
                                                parStringBuilder.append(param);
                                                if (StringUtils.isNotEmpty(secretkey)) {
                                                    parStringBuilder.append(",");
                                                    parStringBuilder.append(secretkey);
                                                }
                                            } else {
                                                if (StringUtils.isNotEmpty(secretkey)) {
                                                    parStringBuilder.append(secretkey);
                                                }
                                            }
                                            algoPara = parStringBuilder.toString();

                                            // 脱敏后的数据
                                            Algorithm algorithm = algorithmMap.get(algoName);
                                            String sparefield1 = algorithm.getSparefield1();
                                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                                tmpMaskData = MaskAlgFactory.getMaskData(data, algoName, algoPara, algorithmMap);
                                            }else {
                                                //自定义算法
                                                String jarname = algorithm.getJarname();//jar包名称
                                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                                String methodname = algorithm.getMethodname();//方法名
                                                Map<String, String> paramMap = new HashMap<>();
                                                paramMap.put("jarname",jarname);
                                                paramMap.put("funcnamepath",funcnamepath);
                                                paramMap.put("methodname",methodname);
                                                paramMap.put("maskData",data);
                                                tmpMaskData = AlgorithmUtils.invokeJarMethod(paramMap);
                                            }

                                        }
                                    } catch (Exception e) {
                                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algoName + "参数:" + algoPara.toString());


                                        tmpMaskData = data;
                                    }
                                    flag = true;
                                    break;
                                }
                            }
                        }
                        if (!flag) {
                            // 非敏感的数据，原文输出
                            tmpMaskData = data;
                        } else if (maskFieldIndex != null && !maskFieldIndex.contains(i)) {
                            //记录脱敏字段的下标
                            maskFieldIndex.add(i);
                        }
                        //字符串数据需要拼接单引号恢复成原样
                        if (isStr) {
                            tmpMaskData = "'" + tmpMaskData + "'";
                        }
                        // 拼接每个单独的数据
                        maskDataSBuilder.append(tmpMaskData + splitstr);
                    }
                }
                maskingData = maskDataSBuilder.toString().substring(0, maskDataSBuilder.toString().length() - 1);
                //4.还原成原字符串样式
                if (maskingData.substring(maskingData.length() - 3, maskingData.length()).equalsIgnoreCase("$&$")) {
                    maskLineData = StringUtils.replace(maskingData, "$&$", "", 10) + splitstr;
                } else {
                    maskLineData = StringUtils.replace(maskingData, "$&$", "", 10);
                }
                maskDataList.add(maskLineData);
            }
            return maskDataList;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取非格式化文件的脱敏数据
     *
     * @param lineDataList
     * @param ruleList
     * @param splitstr
     * @return
     * <AUTHOR>
     * @date 2021-06-21
     */
    public static List<String> getMaskDataListForUnformatFile(List<String> lineDataList,
                                                              List<Map<String, Object>> ruleList,
                                                              String splitstr,
                                                              HashMap<String, Algorithm> algorithmMap,
                                                              List<Integer> maskFieldIndex,
                                                              Map<Integer, MaskStrategyFileUnformatSub> nonFormattedDisposalFilesMap,
                                                              Map<String, String> abnormalHandlingAlarmMap) throws Exception {
        try {
            List<String> maskDataList = new ArrayList<String>();
            for (String lineData : lineDataList) {
                StringBuilder maskDataSBuilder = new StringBuilder();
                String newLine = MaskingHelper.replaceWhile(lineData, splitstr);
                String[] arrData = StringUtils.split(newLine, splitstr);
                String maskingData = "";
                String maskLineData = "";
                if (arrData.length > 0) {
                    for (int i = 0; i < arrData.length; i++) {
                        String data = arrData[i];
                        boolean isStr = false;
                        if (maskFieldIndex != null && StringUtils.isNotEmpty(data) && data.length() > 2) {
                            //最前面一位与最后一位如果是单引号，需要去掉，还原数据
                            String startStr = data.substring(0, 1);
                            String endStr = data.substring(data.length() - 1, data.length());
                            if ("'".equals(startStr) && "'".equals(endStr)) {
                                data = data.substring(1, data.length() - 1);
                                isStr = true;
                            }
                        }
                        String tmpMaskData = "";
                        boolean flag = false;
                        if (ruleList != null) {

                            for (Map<String, Object> ruleMap : ruleList) {

                                RuleDto ruleDto = (RuleDto) ruleMap.get("senRule");
                                //姓名、地址、手机号白名单过滤
                                if (Const.DATA_TYPE_NAME.equals(ruleDto.getApplytypecname()) && Const.nameWhiteList != null && Const.nameWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_ADDRESS.equals(ruleDto.getApplytypecname()) || Const.DATA_TYPE_DETAIL_ADDRESS.equals(ruleDto.getApplytypecname()) && Const.addreWhiteList != null && Const.addreWhiteList.contains(data)) {
                                    continue;
                                }
                                if (Const.DATA_TYPE_PHONE_NUMBER.equals(ruleDto.getApplytypecname()) && Const.phoneWhiteList != null && Const.phoneWhiteList.contains(data)) {
                                    continue;
                                }
                                boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);

                                // TODO 命中识别规则
                                if (checkSensResult) {
                                    String maskData = data;
                                    String algoName = "";
                                    String algoPara = "";
                                    try {
                                        Object maskStrategyStr = ruleMap.get("maskstrategystr");
                                        if (maskStrategyStr != null) {
                                            //TODO 脱敏策略示例： HIDECODE$*,0,0$1-6|RANDOMMAPPING$$15-18HIDECODE$*,0,0$1-6RANDOMMAPPING$$15-18
                                            String[] strategyStr = maskStrategyStr.toString().split("\\|");
                                            //下标从1开始，下标0数据为空
                                            for (int j = 1; j < strategyStr.length; j++) {
                                                String configStr = strategyStr[j];
                                                String[] config = configStr.split("\\$");
                                                algoName = config[0];
                                                algoPara = config[1];
                                                String[] indexArr = config[2].split("-");
                                                Integer startIndex = Integer.valueOf(indexArr[0]) - 1;
                                                Integer endIndex = Integer.valueOf(indexArr[1]);
                                                String substring = maskData.substring(startIndex, endIndex);

                                                Algorithm algorithm = algorithmMap.get(algoName);
                                                String sparefield1 = algorithm.getSparefield1();
                                                String middleData = "";
                                                if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                                    middleData = MaskAlgFactory.getMaskData(substring, algoName, algoPara, algorithmMap);
                                                }else {
                                                    //自定义算法
                                                    String jarname = algorithm.getJarname();//jar包名称
                                                    String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                                    String methodname = algorithm.getMethodname();//方法名
                                                    Map<String, String> paramMap = new HashMap<>();
                                                    paramMap.put("jarname",jarname);
                                                    paramMap.put("funcnamepath",funcnamepath);
                                                    paramMap.put("methodname",methodname);
                                                    paramMap.put("maskData",substring);
                                                    middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                                                }

                                                String startData = maskData.substring(0, startIndex);
                                                String endData = maskData.substring(endIndex, data.length());
                                                maskData = startData + middleData + endData;
                                            }
                                            tmpMaskData = maskData;
                                        } else {
                                            // 该数据为敏感数据
                                            // 脱敏算法
                                            AlgorithmDto algorithmDto = (AlgorithmDto) ruleMap.get("algorithm");
                                            algoName = algorithmDto.getAlgenglishname();
                                            StringBuilder parStringBuilder = new StringBuilder();
                                            String param = ruleMap.get("param") == null ? "" : ruleMap.get("param").toString();
                                            String secretkey = ruleMap.get("secretkey") == null ? "" : ruleMap.get("secretkey").toString();
                                            if (StringUtils.isNotEmpty(param)) {
                                                parStringBuilder.append(param);
                                                if (StringUtils.isNotEmpty(secretkey)) {
                                                    parStringBuilder.append(",");
                                                    parStringBuilder.append(secretkey);
                                                }
                                            } else {
                                                if (StringUtils.isNotEmpty(secretkey)) {
                                                    parStringBuilder.append(secretkey);
                                                }
                                            }
                                            algoPara = parStringBuilder.toString();

                                            // 脱敏后的数据
                                            Algorithm algorithm = algorithmMap.get(algoName);
                                            String sparefield1 = algorithm.getSparefield1();
                                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                                tmpMaskData = MaskAlgFactory.getMaskData(data, algoName, algoPara, algorithmMap);
                                            }else {
                                                //自定义算法
                                                String jarname = algorithm.getJarname();//jar包名称
                                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                                String methodname = algorithm.getMethodname();//方法名
                                                Map<String, String> paramMap = new HashMap<>();
                                                paramMap.put("jarname",jarname);
                                                paramMap.put("funcnamepath",funcnamepath);
                                                paramMap.put("methodname",methodname);
                                                paramMap.put("maskData",data);
                                                tmpMaskData = AlgorithmUtils.invokeJarMethod(paramMap);
                                            }

                                        }
                                    } catch (Exception e) {
                                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algoName + "参数:" + algoPara.toString());

                                        // TODO 异常数据处置
                                        Long ruleDtoId = ruleDto.getId();
                                        MaskStrategyFileUnformatSub maskStrategyFileUnformatSub = nonFormattedDisposalFilesMap.get(ruleDtoId);
                                        String abnormalDataHandling = maskStrategyFileUnformatSub.getSparefield4();
                                        log.warn("异常数据处置算法名为: {}", abnormalDataHandling);
                                        tmpMaskData = abnormalDataHandling(String.valueOf(i + 1), data, abnormalDataHandling, abnormalHandlingAlarmMap,algorithmService);
                                    }
                                    flag = true;
                                    break;
                                }
                            }
                        }
                        if (!flag) {
                            // 非敏感的数据，原文输出
                            tmpMaskData = data;
                        } else if (maskFieldIndex != null && !maskFieldIndex.contains(i)) {
                            //记录脱敏字段的下标
                            maskFieldIndex.add(i);
                        }
                        //字符串数据需要拼接单引号恢复成原样
                        if (isStr) {
                            tmpMaskData = "'" + tmpMaskData + "'";
                        }
                        // 拼接每个单独的数据
                        maskDataSBuilder.append(tmpMaskData + splitstr);
                    }
                }
                maskingData = maskDataSBuilder.toString().substring(0, maskDataSBuilder.toString().length() - 1);
                //4.还原成原字符串样式
                if (maskingData.substring(maskingData.length() - 3, maskingData.length()).equalsIgnoreCase("$&$")) {
                    maskLineData = StringUtils.replace(maskingData, "$&$", "", 10) + splitstr;
                } else {
                    maskLineData = StringUtils.replace(maskingData, "$&$", "", 10);
                }
                maskDataList.add(maskLineData);
            }
            return maskDataList;
        } catch (Exception e) {
            throw e;
        }
    }


    public static String getMaskData(String data, List<Map<String, Object>> ruleList, HashMap<String, Algorithm> algorithmMap) {
        String maskData = data;
        if (ruleList != null) {
            for (Map<String, Object> ruleMap : ruleList) {
                RuleDto ruleDto = (RuleDto) ruleMap.get("senRule");
                //姓名、地址、手机号白名单过滤
                if (Const.DATA_TYPE_NAME.equals(ruleDto.getApplytypecname()) && Const.nameWhiteList != null && Const.nameWhiteList.contains(data)) {
                    continue;
                }
                if (Const.DATA_TYPE_ADDRESS.equals(ruleDto.getApplytypecname()) || Const.DATA_TYPE_DETAIL_ADDRESS.equals(ruleDto.getApplytypecname()) && Const.addreWhiteList != null && Const.addreWhiteList.contains(data)) {
                    continue;
                }
                if (Const.DATA_TYPE_PHONE_NUMBER.equals(ruleDto.getApplytypecname()) && Const.phoneWhiteList != null && Const.phoneWhiteList.contains(data)) {
                    continue;
                }
                boolean checkSensResult = false;
                try {
                    checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto,null);
                } catch (Exception e) {
                    log.error("检测敏感数据出现异常,数据:" + data + "规则名称：:" + ruleDto.getCname());
                }
                if (checkSensResult) {
                    String algoName = "";
                    String algoPara = "";
                    try {
                        Object maskStrategyStr = ruleMap.get("maskstrategystr");
                        if (maskStrategyStr != null) {
                            String temData = maskData;
                            //TODO 脱敏策略示例： HIDECODE$*,0,0$1-6|RANDOMMAPPING$$15-18HIDECODE$*,0,0$1-6RANDOMMAPPING$$15-18
                            String[] strategyStr = maskStrategyStr.toString().split("\\|");
                            //下标从1开始，下标0数据为空
                            for (int j = 1; j < strategyStr.length; j++) {
                                String configStr = strategyStr[j];
                                String[] config = configStr.split("\\$");
                                algoName = config[0];
                                algoPara = config[1];
                                String[] indexArr = config[2].split("-");
                                Integer startIndex = Integer.valueOf(indexArr[0]) - 1;
                                Integer endIndex = Integer.valueOf(indexArr[1]);
                                String substring = temData.substring(startIndex, endIndex);

                                Algorithm algorithm = algorithmMap.get(algoName);
                                String sparefield1 = algorithm.getSparefield1();
                                String middleData = "";
                                if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                    middleData = MaskAlgFactory.getMaskData(substring, algoName, algoPara, algorithmMap);
                                }else {
                                    //自定义算法
                                    String jarname = algorithm.getJarname();//jar包名称
                                    String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                    String methodname = algorithm.getMethodname();//方法名
                                    Map<String, String> paramMap = new HashMap<>();
                                    paramMap.put("jarname",jarname);
                                    paramMap.put("funcnamepath",funcnamepath);
                                    paramMap.put("methodname",methodname);
                                    paramMap.put("maskData",substring);
                                    middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                                }

                                String startData = temData.substring(0, startIndex);
                                String endData = temData.substring(endIndex, data.length());
                                temData = startData + middleData + endData;
                            }
                            maskData = temData;
                        } else {
                            // 该数据为敏感数据
                            // 脱敏算法
                            AlgorithmDto algorithmDto = (AlgorithmDto) ruleMap.get("algorithm");
                            algoName = algorithmDto.getAlgenglishname();
                            StringBuilder parStringBuilder = new StringBuilder();
                            String param = ruleMap.get("param") == null ? "" : ruleMap.get("param").toString();
                            String secretkey = ruleMap.get("secretkey") == null ? "" : ruleMap.get("secretkey").toString();
                            if (StringUtils.isNotEmpty(param)) {
                                parStringBuilder.append(param);
                                if (StringUtils.isNotEmpty(secretkey)) {
                                    parStringBuilder.append(",");
                                    parStringBuilder.append(secretkey);
                                }
                            } else {
                                if (StringUtils.isNotEmpty(secretkey)) {
                                    parStringBuilder.append(secretkey);
                                }
                            }
                            algoPara = parStringBuilder.toString();

                            // 脱敏后的数据
                            Algorithm algorithm = algorithmMap.get(algoName);
                            String sparefield1 = algorithm.getSparefield1();
                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                maskData = MaskAlgFactory.getMaskData(data, algoName, algoPara, algorithmMap);
                            }else {
                                //自定义算法
                                String jarname = algorithm.getJarname();//jar包名称
                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                String methodname = algorithm.getMethodname();//方法名
                                Map<String, String> paramMap = new HashMap<>();
                                paramMap.put("jarname",jarname);
                                paramMap.put("funcnamepath",funcnamepath);
                                paramMap.put("methodname",methodname);
                                paramMap.put("maskData",data);
                                maskData = AlgorithmUtils.invokeJarMethod(paramMap);
                            }


                        }
                    } catch (Exception e) {
                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algoName + "参数:" + algoPara.toString());
                        maskData = data;
                    }
                    break;
                }
            }
        }
        return maskData;

    }
}
