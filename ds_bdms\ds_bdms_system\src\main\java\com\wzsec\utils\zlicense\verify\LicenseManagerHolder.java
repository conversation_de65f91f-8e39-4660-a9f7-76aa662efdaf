package com.wzsec.utils.zlicense.verify;
import com.wzsec.utils.zlicense.de.schlichtherle.license.LicenseManager;
import com.wzsec.utils.zlicense.de.schlichtherle.license.LicenseParam;

/**
 * LicenseManager
 * <AUTHOR>
 */
public class LicenseManagerHolder {
	
	private static LicenseManager licenseManager;
 
	public static synchronized LicenseManager getLicenseManager(LicenseParam licenseParams) {
    	if (licenseManager == null) {
    		licenseManager = new LicenseManager(licenseParams);
    	}
    	return licenseManager;
    }
}