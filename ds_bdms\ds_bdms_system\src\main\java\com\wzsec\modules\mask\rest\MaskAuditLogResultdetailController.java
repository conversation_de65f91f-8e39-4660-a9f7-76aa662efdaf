package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskAuditLogResultdetail;
import com.wzsec.modules.mask.service.MaskAuditLogResultdetailService;
import com.wzsec.modules.mask.service.dto.MaskAuditLogResultdetailQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-01-19
*/
// @Api(tags = "脱敏审计接口日志结果详情管理")
@RestController
@RequestMapping("/api/maskAuditLogResultdetail")
public class MaskAuditLogResultdetailController {

    private final MaskAuditLogResultdetailService MaskAuditLogResultdetailService;

    public MaskAuditLogResultdetailController(MaskAuditLogResultdetailService MaskAuditLogResultdetailService) {
        this.MaskAuditLogResultdetailService = MaskAuditLogResultdetailService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskAuditLogResultdetail:list')")
    public void download(HttpServletResponse response, MaskAuditLogResultdetailQueryCriteria criteria) throws IOException {
        MaskAuditLogResultdetailService.download(MaskAuditLogResultdetailService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询脱敏审计接口日志结果详情")
    // @ApiOperation("查询脱敏审计接口日志结果详情")
    @PreAuthorize("@el.check('maskAuditLogResultdetail:list')")
    public ResponseEntity<Object> getMaskAuditLogResultdetails(MaskAuditLogResultdetailQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(MaskAuditLogResultdetailService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增脱敏审计接口日志结果详情")
    // @ApiOperation("新增脱敏审计接口日志结果详情")
    @PreAuthorize("@el.check('maskAuditLogResultdetail:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskAuditLogResultdetail resources){
        return new ResponseEntity<>(MaskAuditLogResultdetailService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改脱敏审计接口日志结果详情")
    // @ApiOperation("修改脱敏审计接口日志结果详情")
    @PreAuthorize("@el.check('maskAuditLogResultdetail:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskAuditLogResultdetail resources){
        MaskAuditLogResultdetailService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除脱敏审计接口日志结果详情")
    // @ApiOperation("删除脱敏审计接口日志结果详情")
    @PreAuthorize("@el.check('maskAuditLogResultdetail:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        MaskAuditLogResultdetailService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
