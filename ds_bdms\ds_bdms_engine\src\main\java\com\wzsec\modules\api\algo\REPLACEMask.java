package com.wzsec.modules.api.algo;

import org.apache.commons.lang.StringUtils;

/**
 * 改写结果脱敏算法_替换
 *
 */
public class REPLACEMask {
    /**
     * 改写结果脱敏算法_随机替换
     *
     * @param replaceMask 需要替换的字段
     * @param hideCode    掩码
     */
    public static String encrypt(String strData, String replaceMask, String hideCode) {
        if (strData != null && !strData.equals("")) {
            String strMaskResult = strData;

            String[] strMaskResultArr = strMaskResult.split("");
            for (int i = 0; i < strMaskResultArr.length; i++) {
                if (replaceMask.contains(strMaskResultArr[i])) {
                    strMaskResultArr[i] = hideCode;
                }
            }
            return StringUtils.join(strMaskResultArr, "");
        }
        return null;
    }

    public static void main(String[] args) {
        String srcData = "湖北省武汉市经济开发区";
        System.out.println(encrypt(srcData, "湖北省","*"));
        System.out.println(encrypt(srcData, "武汉市","&"));
        System.out.println(encrypt(srcData, "经济区","&"));
    }

}
