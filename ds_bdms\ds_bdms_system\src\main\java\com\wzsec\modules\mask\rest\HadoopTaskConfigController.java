package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.HadoopTaskConfig;
import com.wzsec.modules.mask.service.HadoopTaskConfigService;
import com.wzsec.modules.mask.service.dto.HadoopTaskConfigQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2020-11-17
*/
// @Api(tags = "HDFS文件脱敏任务配置管理")
@RestController
@RequestMapping("/api/hadoopTaskConfig")
public class HadoopTaskConfigController {

    private final HadoopTaskConfigService hadooptaskconfigService;

    public HadoopTaskConfigController(HadoopTaskConfigService hadooptaskconfigService) {
        this.hadooptaskconfigService = hadooptaskconfigService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('hadoopTaskConfig:list')")
    public void download(HttpServletResponse response, HadoopTaskConfigQueryCriteria criteria) throws IOException {
        hadooptaskconfigService.download(hadooptaskconfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询HDFS文件脱敏任务配置")
    // @ApiOperation("查询HDFS文件脱敏任务配置")
    @PreAuthorize("@el.check('hadoopTaskConfig:list')")
    public ResponseEntity<Object> getHadooptaskconfigs(HadoopTaskConfigQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(hadooptaskconfigService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增HDFS文件脱敏任务配置")
    // @ApiOperation("新增HDFS文件脱敏任务配置")
    @PreAuthorize("@el.check('hadoopTaskConfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody HadoopTaskConfig resources){
        return new ResponseEntity<>(hadooptaskconfigService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改HDFS文件脱敏任务配置")
    // @ApiOperation("修改HDFS文件脱敏任务配置")
    @PreAuthorize("@el.check('hadoopTaskConfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody HadoopTaskConfig resources){
        hadooptaskconfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除HDFS文件脱敏任务配置")
    // @ApiOperation("删除HDFS文件脱敏任务配置")
    @PreAuthorize("@el.check('hadoopTaskConfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        hadooptaskconfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取数据库脱敏任务新增任务号")
    // @ApiOperation("获取数据库脱敏任务新增任务号")
    @PreAuthorize("@el.check('hadoopTaskConfig:add')")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(hadooptaskconfigService.getMAXTaskName(), HttpStatus.CREATED);
    }

    @Log("根据数据源类型获取库名")
    // @ApiOperation("根据数据源类型获取库名")
    @GetMapping(value = "/getDbnameByDbSourceType/{datasourcetype}")
    @PreAuthorize("@el.check('hadoopTaskConfig:list')")
    public ResponseEntity<Object> getTabnameBySource(@PathVariable String datasourcetype) {
        return new ResponseEntity<>(hadooptaskconfigService.getDbnameByDbSourceType(datasourcetype), HttpStatus.OK);
    }

    @Log("根据库名获取表名")
    // @ApiOperation("根据库名获取表名")
    @GetMapping(value = "/getTabnameByDbname/{datasourcetype}/{tabdatabase}")
    @PreAuthorize("@el.check('hadoopTaskConfig:list')")
    public ResponseEntity<Object> getTabnameByDbname(@PathVariable String datasourcetype,@PathVariable String tabdatabase) {
        return new ResponseEntity<>(hadooptaskconfigService.getTabnameByDbname(datasourcetype,tabdatabase), HttpStatus.OK);
    }

    @Log("根据库名和表名获取策略")
    // @ApiOperation("根据库名和表名获取策略")
    @GetMapping(value = "/getStrategyByDbnameAndTabname/{datasourcetype}/{tabdatabase}/{tabname}")
    @PreAuthorize("@el.check('hadoopTaskConfig:list')")
    public ResponseEntity<Object> getStrategyByDbnameAndTabname(@PathVariable String datasourcetype,@PathVariable String tabdatabase,@PathVariable String tabname) {
        return new ResponseEntity<>(hadooptaskconfigService.getStrategyByDbnameAndTabname(datasourcetype,tabdatabase,tabname), HttpStatus.OK);
    }

    @Log("执行HDFS静态脱敏任务")
    // @ApiOperation("在引擎执行HDFS脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    // @PreAuthorize("@el.check('wptask:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Integer id, HttpServletRequest request) {
        hadooptaskconfigService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
