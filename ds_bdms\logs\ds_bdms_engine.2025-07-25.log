2025-07-25 08:44:35,746 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 12216 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-25 08:44:35,760 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-25 08:44:41,634 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 08:44:41,637 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-25 08:44:43,440 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1772 ms. Found 88 JPA repository interfaces.
2025-07-25 08:44:44,374 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-25 08:44:44,376 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-25 08:44:44,379 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-25 08:44:44,381 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-25 08:44:44,385 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 08:44:44,388 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-25 08:44:44,390 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-25 08:44:44,390 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 08:44:44,390 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 08:44:45,613 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-25 08:44:45,645 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-25 08:44:45,650 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-25 08:44:47,363 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-25 08:44:47,818 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-25 08:44:47,862 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-25 08:44:47,863 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-25 08:44:47,864 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-25 08:44:47,865 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-25 08:44:47,865 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-25 08:44:47,866 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-25 08:44:47,871 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-25 08:44:47,875 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-25 08:44:52,506 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-25 08:44:53,409 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-25 08:44:53,703 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-25 08:44:54,332 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-25 08:44:54,974 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-25 08:45:03,723 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-25 08:45:03,846 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-25 08:45:09,822 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-25 08:45:10,288 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 33161 ms
2025-07-25 08:45:17,587 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-25 08:45:17,784 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-25 08:45:17,886 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 08:45:17,887 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-25 08:45:17,910 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-25 08:45:17,917 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 08:45:17,917 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-25 08:45:17,917 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-25 08:45:17,918 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@671e3bd4
2025-07-25 08:45:17,919 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-25 08:45:21,737 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@54406b62, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b324f4f, org.springframework.security.web.context.SecurityContextPersistenceFilter@2c9d7ede, org.springframework.security.web.header.HeaderWriterFilter@49ecd74f, org.springframework.security.web.authentication.logout.LogoutFilter@2c6062bd, org.springframework.web.filter.CorsFilter@27d9ba11, com.wzsec.modules.security.security.TokenFilter@44af21cf, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7b5d68fc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1bf22938, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@674d102c, org.springframework.security.web.session.SessionManagementFilter@3631bac6, org.springframework.security.web.access.ExceptionTranslationFilter@2e9290bc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6db22fce]
2025-07-25 08:45:21,781 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-25 08:45:25,782 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-25 08:45:25,783 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 08:45:25,784 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-25 08:45:25,784 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-25 08:45:25,784 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 08:45:25,784 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 08:45:25,784 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-25 08:45:25,784 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5a3cfce5
2025-07-25 08:45:26,024 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-25 08:45:26,108 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-25 08:45:26,113 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-25 08:45:26,114 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-25 08:45:26,114 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-25 08:45:26,114 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-25 08:45:26,114 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-25 08:45:26,114 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-25 08:45:26,115 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 08:45:26,115 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-25 08:45:26,115 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-25 08:45:26,118 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-25 08:45:26,118 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-25 08:45:26,119 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 08:45:26,135 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 51.776 seconds (JVM running for 57.056)
2025-07-25 08:45:27,022 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-25 08:45:27,022 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-25 09:59:40,144 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
