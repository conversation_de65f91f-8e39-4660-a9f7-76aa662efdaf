package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2022-04-21
*/
@Data
public class MaskVideotaskconfigQueryCriteria{

    /** 精确 */
    @Query
    private String taskname;

    /** 精确 */
    @Query
    private String inputdirectory;

    /** 精确 */
    @Query
    private String inputfileformat;

    /** 精确 */
    @Query
    private String computeresources;

    /** 精确 */
    @Query
    private String maskobject;

    /** 精确 */
    @Query
    private String state;

    /** 精确 */
    @Query
    private String outputfileformat;

    /** 精确 */
    @Query
    private String outputdirectory;

    /** 精确 */
    @Query
    private String executionstate;

    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createtime;

    @Query(blurry = "tasknameinputdirectoryinputfileformatcomputeresourcesmaskobjectstateoutputfileformatoutputdirectoryexecutionstate")
    private String blurry;
}
