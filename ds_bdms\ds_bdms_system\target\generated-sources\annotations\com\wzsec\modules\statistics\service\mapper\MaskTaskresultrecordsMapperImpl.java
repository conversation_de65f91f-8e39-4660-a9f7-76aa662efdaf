package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.dto.MaskTaskresultrecordsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:20+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskTaskresultrecordsMapperImpl implements MaskTaskresultrecordsMapper {

    @Override
    public MaskTaskresultrecords toEntity(MaskTaskresultrecordsDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();

        maskTaskresultrecords.setId( dto.getId() );
        maskTaskresultrecords.setTaskname( dto.getTaskname() );
        maskTaskresultrecords.setTasktype( dto.getTasktype() );
        maskTaskresultrecords.setTaskstatus( dto.getTaskstatus() );
        maskTaskresultrecords.setStarttime( dto.getStarttime() );
        maskTaskresultrecords.setEndtime( dto.getEndtime() );
        maskTaskresultrecords.setSparefield1( dto.getSparefield1() );
        maskTaskresultrecords.setSparefield2( dto.getSparefield2() );
        maskTaskresultrecords.setSparefield3( dto.getSparefield3() );
        maskTaskresultrecords.setSparefield4( dto.getSparefield4() );

        return maskTaskresultrecords;
    }

    @Override
    public MaskTaskresultrecordsDto toDto(MaskTaskresultrecords entity) {
        if ( entity == null ) {
            return null;
        }

        MaskTaskresultrecordsDto maskTaskresultrecordsDto = new MaskTaskresultrecordsDto();

        maskTaskresultrecordsDto.setId( entity.getId() );
        maskTaskresultrecordsDto.setTaskname( entity.getTaskname() );
        maskTaskresultrecordsDto.setTasktype( entity.getTasktype() );
        maskTaskresultrecordsDto.setTaskstatus( entity.getTaskstatus() );
        maskTaskresultrecordsDto.setStarttime( entity.getStarttime() );
        maskTaskresultrecordsDto.setEndtime( entity.getEndtime() );
        maskTaskresultrecordsDto.setSparefield1( entity.getSparefield1() );
        maskTaskresultrecordsDto.setSparefield2( entity.getSparefield2() );
        maskTaskresultrecordsDto.setSparefield3( entity.getSparefield3() );
        maskTaskresultrecordsDto.setSparefield4( entity.getSparefield4() );

        return maskTaskresultrecordsDto;
    }

    @Override
    public List<MaskTaskresultrecords> toEntity(List<MaskTaskresultrecordsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskTaskresultrecords> list = new ArrayList<MaskTaskresultrecords>( dtoList.size() );
        for ( MaskTaskresultrecordsDto maskTaskresultrecordsDto : dtoList ) {
            list.add( toEntity( maskTaskresultrecordsDto ) );
        }

        return list;
    }

    @Override
    public List<MaskTaskresultrecordsDto> toDto(List<MaskTaskresultrecords> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskTaskresultrecordsDto> list = new ArrayList<MaskTaskresultrecordsDto>( entityList.size() );
        for ( MaskTaskresultrecords maskTaskresultrecords : entityList ) {
            list.add( toDto( maskTaskresultrecords ) );
        }

        return list;
    }
}
