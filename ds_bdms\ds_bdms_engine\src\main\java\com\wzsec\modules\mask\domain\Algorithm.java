package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-03
*/
@Entity
@Data
@Table(name="sdd_algorithm")
public class Algorithm implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 算法名称 */
    @Column(name = "algorithmname")
    private String algorithmname;

    /** 算法英文名 */
    @Column(name = "algenglishname")
    private String algenglishname;

    /** 算法描述  */
    @Column(name = "algorithmdesc")
    private String algorithmdesc;

    /** 类名 */
    @Column(name = "classname")
    private String classname;

    /** 方法名 */
    @Column(name = "methodname")
    private String methodname;

    /** 时间效率 */
    @Column(name = "timerate")
    private String timerate;

    /** 空间膨胀系数 */
    @Column(name = "spaceexpand")
    private String spaceexpand;

    /** 安全强度 */
    @Column(name = "safetylevel")
    private String safetylevel;

    /** 参数样例 */
    @Column(name = "paramexample")
    private String paramexample;

    /** 参数描述 */
    @Column(name = "paramdesc")
    private String paramdesc;

    /** 参数个数 */
    @Column(name = "paramnum")
    private String paramnum;

    /** 密钥样例 */
    @Column(name = "secretexample")
    private String secretexample;

    /** 密钥描述 */
    @Column(name = "secretdesc")
    private String secretdesc;

    /** 函数名称 */
    @Column(name = "funcname")
    private String funcname;

    /** 方法路径 */
    @Column(name = "funcnamepath")
    private String funcnamepath;

    /** jar包名 */
    @Column(name = "jarname")
    private String jarname;

    /** 0表示有效，1表示无效 */
    @Column(name = "flag")
    private String flag;

    /** 种类(国际1,国内2,摘要3,混合4,自定义,5混合) */
    @Column(name = "kind")
    private String kind;

    /** 是否可逆(可逆为1，不可逆为0) */
    @Column(name = "isreversible")
    private String isreversible;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "memo")
    private String memo;

    /** 版本号 */
    @Column(name = "version")
    private String version;

    /** 实现源 */
    @Column(name = "realizesource")
    private String realizesource;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(Algorithm source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
