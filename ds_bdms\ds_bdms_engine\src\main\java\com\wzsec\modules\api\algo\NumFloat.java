package com.wzsec.modules.api.algo;

/**
 * @description: 数字浮动
 * @author: JOY
 * @date: 2022-03-04
 **/
public class NumFloat {
    /**
     * @param strData     str数据
     * @param coefficient 系数
     * @return {@link String }
     * @description: 加密
     * @author: JOY
     * @date: 2022-03-04
     */
    public static String encrypt(String strData, String coefficient) {
        int length = strData.length();
        Double coefficients = Double.parseDouble(coefficient);
        if (strData.contains(".")) {
            Double num = Double.parseDouble(strData);
            strData = String.valueOf((num * coefficients));
        }else {
            long num = Long.parseLong(strData);
            strData = String.valueOf((num * coefficients));
        }
        return strData;
    }

    public static void main(String[] args) {
        String strData = "1300";
        System.out.println(encrypt(strData, "100"));
    }
}
