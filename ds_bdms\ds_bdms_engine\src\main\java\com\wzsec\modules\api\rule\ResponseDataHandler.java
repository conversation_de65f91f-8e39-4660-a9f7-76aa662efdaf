package com.wzsec.modules.api.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.modules.sdd.api.service.dto.ApiRuleDto;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description：处理响应数据
 * @date ：2021/6/10 17:22
 */
@Slf4j
public class ResponseDataHandler {


    private List<ApiRuleDto> apiRuleDtoList;

    public ResponseDataHandler(List<ApiRuleDto> apiRuleDtoList) {
        this.apiRuleDtoList = apiRuleDtoList;
    }

    /**
     * @param dataStr:待脱敏字符串
     * @param splitStr:非JSON数据拆分符
     * @return
     * <AUTHOR>
     * @description：判断解析JSON字符串，对字符串进行脱敏
     * @date ：2021/6/10 17:22
     */
    public String analysis(String dataStr, String splitStr) {
        if (StringUtils.isEmpty(dataStr)) {
            return dataStr;
        }

        if (isJSON(dataStr)) {
            // 处理JSON数据
            Object jsonObj = JSON.parseObject(dataStr);
            if (jsonObj instanceof JSONObject) {
                analysisJSONObject((JSONObject) jsonObj);
            } else if (jsonObj instanceof JSONArray) {
                analysisJSONArray((JSONArray) jsonObj);
            }
            dataStr = jsonObj.toString();
        } else {
            // 处理非JSON数据，根据拆分规则再进行脱敏


            //这些特殊字符需要转义
            //.  $  |   (   )  [   {   ^  ?  *  +  \
//            String data = ". 1 $ 2 | 3 ( 4 ) 5 [ 6 { 7 ^ 8 ? 9 * 10 + 11 \\ 12 ";
//            System.out.println(data);
//            String splitStr = "\\.|\\$|\\(|\\)|\\[|\\{|\\^|\\?|\\*|\\+|\\\\";
//            System.out.println(splitStr);
////
////         splitStr = splitStr.replace("\\", "\\\\").replace(".", "\\\\.").replace("$", "\\\\$").replace("(", "\\\\(").replace(")", "\\\\)")
////                .replace("[", "\\\\[").replace("{", "\\\\{").replace("^", "\\\\^").replace("?", "\\\\?")
////                .replace("*", "\\\\*").replace("+", "\\\\+");
//            System.out.println(splitStr);
//            System.out.println(Arrays.toString(data.split(splitStr)));
            splitStr = splitStr.replace("\\", "\\\\").replace(".", "\\.").replace("$", "\\$").replace("(", "\\(")
                    .replace(")", "\\)").replace("[", "\\[").replace("{", "\\{")
                    .replace("^", "\\^").replace("?", "\\?").replace("*", "\\*").replace("+", "\\+");

            //根据“;”和“.”进行拆分，但是保留拆分符
//            System.out.println(Arrays.toString("a;b;c;d.e".split("((?<=;)|(?=;))|((?<=.)|(?=.))")));
            String[] splitArr = splitStr.split("\\|");
            StringBuffer splitSB = new StringBuffer();
            for (String splitStr_tmp : splitArr) {
                if (splitSB.length() > 0) {
                    splitSB.append("|");
                }
                splitSB.append("((?<=").append(splitStr_tmp).append(")|(?=").append(splitStr_tmp).append("))");
            }
//            System.out.println("转换后的拆分符" + splitSB);
            String[] dataStrArr = dataStr.split(splitSB.toString());
//            System.out.println(Arrays.toString(dataStrArr));
            for (int i = 0; i < dataStrArr.length; i++) {
                dataStrArr[i] = maskData(dataStrArr[i]).toString();
            }
            //脱敏后转成String
            dataStr = String.join("", Arrays.asList(dataStrArr));
        }
        return dataStr;
    }

    /**
     * <AUTHOR> Kunxiang
     * @description：对String数据脱敏，如果对非String数据脱敏会影响数据包长度
     * @date ：2021/6/10 17:22
     */
    private Object maskData(Object data) {
        if (data instanceof String) {//只对String进行脱敏
            data = DataMaskManager.getMaskData(data.toString(), apiRuleDtoList);
        }
        return data;
    }

    /**
     * <AUTHOR> Kunxiang
     * @description：判断是否是JSON格式字符串
     * @date ：2021/6/10 17:22
     */
    public boolean isJSON(String str) {
        boolean result = false;
        try {
            System.out.println("数据：" + str);
            JSON.parse(str);
            result = true;
        } catch (Exception e) {
//            e.printStackTrace();
            result = false;
        }
        return result;
    }

    /**
     * @param jsonObject
     * @return
     * <AUTHOR> Kunxiang
     * @description：递归处理JSONObject
     * @date ：2021/6/10 17:22
     */
    private void analysisJSONObject(JSONObject jsonObject) {
        Set<String> keys = jsonObject.keySet();
        keys.parallelStream().forEach(key -> {
            Object value = jsonObject.get(key);//获取Value
            if (value instanceof JSONObject) {
                JSONObject valueJsonObject = (JSONObject) value;
                analysisJSONObject(valueJsonObject);
            } else if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                if (jsonArray.size() == 0) {
                    return;
                }
                if (jsonArray.size() > 0) {
                    if (jsonArray.get(0) instanceof JSONArray || jsonArray.get(0) instanceof JSONObject) {
                        analysisJSONArray(jsonArray);
                    }
                }
            } else {
                //进行脱敏
                jsonObject.put(key, maskData(jsonObject.get(key)));
            }
        });
    }


    /**
     * @param jsonArray
     * @return
     * <AUTHOR> Kunxiang
     * @description：递归处理JSONArray
     * @date ：2021/6/10 17:22
     */
    private void analysisJSONArray(JSONArray jsonArray) {
        jsonArray.parallelStream().forEach(json -> {
            if (json instanceof JSONObject) {
                JSONObject valueJsonObject = (JSONObject) json;
                analysisJSONObject(valueJsonObject);
            } else if (json instanceof JSONArray) {
                JSONArray tmpJsonArray = (JSONArray) json;
                if (tmpJsonArray.size() == 0) {
                    return;
                }
                analysisJSONArray(tmpJsonArray);
            }
        });
    }

    public static void main(String[] args) {

        // 这些特殊字符需要转义
        //.  $  |   (   )  [   {   ^  ?  *  +  \
        String data = ". 1 $ 2 | 3 ( 4 ) 5 [ 6 { 7 ^ 8 ? 9 * 10 + 11 \\ 12 ";
        System.out.println(data);
        String splitStr = "\\.|\\$|\\(|\\)|\\[|\\{|\\^|\\?|\\*|\\+|\\\\";
        System.out.println(splitStr);
//
//         splitStr = splitStr.replace("\\\\", "\\\\\\\\").replace(".", "\\\\.").replace("$", "\\\\$").replace("(", "\\\\(").replace(")", "\\\\)")
//                .replace("[", "\\\\[").replace("{", "\\\\{").replace("^", "\\\\^").replace("?", "\\\\?")
//                .replace("*", "\\\\*").replace("+", "\\\\+");
        System.out.println(splitStr);
        System.out.println(Arrays.toString(data.split(splitStr)));


        //根据“;”和“.”进行拆分，但是保留拆分符
        System.out.println(Arrays.toString("a$b;$c;;d;e.f".split("((?<=;;)|(?=;;))|((?<=\\$)|(?=\\$))")));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("1", 1);
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("3", "3");
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put("5", "5");
        jsonObject1.put("4", jsonObject2);
        jsonObject.put("2", jsonObject1);

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(new JSONObject(jsonObject2));
        jsonArray.add(new JSONObject(jsonObject2));

        jsonObject.put("array", jsonArray);

        ResponseDataHandler responseDataHandler = new ResponseDataHandler(null);

//        responseDataHandler.analysisJSONObject(jsonObject);
//        System.out.println(jsonObject.toJSONString());
//
//        System.out.println(responseDataHandler.analysis(jsonObject.toJSONString()));

        System.out.println(responseDataHandler.analysis("17610631163,1888888", ","));
    }

}
