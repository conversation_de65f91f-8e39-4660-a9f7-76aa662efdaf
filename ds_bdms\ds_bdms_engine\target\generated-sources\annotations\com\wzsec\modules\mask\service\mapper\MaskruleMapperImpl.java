package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.Maskrule;
import com.wzsec.modules.mask.service.dto.MaskruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskruleMapperImpl implements MaskruleMapper {

    @Override
    public MaskruleDto toDto(Maskrule entity) {
        if ( entity == null ) {
            return null;
        }

        MaskruleDto maskruleDto = new MaskruleDto();

        maskruleDto.setAlgorithmid( entity.getAlgorithmid() );
        maskruleDto.setCreatetime( entity.getCreatetime() );
        maskruleDto.setCreateuser( entity.getCreateuser() );
        maskruleDto.setFlag( entity.getFlag() );
        maskruleDto.setId( entity.getId() );
        maskruleDto.setMemo( entity.getMemo() );
        maskruleDto.setParam( entity.getParam() );
        maskruleDto.setRulecname( entity.getRulecname() );
        maskruleDto.setRulename( entity.getRulename() );
        maskruleDto.setSparefield1( entity.getSparefield1() );
        maskruleDto.setSparefield2( entity.getSparefield2() );
        maskruleDto.setSparefield3( entity.getSparefield3() );
        maskruleDto.setSparefield4( entity.getSparefield4() );
        maskruleDto.setSparefield5( entity.getSparefield5() );
        maskruleDto.setStandardcname( entity.getStandardcname() );
        maskruleDto.setStandardename( entity.getStandardename() );
        maskruleDto.setUpdatetime( entity.getUpdatetime() );
        maskruleDto.setUpdateuser( entity.getUpdateuser() );

        return maskruleDto;
    }

    @Override
    public List<MaskruleDto> toDto(List<Maskrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskruleDto> list = new ArrayList<MaskruleDto>( entityList.size() );
        for ( Maskrule maskrule : entityList ) {
            list.add( toDto( maskrule ) );
        }

        return list;
    }

    @Override
    public Maskrule toEntity(MaskruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        Maskrule maskrule = new Maskrule();

        maskrule.setAlgorithmid( dto.getAlgorithmid() );
        maskrule.setCreatetime( dto.getCreatetime() );
        maskrule.setCreateuser( dto.getCreateuser() );
        maskrule.setFlag( dto.getFlag() );
        maskrule.setId( dto.getId() );
        maskrule.setMemo( dto.getMemo() );
        maskrule.setParam( dto.getParam() );
        maskrule.setRulecname( dto.getRulecname() );
        maskrule.setRulename( dto.getRulename() );
        maskrule.setSparefield1( dto.getSparefield1() );
        maskrule.setSparefield2( dto.getSparefield2() );
        maskrule.setSparefield3( dto.getSparefield3() );
        maskrule.setSparefield4( dto.getSparefield4() );
        maskrule.setSparefield5( dto.getSparefield5() );
        maskrule.setStandardcname( dto.getStandardcname() );
        maskrule.setStandardename( dto.getStandardename() );
        maskrule.setUpdatetime( dto.getUpdatetime() );
        maskrule.setUpdateuser( dto.getUpdateuser() );

        return maskrule;
    }

    @Override
    public List<Maskrule> toEntity(List<MaskruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Maskrule> list = new ArrayList<Maskrule>( dtoList.size() );
        for ( MaskruleDto maskruleDto : dtoList ) {
            list.add( toEntity( maskruleDto ) );
        }

        return list;
    }
}
