package com.wzsec.dotask.mask.service.excute.file;

import org.apache.commons.lang.StringUtils;

/**
 *@Description:脱敏Helper
 *<AUTHOR>
 *@date 2020-11-13
 */
public class MaskingHelper {
  
	public static String replaceWhile(String str, String sp) {
		String strResult = "";
		if(str.indexOf(sp + sp) != -1) {
	       String r1 = StringUtils.replace(str, sp+sp, sp+"$&$"+sp, 1);
	       replaceWhile(r1, sp);
	    }
	    else
	       strResult = str;
	    return strResult;
    }

	public static String arrayJoinString(String[] array, String separator, int startIndex, int endIndex) {
	    String strarrayJoin = "";
	    int  bufSize = endIndex - startIndex;
        if(bufSize <= 0){
          strarrayJoin = "";
        }
        else
        {
          int len = 0;
          if(array[startIndex] == null){
        	  len = 16;
          }
          else{
        	 len = array[startIndex].toString().length();
          }
          bufSize = bufSize *  len  + separator.length();
          StringBuilder buf = new StringBuilder();
          for(int i=startIndex; i<endIndex+1; i++)
          {
        	 if(i>startIndex){
        	   buf.append(separator);
        	 }
        	 if(array[i] != null){
        	   buf.append(array[i]);
        	 }
          }
          strarrayJoin = buf.toString();
        }
	    return strarrayJoin;
	}

}
