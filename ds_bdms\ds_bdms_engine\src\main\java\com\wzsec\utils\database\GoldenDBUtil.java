package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName GoldenDBUtil
 * @Description goldenDB操作类
 * <AUTHOR>
 * @Date 2024/11/14 16:51
 */
@Slf4j
public class GoldenDBUtil {
    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 17:28
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();
        String driverProgram = map.get("driverProgram");
        String username = map.get("username");
        String password = map.get("password");
        // 如果密码是加密的，进行解密
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }
        String dbname = map.get("dbname");
        String srcport = map.get("srcport");
        String srcip = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            Class.forName(driverProgram);
            String jdbcUrl = "jdbc:goldendb://" + srcip + ":" + srcport + "/default";
            connection = DriverManager.getConnection(jdbcUrl, username, password);
            statement = connection.createStatement();
            resultSet = connection.getMetaData().getCatalogs();
            boolean databaseExists = false;
            while (resultSet.next()) {
                String databaseName = resultSet.getString(1);
                if (dbname.equalsIgnoreCase(databaseName)) {
                    databaseExists = true;
                    break;
                }
            }
            if (!databaseExists) {
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }

        } catch (Exception e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return msgMap;
    }
}
