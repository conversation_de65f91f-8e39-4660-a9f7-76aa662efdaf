package com.wzsec.dotask.mask.service.impl;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sequoiadb.base.CollectionSpace;
import com.sequoiadb.base.DBCollection;
import com.sequoiadb.base.DBCursor;
import com.sequoiadb.base.Sequoiadb;
import com.sequoiadb.exception.BaseException;
import com.wzsec.dotask.mask.service.DoDBBatchTaskService;
import com.wzsec.dotask.mask.service.excute.batch.DoBatchDBTaskJob;
import com.wzsec.dotask.mask.service.excute.file.*;
import com.wzsec.modules.mask.domain.*;
import com.wzsec.modules.mask.repository.*;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.*;
import com.wzsec.modules.mask.service.mapper.DbBatchTaskConfigMapper;
import com.wzsec.modules.mask.service.mapper.DbBatchTaskResultMapper;
import com.wzsec.modules.mask.service.mapper.MaskruleMapper;
import com.wzsec.modules.sdd.metadata.domain.MetaField;
import com.wzsec.modules.sdd.metadata.service.MetaFieldService;
import com.wzsec.modules.sdd.metadata.service.MetaTableService;
import com.wzsec.modules.sdd.metadata.service.dto.MetaTableDto;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.proxy.common.utils.StringUtil;
import com.wzsec.utils.*;
import com.wzsec.utils.database.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.hive.jdbc.HiveStatement;
import org.bson.BSONObject;
import org.bson.BasicBSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;

import java.io.File;
import java.io.IOException;
import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;

import static com.wzsec.utils.database.SequoiaDBUtil.tableNameStr;

/**
 * <AUTHOR>
 * @date 2020-11-12
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "wpTask")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoDBBatchTaskServiceImpl implements DoDBBatchTaskService {

    private final DbBatchTaskResultService dbBatchTaskResultService;

    private final DbBatchTaskConfigService dbBatchTaskConfigService;

    private final DatasourceService dataSourceService;

    private final MaskStrategyFieldService maskStrategyFieldService;

    private final AlgorithmService algorithmService;

    private final DbBatchTaskConfigMapper dbBatchTaskConfigMapper;

    private final DbBatchTaskResultMapper dbBatchTaskResultMapper;

    private final BatchTaskTabStrategyConfigRepository batchTaskTabStrategyConfigRepository;

    private final MetaTableService metaTableService;

    private final UserService userService;

    private final SystemLogService logService;

    private final MaskTaskresultrecordsService maskTaskresultrecordsService;

    private final MaskStrategyTableService maskStrategyTableService;

    private final MetaFieldService metaFieldService;

    public static Map<String, String> idNameMap = new HashMap<String, String>();

    DbBatchTaskConfig dbBatchTaskConfig = null;

    DbBatchTaskResult dbBatchTaskResult = null;

    private final EngineServerRepository engineServerRepository;


    private final RuleService ruleService;

    private final AlgorithmRepository algorithmRepository;
    private final MaskruleMapper maskruleMapper;
    private final MaskruleRepository maskruleRepository;

    private final MaskruleService maskruleService;

    private final MaskstrategyIdentifymaskstrategiesdetailService maskstrategyIdentifymaskstrategiesdetailService;

    private final MaskstrategyIdentifymaskstrategiesService maskstrategyIdentifymaskstrategiesService;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    @Value("${spring.profiles.active}")
    private String active;


    public DoDBBatchTaskServiceImpl(DbBatchTaskConfigService dbBatchTaskConfigService, DatasourceService datasourceService,
                                    MaskStrategyFieldService maskStrategyFieldService, DbBatchTaskResultService dbBatchTaskResultService, UserService userService,
                                    AlgorithmService algorithmService, DbBatchTaskConfigMapper dbBatchTaskConfigMapper, DbBatchTaskResultMapper dbBatchTaskResultMapper,
                                    BatchTaskTabStrategyConfigRepository batchTaskTabStrategyConfigRepository, MetaTableService metaTableService,
                                    SystemLogService logService, MaskTaskresultrecordsService maskTaskresultrecordsService,
                                    MetaFieldService metaFieldService, MaskStrategyTableService maskStrategyTableService,
                                    EngineServerRepository engineServerRepository,
                                    RuleService ruleService, AlgorithmRepository algorithmRepository,
                                    MaskruleService maskruleService,
                                    MaskstrategyIdentifymaskstrategiesdetailService maskstrategyIdentifymaskstrategiesdetailService,
                                    MaskstrategyIdentifymaskstrategiesService maskstrategyIdentifymaskstrategiesService,SdkOperationrecordRepository sdkOperationrecordRepository,SdkApplyconfigRepository sdkApplyconfigRepository,
                                    MaskruleMapper maskruleMapper, MaskruleRepository maskruleRepository) {
        this.dbBatchTaskConfigService = dbBatchTaskConfigService;
        this.dataSourceService = datasourceService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.dbBatchTaskResultService = dbBatchTaskResultService;
        this.userService = userService;
        this.algorithmService = algorithmService;
        this.dbBatchTaskConfigMapper = dbBatchTaskConfigMapper;
        this.dbBatchTaskResultMapper = dbBatchTaskResultMapper;
        this.batchTaskTabStrategyConfigRepository = batchTaskTabStrategyConfigRepository;
        this.metaTableService = metaTableService;
        this.logService = logService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.metaFieldService = metaFieldService;
        this.maskStrategyTableService = maskStrategyTableService;
        this.engineServerRepository = engineServerRepository;
        this.ruleService = ruleService;
        this.algorithmRepository = algorithmRepository;
        this.maskruleService = maskruleService;
        this.maskstrategyIdentifymaskstrategiesdetailService = maskstrategyIdentifymaskstrategiesdetailService;
        this.maskstrategyIdentifymaskstrategiesService = maskstrategyIdentifymaskstrategiesService;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.maskruleMapper = maskruleMapper;
        this.maskruleRepository = maskruleRepository;
    }

    @Async//异步执行
    @Override
    public void execution(Integer id, String submituser) {
        // 分批加载数据不支持使用v1
//        DoDBTaskJob_V1 doDBTaskJob = new DoDBTaskJob_V1(dbtaskconfigService, dataSourceService,
//                maskStrategyFieldService, dbtaskresultService,
//                algorithmService, dbtaskconfigMapper, dbtaskresultMapper,
//                id, submituser);
        //分页查询慢使用v2

        Thread t = Thread.currentThread();
        String name = t.getName();
        System.out.println("dbbatchmasktask_name" + name);
        idNameMap.put(id + "", name);
        System.out.println(id);
        log.info("任务ID【" + id + "】,执行批量脱敏任务开始");

        performTasks(id, submituser);

    }

    /**
     * 执行脱敏任务
     *
     * @param id         id
     * @param submituser submituser
     */
    public void performTasks(Integer id, String submituser) {


//        boolean exec = true;
//        List<BatchTaskTabConfig> dbBatchTaskTabStrategyInfo = batchTaskTabStrategyConfigRepository.getDbBatchTaskTabStrategyInfo(id);
//        Long batchtaskId = dbBatchTaskTabStrategyInfo.get(0).getBatchtaskid();
//
//        DbBatchTaskConfigDto taskConfigServiceById = dbBatchTaskConfigService.findById(batchtaskId.intValue());
//        String engine = taskConfigServiceById.getTaskexecuteengine();
//        String[] ipPort = engine.split("-")[1].trim().split(":");
//        String ip = ipPort[0];
//        String port = ipPort[1];
//
//        //更新执行条进度
//        dbBatchTaskConfigService.alterTaskExecutionAnticipate("15", id);
//
//        //TODO 将所使用的引擎，任务负载数+1
//        engineServerRepository.addCountByIpPort(ip, port);
//
//        for (BatchTaskTabConfig batchTaskTabConfig : dbBatchTaskTabStrategyInfo) {
//            MetaTableDto metaTable = metaTableService.findById(batchTaskTabConfig.getTableid());
//            String tabName = metaTable.getTablename();
//            String strategyId = batchTaskTabConfig.getStrategyid() != null ? batchTaskTabConfig.getStrategyid().toString() : null;
//            DoBatchDBTaskJob doBatchDBTaskJob = new DoBatchDBTaskJob(dbBatchTaskConfigService, dataSourceService,
//                    maskStrategyFieldService, dbBatchTaskResultService, algorithmService, dbBatchTaskConfigMapper,
//                    dbBatchTaskResultMapper, logService, id, submituser, tabName, strategyId, maskTaskresultrecordsService,
//                    metaFieldService, maskStrategyTableService, batchTaskTabConfig);
//            //有一张表任务执行失败，整体任务算做执行是啊比
//            boolean execked = doBatchDBTaskJob.exec();
//            if (exec && !execked) {
//                exec = execked;
//            }
//        }
//
//        //TODO 数据库对象迁移
//        dbObjectMigration(taskConfigServiceById);
//
//        dbBatchTaskConfigService.alterTaskExecutionState(exec ? Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS : Const.TASK_EXECUTESTATE_EXECUTE_FAIL, id);
//        //执行成功即删除线程
//        idNameMap.remove(id + "");
//
//        //TODO 将所使用的引擎，任务负载数-1
//        engineServerRepository.reduceCountByIpPort(ip, port);

        //格式化脱敏

        //非格式化脱敏

        // 任务开始时间
        String startTime = DateUtil.getNowTime();
        boolean exec = true;
        //脱敏任务表策略 配置信息
        List<BatchTaskTabConfig> dbBatchTaskTabStrategyInfo = batchTaskTabStrategyConfigRepository.getDbBatchTaskTabStrategyInfo(id);
        Long batchtaskId = dbBatchTaskTabStrategyInfo.get(0).getBatchtaskid();
        // 脱敏任务
        DbBatchTaskConfigDto taskConfigDto = dbBatchTaskConfigService.findById(batchtaskId.intValue());
        String engine = taskConfigDto.getTaskexecuteengine();
        String masktablename = taskConfigDto.getMasktablename();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String ip = ipPort[0];
        String port = ipPort[1];

        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(ip+":"+port);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        dbBatchTaskConfig = dbBatchTaskConfigMapper.toEntity(taskConfigDto);
        Integer in_dataSourceId = dbBatchTaskConfig.getInputdatasourceid();
        DatasourceDto in_datasourceDto = dataSourceService.findById(Long.valueOf(in_dataSourceId));
        String dbType = in_datasourceDto.getType();


        //更新执行条进度
        dbBatchTaskConfigService.alterTaskExecutionAnticipate("15", id);

        //TODO 将所使用的引擎，任务负载数+1
        if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
            engineServerRepository.addKingbaseCountByIpPort(ip, port);
        }else{
            engineServerRepository.addCountByIpPort(ip, port);
        }

        for (BatchTaskTabConfig taskTabConfigDto : dbBatchTaskTabStrategyInfo) {
            //元数据
            MetaTableDto metaTable = metaTableService.findById(taskTabConfigDto.getTableid());
            String tabName = metaTable.getTablename();
            String strategyId = taskTabConfigDto.getStrategyid() != null ? taskTabConfigDto.getStrategyid().toString() : null;
            DoBatchDBTaskJob doBatchDBTaskJob = new DoBatchDBTaskJob(dbBatchTaskConfigService, dataSourceService,
                    maskStrategyFieldService, dbBatchTaskResultService, algorithmService, dbBatchTaskConfigMapper,
                    dbBatchTaskResultMapper, logService, id, submituser, tabName, strategyId, maskTaskresultrecordsService,
                    metaFieldService, maskStrategyTableService, taskTabConfigDto,  algorithmRepository, maskruleMapper, maskruleRepository);

            String sparefield3 = taskTabConfigDto.getSparefield3();

            if (Const.DB_REDIS.equalsIgnoreCase(dbType)){
                sparefield3 = Const.DB_TYPE_NON_FORMAT;
            }else {
                sparefield3 = Const.DB_TYPE_FORMAT;
            }
            if (sparefield3.equals(Const.DB_TYPE_FORMAT)) {
                //格式化
                //有一张表任务执行失败，整体任务算做执行是啊比
                boolean execked = doBatchDBTaskJob.exec();
                if (exec && !execked) {
                    exec = execked;
                }
            } else {
                //非格式化
                // 异常数据处理告警
                Map<String, String> abnormalHandlingAlarmMap = new HashMap<>();
                Long inputDataSourceId = Long.valueOf(taskConfigDto.getInputdatasourceid());
                Long outputDataSourceId = Long.valueOf(taskConfigDto.getOutputdatasourceid());
                //输入数据源
                DatasourceDto inDataSourceDto = dataSourceService.findById(inputDataSourceId);
                String inSrcurl = inDataSourceDto.getSrcurl();
                //获取一个包含redis集群节点的set
                Set<HostAndPort> inJedisClusterNodes = queryRedisClusterNodeSet(inSrcurl);
                String inSrcIp = inDataSourceDto.getSrcip();
                String inSrcPort = inDataSourceDto.getSrcport();
                String inPassword = inDataSourceDto.getPassword();
                String inDBname = inDataSourceDto.getDbname();
                //输出数据源
                DatasourceDto outDataSourceDto = dataSourceService.findById(outputDataSourceId);
                String outSrcIp = outDataSourceDto.getSrcip();
                String outSrcPort = outDataSourceDto.getSrcport();
                String outPassword = outDataSourceDto.getPassword();
                String outDBname = outDataSourceDto.getDbname();
                String outSrcUrl = outDataSourceDto.getSrcurl();
                //获取一个包含redis集群节点的set
                Set<HostAndPort> outJedisClusterNodes = queryRedisClusterNodeSet(outSrcUrl);
                //数据库脱敏任务结果
                DbBatchTaskResult dbBatchTaskResult = new DbBatchTaskResult();
                dbBatchTaskResult.setTaskname(taskConfigDto.getTaskname());
                dbBatchTaskResult.setTabname(metaTable.getTablename());
                dbBatchTaskResult.setDbname(metaTable.getDbname());
                dbBatchTaskResult.setIpaddress(inSrcIp);
                String newKey = masktablename.equals(Const.MASKTABLENAMEZERO) ? tabName:tabName + "_MASK_" +DateUtil.formatDate(new Date());
                dbBatchTaskResult.setOutputname(newKey);
                dbBatchTaskResult.setOutputpath(outDataSourceDto.getDbname());
                dbBatchTaskResult.setOutputipaddress(outDataSourceDto.getSrcip());
                dbBatchTaskResult.setOutputtype(taskConfigDto.getOutputtype());
                dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTING);
                dbBatchTaskResult.setUsername(submituser);
                dbBatchTaskResult.setJobstarttime(startTime);
                dbBatchTaskResult.setJobstarttime(startTime);
                dbBatchTaskResult.setIsremove(Const.TASK_STATE_USE);
                DbBatchTaskResultDto dbBatchTaskResultDto = dbBatchTaskResultService.create(dbBatchTaskResult);
                this.dbBatchTaskResult = dbBatchTaskResultMapper.toEntity(dbBatchTaskResultDto);
                //脱敏策略id
                Integer strategyid = Integer.valueOf(taskTabConfigDto.getStrategyid() +"");
                // 根据策略id查询脱敏策略
                MaskstrategyIdentifymaskstrategiesDto strategyMainDto = maskstrategyIdentifymaskstrategiesService.findById(strategyid);
                // 获取输入目录下文件内容
                log.info("执行脱敏任务开始");
                // 脱敏前数据前5条
                String beforemaskdata = "";
                // 脱敏后数据前5条
                String aftermaskdata = "";
                //数据行数
                int totallines = 0;
                //文件分隔符
                String splitstr = ",";
                //非格式化策略
                // 加载白名单 非格式化脱敏会用到
                Const.nameWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.NAME_SIGN, Const.whiteList_SIGN);
                Const.addreWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.ADDRESS_SIGN, Const.whiteList_SIGN);
                Const.phoneWhiteList = ruleService.getWhiteListByTypeAndPurpose(Const.PHONE_SIGN, Const.whiteList_SIGN);
                // 根据策略ID查询非格式化脱敏策略
                List<Map<String, Object>> ruleList = new ArrayList<>();
                //根据策略id查询策略详情
                List<MaskstrategyIdentifymaskstrategiesdetail> maskStrategySubList =  maskstrategyIdentifymaskstrategiesdetailService.findByStrategyid(strategyMainDto.getId());
                //获取非格式化文件字段处置规则
                Map<Integer, MaskstrategyIdentifymaskstrategiesdetail> nonFormattedDisposalFilesMap = new LinkedHashMap<>();
                for (MaskstrategyIdentifymaskstrategiesdetail maskstrategyIdentifymaskstrategiesdetail : maskStrategySubList) {
                    nonFormattedDisposalFilesMap.put(maskstrategyIdentifymaskstrategiesdetail.getMaskruleid(), maskstrategyIdentifymaskstrategiesdetail);
                }
                //获取所有算法
                List<com.wzsec.modules.mask.domain.Algorithm> algorithmList = algorithmRepository.findAll();
                Map<String, String> algNameMap = new HashMap<>();
                for (com.wzsec.modules.mask.domain.Algorithm algorithm : algorithmList) {
                    algNameMap.put(algorithm.getId().toString(), algorithm.getAlgenglishname());
                }
                for (MaskstrategyIdentifymaskstrategiesdetail maskStrategyFileUnformatSub : maskStrategySubList) {
                    Map<String, Object> ruleMap = new HashMap<>();
                    MaskruleDto maskruleDto = maskruleService.findById(maskStrategyFileUnformatSub.getMaskruleid());
                    RuleDto ruleDto = ruleService.findById(maskStrategyFileUnformatSub.getSenruleid().longValue());
                    ruleMap.put("senRule", ruleDto);
                    if (Const.ALG_CONFIG_WAY_ENTIRETY.equals(maskruleDto.getSparefield2())) {
                        AlgorithmDto algorithmDto = algorithmService.findById(maskStrategyFileUnformatSub.getAlgorithmid());
                        ruleMap.put("algorithm", algorithmDto);
                        ruleMap.put("param", maskStrategyFileUnformatSub.getParam());
                        ruleMap.put("secretkey", maskStrategyFileUnformatSub.getSecretkey());
                    } else {
                        String extractconfig = maskruleDto.getSparefield3() != null ? maskruleDto.getSparefield3() : null;
                        String extractalgid = maskruleDto.getSparefield4() != null ? maskruleDto.getSparefield4() : null;
                        StringBuffer stringBuffer = new StringBuffer();
                        if (com.wzsec.utils.StringUtils.isNotEmpty(extractconfig) && com.wzsec.utils.StringUtils.isNotEmpty(extractalgid)) {
                            String[] extractConfigArr = extractconfig.split(";");
                            String[] extractAlgIdArr = extractalgid.split(";");
                            for (int i = 0; i < extractAlgIdArr.length; i++) {
                                String algId = extractAlgIdArr[i];
                                String algName = algNameMap.get(algId);
                                String parKey = Const.algorithmParameterSecretKeyMap().get(algName);
                                stringBuffer.append("|");
                                stringBuffer.append(algName + "$");
                                stringBuffer.append(parKey + "$");
                                stringBuffer.append(extractConfigArr[i]);
                            }
                        }
                        ruleMap.put("maskstrategystr", stringBuffer);
                    }
                    ruleList.add(ruleMap);
                }
                //获取所有算法信息
                HashMap<String, Algorithm> algorithmMap = algorithmService.getAlgorithmByEName();

                //获取待脱敏数据
                JedisCluster inJedisCluster = new JedisCluster(inJedisClusterNodes);
                String value = inJedisCluster.get(tabName);
                inJedisCluster.close();
                //空数据直接结束
                if (StringUtils.isEmpty(value)){
                    return;
                }
                //按脱敏策略对数据进行脱敏
                List<String> list = new ArrayList<>();
                List<HashMap<String, List<String>>> lineDataList = RedisUtil.parseData(value);
                for (HashMap<String, List<String>> lineDataMap : lineDataList) {
                    Set<String> keySetList = lineDataMap.keySet();
                    for (String keySet : keySetList) {
                        List<String> valueList = lineDataMap.get(keySet);
                        StringBuilder sb = new StringBuilder();
                        for (String str : valueList) {
                            if (ObjectUtil.isEmpty(sb)) {
                                sb.append(str);
                            } else {
                                sb.append(",").append(str);
                            }
                        }
                        list.add(sb.toString());
                    }
                }

                //获取脱敏数据 默认redis
//                Jedis inJedis = RedisUtil.queryRedisConnect(inSrcIp, inSrcPort, inPassword);
//                inJedis.select(Integer.valueOf(inDBname));
//                String value = inJedis.get(tabName);
//                //空数据直接结束
//                if (StringUtils.isEmpty(value)){
//                    return;
//                }
//                inJedis.close();
//                //按脱敏策略对数据进行脱敏
//                List<String> list = new ArrayList<>();
//                List<HashMap<String, List<String>>> lineDataList = RedisUtil.parseData(value);
//                for (HashMap<String, List<String>> lineDataMap : lineDataList) {
//                    Set<String> keySetList = lineDataMap.keySet();
//                    for (String keySet : keySetList) {
//                        List<String> valueList = lineDataMap.get(keySet);
//                        StringBuilder sb = new StringBuilder();
//                        for (String str : valueList) {
//                            if (ObjectUtil.isEmpty(sb)){
//                                sb.append(str);
//                            }else {
//                                sb.append(",").append(str);
//                            }
//                        }
//                        list.add(sb.toString());
//                    }
//                }


                //获取非格式化的脱敏数据
                List<String> maskDataList = MaskReadService.getMaskDataListIdentifyDetail(list, ruleList, splitstr, algorithmMap, null, nonFormattedDisposalFilesMap, abnormalHandlingAlarmMap,algorithmService);



                totallines += list.size();
                if ("".equals(beforemaskdata)) {
                    beforemaskdata = FileReadService.listToStr(list);
                    aftermaskdata = FileReadService.listToStr(maskDataList);
                }


                //将脱敏数据写入新的数据库 默认redis
//                Jedis outJedis = RedisUtil.queryRedisConnect(outSrcIp, outSrcPort, outPassword);
//                outJedis.select(Integer.valueOf(outDBname));
//                outJedis.set(newKey,listToStr(maskDataList));
//                outJedis.close();
                JedisCluster outJedisCluster = new JedisCluster(outJedisClusterNodes);
                outJedisCluster.set(newKey,listToStr(maskDataList));
                outJedisCluster.close();

                dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_SUCCESS);
                dbBatchTaskResult.setBeforemaskdata(beforemaskdata);
                dbBatchTaskResult.setAftermaskdata(aftermaskdata);
                dbBatchTaskResultService.update(dbBatchTaskResult);
                dbBatchTaskConfigService.alterTaskExecutionAnticipate("100", id);
            }
            exec = true;
        }

        //TODO 数据库对象迁移
        dbObjectMigration(taskConfigDto);
        if(exec){
            sdkOperationrecord.setObjectname(taskConfigDto.getTaskname());
            sdkOperationrecord.setOperation("批量数据库脱敏任务执行成功");
        }else {
            sdkOperationrecord.setObjectname(taskConfigDto.getTaskname());
            sdkOperationrecord.setOperation("批量数据库脱敏任务执行失败");
        }
        dbBatchTaskConfigService.alterTaskExecutionState(exec ? Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS : Const.TASK_EXECUTESTATE_EXECUTE_FAIL, id);
        //执行成功即删除线程
        idNameMap.remove(id + "");

        //插入SDK操作记录
        sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
        sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
        sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
        sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
        sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_BAUTHDBMASK);
        sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
        sdkOperationrecordRepository.save(sdkOperationrecord);

        //TODO 将所使用的引擎，任务负载数-1
        if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
            engineServerRepository.reduceKingbaseCountByIpPort(ip, port);
        }else {
            engineServerRepository.reduceCountByIpPort(ip, port);
        }
    }

    /**
     * 获取一个包含redis集群节点的set
     * @param inSrcurl 数据源url
     * @return
     */
    private Set<HostAndPort> queryRedisClusterNodeSet(String inSrcurl) {
        String[] redisUrlList = inSrcurl.split(";");
        // 创建一个包含集群节点的 Set
        Set<HostAndPort> inJedisClusterNodes = new HashSet<>();
        for (String redisUrl : redisUrlList) {
            String[] url = redisUrl.split(":");
            if (url.length > 4 || url.length < 3){
                throw new RuntimeException("数据源url有误");
            }
            String redisIp = url[0];
            int redisPort = Integer.valueOf(url[1]);
            inJedisClusterNodes.add(new HostAndPort(redisIp, redisPort));
        }
        return inJedisClusterNodes;
    }

    public static String listToStr(List<String> list) {
        String res = "";
        int m = list.size();
        for (int i = 0; i < m; i++) {
            res += list.get(i) + "\n";
        }
        res = res.substring(0, res.length() - 1);
        return res;
    }


    /**
     * 操作指令
     *
     * @param id  id
     * @param cid cid
     */
    @Async
    @Override
    public void operationInstructions(Integer id, Integer cid) {
        String result = "false";
        try {
            ThreadGroup currentGroup = Thread.currentThread().getThreadGroup();
            int noThreads = currentGroup.activeCount();
            Thread[] lstThreads = new Thread[noThreads];
            currentGroup.enumerate(lstThreads);
            System.out.println("现有线程数" + noThreads);
            String name = idNameMap.get(id + "");
            System.out.println(name);
            for (int i = 0; i < noThreads; i++) {
                // sign(1暂停,2恢复,3停止)
                String nm = lstThreads[i].getName();
                // System.out.println("线程号：" + i + " = " + nm);
                if (nm.equals(name)) {
                    if (cid == 1) {
                        System.out.println("暂停" + nm);
                        lstThreads[i].suspend();
                        // 更新结果
                        dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTING); //执行中
                        dbBatchTaskResultService.update(dbBatchTaskResult);
                        // 更新任务表
                        dbBatchTaskConfig.setExecutionstate(Const.TASK_STATUS_SUSPEND); //手动暂停
                        dbBatchTaskConfigService.update(dbBatchTaskConfig);

                    } else if (cid == 2) {
                        System.out.println("恢复" + nm);
                        lstThreads[i].resume();
                        // 更新结果
                        dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTING); //执行中
                        dbBatchTaskResultService.update(dbBatchTaskResult);

                    } else if (cid == 3) {
                        System.out.println("kill" + nm);
                        lstThreads[i].stop();
                    }
                    result = "true";
                    break;
                }
            }
            if (!idNameMap.containsKey(id + "")) {// 说明任务执行完毕
                // 更新结果
                dbBatchTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_SUCCESS); //执行成功
                dbBatchTaskResultService.update(dbBatchTaskResult);
                // 更新任务表
                dbBatchTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS); //任务执行成功
                dbBatchTaskConfigService.update(dbBatchTaskConfig);
            }
            log.info("调用接口操作指令成功");
        } catch (Exception e) {
            log.error("调用接口操作指令出现异常");
        }
    }

    /**
     * 获取脱敏结果预览
     *
     * @param id
     * @return
     */
    @Override
    public List<Object> getMaskResultPreview(Integer id) {

        long recordCount = 5L;
        long initialPosition = 0L;
        Connection con = null;
        Statement stmt = null;
        ResultSet resultSet = null;
        List<Object> maskDataList = new ArrayList<>();

        try {
            //通过任务配置获取对应策略
            BatchTaskTabConfig batchTaskTabConfig = batchTaskTabStrategyConfigRepository.findById(id.longValue()).get();
            String limitingCondition = batchTaskTabConfig.getConlimit();
            String tabEName = batchTaskTabConfig.getTabename();
            Integer batchTaskId = batchTaskTabConfig.getBatchtaskid().intValue();
            DbBatchTaskConfigDto taskConfigServiceById = dbBatchTaskConfigService.findById(batchTaskId);

            //初始化数据库信息
            Long sourceId = taskConfigServiceById.getInputdatasourceid().longValue();
            DatasourceDto datasourceDto = dataSourceService.findById(sourceId);
            String dbType = datasourceDto.getType();
            String driverName = datasourceDto.getDriverprogram();
            String url = datasourceDto.getSrcurl();
            String userName = datasourceDto.getUsername();
            String dbName = datasourceDto.getDbname();
            String password = datasourceDto.getPassword();
            String srcip = datasourceDto.getSrcip();
            String srcport = datasourceDto.getSrcport();
            if (StringUtils.isNotEmpty(password)) {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            }

            Integer strategyId = batchTaskTabConfig.getStrategyid().intValue();
            MaskStrategyTableDto maskStrategyTableDto = maskStrategyTableService.findById(strategyId);
            //初始化所有算法
            HashMap<String, Algorithm> algorithmMap = algorithmService.getAlgorithmByEName();

            // 初始化算法、字段信息配置
            List<Map<String, Object>> algorithmList = null;
            if (Const.STRATEGY_TYPE_SINGLETABLE.equals(maskStrategyTableDto.getStrategytype())) {
                algorithmList = maskStrategyFieldService.getMaskAlgoInfoByStrategyId(strategyId.toString());
            } else {
                List<MetaField> metaFieldsList = metaFieldService.findInfoFieldInfoByTabNameSourceId(tabEName, sourceId);
                List<Map<String, Object>> algoInfo = maskStrategyTableService.getAllStrategyAlgoInfoByStrategyId(strategyId.toString());
                algorithmList = new ArrayList<>();
                for (MetaField metaField : metaFieldsList) {
                    String fieldName = metaField.getFieldname();
                    String algoName = null;
                    String key = null;
                    String param = null;
                    String isextract = null;
                    String extractconfig = null;
                    String extractalgid = null;
                    String fieldSCname = metaField.getFieldscname();
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(fieldSCname)) {
                        for (Map<String, Object> map : algoInfo) {
                            String dataName = map.get("dataname").toString();
                            if (dataName.equals(fieldSCname)) {
                                algoName = map.get("algenglishname") == null ? null : map.get("algenglishname").toString();
                                key = map.get("secretkey") == null ? null : map.get("secretkey").toString();
                                param = map.get("param") == null ? null : map.get("param").toString();
                                isextract = map.get("isextract") == null ? null : map.get("isextract").toString();
                                extractconfig = map.get("extractconfig") == null ? null : map.get("extractconfig").toString();
                                extractalgid = map.get("extractalgid") == null ? null : map.get("extractalgid").toString();
                                break;
                            }
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("fieldename", fieldName);
                    map.put("algenglishname", algoName);
                    map.put("secretkey", key);
                    map.put("param", param);
                    map.put("isextract", isextract);
                    map.put("extractconfig", extractconfig);
                    map.put("extractalgid", extractalgid);
                    algorithmList.add(map);
                }
            }

            Map<String, Object> maskAlgoInfoMap = DBMaskUtils.getMaskAlgoInfo(algorithmList, algorithmMap);

            String fieldNameStr = "";
            for (String fieldName : maskAlgoInfoMap.keySet()) {
                fieldNameStr += fieldName + ",";
            }

            //获取结果集，查5条
            resultSet = getResultSet(con, stmt, resultSet, fieldNameStr, dbType, driverName, url, userName, password,
                    limitingCondition, dbName, tabEName, recordCount, initialPosition);
            //巨杉脱敏效果预览
            if (resultSet == null && dbType.equalsIgnoreCase(Const.DB_SEQUOIADB)) {
                Sequoiadb sdb = SequoiaDBUtil.queryConnect(url, userName, password);
                CollectionSpace cs = sdb.getCollectionSpace(dbName);
                DBCollection cl = cs.getCollection(tableNameStr(tabEName));
                DBCursor cursor = cl.query(null, null, null, null, (long) 0, (long) 5, 0);
                while (cursor.hasNext()) {
                    BSONObject next = cursor.getNext();
                    StringBuffer beforeMaskData = new StringBuffer();
                    StringBuffer afterMaskData = new StringBuffer();
                    Map<String, Object> fieldDataMap = new LinkedHashMap<String, Object>();
                    String[] fieldNameArr = fieldNameStr.split(",");
                    for (String fieldName : fieldNameArr) {
                        Object obj = next.get(fieldName);
                        String data = obj == null ? "" : obj.toString();
                        fieldDataMap.put(fieldName, data);
                        beforeMaskData.append(data + ",");
                    }
                    Map<String, Object> maskRowData = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap,new ArrayList<>(),maskruleRepository);
                    for (Map.Entry<String, Object> entry : maskRowData.entrySet()) {
                        String data = entry.getValue() == null ? "" : entry.getValue().toString();
                        afterMaskData.append(data + ",");
                    }

                    //去掉最后一位逗号
                    beforeMaskData.setLength(beforeMaskData.length() - 1);
                    afterMaskData.setLength(afterMaskData.length() - 1);
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("beforemaskdata", beforeMaskData.toString());
                    dataMap.put("aftermaskdata", afterMaskData.toString());
                    maskDataList.add(dataMap);
                }
            } else if (resultSet == null && dbType.equalsIgnoreCase(Const.DB_REDIS)) {
                //redis脱敏效果预览
                Jedis jedis = RedisUtil.queryRedisConnect(srcip, srcport, password);
                jedis.select(Integer.valueOf(dbName));
                String staticmask = jedis.get(tabEName);
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(staticmask);
                for (JsonNode arrayNode : jsonNode) {
                    StringBuffer beforeMaskData = new StringBuffer();
                    StringBuffer afterMaskData = new StringBuffer();
                    Map<String, Object> fieldDataMap = new LinkedHashMap<String, Object>();

                    Iterator<String> stringIterator = arrayNode.fieldNames();
                    stringIterator.forEachRemaining(fieldName -> {
                        JsonNode data = arrayNode.get(fieldName);
                        fieldDataMap.put(fieldName, data.toString().replaceAll("\"", ""));
                        beforeMaskData.append(data.toString().replace("\"", "") + ",");
                    });
                    Map<String, Object> maskRowData = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap,new ArrayList<>(),maskruleRepository);
                    for (Map.Entry<String, Object> entry : maskRowData.entrySet()) {
                        String data = "";
                        if (entry.getValue() != null){
                            data = entry.getValue().toString().replace("\"", "");
                        }
                        afterMaskData.append(data + ",");
                    }

                    //去掉最后一位逗号
                    beforeMaskData.setLength(beforeMaskData.length() - 1);
                    afterMaskData.setLength(afterMaskData.length() - 1);
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("beforemaskdata", beforeMaskData.toString());
                    dataMap.put("aftermaskdata", afterMaskData.toString());
                    maskDataList.add(dataMap);
                }
                jedis.close();
            }
            if (resultSet != null) {
                List<Map<String, String>> fieldInfoList = new ArrayList<>();
                if (dbType.equalsIgnoreCase(Const.DB_MYSQL)){
                    //获取字段类型
                    fieldInfoList = MysqlUtil.queryFieldInfoList(dbName, tabEName, url, userName, password);
                    fieldInfoList = fieldInfoList.stream().filter(t-> t.get("FieldType").contains("blob")).collect(Collectors.toList());
                }
                while (resultSet.next()) {
                    StringBuffer beforeMaskData = new StringBuffer();
                    StringBuffer afterMaskData = new StringBuffer();
                    Map<String, Object> fieldDataMap = new LinkedHashMap<String, Object>();
                    String[] fieldNameArr = fieldNameStr.split(",");
                    for (String fieldName : fieldNameArr) {
                        boolean flag = true;
                        if (ObjectUtil.isNotEmpty(fieldInfoList) && dbType.equalsIgnoreCase(Const.DB_MYSQL)){
                            List<Map<String, String>> collect = fieldInfoList.stream().filter(t -> t.get("FieldEName").equals(fieldName)).collect(Collectors.toList());
                            if (ObjectUtil.isNotEmpty(collect)){
                                flag = false;
                            }
                        }
                        if (flag){
                            String data = resultSet.getString(fieldName);
                            fieldDataMap.put(fieldName, data);
                            beforeMaskData.append(data + ",");
                        }else {
                            String data = "(BLOB)";
                            fieldDataMap.put(fieldName, data);
                            beforeMaskData.append(data + ",");
                        }
                    }
                    Map<String, Object> maskRowData = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap,fieldInfoList,maskruleRepository);
                    for (Map.Entry<String, Object> entry : maskRowData.entrySet()) {
                        String data = entry.getValue() == null ? "" : entry.getValue().toString();
                        afterMaskData.append(data + ",");
                    }

                    //去掉最后一位逗号
                    beforeMaskData.setLength(beforeMaskData.length() - 1);
                    afterMaskData.setLength(afterMaskData.length() - 1);
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("beforemaskdata", beforeMaskData.toString());
                    dataMap.put("aftermaskdata", afterMaskData.toString());
                    maskDataList.add(dataMap);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            DatabaseUtil.closeCon(resultSet, stmt, con);
            return maskDataList;
        }
    }

    /**
     * 获取查询结果集
     *
     * @param con
     * @param stmt
     * @param resultSet
     * @param fieldNameStr
     * @param dbType
     * @param driverName
     * @param url
     * @param userName
     * @param password
     * @param limitingCondition
     * @param dbName
     * @param tabName
     * @param recordCount
     * @param initialPosition
     * @return
     */
    private ResultSet getResultSet(Connection con, Statement stmt, ResultSet resultSet,
                                   String fieldNameStr, String dbType, String driverName, String url, String userName, String password,
                                   String limitingCondition, String dbName, String tabName, long recordCount, long initialPosition) {
        try {
            String fieldNameOne = "";
            String in_fieldNameStr = fieldNameStr;
            if (dbType.equalsIgnoreCase(Const.DB_MYSQL) || dbType.equalsIgnoreCase(Const.DB_UNIDB) || dbType.equalsIgnoreCase(Const.DB_STARROCKS)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("`" + field + "`,");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            } else if (dbType.equalsIgnoreCase(Const.DB_TERADATA)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("\"" + field + "\",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
                fieldNameOne = split[0];
            } else if (dbType.equalsIgnoreCase(Const.DB_SINODB)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append(field + ",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            } else if (dbType.equalsIgnoreCase(Const.DB_PSOTGRESQL) || dbType.equalsIgnoreCase(Const.DB_ORACLE)
                    ||dbType.equalsIgnoreCase(Const.DB_HIGHGO) || dbType.equalsIgnoreCase(Const.DB_KINGBASE)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("\"" + field + "\",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            }else if (dbType.equalsIgnoreCase(Const.DB_OSCAR)){
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append(field + ",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            } else if (dbType.equalsIgnoreCase(Const.DB_TIDB)) {
                StringBuffer stringBuffer = new StringBuffer();
                String[] split = fieldNameStr.split(",");
                for (String field : split) {
                    stringBuffer.append("`" + field + "`,");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                in_fieldNameStr = stringBuffer.toString();
            }


            // TODO oracle
            if (dbType.toLowerCase().contains("oracle")) {// 2021年2月24日18:24:46未测试
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                //log.info("连接oracle数据库成功");
                // 查抽取字段数据(分页通过子查询实现)
                String sql = null;
                if (org.apache.commons.lang3.StringUtils.isNotBlank(limitingCondition) && limitingCondition.contains("WHERE")) { //仅支持WHERE,无LIMIT
                    String template = "select {} from (select rownum r,t.* from (select * from {}.\"{}\" {}) t where rownum<={}) where r>{} ";
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, limitingCondition, recordCount + initialPosition, initialPosition);
                } else {
                    String template = "select {} from (select rownum r,t.* from {}.\"{}\" t where rownum<={}) where r>{} ";
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, recordCount + initialPosition, initialPosition);
                }
                Console.log("oracle分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO MySQL
            } else if (dbType.toLowerCase().contains("mysql") || dbType.equalsIgnoreCase(Const.DB_STARROCKS) || dbType.equalsIgnoreCase(Const.DB_UNIDB)) {// 2021年2月24日18:24:46已测试
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                //String template = "select {} from {}.{} limit {},{}";
                String template = "select {} from `{}`.`{}`";
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName);
                } else {
                    template += " limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            }else if (dbType.equalsIgnoreCase(Const.DB_TIDB)) {
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                Console.log("连接TiDB数据库成功");
                // 查抽取字段数据
                String template = "select {} from `{}`.`{}`";
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName);
                } else {
                    template += " limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("teradata")) {
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                String template = "select " + in_fieldNameStr + "from" + tabName;
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = "";
                if (initialPosition >= 0 && recordCount > 0) {
                    //分页
                    sql = "SELECT " + in_fieldNameStr + " from (" +
                            "select " + in_fieldNameStr + ",ROW_NUMBER() OVER (ORDER BY " + fieldNameOne + ") as rn  FROM " + dbName + "." + tabName + " ) t " +
                            "WHERE rn BETWEEN " + initialPosition + " AND " + recordCount;
                    if (StringUtils.isNotBlank(limitingCondition)) {
                        sql += " " + limitingCondition;
                    }
                } else {
                    sql = template;
                    if (StringUtils.isNotBlank(limitingCondition)) {
                        sql += " " + limitingCondition;
                    }
                }

                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("sinodb")) {// 2021年2月24日18:24:46已测试
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                // 查抽取字段数据
                //String template = "select {} from {}.{} limit {},{}";
                String template = "select {} from {}";
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, tabName);
                } else {
                    template += " limit {}";
                    sql = StrUtil.format(template, in_fieldNameStr, tabName, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("dm")) {
                // TODO DM
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                //log.info("连接dm数据库成功");
                String template = "select {} from {}.{} limit {},{}";
                String sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, initialPosition, recordCount);
                //Console.log("dm分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO informix
            } else if (dbType.toLowerCase().equals("informix")) {
                Class.forName(driverName);
                //jdbc:informix-sqli://192.168.1.137:9003/mask:informixserver=tramsserver;
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接informix数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = in_fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String template = "select skip {} first {} {} from {}";
                String sql = StrUtil.format(template, initialPosition, recordCount, in_fieldNameStr, tabName);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO MariaDB
            } else if (dbType.toLowerCase().contains("mariadb")) {
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String template = "select {} from {}.{} limit {},{}";
                String sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, initialPosition, recordCount);
                Console.log("MariaDB分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().equals("gbase")) {// 2021年2月24日18:24:46已测试
                Class.forName(driverName);
//                con = (Connection) DriverManager.getConnection(
//                        url + "?useCursorFetch=true&defaultFetchSize=" + pageSize, userName, password);
                log.info("连接gbase数据库成功");
                // 查抽取字段数据
                String sql = "select " + " top " + initialPosition + " , " + recordCount + " " + in_fieldNameStr + " from " + tabName;
                Console.log("gbase分页查询语句: {}", sql);
                if (null != limitingCondition && !"".equals(limitingCondition)) {
                    sql += " " + limitingCondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("sqlserver")) {// 2021年2月24日18:24:46未测试
//				sqlserver表有两种权限，分为dbo和guest权限,dbo权限的下的表可以用表名直接查询,guest权限的下的表必须使用用户.表名进行查询,
//				dbo
//				database owner
//				数据库的创建者,创建该对象的用户
//				DBO是每个数据库的默认用户，具有所有者权限，即DbOwner ，通过用DBO作为所有者来定义对象，能够使数据库中的任何用户引用而不必提供所有者名称。
//				比如：你以User1登录进去并建表Table，而未指定DBO， 当用户User2登进去想访问Table时就得知道这个Table是你User1建立的，要写上User1.Table，如果他不知道是你建的，则访问会有问题。
//				如果你建表时把所有者指给了Dbo，则别的用户进来时写上Dbo.Table就行了，不必知道User1。
//				不光表是如此，视图等等数据库对象建立时也要如此才算是好。
//				guest
//				顾客   能够访问数据库中对象的数据,要求dbo分配权限给guest,一般给他查看的权限select
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接sqlserver数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + tabName + " order by uid offset " + initialPosition + " rows fetch next " + recordCount + " rows only";
                Console.log("sqlserver分页查询语句: {}", sql);

                if (null != limitingCondition && !"".equals(limitingCondition)) {
                    sql += " " + limitingCondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("db2")) {// 2021年2月24日18:24:46未测试
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接db2数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + tabName;
                if (null != limitingCondition && !"".equals(limitingCondition)) {
                    sql += " " + limitingCondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("hive")) {// 2021年2月24日18:24:46未测试
                Class.forName(driverName);
                Connection conn = null;
                HiveStatement hstmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", userName, password, url);
                    hstmt = HiveUtil.getStmt(conn);
                    if (null != dbName && !dbName.equals("")) {
                        String strUseBase = "use " + dbName;
                        hstmt.execute(strUseBase);
                    }
                    String sql = "select " + in_fieldNameStr + " from " + tabName;
                    if (null != limitingCondition && !"".equals(limitingCondition)) {
                        sql += " " + limitingCondition;
                    }
                    resultSet = hstmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(hstmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (dbType.toLowerCase().contains("hive2")) {// 2021年2月24日18:24:46未测试
                Class.forName(driverName);
                Connection conn = null;
                HiveStatement hstmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", userName, password, url);
                    hstmt = HiveUtil.getStmt(conn);
                    if (null != dbName && !dbName.equals("")) {
                        String strUseBase = "use " + dbName;
                        hstmt.execute(strUseBase);
                    }
                    String sql = "select " + in_fieldNameStr + " from " + tabName;
                    if (null != limitingCondition && !"".equals(limitingCondition)) {
                        sql += " " + limitingCondition;
                    }
                    resultSet = hstmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(hstmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (dbType.toLowerCase().contains("postgresql")) {
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接postgresql数据库成功");
                String template = "select {} from \"{}\"";
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, tabName);
                } else {
                    template += " OFFSET {} LIMIT {}";
                    sql = StrUtil.format(template, in_fieldNameStr, tabName, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("kingbase")) {
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接kingbase数据库成功");
                String template = "select {} from \"{}\"";
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, tabName);
                } else {
                    template += " OFFSET {} LIMIT {}";
                    sql = StrUtil.format(template, in_fieldNameStr, tabName, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.equalsIgnoreCase(Const.DB_OSCAR)){
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接oscar数据库成功");
                String template = "select {} from \"{}\"";
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, tabName);
                } else {
                    template += " OFFSET {} LIMIT {}";
                    sql = StrUtil.format(template, in_fieldNameStr, tabName, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains(Const.DB_GAUSS)) {// 2021年2月24日18:24:46未测试
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接Gauss数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + tabName;
                if (null != limitingCondition && !"".equals(limitingCondition)) {
                    sql += " " + limitingCondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("mongodb")) {// 2021年2月24日18:24:46不支持

            } else if (dbType.toLowerCase().equals("gbase8s")) {
                Class.forName(driverName);
                // jdbc:gbasedbt-sqli://192.168.1.71:9088/jdbcdb:GBASEDBTSERVER=gbase01;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=30
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接gbase8s数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = in_fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + tabName;
                if (null != limitingCondition && !"".equals(limitingCondition)) {
                    sql += " " + limitingCondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (dbType.toLowerCase().contains("bytehouse")) {
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(limitingCondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, limitingCondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, dbName, tabName, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);

            } else if (dbType.toLowerCase().contains("highgo")) {
                Class.forName(driverName);
                con = (Connection) DriverManager.getConnection(url, userName, password);
                log.info("连接highgo数据库成功");
                String template = "select {} from \"{}\"";
                if (StringUtils.isNotBlank(limitingCondition)) {
                    template += " " + limitingCondition;
                }
                String sql = null;
                if (template.contains("limit")) {
                    sql = StrUtil.format(template, in_fieldNameStr, tabName);
                } else {
                    template += " OFFSET {} LIMIT {}";
                    sql = StrUtil.format(template, in_fieldNameStr, tabName, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultSet;
    }

    /**
     * 获取脱敏行数据
     *
     * @param maskAlgoInfoMap
     * @param algorithmMap
     * @param fieldDataMap
     * @return
     */
    private Map<String, Object> getMaskRowData(Map<String, Object> maskAlgoInfoMap,
                                               HashMap<String, cn.god.mask.common.Algorithm> algorithmMap,
                                               Map<String, Object> fieldDataMap,
                                               List<Map<String, String>> fieldInfoList,
                                               MaskruleRepository maskruleRepository) {
        // 行数据脱敏
        for (String filedName : fieldDataMap.keySet()) {
            Object obj = fieldDataMap.get(filedName);
            String data = "";
            if (obj != null){
                List<Map<String, String>> collect = fieldInfoList.stream().filter(t -> t.get("FieldEName").equals(filedName)).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(collect)){
                    continue;
                }else {
                    data = fieldDataMap.get(filedName).toString();
                }
            }
            String result = data;
            // 该字段有数据才进行脱敏
            if (data != null && !"".equals(data)) {
                // 获取字段的脱敏算法信息 MD5$123,412
                String maskAlgoInfo = maskAlgoInfoMap.get(filedName) == null?"":maskAlgoInfoMap.get(filedName).toString();
                // 该字段有脱敏算法才进行脱敏
                if (maskAlgoInfo != null && !"".equals(maskAlgoInfo) && !"NUMKEEPAVERAGE$".equals(maskAlgoInfo)) {
                    // TODO 原始逻辑
                    /*String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                    String algo = AlgoParamInfo[0];
                    String param = "";
                    if (AlgoParamInfo.length > 1) {
                        param = AlgoParamInfo[1];
                    }
                    try {
                        // 数据:data算法名:algo参数:param
                        result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                    } catch (Exception e) {
                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algo + "参数:" + param);
                    } catch (Error e) {
                        log.error("执行脱敏出现错误,数据:" + data + "算法名:" + algo + "参数:" + param);
                    }
                    fieldDataMap.put(filedName, result);*/

                    // TODO 脱敏规则新增分段配置后逻辑
                    String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                    String isExtract = AlgoParamInfo[0];
                    if (Const.ALG_CONFIG_WAY_ENTIRETY.equals(isExtract)) {
                        String algo = AlgoParamInfo[1];
                        String param = "";
                        if (AlgoParamInfo.length > 2) {
                            param = AlgoParamInfo[2];
                        }
                        try {
                            // 数据:data算法名:algo参数:param
                            //判断当前执行是否使用自定义算法
                            Algorithm algorithm = algorithmMap.get(algo);
                            String sparefield1 = algorithm.getSparefield1();
                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                            }else {
                                //自定义算法
                                String jarname = algorithm.getJarname();//jar包名称
                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                String methodname = algorithm.getMethodname();//方法名
                                Map<String, String> paramMap = new HashMap<>();
                                paramMap.put("jarname",jarname);
                                paramMap.put("funcnamepath",funcnamepath);
                                paramMap.put("methodname",methodname);
                                paramMap.put("maskData",data);
                                result = AlgorithmUtils.invokeJarMethod(paramMap);
                            }
                        } catch (Exception e) {
                            log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algo + "参数:" + param);
                        }
                        fieldDataMap.put(filedName, result);
                    } else {
                        String tmpData = data;
                        String maskData = "";
                        String algo = "";
                        String param = "";
                        try {
                            String[] split = maskAlgoInfo.split(";");
                            for (String configPar : split) {
                                AlgoParamInfo = configPar.split("\\$");
                                algo = AlgoParamInfo[1];
                                param = "";
                                if (AlgoParamInfo.length > 2) {
                                    param = AlgoParamInfo[2];
                                }
                                String extract = AlgoParamInfo[3];
                                String[] extractArr = extract.split("-");
                                Integer startIndex = Integer.valueOf(extractArr[0]) - 1;
                                Integer endIndex = Integer.valueOf(extractArr[1]);
                                maskData = tmpData.substring(startIndex, endIndex);

                                // 数据:data算法名:algo参数:param
                                String middleData = "";
                                //判断当前执行是否使用自定义算法
                                Algorithm algorithm = algorithmMap.get(algo);
                                String sparefield1 = algorithm.getSparefield1();
                                if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                    middleData = MaskAlgFactory.getMaskData(maskData, algo, param, algorithmMap);
                                }else {
                                    //自定义算法
                                    String jarname = algorithm.getJarname();//jar包名称
                                    String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                    String methodname = algorithm.getMethodname();//方法名
                                    Map<String, String> paramMap = new HashMap<>();
                                    paramMap.put("jarname",jarname);
                                    paramMap.put("funcnamepath",funcnamepath);
                                    paramMap.put("methodname",methodname);
                                    paramMap.put("maskData",maskData);
                                    middleData = AlgorithmUtils.invokeJarMethod(paramMap);
                                }

                                String startData = tmpData.substring(0, startIndex);

                                String endData = tmpData.substring(endIndex, data.length());

                                tmpData = startData + middleData + endData;
                            }
                        } catch (Exception e) {
                            log.error("执行脱敏出现异常,原始数据：" + data + "，拆分后脱敏数据:" + maskData + "算法名:" + algo + "参数:" + param);
                        } finally {
                            //拆分后数据脱敏有异常，结束循环，不要继续脱敏，原本数据返回即可
                            result = tmpData;
                        }
                        fieldDataMap.put(filedName, result);
                    }
                }
            }
        }
        return fieldDataMap;
    }

    /**
     * 数据库对象迁移
     *
     * @param dbBatchTaskConfigDto
     */
    public void dbObjectMigration(DbBatchTaskConfigDto dbBatchTaskConfigDto) {
        Integer in_dataSourceId = dbBatchTaskConfigDto.getInputdatasourceid();
        Integer out_dataSourceId = dbBatchTaskConfigDto.getOutputdatasourceid();
        DatasourceDto in_datasourceDto = dataSourceService.findById(Long.valueOf(in_dataSourceId));
        String in_dbtype = in_datasourceDto.getType();
        String in_dbname = in_datasourceDto.getDbname();
        String in_url = in_datasourceDto.getSrcurl();
        String in_username = in_datasourceDto.getUsername();
        String in_password = in_datasourceDto.getPassword();
        if (StringUtils.isNotEmpty(in_password)) {
            in_password = AES.decrypt(in_password, Const.AES_SECRET_KEY);
        }


        DatasourceDto out_datasourceDto = dataSourceService.findById(Long.valueOf(out_dataSourceId));
        String out_dbtype = out_datasourceDto.getType();
        String out_dbname = out_datasourceDto.getDbname();
        String out_url = out_datasourceDto.getSrcurl();
        String out_username = out_datasourceDto.getUsername();
        String out_password = out_datasourceDto.getPassword();
        if (StringUtils.isNotEmpty(out_password)) {
            out_password = AES.decrypt(out_password, Const.AES_SECRET_KEY);
        }


        if (Const.DB_OBJECT_MIGRATION_YES.equals(dbBatchTaskConfigDto.getSparefield1()) && in_dbtype.equalsIgnoreCase(out_dbtype)) {
            log.info("开始迁移数据库对象=============================");
            DatabaseUtil inDataBaseUtil = DatabaseUtil.getDataBaseUtilByDBType(in_dbtype);
            //存储过程
            List<String> storedProcedureSql = inDataBaseUtil.getStoredProcedureSql(in_url, in_username, in_password, in_dbname);
            if (storedProcedureSql != null && storedProcedureSql.size() > 0) {
                DatabaseUtil.executeSql(storedProcedureSql, out_url, out_username, out_password);
            }

            //函数
            List<String> functionSql = inDataBaseUtil.getFunctionSql(in_url, in_username, in_password, in_dbname);
            if (functionSql != null && functionSql.size() > 0) {
                DatabaseUtil.executeSql(functionSql, out_url, out_username, out_password);
            }

            //触发器
            List<String> triggerSql = inDataBaseUtil.getTriggerSql(in_url, in_username, in_password, in_dbname);
            if (triggerSql != null && triggerSql.size() > 0) {
                DatabaseUtil.executeSql(triggerSql, out_url, out_username, out_password);
            }

            //视图
            List<String> viewSql = inDataBaseUtil.getViewSql(in_url, in_username, in_password, in_dbname, out_dbname);
            if (viewSql != null && viewSql.size() > 0) {
                DatabaseUtil.executeSql(viewSql, out_url, out_username, out_password);
            }

            //序列
            List<String> sequenceSql = inDataBaseUtil.getSequenceSql(in_url, in_username, in_password, in_dbname);
            if (sequenceSql != null && sequenceSql.size() > 0) {
                DatabaseUtil.executeSql(sequenceSql, out_url, out_username, out_password);
            }

            //索引
            List<String> indexesSql = inDataBaseUtil.getIndexesSql(in_url, in_username, in_password, in_dbname, out_dbname);
            if (indexesSql != null && indexesSql.size() > 0) {
                DatabaseUtil.executeSql(indexesSql, out_url, out_username, out_password);
            }
            log.info("数据库对象迁移完成=============================");
        }
    }

}
