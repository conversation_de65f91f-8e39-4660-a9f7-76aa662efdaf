package com.wzsec.modules.mask.rest;

import cn.hutool.core.util.ObjectUtil;
import com.wzsec.aop.log.Log;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.mask.domain.Maskrule;
import com.wzsec.modules.mask.service.AlgorithmService;
import com.wzsec.modules.mask.service.MaskruleService;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import com.wzsec.modules.mask.service.dto.MaskruleDto;
import com.wzsec.modules.mask.service.dto.MaskruleQueryCriteria;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2020-11-06
*/
// @Api(tags = "脱敏规则管理")
@RestController
@RequestMapping("/api/maskrule")
public class MaskruleController {

    private final MaskruleService maskruleService;
    private final AlgorithmService algorithmService;

    public MaskruleController(MaskruleService maskruleService,AlgorithmService algorithmService) {
        this.maskruleService = maskruleService;
        this.algorithmService = algorithmService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskrule:list')")
    public void download(HttpServletResponse response, MaskruleQueryCriteria criteria) throws IOException {
        maskruleService.download(maskruleService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询脱敏规则")
    // @ApiOperation("查询脱敏规则")
//    @AnonymousAccess
    @PreAuthorize("@el.check('maskrule:list')")
    public ResponseEntity<Object> getMaskrules(MaskruleQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskruleService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增脱敏规则")
    // @ApiOperation("新增脱敏规则")
    @PreAuthorize("@el.check('maskrule:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody Maskrule resources){
        String rulename = resources.getRulename();
        String rulecname = resources.getRulecname();
        int  rulecount = maskruleService.findRuleCountByRuleEName(rulename);
        if(rulecount > 0){
            throw new BadRequestException("规则名不能重复");
        }
        if(null!=rulecname && !"".equals(rulecname)){
            rulecount = maskruleService.findRuleCountByRuleCName(rulecname);
            if(rulecount > 0){
                throw new BadRequestException("规则中文名不能重复");
            }
        }
        if (Const.ALG_CONFIG_WAY_ENTIRETY.equals(resources.getSparefield2())){
            // 根据算法id查询算法
            AlgorithmDto algorithmDto = algorithmService.findById(resources.getAlgorithmid());
            // 判断算法是否需要参数
            int paramnum = Integer.parseInt(algorithmDto.getParamnum());
            if (paramnum>0) {
                if (StringUtils.isNotEmpty(resources.getParam())){
                    String[] paramArr = resources.getParam().split(",");
                    if (paramArr.length != paramnum) {
                        throw new RuntimeException("创建脱敏规则失败,请正确配置参数!");
                    }
                } else {
                    throw new RuntimeException("创建脱敏规则失败,请正确配置参数!");
                }
            } else {
                if (StringUtils.isNotEmpty(resources.getParam())){
                    throw new RuntimeException("创建脱敏规则失败,请正确配置参数!");
                }
            }
        }
        return new ResponseEntity<>(maskruleService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改脱敏规则")
    // @ApiOperation("修改脱敏规则")
    @PreAuthorize("@el.check('maskrule:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody Maskrule resources){
        String rulename = resources.getRulename();
        String rulecname = resources.getRulecname();
        MaskruleDto byId = maskruleService.findById(resources.getId());
        if(!byId.getRulename().equals(rulename)){
            int  rulecount = maskruleService.findRuleCountByRuleEName(rulename);
            if(rulecount > 0){
                throw new BadRequestException("规则名不能重复");
            }
        }
        if(null!=rulecname && !"".equals(rulecname)){
            if(!rulecname.equals(byId.getRulecname())){
                int rulecount = maskruleService.findRuleCountByRuleCName(rulecname);
                if(rulecount > 0){
                    throw new BadRequestException("规则中文名不能重复");
                }
            }
        }
        if (Const.ALG_CONFIG_WAY_ENTIRETY.equals(resources.getSparefield2())){
            // 根据算法id查询算法
            AlgorithmDto algorithmDto = algorithmService.findById(resources.getAlgorithmid());
            // 判断算法是否需要参数
            int paramnum = Integer.parseInt(algorithmDto.getParamnum());
            if (paramnum>0) {
                if (StringUtils.isNotEmpty(resources.getParam())){
                    String[] paramArr = resources.getParam().split(",");
                    if (paramArr.length != paramnum) {
                        throw new RuntimeException("创建脱敏规则失败,请正确配置参数!");
                    }
                } else {
                    throw new RuntimeException("创建脱敏规则失败,请正确配置参数!");
                }
            } else {
                if (StringUtils.isNotEmpty(resources.getParam())){
                    throw new RuntimeException("创建脱敏规则失败,请正确配置参数!");
                }
            }
        }
        maskruleService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除脱敏规则")
    // @ApiOperation("删除脱敏规则")
    @PreAuthorize("@el.check('maskrule:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskruleService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("返回所有脱敏规则信息")
    // @ApiOperation("返回所有脱敏规则信息")
    @PostMapping(value = "/getMaskRules")
    public ResponseEntity<Object> getMaskRules(String dataName) {
        return new ResponseEntity<>(maskruleService.getThreeMaskRules(dataName), HttpStatus.OK);
    }

    @Log("根据规则id查询规则")
    // @ApiOperation("根据规则id查询规则")
    @PostMapping(value = "/getRuleInfoStrsByRuleId/{id}")
    @PreAuthorize("@el.check('maskrule:list')")
    public ResponseEntity<Object> getRuleInfoStrsByRuleId(@PathVariable Integer id) {
        //已过滤工作空间
        return new ResponseEntity<>(maskruleService.getRuleInfoStrsByRuleId(id), HttpStatus.OK);
    }

    @GetMapping(value = "/static")
    @Log("获取静态脱敏规则")
    public ResponseEntity<Object> findStaticDesensitizationRules(@RequestParam(value = "appKey") String appKey) {
        if (!appKey.equals("43aa4a05b55dafc2895d5fa354b1e818")) {
            throw new BadRequestException("接口拒绝访问");
        }
        return new ResponseEntity<>(maskruleService.findStaticDesensitizationRules(), HttpStatus.OK);
    }


    @Log("返回匿名化脱敏规则信息")
    @PostMapping(value = "/getAnonymizationMaskRules")
    public ResponseEntity<Object> getAnonymizationMaskRules(String dataName) {
        return new ResponseEntity<>(maskruleService.getAnonymizationMaskRules(dataName), HttpStatus.OK);
    }

    @Log("测试脱敏规则")
    @PutMapping("/queryOne")
    public ResponseEntity<Object> queryOne(@RequestBody Map<String,Object> map){
        Integer id = Integer.valueOf(map.get("id").toString());
        String maskTest = ObjectUtil.isEmpty(map.get("maskTest"))?null:map.get("maskTest").toString();
        return new ResponseEntity<>(maskruleService.queryOne(id, maskTest),HttpStatus.OK);
    }
}
