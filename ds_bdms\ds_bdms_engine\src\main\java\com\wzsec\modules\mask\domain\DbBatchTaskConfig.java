package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2023-02-07
*/
@Entity
@Data
@Table(name="sdd_mask_dbbatchtaskconfig")
public class DbBatchTaskConfig implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 输入数据源 */
    @Column(name = "inputdatasourceid")
    private Integer inputdatasourceid;

    /** 任务状态(0启用,1禁用) */
    @Column(name = "state")
    private String state;

    /** 输出类型（1库表，2文件） */
    @Column(name = "outputtype")
    private String outputtype;

    /** 输出数据源 */
    @Column(name = "outputdatasourceid")
    private Integer outputdatasourceid;

    /** 输出目录 */
    @Column(name = "outputdirectory")
    private String outputdirectory;

    /** 异常目录 */
    @Column(name = "errordirectory")
    private String errordirectory;

    /** 提交方式（1手动提交，2定时执行） */
    @Column(name = "submittype")
    private String submittype;

    /** 执行时间cron表达式 */
    @Column(name = "cron")
    private String cron;

    /** 任务执行引擎 */
    @Column(name = "taskexecuteengine")
    private String taskexecuteengine;

    /** 脱敏后表名 0与原表名一致 1表名后拼接MASK+时间戳 */
    @Column(name = "masktablename")
    private String masktablename;

    /**
     *  脱敏方式 1全量脱敏，2增量脱敏
     */
    @Column(name = "maskway")
    private String maskway;

    /** 任务状态：0 :初始创建1:提交失败2:执行中3:执行成功4:执行失败 */
    @Column(name = "executionstate")
    private String executionstate;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    private Timestamp updatetime;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(DbBatchTaskConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}