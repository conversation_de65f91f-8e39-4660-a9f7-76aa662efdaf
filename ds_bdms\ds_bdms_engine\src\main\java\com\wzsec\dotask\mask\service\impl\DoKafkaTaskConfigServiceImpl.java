package com.wzsec.dotask.mask.service.impl;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import com.wzsec.dotask.mask.service.DoKafkaTaskConfigService;
import com.wzsec.dotask.mask.service.excute.kafka.KafkaUtil;
import com.wzsec.dotask.mask.service.excute.kafka.SRedisRepository;
import com.wzsec.modules.mask.domain.EngineServer;
import com.wzsec.modules.mask.domain.KafkaTaskConfig;
import com.wzsec.modules.mask.domain.KafkaTaskResult;
import com.wzsec.modules.mask.repository.EngineServerRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.KafkaTaskConfigDto;
import com.wzsec.modules.mask.service.dto.MaskStrategyTableDto;
import com.wzsec.modules.mask.service.mapper.KafkaTaskConfigMapper;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.utils.AlgorithmUtils;
import com.wzsec.utils.Const;
import com.wzsec.utils.ConstEngine;
import com.wzsec.utils.DateUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

/**
 * <AUTHOR>
 * @discription
 * @date 2020/12/3
 */
@Service
public class DoKafkaTaskConfigServiceImpl implements DoKafkaTaskConfigService {

    private final static Logger log = LoggerFactory.getLogger(DoKafkaTaskConfigServiceImpl.class);

    private SRedisRepository sRedisRepository;

    private KafkaTaskConfigService kafkaTaskConfigService;

    private KafkaTaskResultService kafkaTaskResultService;

    private MaskStrategyTableService maskStrategyTableService;

    private MaskStrategyFieldService maskStrategyFieldService;

    private AlgorithmService algorithmService;

    private KafkaTaskConfigMapper kafkaTaskConfigMapper;

    private MaskTaskresultrecordsService maskTaskresultrecordsService;

    private final EngineServerRepository engineServerRepository;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    @Value("${spring.profiles.active}")
    private String active;

    public DoKafkaTaskConfigServiceImpl(SRedisRepository sRedisRepository, KafkaTaskConfigService kafkaTaskConfigService, KafkaTaskResultService kafkaTaskResultService,
                                        MaskStrategyTableService maskStrategyTableService, MaskStrategyFieldService maskStrategyFieldService, AlgorithmService algorithmService,
                                        KafkaTaskConfigMapper kafkaTaskConfigMapper,MaskTaskresultrecordsService maskTaskresultrecordsService,
                                        EngineServerRepository engineServerRepository,SdkApplyconfigRepository sdkApplyconfigRepository,SdkOperationrecordRepository sdkOperationrecordRepository) {
        this.sRedisRepository = sRedisRepository;
        this.kafkaTaskConfigService = kafkaTaskConfigService;
        this.kafkaTaskResultService = kafkaTaskResultService;
        this.maskStrategyTableService = maskStrategyTableService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.algorithmService = algorithmService;
        this.kafkaTaskResultService = kafkaTaskResultService;
        this.kafkaTaskConfigMapper = kafkaTaskConfigMapper;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.engineServerRepository = engineServerRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }

    // 非必选key
    public static final String[] fieldArr = {"timeConfidence", "posAccuracy", "posConfidence", "angle", "motionCfd",
            "safetyExt", "emergencyExt"};

    /**
     * @param id:任务ID
     * @param submituser:提交人
     * @return void
     * @Description 执行Kafka脱敏任务，可执行多个topic
     * <AUTHOR>
     * @date 2021年2月26日15:14:09
     */
    @Async
    @Override
    public void execution(Long id, String submituser) {
        KafkaTaskConfigDto kafkaTaskConfigDto = kafkaTaskConfigService.findById(id);
        KafkaTaskConfig kafkaTaskConfig = kafkaTaskConfigMapper.toEntity(kafkaTaskConfigDto);
        String eip = null, eport = null;
        try {
            if (Const.TASK_STATE_OFF.equals(kafkaTaskConfigDto.getIsvalid())) {
                throw new Exception("id【" + id + "】该任务无效");
            }
            if (Const.KAFKA_TASK_STATUS_EXECUTION.equals(kafkaTaskConfigDto.getStatus())) {
                throw new Exception("id【" + id + "】该任务正在执行中");
            }
            String topic = kafkaTaskConfigDto.getDatatopic();
            int count = kafkaTaskConfigService.getRunningTaskCountByTopic(topic);
            if (count > 0) {
                throw new Exception("该topic【" + topic + "】已存在运行中任务");
            }

            String engine = kafkaTaskConfigDto.getSparefield2();
            String[] ipPort = engine.split("-")[1].trim().split(":");
            eip = ipPort[0];
            eport = ipPort[1];

            SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(eip+":"+eport);

            //TODO 将所使用的引擎，任务负载数+1
            if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
                engineServerRepository.addKingbaseCountByIpPort(eip, eport);
            }else{
                engineServerRepository.addCountByIpPort(eip, eport);
            }

            MaskStrategyTableDto maskStrategyTableDto = maskStrategyTableService.findById(kafkaTaskConfig.getStrategyid().intValue());
            String strategyName = maskStrategyTableDto.getStrategyname();
            List<Map<String, Object>> maskAlgoInfoList = maskStrategyFieldService.getMaskAlgoInfoByStrategyId(kafkaTaskConfig.getStrategyid().toString());
            // 获取策略
            Map<String, String> maskAlgoInfoMap = getMaskAlgoInfo(maskAlgoInfoList);
            System.out.println(JSON.toJSONString(maskAlgoInfoMap));

            // 更改任务状态
            kafkaTaskConfig.setStatus(Const.KAFKA_TASK_STATUS_EXECUTION); // 更改任务状态为进行中
            kafkaTaskConfigService.update(kafkaTaskConfig);
            log.error("任务ID【" + id + "】，开始执行Kafka脱敏任务");
            // 消费kafka消息进行脱敏，暂时不能停止(2020年12月4日16:07:46)
            consumeKafkaMessage(kafkaTaskConfig, strategyName, maskAlgoInfoList, maskAlgoInfoMap,sdkApplyconfig);
            log.error("任务ID【" + id + "】，结束执行Kafka脱敏任务");
        } catch (Exception e) {
            log.error("任务ID【" + id + "】执行中出现异常，任务停止，错误消息：" + e.getMessage());
            // 更改任务状态
            kafkaTaskConfig.setStatus(Const.KAFKA_TASK_STATUS_KILL); // 更改任务状态为已停止
            kafkaTaskConfigService.update(kafkaTaskConfig);
            e.printStackTrace();
        } finally {
            if (eip != null && eport != null){
                //TODO 将所使用的引擎，任务负载数-1
                if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
                    engineServerRepository.reduceKingbaseCountByIpPort(eip, eport);
                }else {
                    engineServerRepository.reduceCountByIpPort(eip, eport);
                }
            }
        }
    }

    /**
     * @param id:任务ID
     * @param submituser:提交人
     * @Description 执行Kafka脱敏任务，可执行多个topic
     * <AUTHOR>
     * @date 2021年2月26日15:14:09
     */
    @Async
    @Override
    public void stopTask(Long id, String submituser) {
        KafkaUtil.isRunningMap.remove(id); // 移除该任务id对应的任务状态
        KafkaTaskConfigDto kafkaTaskConfigDto = kafkaTaskConfigService.findById(id);
        KafkaTaskConfig kafkaTaskConfig = kafkaTaskConfigMapper.toEntity(kafkaTaskConfigDto);
        // 更改任务状态
        kafkaTaskConfig.setStatus(Const.KAFKA_TASK_STATUS_KILL); // 更改任务状态为已停止
        kafkaTaskConfigService.update(kafkaTaskConfig);
    }

    /**
     * @param kafkaTaskConfig
     * @param maskAlgoInfoList
     * @param maskAlgoInfoMap
     * @return void
     * @Description 消费Kafka消息，主要方法，核心
     * <AUTHOR>
     * @date 2020年12月7日 下午1:50:16
     */
    public void consumeKafkaMessage(KafkaTaskConfig kafkaTaskConfig, String strategyName, List<Map<String, Object>> maskAlgoInfoList,
                                    Map<String, String> maskAlgoInfoMap,SdkApplyconfig sdkApplyconfig) {
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();
        String topic = kafkaTaskConfig.getDatatopic();
        Properties props = KafkaUtil.initConfig();
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(Arrays.asList(topic));
        long startTime = System.currentTimeMillis(), endTime; // 批次开始结束时间
        long cycle = Long.parseLong(ConstEngine.sddEngineConfs.get("kafka.mask.result.cycle")) * 1000; // 10秒打印一次正在消费的topic
        long lineCount = 0; // 处理行数
        String redisKey = null, beforeMaskLine = null, afterMaskLine = null;
        try {
            KafkaUtil.isRunningMap.put(kafkaTaskConfig.getId(), true); // 设置任务状态为开始
            while (KafkaUtil.isRunningMap.containsKey(kafkaTaskConfig.getId())
                    && KafkaUtil.isRunningMap.get(kafkaTaskConfig.getId())) {
                ConsumerRecords<String, String> records = consumer.poll(1000);
                redisKey = kafkaTaskConfig.getRediskey();
                if (records != null && records.count() > 0) {
                    for (ConsumerRecord<String, String> record : records) {
                        if (Const.KAFKA_REDISKEY_0.equals(kafkaTaskConfig.getRediskey()))
                            redisKey = DateUtil.getNowTimeString("yyyyMMdd-HH"); // 小时
                        else if (Const.KAFKA_REDISKEY_1.equals(kafkaTaskConfig.getRediskey()))
                            redisKey = DateUtil.getNowTimeString("yyyyMMdd-HHmm"); // 分钟

                        beforeMaskLine = record.value();
                        afterMaskLine = handleMessage(beforeMaskLine, maskAlgoInfoMap, redisKey);
                        lineCount++; // 处理行数+1
                    }
                }
                endTime = System.currentTimeMillis();
                if (endTime >= startTime + cycle) {
                    System.out.println("正在消费topic:" + topic + "，Kafka脱敏正在执行中...");
                    saveResult(kafkaTaskConfig, strategyName, maskAlgoInfoList, startTime, endTime, lineCount, redisKey,
                            beforeMaskLine, afterMaskLine, Const.TASK_RESULT_EXECUTE_SUCCESS,Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE);
                    sdkOperationrecord.setObjectname(kafkaTaskConfig.getTaskname());
                    sdkOperationrecord.setOperation("Kafka脱敏任务执行成功");
                    startTime = endTime;
                    lineCount = 0;
                    redisKey = beforeMaskLine = afterMaskLine = null;
                }
            }
        } catch (Exception ex) {
            throw ex;
        } finally {
            consumer.close();
            String taskStatus = Const.TASK_RESULT_EXECUTE_SUCCESS;
            String taskMessage = null;
            endTime = System.currentTimeMillis();
            // 正常停止
            if (!KafkaUtil.isRunningMap.containsKey(kafkaTaskConfig.getId())) { // 说明任务已经停止，需要记录最后一次任务的结果
                System.out.println("停止消费topic:" + topic + "，Kafka脱敏已停止，正常停止");
                taskStatus = Const.TASK_RESULT_EXECUTE_SUCCESS;
                taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE;
                sdkOperationrecord.setObjectname(kafkaTaskConfig.getTaskname());
                sdkOperationrecord.setOperation("Kafka脱敏任务执行成功");
            } else {
                // 非正常停止
                System.out.println("停止消费topic:" + topic + "，Kafka脱敏已停止，异常停止");
                taskStatus = Const.TASK_RESULT_EXECUTE_FAIL;
                taskMessage = Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE;
                sdkOperationrecord.setObjectname(kafkaTaskConfig.getTaskname());
                sdkOperationrecord.setOperation("Kafka脱敏任务执行失败");
            }
            //插入SDK操作记录
            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
            sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_KAFKA);
            sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
            sdkOperationrecordRepository.save(sdkOperationrecord);

            saveResult(kafkaTaskConfig, strategyName, maskAlgoInfoList, startTime, endTime, lineCount, redisKey, beforeMaskLine,
                    afterMaskLine, taskStatus,taskMessage);
        }
    }

    /**
     * @param message:kafka中消息
     * @param maskAlgoInfoMap:策略
     * @Description 处理从kafka消费到的数据
     * <AUTHOR>
     * @date 2020年12月4日 下午4:01:45
     */
    public String handleMessage(String message, Map<String, String> maskAlgoInfoMap, String redisKey) {
        String maskLine = message;
        System.out.println("消费到数据：" + message);
        String prefixStr = message.substring(0, message.indexOf(Const.KAFKA_MESSAGE_PREFIX)); // 存在前缀
        String messageStr = message.substring(message.indexOf(Const.KAFKA_MESSAGE_PREFIX));
        Map<String, String> fieldDataMap = JSON.parseObject(messageStr, new HashMap<String, String>().getClass());
        for (String field : fieldArr) { // 判断是否包含所有字段，不包含添加空值key
            if (!fieldDataMap.containsKey(field)) {
                fieldDataMap.put(field, "");
            }
        }
        // String data = JSON.toJSONString(fieldDataMap,
        // SerializerFeature.WriteMapNullValue);
        // System.out.println("脱敏前数据(已填充缺失KEY)：" + data);
        // 行进行脱敏
        Map<String, String> fieldDataMaskMap = getMaskDataMap(fieldDataMap, maskAlgoInfoMap);
        maskLine = JSON.toJSONString(fieldDataMaskMap, SerializerFeature.WriteMapNullValue);
        // System.out.println("脱敏后数据(抽取脱敏后数据)：" + maskLine);
        // 输出到Redis
        maskLine = prefixStr + maskLine;
        sRedisRepository.appendValue(redisKey, maskLine + Const.KAFKA_MESSAGE_NEWLINE); // 如果key不存在则新建，如果存在则在末尾追加
        long timeout = Long.parseLong(ConstEngine.sddEngineConfs.get("kafka.mask.data.timeout")); // 设置当前key失效时间
        sRedisRepository.setTimeOut(timeout, redisKey);
        return maskLine;
    }

    /**
     * @param startTime:批次开始时间
     * @param endTime:批次结束时间
     * @param lineCount:处理行数
     * @param redisKey:redis存储key
     * @param maskAlgoInfoList:策略详情集合
     * @param beforeMaskLine:脱敏前数据1行
     * @param afterMaskLine:脱敏后数据1行
     * @param taskStatus:结果状态
     * @return void
     * @Description 保存kafka脱敏结果
     * <AUTHOR>
     * @date 2020年12月7日 下午2:36:38
     */
    public void saveResult(KafkaTaskConfig kafkaTaskConfig, String strategyName, List<Map<String, Object>> maskAlgoInfoList, long startTime,
                           long endTime, long lineCount, String redisKey, String beforeMaskLine, String afterMaskLine,
                           String taskStatus,String taskMessage) {
        KafkaTaskResult kafkaTaskResult = new KafkaTaskResult();
        kafkaTaskResult.setTaskname(kafkaTaskConfig.getTaskname());
        kafkaTaskResult.setDbname(kafkaTaskConfig.getDbname());
        kafkaTaskResult.setTabname(kafkaTaskConfig.getTbname());
        kafkaTaskResult.setStarttime(DateUtil.long2Timestamp(startTime));
        kafkaTaskResult.setEndtime(DateUtil.long2Timestamp(endTime));
        kafkaTaskResult.setMaskline(String.valueOf(lineCount));
        kafkaTaskResult.setStrategyname(strategyName);
        kafkaTaskResult.setStrategydetail(JSON.toJSONString(maskAlgoInfoList));
        kafkaTaskResult.setRediskey(redisKey);
        kafkaTaskResult.setTaskstatus(taskStatus);
        kafkaTaskResult.setBeforemaskdata(beforeMaskLine);
        kafkaTaskResult.setAftermaskdata(afterMaskLine);
        kafkaTaskResultService.create(kafkaTaskResult);

        MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
        maskTaskresultrecords.setTaskname(kafkaTaskResult.getTaskname());
        maskTaskresultrecords.setTasktype(Const.MASK_TASK_KAFKA);
        maskTaskresultrecords.setTaskstatus(taskMessage);
        maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", String.valueOf(startTime)));
        maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", String.valueOf(endTime)));
        maskTaskresultrecordsService.create(maskTaskresultrecords);

    }

    /**
     * @param fieldDataMap:  数据
     * @param maskAlgoInfoMap:策略
     * @return Map<String               ,               String>:抽取脱敏后数据
     * @Description 对抽取字段中脱敏字段的数据进行脱敏并返回脱敏后数据
     * <AUTHOR>
     * @date 2020年12月4日 上午9:50:24
     */
    public Map<String, String> getMaskDataMap(Map<String, String> fieldDataMap, Map<String, String> maskAlgoInfoMap) {
        HashMap<String, Algorithm> algorithmMap = algorithmService.getAlgorithmByEName();
        Map<String, String> fieldDataMaskMap = new HashMap<>();
        for (String filedName : maskAlgoInfoMap.keySet()) { // 只获取抽取和脱敏的字段
            Object dataObj = fieldDataMap.get(filedName); // 获取待脱敏数据
            String data = dataObj != null ? dataObj.toString() : null;
            String result = data; // 脱敏失败返回原始数据
            // 该字段有数据才进行脱敏
            if (data != null && !"".equals(data)) {
                // 获取字段的脱敏算法信息 MD5$123,412
                String maskAlgoInfo = maskAlgoInfoMap.get(filedName);
                // 该字段有脱敏算法才进行脱敏
                if (maskAlgoInfo != null && !"".equals(maskAlgoInfo)) {
                    String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                    String algo = AlgoParamInfo[0];
                    String param = "";
                    if (AlgoParamInfo.length > 1) {
                        param = AlgoParamInfo[1];
                    }
                    try {
                        // 数据:data算法名:algo参数:param
                        Algorithm algorithm = algorithmMap.get(algo);
                        String sparefield1 = algorithm.getSparefield1();
                        if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                            result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                        }else {
                            //自定义算法
                            String jarname = algorithm.getJarname();//jar包名称
                            String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                            String methodname = algorithm.getMethodname();//方法名
                            Map<String, String> paramMap = new HashMap<>();
                            paramMap.put("jarname",jarname);
                            paramMap.put("funcnamepath",funcnamepath);
                            paramMap.put("methodname",methodname);
                            paramMap.put("maskData",data);
                            result = AlgorithmUtils.invokeJarMethod(paramMap);
                        }
                    } catch (Exception e) {
                        log.error("执行脱敏出现异常,字段:" + filedName + ",数据:" + data + ",算法名:" + algo + ",参数:" + param);
                        e.printStackTrace();
                    }
                }
            }
            fieldDataMaskMap.put(filedName, result);
        }
        return fieldDataMaskMap;
    }

    /**
     * @param maskAlgoInfoList
     * @return Map<String               ,               String>
     * @Description 获取字段脱敏算法信息根据策略名
     * <AUTHOR>
     * @date 2020年12月4日 上午9:42:48
     */
    public Map<String, String> getMaskAlgoInfo(List<Map<String, Object>> maskAlgoInfoList) {
        Map<String, String> maskAlgoInfoMap = new LinkedHashMap<String, String>();
        try {
            for (Map<String, Object> map : maskAlgoInfoList) {
                String strAlgoInfo = "";
                if (map.get("algenglishname") != null && !"".equals(map.get("algenglishname"))) {
                    StringBuilder fieldStrBuilder = new StringBuilder();
                    fieldStrBuilder.append(map.get("algenglishname"));
                    fieldStrBuilder.append("$");
                    if (null != map.get("param") && !"".equals(map.get("param"))) {
                        fieldStrBuilder.append(map.get("param"));  // 参数
                        fieldStrBuilder.append(",");
                    }
                    if (null != map.get("secretkey") && !"".equals(map.get("secretkey"))) {
                        fieldStrBuilder.append(map.get("secretkey"));  // 密钥
                    }
                    strAlgoInfo = fieldStrBuilder.toString();
                    if (strAlgoInfo.endsWith(",")) {  // 如果以","结尾，去除","
                        strAlgoInfo = strAlgoInfo.substring(0, strAlgoInfo.lastIndexOf(","));
                    }
                }
                maskAlgoInfoMap.put((String) map.get("fieldename"), strAlgoInfo);
            }
        } catch (Exception ex) {
            log.error("获取字段脱敏算法信息出现异常");
        }
        return maskAlgoInfoMap;
    }
}
