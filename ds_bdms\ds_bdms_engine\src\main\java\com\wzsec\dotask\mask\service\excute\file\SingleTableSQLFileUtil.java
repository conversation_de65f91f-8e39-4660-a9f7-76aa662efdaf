package com.wzsec.dotask.mask.service.excute.file;

import com.wzsec.utils.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 单表sql文件Util
 */
@Data
@Slf4j
public class SingleTableSQLFileUtil {

    private String dropTableSql;//删表sql
    private String createTableSql;//建表sql
    private List<String> insertSql;//新增sql
    private List<String> restsSql;//其他sql

    /**
     * 读取insert数据，使用指定符号分隔
     *
     * @param splitStr
     * @return
     */
    public ArrayList<String> readInsertDataStr(String splitStr) {
        ArrayList<String> list = new ArrayList<>();
        if (getInsertSql()!=null){
            for (String sql : getInsertSql()) {
                int index = sql.indexOf("(") + 1;
                int lastIndex = sql.lastIndexOf(")");
                sql = sql.substring(index, lastIndex);
                String lineData = "";
                String[] split = sql.split(",");
                for (int i = 0; i < split.length; i++) {
                    String data = split[i].trim();
                    // 'data' 格式数据需要拆掉单引号，数字类型、NULL等数据不需要拆分
                /*if (StringUtils.isNotEmpty(data) && data.length()>2){
                    //最前面一位与最后一位如果是单引号，去掉该符号
                    String startStr = data.substring(0, 1);
                    String endStr = data.substring(data.length() - 1, data.length());
                    if ("'".equals(startStr) && "'".equals(endStr)) {
                        data = data.substring(1, data.length() - 1);
                    }
                }*/
                    lineData += data + splitStr;
                }
                list.add(lineData.substring(0, lineData.length() - 1));
            }
        }
        return list;
    }

    /**
     * 建表sql更新，通过脱敏字段的下标改变原始脱敏建表语句
     *
     * @param maskFieldIndex
     * @return
     */
    public String createTableSqlUpdate(List<Integer> maskFieldIndex) {
        String createTableSql = getMysqlDBCreateTableSql(maskFieldIndex);
        if (createTableSql == null) {
            createTableSql = getOracleDBCreateTableSql(maskFieldIndex);
        }
        return createTableSql;
    }

    /**
     * 获取mysql数据库建表sql
     * @param maskFieldIndex
     * @return
     */
    public String getMysqlDBCreateTableSql(List<Integer> maskFieldIndex) {
        String sql = null;
        try {
            String crateTableSql = this.createTableSql;
            Integer startIndex = crateTableSql.indexOf("(") + 1;
            Integer lastIndex = crateTableSql.lastIndexOf(")");
            String start_sql = crateTableSql.substring(0, startIndex);
            String end_sql = crateTableSql.substring(lastIndex, crateTableSql.length());
            //最前方保留两个空格，建表sql输出样式对齐
            String middle_sql = "  " + crateTableSql.substring(startIndex, lastIndex).trim();
            String[] fileMiddleSql = middle_sql.split("\\n");

            //拆出字段名、字段类型
            List<String> fieldNameList = new ArrayList<>();
            List<String> fieldTypeList = new ArrayList<>();
            for (String s : fileMiddleSql) {
                String str = "";
                if (s.endsWith(",")) {
                    str = s.substring(0, s.length() - 1).trim();
                } else {
                    str = s.trim();
                }
                String fieldName = str.substring(1, str.lastIndexOf("`"));
                if (str.startsWith("`")) {
                    fieldNameList.add(fieldName);

                    int index1 = str.indexOf(" ") + 1;
                    String str2 = str.substring(index1);
                    int index3 = str2.indexOf(" ");
                    String fieldType = str2.substring(0, index3);
                    fieldTypeList.add(fieldType);
                }
            }

            String primaryKeyStr = "PRIMARY KEY";//主键
            String foreignKeyStr = "FOREIGN KEY";//外键
            String uniqueIndexStr = "UNIQUE INDEX";//唯一索引

            String autoIncrementStr = "AUTO_INCREMENT";
            String unsignedStr = "unsigned";

            // 检查当前表 所有列字段总长度乘算过后是否大于 65535
            // UTF-8编码下列字段总长度*3，gbk编码下列字段中长度*2
            Integer fieldLength = 0;
            boolean fieldFlag = false;
            for (String fieldType : fieldTypeList) {
                if (fieldType.contains("(") && fieldType.contains(")")) {
                    int begIndex = fieldType.indexOf("(") + 1;
                    int endIndex = fieldType.indexOf(")");
                    //数据类型可能是 decimal(10,0) 这种，不需要统计长度
                    int i = fieldType.substring(begIndex, endIndex).indexOf(",");
                    if (i == -1) {
                        Integer integer = Integer.valueOf(fieldType.substring(begIndex, endIndex));
                        fieldLength += integer;
                    }
                }
            }
            // 脱敏后的表字段长度会改动，改动幅度估为原本字段长度*3(utf8编码) *2(脱敏字段长度翻倍)
            // 超过此幅度该表的脱敏字段全部置为text类型
            if (fieldLength * 3 * 2 >= 65535) {
                fieldFlag = true;
            }

            List<String> maskFieldName = new ArrayList<>();
            for (int i = 0; i < fileMiddleSql.length; i++) {
                String fieldSql = fileMiddleSql[i];
                if (maskFieldIndex.contains(i) && i < fieldTypeList.size()) {
                    String oriFieldType = fieldTypeList.get(i);
                    String maskFieldType = getModificationFiledType(oriFieldType, fieldFlag);

                    String fieldName = "`" + fieldNameList.get(i) + "`";
                    //修改脱敏字段类型,字段名可能和字段类型有重叠(例如updatetime字段名与datetime字段类型)，需要先去除字段名，再替换字段类型
                    fieldSql = fieldSql.replace(fieldName,"{}").replace(oriFieldType, maskFieldType);
                    //更改完字段类型后，再将字段名拼回去改回去
                    fieldSql = fieldSql.replace("{}",fieldName);
                    //脱敏字段不能设置自增
                    if (fieldSql.contains(autoIncrementStr)) {
                        fieldSql = fieldSql.replace(autoIncrementStr, "");
                    }
                    //脱敏字段去除unsigned关键字，该关键字只能对数字类型生效，脱敏后数据变成其他类型建表会报错
                    if (fieldSql.contains(unsignedStr)) {
                        fieldSql = fieldSql.replace(unsignedStr, "");
                    }
                    //记录脱敏字段的字段名
                    maskFieldName.add(fieldName);
                } else {

                    //脱敏字段不能为主键、不能设置唯一索引
                    for (String fieldName : maskFieldName) {
                        if (fieldSql.contains(fieldName) &&
                                (fieldSql.contains(primaryKeyStr) || fieldSql.contains(uniqueIndexStr))) {
                            fieldSql = "";
                        }
                    }
                    //去掉所有外键设置，不然建表时没有对应关联表会报错
                    if (fieldSql.contains(foreignKeyStr)) {
                        fieldSql = "";
                    }
                }
                fileMiddleSql[i] = fieldSql;
            }

            StringBuffer buffer = new StringBuffer();
            buffer.append(start_sql + "\n");

            //拼接字段sql
            String fieldSqlStr = "";
            for (int i = 0; i < fileMiddleSql.length; i++) {
                String fileSql = fileMiddleSql[i];
                if (StringUtils.isNotEmpty(fileSql)) {
                    fieldSqlStr += fileMiddleSql[i] + "\n";
                }
            }
            //最后一位是换行符，检查倒数第二位是否是逗号，是逗号则去掉
            if (",".equals(fieldSqlStr.substring(fieldSqlStr.length() - 2, fieldSqlStr.length() - 1))) {
                fieldSqlStr = fieldSqlStr.substring(0, fieldSqlStr.length() - 2) + "\n";
            }
            buffer.append(fieldSqlStr);
            buffer.append(end_sql + "\n");
            sql = buffer.toString();
        } catch (Exception ex) {
            log.info("Mysql建表方式解析失败，错误：" + ex.getMessage());
            ex.printStackTrace();
            sql = null;
        } finally {
            return sql;
        }
    }

    /**
     * 获取oracle数据库建表sql
     * @param maskFieldIndex
     * @return
     */
    public String getOracleDBCreateTableSql(List<Integer> maskFieldIndex) {
        String sql = null;
        try {
            String crateTableSql = this.createTableSql;
            Integer startIndex = crateTableSql.indexOf("(") + 1;
            Integer lastIndex = crateTableSql.lastIndexOf(")");
            String start_sql = crateTableSql.substring(0, startIndex);
            String end_sql = crateTableSql.substring(lastIndex, crateTableSql.length());
            //最前方保留两个空格，建表sql输出样式对齐
            String middle_sql = "  " + crateTableSql.substring(startIndex, lastIndex).trim();
            String[] fileMiddleSql = middle_sql.split("\\n");

            //拆出字段名、字段类型
            List<String> fieldNameList = new ArrayList<>();
            List<String> fieldTypeList = new ArrayList<>();
            boolean isField = false;
            for (String s : fileMiddleSql) {
                String str = "";
                if (s.endsWith(",")) {
                    str = s.substring(0, s.length() - 1).trim();
                } else {
                    str = s.trim();
                    //如果不是逗号结尾了，说明当前已经是最后一个字段
                    isField = true;
                }
                String fieldName = str.substring(1, str.lastIndexOf("\""));
                if (str.startsWith("\"")) {
                    fieldNameList.add(fieldName);

                    int index1 = str.indexOf(" ") + 1;
                    String str2 = str.substring(index1);

                    //拆法1 "TEST" NUMBER NOT NULL, 可根据空格拆分数据类型
                    int index3 = str2.indexOf(" ");
                    if (index3==-1){
                        //拆法2  "TES" NVARCHAR2(20),可根据括号拆分数据类型
                        index3 = str2.indexOf(")") + 1;
                        if (index3 == -1) {
                            //拆法3  "TES" DATE,可根据逗号拆分数据类型
                            index3 = str2.indexOf(",");
                        }
                    }
                    String fieldType = str2.substring(0, index3);
                    fieldTypeList.add(fieldType);
                }

                //最后一个字段添加完后，结束循环
                if (isField){
                    break;
                }
            }

            List<String> maskFieldName = new ArrayList<>();
            for (int i = 0; i < fileMiddleSql.length; i++) {
                String fieldSql = fileMiddleSql[i];
                if (maskFieldIndex.contains(i) && i < fieldTypeList.size()) {
                    String oriFieldType = fieldTypeList.get(i);
                    String maskFieldType = "";
                    //oracle的char、varchar等类型默认存储byte，需手动设置成char，否则存储中文时会长度不够
                    if (oriFieldType.toLowerCase().contains("char")){
                        int typeIndex = oriFieldType.indexOf(")");
                        maskFieldType = oriFieldType.substring(0, typeIndex) +" char)";
                    } else if (oriFieldType.toLowerCase().contains("date")){
                        //maskFieldType = " varchar2(50 char)";
                        maskFieldType = " varchar2(50)";
                    } else if (oriFieldType.toLowerCase().contains("decimal")){
                        //其他数据库的decimal类型的精度指定可能会超过oracle的限制，这里将精度固定 小数10位大都够用
                        maskFieldType = " decimal(38,10)";
                    } else {
                        //其他字段类型脱敏数据一律用varchar存储，以免数据脱敏后原字段类型存储错误
                        //maskFieldType = " varchar2(255 char)";
                        maskFieldType = " varchar2(255)";
                    }

                    String fieldName = "`" + fieldNameList.get(i) + "`";
                    //修改脱敏字段类型,字段名可能和字段类型有重叠(例如updatetime字段名与datetime字段类型)，需要先去除字段名，再替换字段类型
                    fieldSql = fieldSql.replace(fieldName,"{}").replace(oriFieldType, maskFieldType);
                    //更改完字段类型后，再将字段名拼回去改回去
                    fieldSql = fieldSql.replace("{}",fieldName);

                    //记录脱敏字段的字段名
                    maskFieldName.add(fieldName);
                }
                fileMiddleSql[i] = fieldSql;
            }

            StringBuffer buffer = new StringBuffer();
            buffer.append(start_sql + "\n");

            //拼接字段sql
            String fieldSqlStr = "";
            for (int i = 0; i < fileMiddleSql.length; i++) {
                String fileSql = fileMiddleSql[i];
                if (StringUtils.isNotEmpty(fileSql)) {
                    fieldSqlStr += fileMiddleSql[i] + "\n";
                }
            }
            //最后一位是换行符，检查倒数第二位是否是逗号，是逗号则去掉
            if (",".equals(fieldSqlStr.substring(fieldSqlStr.length() - 2, fieldSqlStr.length() - 1))) {
                fieldSqlStr = fieldSqlStr.substring(0, fieldSqlStr.length() - 2) + "\n";
            }
            buffer.append(fieldSqlStr);
            buffer.append(end_sql + "\n");
            sql = buffer.toString();
        } catch (Exception ex) {
            log.info("Oracle建表方式解析失败，错误：" + ex.getMessage());
            ex.printStackTrace();
            sql = null;
        } finally {
            return sql;
        }
    }

    /**
     * 根据当前字段类别，获取脱敏字段类别（改变原有字段类型、扩充字段长度）
     *
     * @param fieldType 字段类别，示例：varchar(255)
     * @param fieldFlag 字段总长度是否超过建表限制
     * @return
     */
    private static String getModificationFiledType(String fieldType, boolean fieldFlag) {
        String returnType = "";
        //原本字段是text类型，原封不动
        if (fieldType.contains("text")) {
            returnType = " " + fieldType + " ";
            return returnType;
        }

        int begIndex = fieldType.indexOf("(") + 1;
        int endIndex = fieldType.indexOf(")");
        Integer filedExtent = 0;

        //fieldType可能没有长度，获取下标为-1时，脱敏后长度为100大都够用
        if (endIndex == -1) {
            filedExtent = 50;
        } else {
            //数据类型可能是 decimal(10,0) 这种，需要特殊处理长度，脱敏后长度为100大都够用
            int i = fieldType.substring(begIndex, endIndex).indexOf(",");
            if (i == -1) {
                filedExtent = Integer.valueOf(fieldType.substring(begIndex, endIndex));
                //mysql8导出的.sql文件，会存在datetime(0)这种数据，精度等于0的数据类型，都设置为50*2
                if (filedExtent == 0) {
                    filedExtent = 50;
                }
            } else {
                filedExtent = 50;
            }
        }

        if (fieldFlag) {
            returnType = " text ";
        } else {
            //varchar类型，根据原本长度判断需要扩充多少长度
            if (fieldType.contains("varchar")) {
                //varchar长度不宜设置过长，最好不要超过255，过长的数据直接换成text
                if (filedExtent <= 60) {
                    returnType = " varchar(" + filedExtent * 2 + ") ";
                } else if (filedExtent <= 127) {
                    returnType = " varchar(255) ";
                } else if (filedExtent <= 255) {
                    returnType = " varchar(500) ";
                } else {
                    returnType = " text ";
                }
            } else {
                //其他类型换成原本长度*2大部分情况都够用
                returnType = " varchar(" + filedExtent * 2 + ") ";
            }
        }
        return returnType;
    }

}
