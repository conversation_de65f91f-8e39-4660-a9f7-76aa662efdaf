package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
//import javax.validation.constraints.NotBlank;
//import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2021-06-20
*/
@Entity
@Data
@Table(name="sdd_mask_strategy_file_main")
public class MaskStrategyFileMain implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 策略名称 */
    @Column(name = "strategyname")
    //@NotBlank
    private String strategyname;

    /** 策略描述 */
    @Column(name = "strategydesc")
    //@NotBlank
    private String strategydesc;

    /** 策略类型（0：文件，1：非格格式化式化文件） */
    @Column(name = "strategytype")
    //@NotBlank
    private String strategytype;

    /** 文件总列数（适用于格式化文件） */
    @Column(name = "totalcolumn")
    //@NotNull
    private Integer totalcolumn;

    /** 抽取列（适用于格式化文件） */
    @Column(name = "extractcolum")
    //@NotBlank
    private String extractcolum;

    /** 状态(0：启用，1：禁用) */
    @Column(name = "status")
    //@NotBlank
    private String status;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @CreationTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskStrategyFileMain source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
