package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-17
*/
@Entity
@Data
@Table(name="sdd_mask_hadooptaskconfig")
public class HadoopTaskConfig implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 文件所用库 */
    @Column(name = "tabdatabase")
    private String tabdatabase;

    /** 文件所用表 */
    @Column(name = "tabname")
    private String tabname;

    /** 作业平台 */
    @Column(name = "platform")
    private String platform;

    /** 执行所用队列 */
    @Column(name = "queuename")
    private String queuename;

    /** 文件格式 */
    @Column(name = "fileformat")
    private String fileformat;

    /** 数据输入目录 */
    @Column(name = "datainputdir")
    private String datainputdir;

    /** 数据输出目录 */
    @Column(name = "dataoutputdir")
    private String dataoutputdir;

    /** 数据分隔符 */
    @Column(name = "datasplit")
    private String datasplit;

    /** 任务状态：(0初始创建,1执行中,2执行成功,3执行失败,4提交失败) */
    @Column(name = "status")
    private String status;

    /** 提交人 */
    @Column(name = "username")
    private String username;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 任务号 */
    @Column(name = "taskname")
    private String taskname;

    /** 数据源类型 */
    @Column(name = "datasourcetype")
    private String datasourcetype;

    /** 策略id */
    @Column(name = "strategyid")
    private Integer strategyid;

    /** 字段脱敏信息 */
    @Column(name = "fieldpostionalgoconfig")
    private String fieldpostionalgoconfig;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    public void copy(HadoopTaskConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
