package com.wzsec.modules.mask.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

/**
* <AUTHOR>
* @date 2024-10-21
*/
@Data
public class MaskstrategyIdentifymaskstrategiesQueryCriteria{

    @Query
    private String strategytype;

    /** 模糊 */
    @Query
    private String status;

    @Query(blurry = "strategyname,strategydesc,createuser,updateuser,remark")
    private String blurry;
}
