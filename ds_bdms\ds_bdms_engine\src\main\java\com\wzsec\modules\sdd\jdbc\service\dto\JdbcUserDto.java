package com.wzsec.modules.sdd.jdbc.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Set;

/**
* <AUTHOR>
* @date 2021-04-15
*/
@Data
public class JdbcUserDto implements Serializable {

    /** 自增Id */
    private Long id;

    /** 用户名 */
    private String username;

    /** 密码 */
    private String password;

    /** 姓名 */
    private String nickName;

    /** 性别 */
    private String sex;

    /** 邮箱 */
    private String email;

    /** 手机号 */
    private String phone;

    /** 角色 */
    private String role;

    /** 状态：1启用、0禁用 */
    private Boolean enabled;

    /** 创建时间 */
    private Timestamp createtime;

    /** 最后登录时间 */
    private Timestamp lastLoginTime;

    /** 最后修改密码的时间 */
    private Timestamp lastPasswordResetTime;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;

    /** 逻辑库 */
    private Set<JdbcDbSmallDto> jdbcDbs;
}