package com.wzsec.modules.sdd.sdk.service.mapper;

import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.service.dto.SdkOperationrecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SdkOperationrecordMapperImpl implements SdkOperationrecordMapper {

    @Override
    public SdkOperationrecordDto toDto(SdkOperationrecord entity) {
        if ( entity == null ) {
            return null;
        }

        SdkOperationrecordDto sdkOperationrecordDto = new SdkOperationrecordDto();

        sdkOperationrecordDto.setApplysystemname( entity.getApplysystemname() );
        sdkOperationrecordDto.setId( entity.getId() );
        sdkOperationrecordDto.setObjectname( entity.getObjectname() );
        sdkOperationrecordDto.setObjecttype( entity.getObjecttype() );
        sdkOperationrecordDto.setOperation( entity.getOperation() );
        sdkOperationrecordDto.setOperationtime( entity.getOperationtime() );
        sdkOperationrecordDto.setSdkid( entity.getSdkid() );
        sdkOperationrecordDto.setSdkname( entity.getSdkname() );
        sdkOperationrecordDto.setSparefield1( entity.getSparefield1() );
        sdkOperationrecordDto.setSparefield2( entity.getSparefield2() );
        sdkOperationrecordDto.setSparefield3( entity.getSparefield3() );
        sdkOperationrecordDto.setSparefield4( entity.getSparefield4() );
        sdkOperationrecordDto.setVersion( entity.getVersion() );

        return sdkOperationrecordDto;
    }

    @Override
    public List<SdkOperationrecordDto> toDto(List<SdkOperationrecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SdkOperationrecordDto> list = new ArrayList<SdkOperationrecordDto>( entityList.size() );
        for ( SdkOperationrecord sdkOperationrecord : entityList ) {
            list.add( toDto( sdkOperationrecord ) );
        }

        return list;
    }

    @Override
    public SdkOperationrecord toEntity(SdkOperationrecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        sdkOperationrecord.setApplysystemname( dto.getApplysystemname() );
        sdkOperationrecord.setId( dto.getId() );
        sdkOperationrecord.setObjectname( dto.getObjectname() );
        sdkOperationrecord.setObjecttype( dto.getObjecttype() );
        sdkOperationrecord.setOperation( dto.getOperation() );
        sdkOperationrecord.setOperationtime( dto.getOperationtime() );
        sdkOperationrecord.setSdkid( dto.getSdkid() );
        sdkOperationrecord.setSdkname( dto.getSdkname() );
        sdkOperationrecord.setSparefield1( dto.getSparefield1() );
        sdkOperationrecord.setSparefield2( dto.getSparefield2() );
        sdkOperationrecord.setSparefield3( dto.getSparefield3() );
        sdkOperationrecord.setSparefield4( dto.getSparefield4() );
        sdkOperationrecord.setVersion( dto.getVersion() );

        return sdkOperationrecord;
    }

    @Override
    public List<SdkOperationrecord> toEntity(List<SdkOperationrecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SdkOperationrecord> list = new ArrayList<SdkOperationrecord>( dtoList.size() );
        for ( SdkOperationrecordDto sdkOperationrecordDto : dtoList ) {
            list.add( toEntity( sdkOperationrecordDto ) );
        }

        return list;
    }
}
