package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.FileTaskResult;
import com.wzsec.modules.mask.service.FileTaskResultService;
import com.wzsec.modules.mask.service.dto.DBTaskResultDto;
import com.wzsec.modules.mask.service.dto.DBTaskResultQueryCriteria;
import com.wzsec.modules.mask.service.dto.FileTaskResultDto;
import com.wzsec.modules.mask.service.dto.FileTaskResultQueryCriteria;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2020-11-12
*/
// @Api(tags = "格式化文件脱敏任务结果管理")
@RestController
@RequestMapping("/api/fileTaskResult")
public class FileTaskResultController {

    private final FileTaskResultService fileTaskResultService;

    public FileTaskResultController(FileTaskResultService fileTaskResultService) {
        this.fileTaskResultService = fileTaskResultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('fileTaskResult:list')")
    public void download(HttpServletResponse response, FileTaskResultQueryCriteria criteria) throws IOException {
        fileTaskResultService.download(fileTaskResultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询格式化文件脱敏任务结果")
    // @ApiOperation("查询格式化文件脱敏任务结果")
    @PreAuthorize("@el.check('fileTaskResult:list')")
    public ResponseEntity<Object> getFileTaskResults(FileTaskResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(fileTaskResultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增格式化文件脱敏任务结果")
    // @ApiOperation("新增格式化文件脱敏任务结果")
    @PreAuthorize("@el.check('fileTaskResult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody FileTaskResult resources){
        return new ResponseEntity<>(fileTaskResultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改格式化文件脱敏任务结果")
    // @ApiOperation("修改格式化文件脱敏任务结果")
    @PreAuthorize("@el.check('fileTaskResult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody FileTaskResult resources){
        fileTaskResultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除格式化文件脱敏任务结果")
    // @ApiOperation("删除格式化文件脱敏任务结果")
    @PreAuthorize("@el.check('fileTaskResult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        fileTaskResultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("敏感数据发现文件脱敏结果预览")
    // @ApiOperation("敏感数据发现文件脱敏结果预览")
    @GetMapping(value = "/fileTaskResultDetails")
    @PreAuthorize("@el.check('fileTaskResult:list')")
    public ResponseEntity<Object> dbTaskResultDetails(FileTaskResultQueryCriteria criteria){
        FileTaskResultDto byId = fileTaskResultService.findById(Integer.valueOf(criteria.getId()));
        String beforemaskdata = byId.getBeforemaskdata();
        String[] before = beforemaskdata.split("\\n");
        String aftermaskdata = byId.getAftermaskdata();
        String[] after = aftermaskdata.split("\\n");
        List<FileTaskResultDto> FileTaskResultList = new ArrayList<FileTaskResultDto>();
        if (before.length == after.length) {
            FileTaskResultDto FileTaskResulttmp =null;
            for (int i = 0; i < before.length; i++) {
                FileTaskResulttmp = new FileTaskResultDto();
                FileTaskResulttmp.setBeforemaskdata(before[i]);
                FileTaskResulttmp.setAftermaskdata(after[i]);
                FileTaskResultList.add(FileTaskResulttmp);
            }
        }
        return new ResponseEntity<>(PageUtil.toPage(FileTaskResultList,FileTaskResultList.size()),HttpStatus.OK);

    }

    @Log("文件下载")
    @GetMapping("/fileDownload")
    public void fileDownload(@RequestParam Integer id,@RequestParam String filenames, HttpServletRequest request, HttpServletResponse response){
        fileTaskResultService.fileDownload(id,filenames,request,response);
    }
}
