package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2022-04-21
*/
@Entity
@Data
@Table(name="sdd_mask_videotaskresult")
public class MaskVideotaskresult implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 任务结果状态 */
    @Column(name = "taskstatus")
    private String taskstatus;

    /** 原始图片视频目录 */
    @Column(name = "inputdirectory")
    private String inputdirectory;

    /** 原视频格式 */
    @Column(name = "inputfileformat")
    private String inputfileformat;

    /** 原视频数 */
    @Column(name = "beforepicamount")
    private String beforepicamount;

    /** 脱敏对象 */
    @Column(name = "maskobject")
    private String maskobject;

    /** 脱敏后图片或视频目录 */
    @Column(name = "outputdirectory")
    private String outputdirectory;

    /** 输出文件格式 */
    @Column(name = "outputfileformat")
    private String outputfileformat;

    /** 脱敏成功图片或视频数 */
    @Column(name = "afterpicamount")
    private String afterpicamount;

    /** 任务耗时(秒) */
    @Column(name = "jobtotaltime")
    private String jobtotaltime;

    /** 任务结束时间 */
    @Column(name = "jobendtime")
    private String jobendtime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(MaskVideotaskresult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}