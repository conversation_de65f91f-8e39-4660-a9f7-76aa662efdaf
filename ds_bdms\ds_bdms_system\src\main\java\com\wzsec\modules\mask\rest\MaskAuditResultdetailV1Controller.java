package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskAuditResultdetailV1;
import com.wzsec.modules.mask.service.MaskAuditResultdetailV1Service;
import com.wzsec.modules.mask.service.dto.MaskAuditResultdetailV1QueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-01-15
*/
// @Api(tags = "脱敏审计结果详情管理")
@RestController
@RequestMapping("/api/maskAuditResultdetailV1")
public class MaskAuditResultdetailV1Controller {

    private final MaskAuditResultdetailV1Service maskAuditResultdetailV1Service;

    public MaskAuditResultdetailV1Controller(MaskAuditResultdetailV1Service maskAuditResultdetailV1Service) {
        this.maskAuditResultdetailV1Service = maskAuditResultdetailV1Service;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskAuditResultdetailV1:list')")
    public void download(HttpServletResponse response, MaskAuditResultdetailV1QueryCriteria criteria) throws IOException {
        maskAuditResultdetailV1Service.download(maskAuditResultdetailV1Service.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询脱敏审计结果详情")
    // @ApiOperation("查询脱敏审计结果详情")
    @PreAuthorize("@el.check('maskAuditResultdetailV1:list')")
    public ResponseEntity<Object> getMaskAuditResultdetailV1s(MaskAuditResultdetailV1QueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskAuditResultdetailV1Service.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增脱敏审计结果详情")
    // @ApiOperation("新增脱敏审计结果详情")
    @PreAuthorize("@el.check('maskAuditResultdetailV1:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskAuditResultdetailV1 resources){
        return new ResponseEntity<>(maskAuditResultdetailV1Service.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改脱敏审计结果详情")
    // @ApiOperation("修改脱敏审计结果详情")
    @PreAuthorize("@el.check('maskAuditResultdetailV1:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskAuditResultdetailV1 resources){
        maskAuditResultdetailV1Service.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除脱敏审计结果详情")
    // @ApiOperation("删除脱敏审计结果详情")
    @PreAuthorize("@el.check('maskAuditResultdetailV1:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskAuditResultdetailV1Service.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
