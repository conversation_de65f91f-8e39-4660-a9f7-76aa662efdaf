
fileFilter.description={0} License

exc.noLicenseInstalled=There is no license certificate installed for {0}.

exc.jceSetupError=It seems that the Java Cryptography Extension (JCE) is not installed or corrupted - please (re)install\!

exc.consumerAmountIsNotPositive=License Consumer amount is not positive\!

exc.consumerTypeIsNull=License Consumer type is null\!

exc.licenseHasExpired=License Certificate has expired\!

exc.licenseIsNotYetValid=License Certificate is not yet valid\!

exc.issuedIsNull=License Issue date is null\!

exc.issuerIsNull=License Issuer is null\!

exc.holderIsNull=License Holder is null\!

exc.invalidSubject=Invalid licensing subject\!

exc.privateKeyOrPwdIsNotAllowed=For security reasons a client application is not allowed to provide private keys or passwords for private keys in a Java keystore\!

exc.noKeyPwd=No password for key entry '{0}' provided\!

exc.noCertificateEntry=Certificate or alias '{0}' does not exist in keystore\!

exc.noKeyEntry=Private key or alias '{0}' does not exist in keystore\!

exc.consumerTypeIsNotUser=License Consumer Type is not "User"\!

exc.consumerAmountIsNotOne=License Consumer amount is not equal to one\!

exc.policy.IllegalPwd=The password does not match the default policy\: At least six characters consisting of letters and digits\!

user=Anonymous User
