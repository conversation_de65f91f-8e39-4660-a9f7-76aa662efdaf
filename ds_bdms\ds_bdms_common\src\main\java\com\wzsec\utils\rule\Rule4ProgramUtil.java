package com.wzsec.utils.rule;

import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;


/**
 * 程序自定义规则工具类，标识符规则在Rule4IdentifierUtil.java
 *
 * <AUTHOR>
 * @date 2020-4-28
 */
public class Rule4ProgramUtil {


//    public static void main(String[] args) {
//        System.out.println(p_checkProvinces("北京市"));
//    }


    /**
     * @Description:省份校验
     * <AUTHOR>
     * @date 2020年5月20日18:05:44
     */
    public static boolean p_checkProvinces(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        return data.matches("^(北京市|天津市|上海市|重庆市|河北省|山西省|辽宁省|吉林省|黑龙江省|江苏省|浙江省|安徽省|福建省|江西省|山东省|河南省|湖北省|湖南省|广东省|海南省|四川省|贵州省|云南省|陕西省|甘肃省|青海省|台湾省|内蒙古自治区|广西壮族自治区|西藏自治区|宁夏回族自治区|新疆维吾尔自治区|香港特别行政区|澳门特别行政区)$");
    }

    /**
     * @Description:民族校验
     * <AUTHOR>
     * @date 2020-5-20 17:47:22
     */
    public static boolean p_checkNational(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        return data.matches("^((汉|满|蒙古|回|藏|维吾尔|苗|彝|壮|布依|侗|瑶|白|土家|哈尼|哈萨克|傣|黎|傈僳|佤|畲|高山|拉祜|水|东乡|纳西|景颇|柯尔克孜|土|达斡尔|仫佬|羌|布朗|撒拉|毛南|仡佬|锡伯|阿昌|普米|朝鲜|塔吉克|怒|乌孜别克|俄罗斯|鄂温克|德昂|保安|裕固|京|塔塔尔|独龙|鄂伦春|赫哲|门巴|珞巴|基诺)|(汉族|满族|蒙古族|回族|藏族|维吾尔族|苗族|彝族|壮族|布依族|侗族|瑶族|白族|土家族|哈尼族|哈萨克族|傣族|黎族|傈僳族|佤族|畲族|高山族|拉祜族|水族|东乡族|纳西族|景颇族|柯尔克孜族|土族|达斡尔族|仫佬族|羌族|布朗族|撒拉族|毛南族|仡佬族|锡伯族|阿昌族|普米族|朝鲜族|塔吉克族|怒族|乌孜别克族|俄罗斯族|鄂温克族|德昂族|保安族|裕固族|京族|塔塔尔族|独龙族|鄂伦春族|赫哲族|门巴族|珞巴族|基诺族))$");
    }

    /**
     * @Description:地址校验，精确到具体地址
     * <AUTHOR>
     * @date 2019-1-16
     */
    public static boolean p_checkAddress(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.contains("省市") || data.contains("区县") || data.contains("区镇") ||
                data.contains("县乡") || data.contains("县镇") || data.contains("乡镇") ||
                data.contains("乡村") || data.contains("镇村")) {
            return false;
        } else {
            if (data.contains("区") || data.contains("县")) {
                if (data.contains("乡") || data.contains("镇") || data.contains("村") || data.contains("组")
                        || data.contains("号") || data.contains("路")) {
                    String address = String.valueOf(data.charAt(0));
                    if (address.equals("区") || address.equals("县") || address.equals("乡") || address.equals("镇")
                            || address.equals("村") || address.equals("组") || address.equals("号") || address.equals("路")) {
                        return false;
                    } else {
//                        for (String s : Const.addreWhiteList) { // 在地址白名单中，返回false
//                            if (data.contains(s)) {
//                                return false;
//                            }
//                        }
                        return true;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }
    }

    /**
     * @Description:地址校验，不精确地址
     * <AUTHOR>
     * @date 2020-4-28
     */
    public static boolean recognize(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.matches("^\\w+$"))
            return false;
        if ((data.indexOf("省") > 0) && (data.indexOf("市") > 0) && ((data.indexOf("市") - (data.indexOf("省")) > 1)))
            return true;
        if ((data.indexOf("省") > 0) && (data.indexOf("县") > 0) && ((data.indexOf("县") - (data.indexOf("省")) > 1)))
            return true;
        if ((data.indexOf("省") > 0) && (data.indexOf("区") > 0) && ((data.indexOf("区") - (data.indexOf("省")) > 1)))
            return true;
        if ((data.indexOf("市") > 0) && (data.indexOf("区") > 0) && ((data.indexOf("区") - (data.indexOf("市")) > 1)))
            return true;
        if ((data.indexOf("市") > 0) && (data.indexOf("路") > 0) && ((data.indexOf("路") - (data.indexOf("市")) > 1)))
            return true;
        if ((data.indexOf("市") > 0) && (data.indexOf("街道") > 0) && ((data.indexOf("街道") - (data.indexOf("市")) > 1)))
            return true;
        if ((data.indexOf("市") > 0) && (data.indexOf("县") > 0) && ((data.indexOf("县") - (data.indexOf("市")) > 1)))
            return true;
        if ((data.indexOf("市") > 0) && (data.indexOf("镇") > 0) && ((data.indexOf("镇") - (data.indexOf("市")) > 1)))
            return true;
        if ((data.indexOf("市") > 0) && (data.indexOf("乡") > 0) && ((data.indexOf("乡") - (data.indexOf("市")) > 1))) {
            return true;
        }
        return (data.indexOf("自治区") > 0);
    }

    /**
     * @Description:姓名校验
     * <AUTHOR>
     * @date 2019-1-16
     */
    public static boolean p_checkName(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() > 1 && data.length() < 5) {
            if (data.matches("^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,4}$")) {
                if (Const.nameWhiteList.contains(data)) { // 在姓名白名单中，返回false
                    return false;
                } else if (Const.addressCharList.contains(data)) { // 地级单位或省市中，返回false
                    return false;
                } else {
                    if (data.length() < 4) {

                        // 判断首字是否是在百家姓中
                        if (Const.firstNameList.contains(String.valueOf(data.charAt(0)))) {
                            if (data.endsWith("省") || data.endsWith("市") || data.endsWith("县")
                                    || data.endsWith("区") || data.endsWith("镇") || data.endsWith("乡")
                                    || data.endsWith("村") || data.endsWith("路") || data.endsWith("街")
                                    || data.endsWith("坊") || data.endsWith("亭") || data.endsWith("庄")) { // 结尾
                                // 以省、市、县、区、镇、乡、村、路、街、坊、亭、庄结尾, 截取后在地址列表中
                                return false;
                            } else {
                                if (Const.countryNameList.contains(data)) {
                                    return false;
                                } else {
                                    return true;
                                }
                            }
                        } else {
                            return false;
                        }
                    } else {
                        if (Const.doubleSurNameList.contains(data.substring(0, 2))) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:企业名称校验
     * <AUTHOR>
     * @date 2019-4-28
     */
    public static boolean p_checkEnterpriseName(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.matches("^\\w+$"))
            return false;
        if (data.endsWith("公司"))
            return true;
        if (data.endsWith("办事处"))
            return true;
        if (data.endsWith("代表处")) {
            return true;
        }
        return data.endsWith("厂") &&
                (
                        (data.indexOf("市") > 0)
                                || (data.indexOf("乡") > 0)
                                || (data.indexOf("区") > 0)
                                || (data.indexOf("村") > 0)
                                || (data.indexOf("县") > 0)
                                || (data.indexOf("省") > 0)
                                || (data.indexOf("盟") > 0)
                                || (data.indexOf("旗") > 0)
                );
    }

}
