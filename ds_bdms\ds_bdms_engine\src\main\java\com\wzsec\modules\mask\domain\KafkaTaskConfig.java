package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
//import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2021-02-26
*/
@Entity
@Data
@Table(name="sdd_mask_kafkataskconfig")
public class KafkaTaskConfig implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 任务名 */
    @Column(name = "taskname")
    //@NotBlank
    private String taskname;

    /** 库名 */
    @Column(name = "dbname")
    //@NotBlank
    private String dbname;

    /** 表名 */
    @Column(name = "tbname")
    //@NotBlank
    private String tbname;

    /** redis的key */
    @Column(name = "rediskey")
    //@NotBlank
    private String rediskey;

    /** 策略id */
    @Column(name = "strategyid")
    private Long strategyid;

    /** kafka原始数据topic */
    @Column(name = "datatopic")
    //@NotBlank
    private String datatopic;

    /** 数据格式 */
    @Column(name = "datatype")
    //@NotBlank
    private String datatype;

    /** 是否有效0无效，1有效 */
    @Column(name = "isvalid")
    //@NotBlank
    private String isvalid;

    /** 任务状态0 :初始创建 1:执行中 2:已停止,3提交失败 */
    @Column(name = "status")
    //@NotBlank
    private String status;

    /** 创建用户名 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户名 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "note")
    private String note;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(KafkaTaskConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}