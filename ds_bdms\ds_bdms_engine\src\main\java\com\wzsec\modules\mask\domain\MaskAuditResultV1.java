package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2021-01-15
*/
@Entity
@Data
@Table(name="sdd_maskaudit_result")
public class MaskAuditResultV1 implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名称 */
    @Column(name = "taskname")
    private String taskname;

    /** 系统标识 */
    @Column(name = "systemid")
    private String systemid;

    /** 检测类型 */
    @Column(name = "checktype")
    private String checktype;

    /** 输出路径 */
    @Column(name = "outpath")
    private String outpath;

    /** 风险程度（0：无，1：低危，2：中危，3：高危） */
    @Column(name = "risk")
    private String risk;

    /** 风险程度统计次数 */
    @Column(name = "count")
    private String count;

    /** 检测时间 */
    @Column(name = "checktime")
    private String checktime;

    /** 提交人 */
    @Column(name = "submitter")
    private String submitter;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskAuditResultV1 source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
