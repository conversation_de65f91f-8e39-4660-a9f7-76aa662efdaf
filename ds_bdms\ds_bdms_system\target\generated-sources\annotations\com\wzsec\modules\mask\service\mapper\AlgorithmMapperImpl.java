package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.Algorithm;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:02+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AlgorithmMapperImpl implements AlgorithmMapper {

    @Override
    public AlgorithmDto toDto(Algorithm entity) {
        if ( entity == null ) {
            return null;
        }

        AlgorithmDto algorithmDto = new AlgorithmDto();

        algorithmDto.setAlgenglishname( entity.getAlgenglishname() );
        algorithmDto.setAlgorithmdesc( entity.getAlgorithmdesc() );
        algorithmDto.setAlgorithmname( entity.getAlgorithmname() );
        algorithmDto.setClassname( entity.getClassname() );
        algorithmDto.setCreatetime( entity.getCreatetime() );
        algorithmDto.setCreateuser( entity.getCreateuser() );
        algorithmDto.setFlag( entity.getFlag() );
        algorithmDto.setFuncname( entity.getFuncname() );
        algorithmDto.setFuncnamepath( entity.getFuncnamepath() );
        algorithmDto.setId( entity.getId() );
        algorithmDto.setIsreversible( entity.getIsreversible() );
        algorithmDto.setJarname( entity.getJarname() );
        algorithmDto.setKind( entity.getKind() );
        algorithmDto.setMemo( entity.getMemo() );
        algorithmDto.setMethodname( entity.getMethodname() );
        algorithmDto.setParamdesc( entity.getParamdesc() );
        algorithmDto.setParamexample( entity.getParamexample() );
        algorithmDto.setParamnum( entity.getParamnum() );
        algorithmDto.setRealizesource( entity.getRealizesource() );
        algorithmDto.setSafetylevel( entity.getSafetylevel() );
        algorithmDto.setSecretdesc( entity.getSecretdesc() );
        algorithmDto.setSecretexample( entity.getSecretexample() );
        algorithmDto.setSpaceexpand( entity.getSpaceexpand() );
        algorithmDto.setSparefield1( entity.getSparefield1() );
        algorithmDto.setSparefield2( entity.getSparefield2() );
        algorithmDto.setSparefield3( entity.getSparefield3() );
        algorithmDto.setSparefield4( entity.getSparefield4() );
        algorithmDto.setSparefield5( entity.getSparefield5() );
        algorithmDto.setTimerate( entity.getTimerate() );
        algorithmDto.setUpdatetime( entity.getUpdatetime() );
        algorithmDto.setUpdateuser( entity.getUpdateuser() );
        algorithmDto.setVersion( entity.getVersion() );

        return algorithmDto;
    }

    @Override
    public List<AlgorithmDto> toDto(List<Algorithm> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AlgorithmDto> list = new ArrayList<AlgorithmDto>( entityList.size() );
        for ( Algorithm algorithm : entityList ) {
            list.add( toDto( algorithm ) );
        }

        return list;
    }

    @Override
    public Algorithm toEntity(AlgorithmDto dto) {
        if ( dto == null ) {
            return null;
        }

        Algorithm algorithm = new Algorithm();

        algorithm.setAlgenglishname( dto.getAlgenglishname() );
        algorithm.setAlgorithmdesc( dto.getAlgorithmdesc() );
        algorithm.setAlgorithmname( dto.getAlgorithmname() );
        algorithm.setClassname( dto.getClassname() );
        algorithm.setCreatetime( dto.getCreatetime() );
        algorithm.setCreateuser( dto.getCreateuser() );
        algorithm.setFlag( dto.getFlag() );
        algorithm.setFuncname( dto.getFuncname() );
        algorithm.setFuncnamepath( dto.getFuncnamepath() );
        algorithm.setId( dto.getId() );
        algorithm.setIsreversible( dto.getIsreversible() );
        algorithm.setJarname( dto.getJarname() );
        algorithm.setKind( dto.getKind() );
        algorithm.setMemo( dto.getMemo() );
        algorithm.setMethodname( dto.getMethodname() );
        algorithm.setParamdesc( dto.getParamdesc() );
        algorithm.setParamexample( dto.getParamexample() );
        algorithm.setParamnum( dto.getParamnum() );
        algorithm.setRealizesource( dto.getRealizesource() );
        algorithm.setSafetylevel( dto.getSafetylevel() );
        algorithm.setSecretdesc( dto.getSecretdesc() );
        algorithm.setSecretexample( dto.getSecretexample() );
        algorithm.setSpaceexpand( dto.getSpaceexpand() );
        algorithm.setSparefield1( dto.getSparefield1() );
        algorithm.setSparefield2( dto.getSparefield2() );
        algorithm.setSparefield3( dto.getSparefield3() );
        algorithm.setSparefield4( dto.getSparefield4() );
        algorithm.setSparefield5( dto.getSparefield5() );
        algorithm.setTimerate( dto.getTimerate() );
        algorithm.setUpdatetime( dto.getUpdatetime() );
        algorithm.setUpdateuser( dto.getUpdateuser() );
        algorithm.setVersion( dto.getVersion() );

        return algorithm;
    }

    @Override
    public List<Algorithm> toEntity(List<AlgorithmDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Algorithm> list = new ArrayList<Algorithm>( dtoList.size() );
        for ( AlgorithmDto algorithmDto : dtoList ) {
            list.add( toEntity( algorithmDto ) );
        }

        return list;
    }
}
