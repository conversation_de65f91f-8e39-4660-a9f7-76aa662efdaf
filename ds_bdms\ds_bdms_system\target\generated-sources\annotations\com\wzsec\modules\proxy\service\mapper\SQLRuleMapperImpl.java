package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.SQLRule;
import com.wzsec.modules.proxy.service.dto.SQLRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:02+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SQLRuleMapperImpl implements SQLRuleMapper {

    @Override
    public SQLRuleDto toDto(SQLRule entity) {
        if ( entity == null ) {
            return null;
        }

        SQLRuleDto sQLRuleDto = new SQLRuleDto();

        sQLRuleDto.setAlgorithm( entity.getAlgorithm() );
        sQLRuleDto.setCreatetime( entity.getCreatetime() );
        sQLRuleDto.setCreateuser( entity.getCreateuser() );
        sQLRuleDto.setId( entity.getId() );
        sQLRuleDto.setNote( entity.getNote() );
        sQLRuleDto.setParam( entity.getParam() );
        sQLRuleDto.setRole( entity.getRole() );
        sQLRuleDto.setSname( entity.getSname() );
        sQLRuleDto.setSparefield1( entity.getSparefield1() );
        sQLRuleDto.setSparefield2( entity.getSparefield2() );
        sQLRuleDto.setSparefield3( entity.getSparefield3() );
        sQLRuleDto.setSparefield4( entity.getSparefield4() );
        sQLRuleDto.setSparefield5( entity.getSparefield5() );
        sQLRuleDto.setState( entity.getState() );
        sQLRuleDto.setUpdatetime( entity.getUpdatetime() );
        sQLRuleDto.setUpdateuser( entity.getUpdateuser() );

        return sQLRuleDto;
    }

    @Override
    public List<SQLRuleDto> toDto(List<SQLRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SQLRuleDto> list = new ArrayList<SQLRuleDto>( entityList.size() );
        for ( SQLRule sQLRule : entityList ) {
            list.add( toDto( sQLRule ) );
        }

        return list;
    }

    @Override
    public SQLRule toEntity(SQLRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        SQLRule sQLRule = new SQLRule();

        sQLRule.setAlgorithm( dto.getAlgorithm() );
        sQLRule.setCreatetime( dto.getCreatetime() );
        sQLRule.setCreateuser( dto.getCreateuser() );
        sQLRule.setId( dto.getId() );
        sQLRule.setNote( dto.getNote() );
        sQLRule.setParam( dto.getParam() );
        sQLRule.setRole( dto.getRole() );
        sQLRule.setSname( dto.getSname() );
        sQLRule.setSparefield1( dto.getSparefield1() );
        sQLRule.setSparefield2( dto.getSparefield2() );
        sQLRule.setSparefield3( dto.getSparefield3() );
        sQLRule.setSparefield4( dto.getSparefield4() );
        sQLRule.setSparefield5( dto.getSparefield5() );
        sQLRule.setState( dto.getState() );
        sQLRule.setUpdatetime( dto.getUpdatetime() );
        sQLRule.setUpdateuser( dto.getUpdateuser() );

        return sQLRule;
    }

    @Override
    public List<SQLRule> toEntity(List<SQLRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SQLRule> list = new ArrayList<SQLRule>( dtoList.size() );
        for ( SQLRuleDto sQLRuleDto : dtoList ) {
            list.add( toEntity( sQLRuleDto ) );
        }

        return list;
    }
}
