package com.wzsec.utils.database;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.sequoiadb.base.CollectionSpace;
import com.sequoiadb.base.DBCollection;
import com.sequoiadb.base.DBCursor;
import com.sequoiadb.base.Sequoiadb;
import com.sequoiadb.exception.BaseException;
import com.wzsec.proxy.common.utils.StringUtil;
import com.wzsec.utils.DateUtil;
import com.wzsec.utils.TimeUtils;
import com.wzsec.utils.file.TxtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bson.BSONObject;
import org.bson.BasicBSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.data.redis.core.RedisTemplate;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.ScanParams;
import redis.clients.jedis.resps.ScanResult;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.*;

import static com.wzsec.utils.database.SequoiaDBUtil.tableNameStr;

/**
 * Redis批量插入数据操作类
 *
 * <AUTHOR>
 * @Description Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2024年10月18日 上午11:15:10
 */
@Slf4j
public class RedisUtil {

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 测试连接
     *
     * @param ip       ip
     * @param port     端口
     * @param password 密码
     * @return
     */
    public static Map<String, Object> getConnect(String ip, int port, String password) {
        Map<String, Object> objMap = new HashMap<>();
        objMap.put("code", 200);
        objMap.put("msg", "连接成功");
        // 尝试连接到 redis
        Jedis jedis = null;
        try {
            //1.建立连接
            jedis = new Jedis(ip, port);
            //2.设置密码
            if (StringUtils.isNotEmpty(password)) {
                jedis.auth(password);
            }
            //测试连接
            jedis.ping();
        } catch (Exception e) {
            objMap.put("code", 500);
            objMap.put("msg", e.getMessage());
        } finally {
            jedis.close();
        }
        return objMap;
    }

    /**
     * 获取redis连接封装
     *
     * @param ip       ip
     * @param port     端口
     * @param password 密码
     * @return
     */
    public static Jedis queryRedisConnect(String ip, String port, String password) {
        // 尝试连接到 redis
        Jedis jedis = null;
        try {
            //1.建立连接
            jedis = new Jedis(ip, Integer.valueOf(port));
            //2.设置密码
            if (StringUtils.isNotEmpty(password)) {
                jedis.auth(password);
            }
            //测试连接
            jedis.ping();
        } catch (Exception e) {
            throw new RuntimeException("连接失败:" + e.getMessage());
        }
        return jedis;
    }

    /**
     * 获取数据库中所有库名表名
     *
     * @param dbnames  库
     * @param ip       ip
     * @param port     端口
     * @param password 密码
     * @return
     */
    public static Map<String, String> getAllDbAndTabMap(String dbnames, String ip, String port, String password) {
        Map<String, String> map = new HashMap<>();
        Jedis jedis = queryRedisConnect(ip, port, password);
        try {
            // 遍历所有数据库
            String[] split = dbnames.split(",");
            for (String spl : split) {
                jedis.select(Integer.valueOf(spl).intValue());
                // 使用SCAN命令来迭代键空间
                ScanParams scanParams = new ScanParams().match("*").count(100);
                String cursor = "0";
                ScanResult<String> scanResult;
                do {
                    scanResult = jedis.scan(cursor, scanParams);
                    cursor = scanResult.getCursor();
                    scanResult.getResult().forEach(key -> {
                        if (map.containsKey(spl)) {
                            map.put(spl, map.get(spl) + "," + key);
                        } else {
                            map.put(spl, key);
                        }
                    });
                } while (!cursor.equals("0"));
            }
        } finally {
            jedis.close();
        }
        return map;
    }

    /**
     * 获取表数据数量
     *
     * @param dbname   库名
     * @param tabname  表名
     * @param ip       ip
     * @param port     端口
     * @param password 密码
     * @return
     */
    public static int getTabDataCount(String dbname, String tabname, String ip, String port, String password) {
        long rowCount = 0;
        Jedis jedis = null;
        try {
            jedis = queryRedisConnect(ip, port, password);
            //选择数据库
            jedis.select(Integer.valueOf(dbname));
            //获取value类型
            String type = jedis.type(tabname);
            switch (type) {
                case "list":
                    rowCount = jedis.llen(tabname); // 列表长度
                    break;
                case "set":
                    rowCount = jedis.scard(tabname); // 集合元素数量
                    break;
                case "zset":
                    rowCount = jedis.zcard(tabname); // 有序集合元素数量
                    break;
                case "hash":
                    rowCount = jedis.hlen(tabname); // 哈希表字段数量
                    break;
                case "string":
                    // 字符串没有“行数”的概念
                    String value = jedis.get(tabname);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        JsonNode jsonNode = objectMapper.readTree(value);
                        // 计算数组中的元素数量，即“行数”
                        rowCount = jsonNode.size();
                    } catch (Exception e) {
                        rowCount = 0;
                    }
                    break;
                default:
                    System.out.println("Key '" + tabname + "' 不存在或类型未知！");
                    break;
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            jedis.close();
        }
        return Integer.valueOf(String.valueOf(rowCount));
    }

    /**
     * 获取表信息
     *
     * @param dbName    库名
     * @param tableName 表名
     * @param ip        ip
     * @param port      端口
     * @param password  密码
     * @return
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, String ip, String port, String password) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Jedis jedis = null;
        try {
            jedis = queryRedisConnect(ip, port, password);
            //选择数据库
            jedis.select(Integer.valueOf(dbName));
            tableInfoMap.put("tableName", tableName);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            jedis.close();
        }
        return tableInfoMap;
    }

    /**
     * 获取数据库表数据
     *
     * @param dbname   库名
     * @param tabname  表
     * @param lineNum  数量
     * @param ip       ip
     * @param port     端口
     * @param password 密码
     * @return
     */
    public static List<String[]> getTabDataList(String dbname, String tabname, Integer lineNum, String ip, String port, String password) {
        List<String[]> tabDataList = new ArrayList<String[]>();
        Jedis jedis = queryRedisConnect(ip, port, password);
        //选择数据库
        jedis.select(Integer.valueOf(dbname));
        String data = jedis.get(tabname);
        String[] lines = data.split("\n");
        for (String line : lines) {
            // 去掉行首和行尾的空白字符（如果有的话）
            line = line.trim();
            // 按照逗号分割字符串，得到当前行的数组
            String[] elements = line.split(",");
            // 将数组添加到List中
            tabDataList.add(elements);
        }
        jedis.close();
        return tabDataList;
    }


    /**
     * 获取数据库表中所有字段
     *
     * @param dbname   数据库
     * @param tabname  表
     * @param ip       ip
     * @param port     端口
     * @param password 密码
     * @return
     */
    public static List<String> getFieldNameList(String dbname, String tabname, String ip, String port, String password) {
        List<String> fieldsList = new ArrayList<>();
        Jedis jedis = queryRedisConnect(ip, port, password);
        try {
            //选择数据库
            jedis.select(Integer.valueOf(dbname));
            String staticmask = jedis.get(tabname);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(staticmask);
            for (JsonNode arrayNode : jsonNode) {
                Iterator<String> fieldNames = arrayNode.fieldNames();
                while (fieldNames.hasNext()) {
                    String fieldName = fieldNames.next();
                    if (ObjectUtil.isEmpty(fieldsList)) {
                        fieldsList.add(fieldName);
                    } else if (ObjectUtil.isNotEmpty(fieldsList) && !fieldsList.contains(fieldName)) {
                        fieldsList.add(fieldName);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("格式转换异常");
        }
        return fieldsList;
    }

    /**
     * 数据库表字段信息
     *
     * @param dbName    库
     * @param tableName 表
     * @param ip        ip
     * @param port      端口
     * @param password  密码
     * @return
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, String ip, String port, String password) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        List<String> fieldsList = new ArrayList<>();
        Jedis jedis = queryRedisConnect(ip, port, password);
        try {
            //选择数据库
            jedis.select(Integer.valueOf(dbName));
            String staticmask = jedis.get(tableName);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(staticmask);
            for (JsonNode arrayNode : jsonNode) {
                Iterator<String> fieldNames = arrayNode.fieldNames();
                while (fieldNames.hasNext()) {
                    String fieldName = fieldNames.next();
                    if (ObjectUtil.isEmpty(fieldsList)) {
                        fieldsList.add(fieldName);
                    } else if (ObjectUtil.isNotEmpty(fieldsList) && !fieldsList.contains(fieldName)) {
                        fieldsList.add(fieldName);
                    }
                }
            }
            for (String field : fieldsList) {
                Map<String, String> fieldInfoMap = new HashMap<>();
                fieldInfoMap.put("fieldName", field);
                fieldInfoList.add(fieldInfoMap);
            }
        } catch (Exception e) {
            throw new RuntimeException("格式转换异常");
        }
        return fieldInfoList;
    }

    /**
     * string类型转List<HashMap<String, List<String>>> 类型
     * @param data 读取到的数据
     * @return 拆分之后的数据
     */
    public static List<HashMap<String, List<String>>> parseData(String data) {
        data = data.replaceAll("\\s","");
        List<HashMap<String, List<String>>> lineDataList = new ArrayList<>();
        // 使用换行符拆分数据
        String[] lines = data.split("\n");
        // 遍历每行数据
        for (String line : lines) {
            // 跳过空行
            if (line.trim().isEmpty()) {
                continue;
            }
            // 使用逗号分隔符拆分每行数据
//            String[] fields = line.split(",");
            String[] fields = splitPaths(line);
            // 创建一个List来存储字段
            List<String> fieldList = Arrays.asList(fields);
            // 创建一个HashMap来存储每行的数据
            HashMap<String, List<String>> map = new HashMap<>();

            // 使用预定义的键来存储字段列表
            // 你可以根据实际情况更改这个键名
            map.put("fields", fieldList);

            // 将HashMap添加到List中
            lineDataList.add(map);
        }
        return lineDataList;
    }

    /**
     * 自定义方法，用于拆分包含中文逗号和普通逗号的字符串
     *
     * @param input 输入字符串
     * @return 拆分后的字符串数组
     */
    public static String[] splitPaths(String input) {
        // 使用正则表达式，支持中文逗号和普通逗号
        String regex = "[,，]";
        return input.split(regex);
    }

    public static void main(String[] args) {
        String srcip = "127.0.0.1";
        String srcport = "6379";
        String password = "";
        String dbname = "1";
        String dbnames = "1";
        String tabname = "staticmask";

        //redis脱敏效果预览
        Jedis jedis = RedisUtil.queryRedisConnect(srcip, srcport, password);
        jedis.select(Integer.valueOf(dbname));
        String value = jedis.get(tabname);
        //空数据直接结束
        if (StringUtils.isEmpty(value)){
            return;
        }
        jedis.close();
        //按脱敏策略对数据进行脱敏
        List<String> list = new ArrayList<>();
        List<HashMap<String, List<String>>> lineDataList = RedisUtil.parseData(value);
        for (HashMap<String, List<String>> lineDataMap : lineDataList) {
            Set<String> keySetList = lineDataMap.keySet();
            for (String keySet : keySetList) {
                List<String> valueList = lineDataMap.get(keySet);
                StringBuilder sb = new StringBuilder();
                for (String str : valueList) {
                    if (ObjectUtil.isEmpty(sb)){
                        sb.append(str);
                    }else {
                        sb.append(",").append(str);
                    }
                }
                list.add(sb.toString());
            }
        }
        for (String str : list) {
            System.out.println(str);
        }
        System.out.println("执行完毕");
    }

    /**
     * 读取数据返回拆分后的String集合
     *
     * @param value 内容
     */
    public static List<String> getValueData(String value) {
        List<String> documentList = new ArrayList<>();
        String fileStr = value;
        if (fileStr != null || fileStr.length() > 0) {
            String[] split = fileStr.split("。|，|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");
            for (String s : split) {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(s)) {
                    documentList.add(s.trim());
                }
            }

        }
        return documentList;
    }


}