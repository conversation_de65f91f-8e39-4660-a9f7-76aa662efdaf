package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.HiveTaskConfig;
import com.wzsec.modules.mask.service.HiveTaskConfigService;
import com.wzsec.modules.mask.service.dto.HiveTaskConfigQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2020-12-11
*/
// @Api(tags = "Hive脱敏任务配置管理")
@RestController
@RequestMapping("/api/hiveTaskConfig")
public class HiveTaskConfigController {

    private final HiveTaskConfigService hiveTaskConfigService;

    public HiveTaskConfigController(HiveTaskConfigService hiveTaskConfigService) {
        this.hiveTaskConfigService = hiveTaskConfigService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('hiveTaskConfig:list')")
    public void download(HttpServletResponse response, HiveTaskConfigQueryCriteria criteria) throws IOException {
        hiveTaskConfigService.download(hiveTaskConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询Hive脱敏任务配置")
    // @ApiOperation("查询Hive脱敏任务配置")
    @PreAuthorize("@el.check('hiveTaskConfig:list')")
    public ResponseEntity<Object> getHiveTaskConfigs(HiveTaskConfigQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(hiveTaskConfigService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增Hive脱敏任务配置")
    // @ApiOperation("新增Hive脱敏任务配置")
    @PreAuthorize("@el.check('hiveTaskConfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody HiveTaskConfig resources){
        return new ResponseEntity<>(hiveTaskConfigService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改Hive脱敏任务配置")
    // @ApiOperation("修改Hive脱敏任务配置")
    @PreAuthorize("@el.check('hiveTaskConfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody HiveTaskConfig resources){
        hiveTaskConfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除Hive脱敏任务配置")
    // @ApiOperation("删除Hive脱敏任务配置")
    @PreAuthorize("@el.check('hiveTaskConfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        hiveTaskConfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取Hive脱敏任务新增任务号")
    // @ApiOperation("获取Hive脱敏任务新增任务号")
    @PreAuthorize("@el.check('hiveTaskConfig:add')")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(hiveTaskConfigService.getMAXTaskName(), HttpStatus.CREATED);
    }

    @Log("根据数据源类型获取库名")
    // @ApiOperation("根据数据源类型获取库名")
    @GetMapping(value = "/getDbnameBySourceid/{sourceid}")
    @PreAuthorize("@el.check('hiveTaskConfig:list')")
    public ResponseEntity<Object> getDbnameBySourceid(@PathVariable String sourceid) {
        return new ResponseEntity<>(hiveTaskConfigService.getDbnameBySourceid(sourceid), HttpStatus.OK);
    }

    @Log("根据库名获取表名")
    // @ApiOperation("根据库名获取表名")
    @GetMapping(value = "/getTabnameByDbname/{sourceid}/{dbname}")
    @PreAuthorize("@el.check('hiveTaskConfig:list')")
    public ResponseEntity<Object> getTabnameByDbname(@PathVariable String sourceid,@PathVariable String dbname) {
        return new ResponseEntity<>(hiveTaskConfigService.getTabnameByDbname(sourceid,dbname), HttpStatus.OK);
    }

    @Log("根据库名和表名获取策略")
    // @ApiOperation("根据库名和表名获取策略")
    @GetMapping(value = "/getStrategyByDbnameAndTabname/{sourceid}/{dbname}/{tabname}")
    @PreAuthorize("@el.check('hiveTaskConfig:list')")
    public ResponseEntity<Object> getStrategyByDbnameAndTabname(@PathVariable String sourceid,@PathVariable String dbname,@PathVariable String tabname) {
        return new ResponseEntity<>(hiveTaskConfigService.getStrategyByDbnameAndTabname(sourceid,dbname,tabname), HttpStatus.OK);
    }

    @Log("执行Hive静态脱敏任务")
    // @ApiOperation("在引擎执行Hive脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    // @PreAuthorize("@el.check('wptask:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Integer id, HttpServletRequest request) {
        hiveTaskConfigService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
