package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.AlarmSettings;
import com.wzsec.modules.mask.service.AlarmSettingsService;
import com.wzsec.modules.mask.service.dto.AlarmSettingsQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2022-06-01
*/
// @Api(tags = "日志告警配置管理")
@RestController
@RequestMapping("/api/alarmSettings")
public class AlarmSettingsController {

    private final AlarmSettingsService alarmSettingsService;

    public AlarmSettingsController(AlarmSettingsService alarmSettingsService) {
        this.alarmSettingsService = alarmSettingsService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('alarmSettings:list')")
    public void download(HttpServletResponse response, AlarmSettingsQueryCriteria criteria) throws IOException {
        alarmSettingsService.download(alarmSettingsService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询日志告警配置")
    // @ApiOperation("查询日志告警配置")
    @PreAuthorize("@el.check('alarmSettings:list')")
    public ResponseEntity<Object> getAlarmSettingss(AlarmSettingsQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(alarmSettingsService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增日志告警配置")
    // @ApiOperation("新增日志告警配置")
    @PreAuthorize("@el.check('alarmSettings:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody AlarmSettings resources){
        return new ResponseEntity<>(alarmSettingsService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改日志告警配置")
    // @ApiOperation("修改日志告警配置")
    @PreAuthorize("@el.check('alarmSettings:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody AlarmSettings resources){

        alarmSettingsService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除日志告警配置")
    // @ApiOperation("删除日志告警配置")
    @PreAuthorize("@el.check('alarmSettings:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        alarmSettingsService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
