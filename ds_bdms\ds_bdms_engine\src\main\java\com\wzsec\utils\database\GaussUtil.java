package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName GaussUtil
 * @Description
 * <AUTHOR>
 * @Date 2024/11/14 14:41
 */
@Slf4j
public class GaussUtil {

    /**
    * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
    * @param: map   连接数据库需要参数
    * @return:
    * @author: penglei
    * @date: 2024/11/14 14:42
    */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        // 1. 从输入参数中获取数据库连接信息
        String driverProgram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");
        // 解密密码（如果需要）
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }

        String dbname = map.get("dbname");
        String srcPort = map.get("srcport");
        String srcIp = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 注册 JDBC 驱动
            Class.forName(driverProgram);

            // 2. 打开连接到 GaussDB 数据库
            String jdbcUrl = "jdbc:postgresql://" + srcIp + ":" + srcPort + "/postgres";
            connection = DriverManager.getConnection(jdbcUrl, username, password);

            // 3. 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            String checkDatabaseSQL = "SELECT datname FROM pg_database WHERE datname = '" + dbname + "'";
            statement = connection.createStatement();
            resultSet = statement.executeQuery(checkDatabaseSQL);
            boolean databaseExists = resultSet.next();

            // 4. 如果数据库不存在，则创建它
            if (!databaseExists) {
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "JDBC驱动未找到：" + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
        return msgMap;
    }
}
