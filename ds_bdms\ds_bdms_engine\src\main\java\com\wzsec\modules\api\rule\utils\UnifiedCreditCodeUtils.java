package com.wzsec.modules.api.rule.utils;

//import javax.validation.ValidationException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @ClassName: UnifiedCreditCodeUtils
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年11月18日
 */
public class UnifiedCreditCodeUtils {
	private static String baseCode = "0123456789ABCDEFGHJKLMNPQRTUWXY";
	private static char[] baseCodeArray = baseCode.toCharArray();
	private static int[] wi = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 };

	/**
	 * @Description 社会统一信用代码校验
	 * <AUTHOR>
	 * @date 2019年11月14日 下午4:46:12
	 * @param unifiedCreditCode
	 * @return boolean
	 */
	public static boolean validateUnifiedCreditCode(String unifiedCreditCode) {
		if ((unifiedCreditCode.equals("")) || unifiedCreditCode.length() != 18) {
			return false;
		}
		Map<Character, Integer> codes = generateCodes();
		int parityBit;
		try {
			parityBit = getParityBit(unifiedCreditCode, codes);
		} catch (Exception e) {
			return false;
		}
		return parityBit == codes.get(unifiedCreditCode.charAt(unifiedCreditCode.length() - 1));
	}

	/**
	 * 生成供较验使用的 Code Map
	 *
	 * @return BidiMap
	 */
	private static LinkedHashMap<Character, Integer> generateCodes() {
		LinkedHashMap<Character, Integer> codes = new LinkedHashMap<>();
		for (int i = 0; i < baseCode.length(); i++) {
			codes.put(baseCodeArray[i], i);
		}
		return codes;
	}

	/**
	 * @Description 获取校验码
	 * <AUTHOR>
	 * @date 2019年11月14日 下午4:46:34
	 * @param unifiedCreditCode：统一社会信息代码
	 * @param codes：带有映射关系的国家代码
	 * @return int：获取较验位的值
	 */
	private static int getParityBit(String unifiedCreditCode, Map<Character, Integer> codes) throws Exception {
		char[] businessCodeArray = unifiedCreditCode.toCharArray();
		int sum = 0;
		for (int i = 0; i < 17; i++) {
			char key = businessCodeArray[i];
			if (baseCode.indexOf(key) == -1) {
				throw new Exception("第" + String.valueOf(i + 1) + "位传入了非法的字符" + key);
			}
			sum += (codes.get(key) * wi[i]);
		}
		int result = 31 - sum % 31;
		return result == 31 ? 0 : result;
	}

	// /**
	// * @Description 获取一个随机的统一社会信用代码
	// * <AUTHOR>
	// * @date 2019年11月14日 下午4:47:08
	// * @return String：统一社会信用代码
	// */
	// public static String generateOneUnifiedCreditCode() {
	// Random random = new Random();
	// StringBuilder buf = new StringBuilder();
	// for (int i = 0; i < 17; ++i) {
	// int num = random.nextInt(baseCode.length() - 1);
	// buf.append(baseCode.charAt(num));
	// }
	// String code = buf.toString();
	// String upperCode = code.toUpperCase();
	// BidiMap<Character, Integer> codes = generateCodes();
	// int parityBit = getParityBit(upperCode, codes);
	// if (codes.getKey(parityBit) == null) {
	// System.out.println("生成社会统一信用代码不符合规则");
	// upperCode = generateOneUnifiedCreditCode();
	// } else {
	// upperCode = upperCode + codes.getKey(parityBit);
	// }
	// return upperCode;
	// }

}
