package com.wzsec.dotask.sdd.service.excute.file;

import java.io.*;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.wzsec.dotask.sdd.service.excute.common.CheckFileManager;
import com.wzsec.modules.sdd.discover.domain.Detailresult;
import com.wzsec.modules.sdd.discover.domain.Outlineresult;
import com.wzsec.modules.sdd.discover.service.DetailresultService;
import com.wzsec.modules.sdd.discover.service.OutlineresultService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件敏感数据发现，先下载后检测
 *
 * <AUTHOR>
 * @date 2020-4-26
 */
@Slf4j
public class FileSensitiveDataDiscovery {


    private final OutlineresultService outlineresultService;

    private final DetailresultService detailresultService;

    public FileSensitiveDataDiscovery(OutlineresultService outlineresultService, DetailresultService detailresultService) {
        this.outlineresultService = outlineresultService;
        this.detailresultService = detailresultService;
    }

    /**
     * 执行文件敏感数据发现任务
     *
     * @param taskname    任务名
     * @param submituser  提交人
     * @param type        数据源类型
     * @param srcurl      数据源地址(包含IP和端口)
     * @param username    用户名
     * @param datapath    数据目录
     * @param ruleDtoList 规则
     * <AUTHOR>
     * @date 2020年4月26日
     */
    public boolean doFileSensitiveDataDiscovery(String taskname, String submituser, String type, String srcurl, String username,
                                                String password, String datapath, List<RuleDto> ruleDtoList) throws Exception {
        datapath = datapath.replace(Const.SEPARATOR_WINDOWS, Const.SEPARATOR_LINUX);//替换windows环境路径分割符号为linux分隔符，用于本地路径与远程路径对应处理
        boolean result = false;
        if (StringUtils.isEmpty(type)) {//1、本地文件检测
            InetAddress addr = null;
            String ip = null;
            try {
                addr = InetAddress.getLocalHost();
                ip = addr.getHostAddress();
            } catch (UnknownHostException e) {
                ip = Const.LOCALHOST;
            }
            String localPath = datapath;//需要检测目录
            result = checkLocalDirFile(taskname, submituser, localPath, datapath, ip, ruleDtoList);
        } else if (type.equalsIgnoreCase(Const.FILE_FTP)) {//2、FTP服务器文件检测
            String[] hostNameAndPort = srcurl.split(":|：");
            String loaclPath = ConstEngine.sddEngineConfs.get("ftp.download.save.localpath");//下载保存目录
            log.info("下载保存目录：" + loaclPath);
            //1.下载
            boolean downloadResult = FTPUtil.downloadFileByDirectory(hostNameAndPort[0], Integer.parseInt(hostNameAndPort[1]), username, password, datapath, loaclPath);
            if (downloadResult) {
                //2.检测
                result = checkLocalDirFile(taskname, submituser, loaclPath, datapath, hostNameAndPort[0], ruleDtoList);
            }
        } else if (type.equalsIgnoreCase(Const.FILE_HDFS)) {//3、HDFS集群文件检测

            log.info("暂不支持文件源为HDFS");
        } else {
            log.info("暂不支持的文件源：" + type);
        }
        return result;
    }

    /**
     * 检测本地目录下文件敏感数据(通用)
     * <p>
     * 保存时对路径进行处理，本地检测为本地路径，文件服务器检测为远程路径
     *
     * @param taskname    任务名
     * @param submituser  提交人
     * @param localPath   检测目录(本地与数据目录一直，通过别服务器下载时则不一致，此路径为本地保存路径)
     * @param datapath    数据目录(用于保存结果)
     * @param ip
     * @param ruleDtoList 规则
     * <AUTHOR>
     * @date 2020年4月26日
     */
    private boolean checkLocalDirFile(String taskname, String submituser, String localPath, String datapath, String ip, List<RuleDto> ruleDtoList) {
        try {
            Outlineresult outlineresult = new Outlineresult();
            long startTime = System.currentTimeMillis();
            outlineresult.setTaskname(taskname);
            outlineresult.setSubmituser(submituser);
            outlineresult.setStarttime(TimestampUtil.getNowTime());
            outlineresult.setLocation(ip);
            List<String> dataTypeList = new ArrayList<>();
            List<File> fileList = new ArrayList<>();
            FileUtil.getFileList(localPath, fileList);//获取目录下所有文件，含子目录
            if (fileList == null || fileList.size() == 0) { // 如果目录为空，直接退出
                log.info("目录" + localPath + "下无文件,无需读取解析");
                return false;
            }
            // 读取解析文件
            for (File file : fileList) {
                //任务敏感数据规则检测信息
                Map<String, Map<String, String>> fileDataTypeExampleMap = new HashMap<>();
                log.info("====================对" + file.getAbsolutePath() + "文件分析开始====================");
                List<String> fileDataList = CheckFileManager.getFileData(file);
                System.out.println("拆分后的字符串：" + JSON.toJSONString(fileDataList));
                fileDataTypeExampleMap = CheckFileManager.getFileDataTypeMap(ruleDtoList, fileDataList);
                log.info("====================对" + file.getAbsolutePath() + "文件分析结束====================");
                Map<String, String> dataTypeMap = fileDataTypeExampleMap.get("fileDataType");
                Map<String, String> dataExampleMap = fileDataTypeExampleMap.get("dataExample");
                for (String dataType : dataTypeMap.keySet()) {
                    if (!dataTypeList.contains(dataType)) {
                        dataTypeList.add(dataType);
                    }
                    Detailresult detailresult = new Detailresult();
                    detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_FILE);//文件
                    detailresult.setTaskname(taskname);
                    //拼接子目录，用于本地路径与远程路径对应处理
                    String filePath = datapath + file.getAbsolutePath().replace(Const.SEPARATOR_WINDOWS, Const.SEPARATOR_LINUX).replaceFirst(localPath, Const.STRING_EMPTY);//文件路径(含文件名)
                    detailresult.setDborpath(filePath.substring(0, filePath.lastIndexOf(Const.SEPARATOR_LINUX)));//源文件所在路径
                    detailresult.setTableorfile(file.getName());
                    detailresult.setDatatype(dataType);
                    detailresult.setDatacount(Integer.valueOf(dataTypeMap.get(dataType)));
                    detailresult.setExample(dataExampleMap.get(dataType));
                    detailresultService.create(detailresult);
                }
            }
            outlineresult.setEndtime(TimestampUtil.getNowTime());
            //用时计算
            long endTime = System.currentTimeMillis();
            outlineresult.setUsetime(String.valueOf(Math.round(endTime - startTime) / 1000.000));
            outlineresult.setTypenum(String.valueOf(dataTypeList.size()));
            outlineresult.setDatasourcetype(Const.OUTLINERESULT_DATASOURCETYPE_FILE);//文件
            outlineresultService.create(outlineresult);
//            //删除下载目录
//            File file = new File(localPath);
//            file.delete();
            return true;
        } catch (Exception e) {
//            String loginfo = "执行敏感数据发现任务失败";
            log.error("检测" + localPath + "目录出现异常！", e);
            return false;
        }
    }

}
