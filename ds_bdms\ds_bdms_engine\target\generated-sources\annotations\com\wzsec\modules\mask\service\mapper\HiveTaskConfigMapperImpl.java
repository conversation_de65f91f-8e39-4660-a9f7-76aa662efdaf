package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HiveTaskConfig;
import com.wzsec.modules.mask.service.dto.HiveTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class HiveTaskConfigMapperImpl implements HiveTaskConfigMapper {

    @Override
    public HiveTaskConfigDto toDto(HiveTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        HiveTaskConfigDto hiveTaskConfigDto = new HiveTaskConfigDto();

        hiveTaskConfigDto.setCreatetime( entity.getCreatetime() );
        hiveTaskConfigDto.setCreateuser( entity.getCreateuser() );
        hiveTaskConfigDto.setDatarows( entity.getDatarows() );
        hiveTaskConfigDto.setDatasplit( entity.getDatasplit() );
        hiveTaskConfigDto.setDbname( entity.getDbname() );
        hiveTaskConfigDto.setExtractfields( entity.getExtractfields() );
        hiveTaskConfigDto.setFieldnames( entity.getFieldnames() );
        hiveTaskConfigDto.setFileformat( entity.getFileformat() );
        hiveTaskConfigDto.setId( entity.getId() );
        hiveTaskConfigDto.setJarpath( entity.getJarpath() );
        hiveTaskConfigDto.setMaskedtable( entity.getMaskedtable() );
        hiveTaskConfigDto.setMaskfieldnames( entity.getMaskfieldnames() );
        hiveTaskConfigDto.setMaskstrategystr( entity.getMaskstrategystr() );
        hiveTaskConfigDto.setOutputdbname( entity.getOutputdbname() );
        hiveTaskConfigDto.setOutputdir( entity.getOutputdir() );
        hiveTaskConfigDto.setOutputtype( entity.getOutputtype() );
        hiveTaskConfigDto.setPartitioninfo( entity.getPartitioninfo() );
        hiveTaskConfigDto.setQueuename( entity.getQueuename() );
        hiveTaskConfigDto.setRemark( entity.getRemark() );
        hiveTaskConfigDto.setSourceid( entity.getSourceid() );
        hiveTaskConfigDto.setSparefield1( entity.getSparefield1() );
        hiveTaskConfigDto.setSparefield2( entity.getSparefield2() );
        hiveTaskConfigDto.setSparefield3( entity.getSparefield3() );
        hiveTaskConfigDto.setSparefield4( entity.getSparefield4() );
        hiveTaskConfigDto.setSparefield5( entity.getSparefield5() );
        hiveTaskConfigDto.setStatus( entity.getStatus() );
        hiveTaskConfigDto.setStrategyid( entity.getStrategyid() );
        hiveTaskConfigDto.setTablename( entity.getTablename() );
        hiveTaskConfigDto.setTaskname( entity.getTaskname() );
        hiveTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        hiveTaskConfigDto.setUpdateuser( entity.getUpdateuser() );

        return hiveTaskConfigDto;
    }

    @Override
    public List<HiveTaskConfigDto> toDto(List<HiveTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HiveTaskConfigDto> list = new ArrayList<HiveTaskConfigDto>( entityList.size() );
        for ( HiveTaskConfig hiveTaskConfig : entityList ) {
            list.add( toDto( hiveTaskConfig ) );
        }

        return list;
    }

    @Override
    public HiveTaskConfig toEntity(HiveTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        HiveTaskConfig hiveTaskConfig = new HiveTaskConfig();

        hiveTaskConfig.setCreatetime( dto.getCreatetime() );
        hiveTaskConfig.setCreateuser( dto.getCreateuser() );
        hiveTaskConfig.setDatarows( dto.getDatarows() );
        hiveTaskConfig.setDatasplit( dto.getDatasplit() );
        hiveTaskConfig.setDbname( dto.getDbname() );
        hiveTaskConfig.setExtractfields( dto.getExtractfields() );
        hiveTaskConfig.setFieldnames( dto.getFieldnames() );
        hiveTaskConfig.setFileformat( dto.getFileformat() );
        hiveTaskConfig.setId( dto.getId() );
        hiveTaskConfig.setJarpath( dto.getJarpath() );
        hiveTaskConfig.setMaskedtable( dto.getMaskedtable() );
        hiveTaskConfig.setMaskfieldnames( dto.getMaskfieldnames() );
        hiveTaskConfig.setMaskstrategystr( dto.getMaskstrategystr() );
        hiveTaskConfig.setOutputdbname( dto.getOutputdbname() );
        hiveTaskConfig.setOutputdir( dto.getOutputdir() );
        hiveTaskConfig.setOutputtype( dto.getOutputtype() );
        hiveTaskConfig.setPartitioninfo( dto.getPartitioninfo() );
        hiveTaskConfig.setQueuename( dto.getQueuename() );
        hiveTaskConfig.setRemark( dto.getRemark() );
        hiveTaskConfig.setSourceid( dto.getSourceid() );
        hiveTaskConfig.setSparefield1( dto.getSparefield1() );
        hiveTaskConfig.setSparefield2( dto.getSparefield2() );
        hiveTaskConfig.setSparefield3( dto.getSparefield3() );
        hiveTaskConfig.setSparefield4( dto.getSparefield4() );
        hiveTaskConfig.setSparefield5( dto.getSparefield5() );
        hiveTaskConfig.setStatus( dto.getStatus() );
        hiveTaskConfig.setStrategyid( dto.getStrategyid() );
        hiveTaskConfig.setTablename( dto.getTablename() );
        hiveTaskConfig.setTaskname( dto.getTaskname() );
        hiveTaskConfig.setUpdatetime( dto.getUpdatetime() );
        hiveTaskConfig.setUpdateuser( dto.getUpdateuser() );

        return hiveTaskConfig;
    }

    @Override
    public List<HiveTaskConfig> toEntity(List<HiveTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HiveTaskConfig> list = new ArrayList<HiveTaskConfig>( dtoList.size() );
        for ( HiveTaskConfigDto hiveTaskConfigDto : dtoList ) {
            list.add( toEntity( hiveTaskConfigDto ) );
        }

        return list;
    }
}
