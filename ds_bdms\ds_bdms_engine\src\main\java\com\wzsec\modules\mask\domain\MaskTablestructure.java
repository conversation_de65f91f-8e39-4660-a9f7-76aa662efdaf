package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

//import javax.validation.constraints.*;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Entity
@Data
@Table(name="sdd_mask_tablestructure")
public class MaskTablestructure implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 字段id */
    @Column(name = "fieldid")
    private String fieldid;

    /** 字段名 */
    @Column(name = "fieldename")
    private String fieldename;

    /** 字段中文名 */
    @Column(name = "fieldcname")
    private String fieldcname;

    /** 表id */
    @Column(name = "tableid")
    private String tableid;

    /** 表名 */
    @Column(name = "tablename")
    private String tablename;

    /** 表中文名 */
    @Column(name = "tablecname")
    private String tablecname;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 数据属性(1标识符，2，准标识，3，敏感，4，非敏感） */
    @Column(name = "attribute")
    private String attribute;

    /** 数据源 */
    @Column(name = "source")
    private String source;

    /** 创建时间 */
    @Column(name = "createtime")
    private String createtime;

    /** 更新时间 */
    @Column(name = "updatetime")
    private String updatetime;

    /** K匿名算法 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskTablestructure source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
