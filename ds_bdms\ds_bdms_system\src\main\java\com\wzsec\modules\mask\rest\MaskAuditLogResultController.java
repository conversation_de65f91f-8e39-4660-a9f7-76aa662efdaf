package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskAuditLogResult;
import com.wzsec.modules.mask.service.MaskAuditLogResultService;
import com.wzsec.modules.mask.service.dto.MaskAuditLogResultQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-01-19
*/
// @Api(tags = "脱敏审计接口日志结果管理")
@RestController
@RequestMapping("/api/maskAuditLogResult")
public class MaskAuditLogResultController {

    private final MaskAuditLogResultService maskAuditLogResultService;

    public MaskAuditLogResultController(MaskAuditLogResultService maskAuditLogResultService) {
        this.maskAuditLogResultService = maskAuditLogResultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskAuditLogResult:list')")
    public void download(HttpServletResponse response, MaskAuditLogResultQueryCriteria criteria) throws IOException {
        maskAuditLogResultService.download(maskAuditLogResultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询脱敏审计接口日志结果")
    // @ApiOperation("查询脱敏审计接口日志结果")
    @PreAuthorize("@el.check('maskAuditLogResult:list')")
    public ResponseEntity<Object> getMaskAuditLogResults(MaskAuditLogResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskAuditLogResultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增脱敏审计接口日志结果")
    // @ApiOperation("新增脱敏审计接口日志结果")
    @PreAuthorize("@el.check('maskAuditLogResult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskAuditLogResult resources){
        return new ResponseEntity<>(maskAuditLogResultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改脱敏审计接口日志结果")
    // @ApiOperation("修改脱敏审计接口日志结果")
    @PreAuthorize("@el.check('maskAuditLogResult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskAuditLogResult resources){
        maskAuditLogResultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除脱敏审计接口日志结果")
    // @ApiOperation("删除脱敏审计接口日志结果")
    @PreAuthorize("@el.check('maskAuditLogResult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskAuditLogResultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
