package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoAnonymizationTaskConfigService;
import com.wzsec.dotask.mask.service.DoKafkaTaskConfigService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;

/**
 * 匿名化执行任务
 */
@RestController
@RequestMapping("/engine/anonymization/task")
public class AnonymizationTaskController {

    private final DoAnonymizationTaskConfigService doAnonymizationTaskConfigService;

    private final UserService userService;

    public AnonymizationTaskController(DoAnonymizationTaskConfigService doAnonymizationTaskConfigService, UserService userService) {
        this.doAnonymizationTaskConfigService = doAnonymizationTaskConfigService;
        this.userService = userService;
    }

    @Log("执行匿名化脱敏任务")
    @PostMapping(value = "/exec/{id}")
    public ResponseEntity<Object> execution(@PathVariable Long id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doAnonymizationTaskConfigService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
