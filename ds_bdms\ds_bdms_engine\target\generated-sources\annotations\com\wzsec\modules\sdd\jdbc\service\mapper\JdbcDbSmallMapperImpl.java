package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcDb;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcDbSmallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JdbcDbSmallMapperImpl implements JdbcDbSmallMapper {

    @Override
    public JdbcDbSmallDto toDto(JdbcDb entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcDbSmallDto jdbcDbSmallDto = new JdbcDbSmallDto();

        jdbcDbSmallDto.setId( entity.getId() );
        jdbcDbSmallDto.setLogicdbcname( entity.getLogicdbcname() );
        jdbcDbSmallDto.setLogicdbename( entity.getLogicdbename() );

        return jdbcDbSmallDto;
    }

    @Override
    public List<JdbcDbSmallDto> toDto(List<JdbcDb> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcDbSmallDto> list = new ArrayList<JdbcDbSmallDto>( entityList.size() );
        for ( JdbcDb jdbcDb : entityList ) {
            list.add( toDto( jdbcDb ) );
        }

        return list;
    }

    @Override
    public JdbcDb toEntity(JdbcDbSmallDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcDb jdbcDb = new JdbcDb();

        jdbcDb.setId( dto.getId() );
        jdbcDb.setLogicdbcname( dto.getLogicdbcname() );
        jdbcDb.setLogicdbename( dto.getLogicdbename() );

        return jdbcDb;
    }

    @Override
    public List<JdbcDb> toEntity(List<JdbcDbSmallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcDb> list = new ArrayList<JdbcDb>( dtoList.size() );
        for ( JdbcDbSmallDto jdbcDbSmallDto : dtoList ) {
            list.add( toEntity( jdbcDbSmallDto ) );
        }

        return list;
    }
}
