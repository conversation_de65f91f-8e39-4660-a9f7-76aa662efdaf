package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyTableAllSub;
import com.wzsec.modules.mask.service.dto.MaskStrategyTableAllSubDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:02+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskStrategyTableAllSubMapperImpl implements MaskStrategyTableAllSubMapper {

    @Override
    public MaskStrategyTableAllSubDto toDto(MaskStrategyTableAllSub entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyTableAllSubDto maskStrategyTableAllSubDto = new MaskStrategyTableAllSubDto();

        maskStrategyTableAllSubDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyTableAllSubDto.setDataname( entity.getDataname() );
        maskStrategyTableAllSubDto.setId( entity.getId() );
        maskStrategyTableAllSubDto.setMaskruleid( entity.getMaskruleid() );
        maskStrategyTableAllSubDto.setParam( entity.getParam() );
        maskStrategyTableAllSubDto.setSecretkey( entity.getSecretkey() );
        maskStrategyTableAllSubDto.setSparefield1( entity.getSparefield1() );
        maskStrategyTableAllSubDto.setSparefield2( entity.getSparefield2() );
        maskStrategyTableAllSubDto.setSparefield3( entity.getSparefield3() );
        maskStrategyTableAllSubDto.setSparefield4( entity.getSparefield4() );
        maskStrategyTableAllSubDto.setStrategyid( entity.getStrategyid() );

        return maskStrategyTableAllSubDto;
    }

    @Override
    public List<MaskStrategyTableAllSubDto> toDto(List<MaskStrategyTableAllSub> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyTableAllSubDto> list = new ArrayList<MaskStrategyTableAllSubDto>( entityList.size() );
        for ( MaskStrategyTableAllSub maskStrategyTableAllSub : entityList ) {
            list.add( toDto( maskStrategyTableAllSub ) );
        }

        return list;
    }

    @Override
    public MaskStrategyTableAllSub toEntity(MaskStrategyTableAllSubDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyTableAllSub maskStrategyTableAllSub = new MaskStrategyTableAllSub();

        maskStrategyTableAllSub.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyTableAllSub.setDataname( dto.getDataname() );
        maskStrategyTableAllSub.setId( dto.getId() );
        maskStrategyTableAllSub.setMaskruleid( dto.getMaskruleid() );
        maskStrategyTableAllSub.setParam( dto.getParam() );
        maskStrategyTableAllSub.setSecretkey( dto.getSecretkey() );
        maskStrategyTableAllSub.setSparefield1( dto.getSparefield1() );
        maskStrategyTableAllSub.setSparefield2( dto.getSparefield2() );
        maskStrategyTableAllSub.setSparefield3( dto.getSparefield3() );
        maskStrategyTableAllSub.setSparefield4( dto.getSparefield4() );
        maskStrategyTableAllSub.setStrategyid( dto.getStrategyid() );

        return maskStrategyTableAllSub;
    }

    @Override
    public List<MaskStrategyTableAllSub> toEntity(List<MaskStrategyTableAllSubDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyTableAllSub> list = new ArrayList<MaskStrategyTableAllSub>( dtoList.size() );
        for ( MaskStrategyTableAllSubDto maskStrategyTableAllSubDto : dtoList ) {
            list.add( toEntity( maskStrategyTableAllSubDto ) );
        }

        return list;
    }
}
