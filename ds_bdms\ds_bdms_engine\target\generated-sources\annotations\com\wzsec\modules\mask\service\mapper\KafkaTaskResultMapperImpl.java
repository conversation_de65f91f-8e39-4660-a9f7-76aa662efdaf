package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.KafkaTaskResult;
import com.wzsec.modules.mask.service.dto.KafkaTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KafkaTaskResultMapperImpl implements KafkaTaskResultMapper {

    @Override
    public KafkaTaskResultDto toDto(KafkaTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        KafkaTaskResultDto kafkaTaskResultDto = new KafkaTaskResultDto();

        kafkaTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        kafkaTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        kafkaTaskResultDto.setDbname( entity.getDbname() );
        kafkaTaskResultDto.setEndtime( entity.getEndtime() );
        kafkaTaskResultDto.setId( entity.getId() );
        kafkaTaskResultDto.setMaskline( entity.getMaskline() );
        kafkaTaskResultDto.setRediskey( entity.getRediskey() );
        kafkaTaskResultDto.setSparefield1( entity.getSparefield1() );
        kafkaTaskResultDto.setSparefield2( entity.getSparefield2() );
        kafkaTaskResultDto.setSparefield3( entity.getSparefield3() );
        kafkaTaskResultDto.setSparefield4( entity.getSparefield4() );
        kafkaTaskResultDto.setSparefield5( entity.getSparefield5() );
        kafkaTaskResultDto.setStarttime( entity.getStarttime() );
        kafkaTaskResultDto.setStrategydetail( entity.getStrategydetail() );
        kafkaTaskResultDto.setStrategyname( entity.getStrategyname() );
        kafkaTaskResultDto.setTabname( entity.getTabname() );
        kafkaTaskResultDto.setTaskname( entity.getTaskname() );
        kafkaTaskResultDto.setTaskstatus( entity.getTaskstatus() );

        return kafkaTaskResultDto;
    }

    @Override
    public List<KafkaTaskResultDto> toDto(List<KafkaTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KafkaTaskResultDto> list = new ArrayList<KafkaTaskResultDto>( entityList.size() );
        for ( KafkaTaskResult kafkaTaskResult : entityList ) {
            list.add( toDto( kafkaTaskResult ) );
        }

        return list;
    }

    @Override
    public KafkaTaskResult toEntity(KafkaTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        KafkaTaskResult kafkaTaskResult = new KafkaTaskResult();

        kafkaTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        kafkaTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        kafkaTaskResult.setDbname( dto.getDbname() );
        kafkaTaskResult.setEndtime( dto.getEndtime() );
        kafkaTaskResult.setId( dto.getId() );
        kafkaTaskResult.setMaskline( dto.getMaskline() );
        kafkaTaskResult.setRediskey( dto.getRediskey() );
        kafkaTaskResult.setSparefield1( dto.getSparefield1() );
        kafkaTaskResult.setSparefield2( dto.getSparefield2() );
        kafkaTaskResult.setSparefield3( dto.getSparefield3() );
        kafkaTaskResult.setSparefield4( dto.getSparefield4() );
        kafkaTaskResult.setSparefield5( dto.getSparefield5() );
        kafkaTaskResult.setStarttime( dto.getStarttime() );
        kafkaTaskResult.setStrategydetail( dto.getStrategydetail() );
        kafkaTaskResult.setStrategyname( dto.getStrategyname() );
        kafkaTaskResult.setTabname( dto.getTabname() );
        kafkaTaskResult.setTaskname( dto.getTaskname() );
        kafkaTaskResult.setTaskstatus( dto.getTaskstatus() );

        return kafkaTaskResult;
    }

    @Override
    public List<KafkaTaskResult> toEntity(List<KafkaTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KafkaTaskResult> list = new ArrayList<KafkaTaskResult>( dtoList.size() );
        for ( KafkaTaskResultDto kafkaTaskResultDto : dtoList ) {
            list.add( toEntity( kafkaTaskResultDto ) );
        }

        return list;
    }
}
