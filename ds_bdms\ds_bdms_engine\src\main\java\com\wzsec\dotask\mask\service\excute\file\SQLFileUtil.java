package com.wzsec.dotask.mask.service.excute.file;

import lombok.Data;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

/**
 * sql文件Util
 */
@Data
public class SQLFileUtil {

    private String fileName;//文件名称(不含后缀)
    private String fileFullName;//文件全称(含后缀)
    private String fileSuffix;//文件后缀(例如：.sql)

    private List<SingleTableSQLFileUtil> singleTableSQLFileUtilList = new ArrayList<>();

    public SQLFileUtil(String absolutePath) throws Exception {
        // 创建一个File对象
        File file = new File(absolutePath);
        // 获取文件名
        String filedFullName = file.getName();
        // 获取文件后缀
        String filedSuffix = "";
        String fileName = "";
        int lastDotIndex = filedFullName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            filedSuffix = filedFullName.substring(lastDotIndex);
            fileName = filedFullName.substring(0,lastDotIndex);
        } else {
            //不能被.拆分，说明该文件没有后缀
            fileName = filedFullName;
        }
        this.fileName = fileName;
        this.fileFullName = filedFullName;
        this.fileSuffix = filedSuffix;

        // 1. 按行读取.sql文件
        BufferedReader reader = new BufferedReader(new FileReader(absolutePath));
        String readerLineData = null;
        List<String> lineList = new ArrayList<>();
        while ((readerLineData = reader.readLine()) != null) {
            lineList.add(readerLineData);
        }
        reader.close();

        boolean isCreateSql = false;
        String createSql = "";
        Integer tabCount = 0;
        SingleTableSQLFileUtil singleTableSQLFileUtil = new SingleTableSQLFileUtil();
        // 2. 寻找insert into语句并提取数据
        for (String line : lineList) {
            //TODO CREATE TABLE开头 为建表sql，需提取出来做字段改造
            if (line.startsWith("CREATE TABLE")) {
                isCreateSql = true;
            }
            if (isCreateSql) {
                createSql += line + "\n";
            }
            if (isCreateSql && line.endsWith(";")) {
                singleTableSQLFileUtil.setCreateTableSql(createSql);
                isCreateSql = false;
                createSql = "";
            } else {
                //TODO 删除原表sql
                if (line.startsWith("DROP TABLE") && line.endsWith(";")) {
                    if (tabCount != 0) {
                        singleTableSQLFileUtilList.add(singleTableSQLFileUtil);
                    }
                    //到新表了执行删除原表的sql，重新生成成单表Util
                    singleTableSQLFileUtil = new SingleTableSQLFileUtil();
                    singleTableSQLFileUtil.setDropTableSql(line);
                    tabCount ++;
                    //TODO INSERT INTO开头，分号结尾，说明是插入的sql，需要从里提取出敏感数据
                } else if (line.startsWith("INSERT INTO") && line.endsWith(";")) {
                    List<String> insertSql;
                    if (singleTableSQLFileUtil.getInsertSql() != null) {
                        insertSql = singleTableSQLFileUtil.getInsertSql();
                    } else {
                        insertSql = new ArrayList<>();
                    }
                    insertSql.add(line);
                    singleTableSQLFileUtil.setInsertSql(insertSql);

                    //TODO 其他sql
                } else if (line.endsWith(";")) {
                    List<String> restsSql;
                    if (singleTableSQLFileUtil.getRestsSql() != null) {
                        restsSql = singleTableSQLFileUtil.getRestsSql();
                    } else {
                        restsSql = new ArrayList<>();
                    }
                    restsSql.add(line);
                    singleTableSQLFileUtil.setRestsSql(restsSql);
                }
            }
        }

        //set最后一张表SQL
        singleTableSQLFileUtilList.add(singleTableSQLFileUtil);
    }

}
