package com.wzsec.utils.database;

import com.alibaba.fastjson.JSON;
import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 创建MySQL表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description 通过java List<Map<String,
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2019年10月11日 上午10:46:44
 */
@Slf4j
public class MysqlUtil extends DatabaseUtil {

    private static String JDBC_DRIVER = "com.mysql.jdbc.Driver";


    /*public static void main(String[] args) {
        String srcCreateTableSql_Test = getSrcCreateTableSql("t_test", "*****************************************************************", "root", "root");
        //System.out.println(srcCreateTableSql_Test);
        String a = srcCreateTableSql_Test.substring(srcCreateTableSql_Test.indexOf("(") + 1, srcCreateTableSql_Test.lastIndexOf(")"));
        List<String> maskfields = new ArrayList<String>();
        maskfields.add("name");
        List<String> fields = new ArrayList<String>();
        fields.add("id");
        fields.add("name");
        fields.add("age");
    	*//*String createTableSql_Test = getCreateTableSql(srcCreateTableSql_Test,"t_test11",maskfields,fields);
    	System.out.println(createTableSql_Test);*//*
     *//*List<String>list=getTabDataList("***************************","root","root","dses_20190821","t_user");
    	for (int i = 0; i < list.size(); i++) {
			System.out.println(list.get(i));
		}
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		Map<String, String> map = new HashMap<>();
		map.put("username", "张三");
		map.put("createDate", "2019-10-11 11:01:11");
		list.add(map);
		map = new HashMap<>();
		map.put("username", null);
		map.put("createDate", "2018-10-11 11:01:11");
		list.add(map);
		List<String> maskfields = new ArrayList<>();

		maskfields.add("createDate");
		System.out.println("====创建并批量插入数据到新表最终结果：" + DatabaseUtil.insertData2NewTable("at_task",
				"jdbc:mysql://*************:3306/jweb?characterEncoding=utf-8", "root", "root", list,
				"bb_" + System.currentTimeMillis(),
				"jdbc:mysql://*************:3306/test_zkx_1011?characterEncoding=utf-8", "root", "root", maskfields));*//*
    }
*/
    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param tableName：表名
     * @param dburl：数据库连接信息
     * @param username：数据库用户名
     * @param password：数据库密码
     * @return List<Map < String, String>>：数据库字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2019年10月12日 下午3:15:07
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SHOW FULL COLUMNS FROM `" + tableName + "`;");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
//                    fieldInfoMap.put("Field", rs.getString("Field"));
//                    fieldInfoMap.put("Type", rs.getString("Type"));
//                    fieldInfoMap.put("Null", rs.getString("Null"));
//                    fieldInfoMap.put("Key", rs.getString("Key"));
//                    fieldInfoMap.put("Default", rs.getString("Default"));
//                    fieldInfoMap.put("Extra", rs.getString("Extra"));
//                    fieldInfoMap.put("Privileges", rs.getString("Privileges"));
//                    fieldInfoMap.put("Comment", rs.getString("Comment"));

                    //TODO 所有DBUtil字段信息尽量统一，以便支持不同数据源类型之间脱敏
                    fieldInfoMap.put("FieldEName", rs.getString("Field"));//字段名
                    fieldInfoMap.put("FieldCName", rs.getString("Comment"));//注释
                    fieldInfoMap.put("FieldType", rs.getString("Type"));//格式：varchar(255)

                    //TODO Mysql特有
                    fieldInfoMap.put("Key", rs.getString("Key"));//主外键
                    fieldInfoMap.put("Null", rs.getString("Null"));//是否为空
                    fieldInfoMap.put("Default", rs.getString("Default"));//默认值
                    fieldInfoMap.put("Extra", rs.getString("Extra"));// auto_increment为自增
                    fieldInfoMap.put("Privileges", rs.getString("Privileges"));//权限
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(rs, stmt, conn);
        return fieldInfoList;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("FieldEName")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    if (fieldInfo.get("FieldType").contains("blob")){
                        sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                    }else {
                        sb.append(" varchar(255)");// 类型
                    }
                } else {
                    sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    }
//                    else {
//                        sb.append(" DEFAULT NULL");
//                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("FieldCName") != null && !"".equals(fieldInfo.get("FieldCName"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("FieldCName")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)ENGINE=InnoDB DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");

            for (Map<String, String> fieldInfo : fieldInfoList) {
                String fieldEName = fieldInfo.get("FieldEName");
                String fieldType = fieldInfo.get("FieldType");
                if (!obj.keySet().contains(fieldEName)) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldEName).append("`");// 字段名

                if (watermarkField != null && watermarkField.equals(fieldEName)) {
                    sb.append(" longtext");// 选择注入水印的字段长度改为
                } else if (maskfields != null && maskfields.contains(fieldEName)) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    //字符型数据转换
                    if (fieldType.toLowerCase().contains("nvarchar") || fieldType.toLowerCase().contains("char2")) {
                        sb.append(" varchar(255)");

                        //数字型数据转换
                    } else if (fieldType.toLowerCase().contains("number")) {
                        /*String dataLength = "11";//默认给个11，大都够用
                        if (fieldType.contains("(")){
                            int startIndex = fieldType.indexOf("(") + 1;
                            int endIndex = fieldType.indexOf(")");
                            dataLength = fieldType.substring(startIndex,endIndex);
                        }*/
                        sb.append(" long");

                        //大文本类型转换
                    } else if (fieldType.toLowerCase().contains("text") || fieldType.toLowerCase().contains("clob")) {
                        sb.append(" longtext");

                        //日期类型转换
                    } else if (fieldType.toLowerCase().contains("decimal") || fieldType.toLowerCase().contains("date")) {
                        sb.append(" datetime");
                    } else {
                        sb.append(" ").append(fieldType);
                    }
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    }
//                    else {
//                        sb.append(" DEFAULT NULL");
//                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("FieldCName") != null && !"".equals(fieldInfo.get("FieldCName"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("FieldCName")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)ENGINE=InnoDB DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, String> obj,
                                       String tableName, List<String> maskfields, String dbname) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            // sb.append("\r\nDROP TABLE IF EXISTS
            // ").append("`").append(tableName).append("`").append(";\r\n");//删除表语句
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("FieldEName")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    if (fieldInfo.get("FieldType").contains("blob")){
                        sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                    }else {
                        sb.append(" varchar(255)");// 类型
                    }
                } else {
                    sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    }
//                    else {
//                        sb.append(" DEFAULT NULL");
//                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("FieldCName") != null && !"".equals(fieldInfo.get("FieldCName"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("FieldCName")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)ENGINE=InnoDB DEFAULT CHARSET= utf8;\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "select `table_schema`,`table_name` from `information_schema`.tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where `table_schema` in ('" + dbnames + "') ";
            }
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String dbName = rs.getString(1);
                String table_name = rs.getString(2);
//                if (!"sys".equals(dbName) && !"mysql".equals(dbName) && !"information_schema".equals(dbName) && !"performance_schema".equals(dbName)) {
                if (!"sys".equals(dbName) && !"information_schema".equals(dbName) && !"performance_schema".equals(dbName)) {
                    if (dbTabMap.containsKey(dbName)) {
                        dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + table_name);
                    } else {
                        dbTabMap.put(dbName, table_name);
                    }
                }

            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        }finally {
            closeCon(rs,stmt,null);
        }
        return dbTabMap;
    }

    /**
     * 获取数据库中所有的库名表名
     * @param dburl
     * @param username
     * @param password
     * @param dbnames
     * @return
     */
    public static Map<String, String> getAllDbAndTabMap(String dburl, String username, String password, String dbnames) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "select `table_schema`,`table_name` from `information_schema`.tables ";
            if (dbnames != null && !"".equals(dbnames)) {
                strSQL += " where `table_schema` in (" + dbnames + ") ";
            }
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String dbName = rs.getString(1);
                String table_name = rs.getString(2);
                if (!"sys".equals(dbName) && !"mysql".equals(dbName) && !"information_schema".equals(dbName) && !"performance_schema".equals(dbName)) {
                    if (dbTabMap.containsKey(dbName)) {
                        dbTabMap.put(dbName, dbTabMap.get(dbName) + "," + table_name);
                    } else {
                        dbTabMap.put(dbName, table_name);
                    }
                }

            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return dbTabMap;
    }

    /**
     * @Description:获取数据库表数据
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " order by rand() LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                //                StringBuffer sub = new StringBuffer();
//                for (int i = 1; i <= columnCount; i++) {
//                    if (i > 1) {
//                        sub.append(";;");
//                    }
//                    sub.append(rs.getObject(i));
//                }
//                tabDataList.add(sub.toString());

                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        }finally {
            closeCon(rs,stmt,null);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static List<String> getTabDataList(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String> tabDataList = new ArrayList<String>();
        try {
            String strSQL = "select * from " + dbname + "." + tabname + " order by rand() LIMIT 100 ";
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                StringBuffer sub = new StringBuffer();
                for (int i = 1; i <= columnCount; i++) {
                    if (i > 1) {
                        sub.append(Const.DB_TAB_DATA_JOIN);
                    }
                    sub.append(rs.getObject(i));
                }
                tabDataList.add(sub.toString());
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + dbname + "." + tabname + " ";
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }

    /**
     * @Description:获取数据库某个字段非空数据
     * <AUTHOR>
     * @date 2021-03-12
     */
    public static List<String[]> getFieldDataList(Connection conn, String dbname, String tabname, String field, Integer lineNum) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select " + field + " from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " where " + field + " is not null and " + field + " !='' order by rand() LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception e) {
            String[] passNull = {""};
            tabDataList.add(passNull);
            //System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        }finally {
            closeCon(rs,stmt,null);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return count;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' ORDER BY  ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } catch (Exception ex) {
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
            throw ex;
        } finally {
            closeCon(rs,stmt,null);
        }
        return list;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(String url, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            conn = getConn(JDBC_DRIVER, url, username, password);// 打开连接
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' ORDER BY  ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1).toUpperCase());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return list;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(String url, String username, String password, String dbname, String tabname, String tablenameAlias) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            conn = getConn(JDBC_DRIVER, url, username, password);// 打开连接
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' ORDER BY  ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(tablenameAlias + "." + rs.getString(1));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return list;
    }


    /**
     * @param tablename：表名
     * @param dbname：库名
     * @param url：数据库连接信息
     * @param dbUserName：数据库用户名
     * @param dbPassword：数据库密码
     * @return Map<String, String>  Map<字段,表名>
     * @throws Exception
     * @Description 获取到多个表中独有的字段
     * <AUTHOR>
     * @date 2020年8月13日 下午8:23:25
     */
    public static Map<String, String> getFieldTabMap(String url, String dbUserName, String dbPassword, String dbname,
                                                     String tablename) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        //统计多个表中重复字段
        List<String> fieldList = new ArrayList<String>();
        Map<String, String> fieldTabMap = new HashMap<String, String>();
        String[] tabnames = tablename.split(",");
        try {
            conn = getConn(JDBC_DRIVER, url, dbUserName, dbPassword);
            conn.setAutoCommit(false);
            stmt = conn.createStatement();
//            setIgnoreCase(stmt);
            for (int i = 0; i < tabnames.length; i++) {
                tablename = tabnames[i].split(" ")[0];
                String sql = "";
                if (tablename.contains(".")) {
                    String strDbname = tablename.split("\\.")[0];
                    String strTabname = tablename.split("\\.")[1];
                    sql = "select COLUMN_NAME from INFORMATION_SCHEMA.Columns where table_name= '" + strTabname + "' and table_schema ='" + strDbname + "' ORDER BY  ORDINAL_POSITION";
                } else {
                    sql = "select COLUMN_NAME from INFORMATION_SCHEMA.Columns where table_name= '" + tablename + "' and table_schema ='" + dbname + "'  ORDER BY  ORDINAL_POSITION";
                }
                rs = stmt.executeQuery(sql);
                conn.commit();
                while (rs.next()) {
                    String field = rs.getString("COLUMN_NAME");
                    if (fieldTabMap.containsKey(field)) {
                        if (!fieldList.contains(field)) {
                            fieldList.add(field);
                        }
                    } else {
                        fieldTabMap.put(field, tablename);
                    }
                }
            }
            //删除多个表中重复字段
            for (int i = 0; i < fieldList.size(); i++) {
                if (fieldTabMap.containsKey(fieldList.get(i))) {
                    fieldTabMap.remove(fieldList.get(i));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return fieldTabMap;
    }

    /**
     * @Description: 拼接建表语句
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static String getCreateTableSql(String srcsql, List<Map<String, String>> fieldInfoList,
                                           Map<String, Object> obj, String tableName,String dbName,
                                           List<String> maskfields, String watermarkField) {
        Set<String> keySet = obj.keySet();
        StringBuffer sb = new StringBuffer();
        StringBuffer end_sb = new StringBuffer();
        String end_sql = null;

        String primaryKeyStr = "PRIMARY KEY";
        String foreignKeyStr = "FOREIGN KEY";
        String usingBtreeStr = "USING BTREE";

        //截取表注释，以免表注释里含有括号，影响表结构截取拆分
        int index = srcsql.lastIndexOf(")") - 1;
        index = srcsql.lastIndexOf(")", index);
        if (index > 0) {
            int end_index = srcsql.indexOf(") ENGINE");
            end_sql = srcsql.substring(end_index,srcsql.length());
        } else {
            end_sql = srcsql.substring(srcsql.lastIndexOf(")"), srcsql.length());
        }

        String middle_sql = srcsql.substring(srcsql.indexOf("(") + 1, srcsql.indexOf(end_sql));
        String[] srcfilemiddlesql = middle_sql.split(",\n");

        HashMap<String, String> srcfieldsql = new HashMap<String, String>();
        for (String s : srcfilemiddlesql) {
            String str = s.trim();
            String fieldname = str.substring(1, str.lastIndexOf("`"));
            if (str.startsWith("`")) {
                srcfieldsql.put(fieldname, str);
            } else {
//                if (str.contains(primaryKeyStr) || str.contains(foreignKeyStr) || str.contains(usingBtreeStr)){
//                    continue;
//                }
                for (String extractfieldnamestr : keySet) {
                    if (str.contains(extractfieldnamestr)) {
                        //能进入到这个if，说明该表字段有包含关系，例如：txts - txt
                        //其中 txts 字段追加进end_sb过之后，txt 一样可以进入到该if中
                        //因此，需要校验end_sb里，在本次if之前是否已经追加过str
                        //如果追加过不需要重复加入了，没有追加过可继续追加
                        String str1 = str+",\r\n";
                        boolean contains = end_sb.toString().contains(str1);
                        if (!contains){
                            end_sb.append(str).append(",\r\n");
                        }
                    }
                }
            }
        }
        sb.append("CREATE TABLE ").append("`"+dbName+"`.").append("`"+tableName+"`").append(" (\r\n");

        // 检查当前表 所有列字段总长度乘算过后是否大于 65535
        // UTF-8编码下列字段总长度*3，gbk编码下列字段中长度*2
        Integer fieldLength = 0;
        boolean fieldFlag = false;
        for (Map<String, String> fieldInfo : fieldInfoList) {
            String fieldType = fieldInfo.get("FieldType");
            if (fieldType.contains("(") && fieldType.contains(")")) {
                int begIndex = fieldType.indexOf("(") + 1;
                int endIndex = fieldType.indexOf(")");
                //数据类型可能是 decimal(10,0) 这种，不需要统计长度
                int i = fieldType.substring(begIndex, endIndex).indexOf(",");
                if (i==-1){
                    Integer integer = Integer.valueOf(fieldType.substring(begIndex, endIndex));
                    fieldLength += integer;
                }
            }
        }
        // 脱敏后的表字段长度会改动，改动幅度估为原本字段长度*4(默认utf8mb4编码，一个字符可能需要最多4个字节) *2(脱敏字段长度翻倍)
        // 超过此幅度该表的脱敏字段全部置为text类型
		/*if (fieldLength * 4 * 2 >= 65535) {
			fieldFlag = true;
		}*/
        // TODO 2024.3.29调整  长度和mysql数据库配置有关(InnoDB存储引擎对单行的最大字节大小有限制)
        // TODO 错误信息：Row size too large (> 8126). Changing some columns to TEXT or BLOB may help. In current row format, BLOB prefix of 0 bytes is stored inline.
        if (fieldLength * 4 * 2 > 8126) {
            fieldFlag = true;
        }

        for (Map<String, String> fieldInfo : fieldInfoList) {
            String field = fieldInfo.get("FieldEName");
            String fieldType = fieldInfo.get("FieldType");
            if (!keySet.contains(field)) {// 跳过没有抽取的列
                //匹配到该字段设置成了主键或外键，取消设置
                String endSbString = end_sb.toString();
                if (maskfields.contains(field) && (endSbString.contains(primaryKeyStr) || endSbString.contains(foreignKeyStr) || endSbString.contains(usingBtreeStr))
                        && endSbString.toString().contains("`" + field + "`")) {
                    String ketField = "`" + field + "`";
                    String[] split = endSbString.split("\n");
                    for (String keSql : split) {
                        if (keSql.contains(ketField)){
                            endSbString = endSbString.replace(keSql,"");
                        }
                    }
                    end_sb = new StringBuffer(endSbString);
                }
                continue;
            }
            String fieldsql = srcfieldsql.get(field);

            if (watermarkField!= null && watermarkField.equalsIgnoreCase(field)){
                fieldType = " longtext ";// 选择注入水印的字段长度设置为最大
                int post = 0 ;
                if (fieldType.contains("blob")){
                    post = fieldsql.length();
                }else {
                    post = appearNumber(fieldsql, " ", 2);
                }
                String str = fieldsql.substring(post, fieldsql.length());
                String sql = "`" + field + "`" + fieldType + str;
                fieldsql = sql;
            }

            //脱敏字段去除unsigned关键字，该关键字只能对数字类型生效，脱敏后数据变成其他类型建表会报错
            if (fieldsql.contains("unsigned")){
                fieldsql = fieldsql.replace("unsigned","");
            }

            //脱敏字段去除zerofill关键字，该关键字只能对数字类型生效
            if (fieldsql.contains("zerofill")){
                fieldsql = fieldsql.replace("zerofill","");
            }

            //bit类型数据写入时不兼容 '0' 写法，最小化改动生成建表sql时替换为varchar类型
            //bit(1)，使用bit(作为关键字替换，以免字段名包含bit关键字
            if (fieldType.contains("bit(")){
                fieldType = fieldType.replace("bit(","varchar(");
                fieldsql = fieldsql.replace("bit(","varchar(");
            }

            //判断是否有设置自增，如果有就去掉
            if (fieldsql.contains("AUTO_INCREMENT")){
                fieldsql = fieldsql.replace("AUTO_INCREMENT","");
            }

            if (maskfields != null && maskfields.contains(field)) {// 脱敏的字段类型更改为varchar
                //post为字段类别后下标开始位置
                int post = 0 ;
                if (fieldType.contains("blob")){
                    post = fieldsql.length();
                }else {
                    post = appearNumber(fieldsql, " ", 2);
                }
                //str为字段类别后的内容，比如表注释，可能为空
                String str = fieldsql.substring(post, fieldsql.length());

                //脱敏字段去除默认设置
                if (str.contains("DEFAULT")){
                    int aDefault = str.indexOf("DEFAULT");
                    int lastIndex = str.length();
                    if (str.contains("COMMENT")) {
                        lastIndex = str.indexOf("COMMENT");
                    }
                    String defaultStr = str.substring(aDefault,lastIndex);
                    str = str.replace(defaultStr,"");
                }

                //判断脱敏字段是否设置为主键、外键
                //主键、外键设置在建表sql下方，倒序循环可以提升匹配效率
                for (int k = srcfilemiddlesql.length - 1; k > 0; k--) {
                    //匹配到该字段设置成了主键、外键，就置空
                    if (maskfields.contains(field) && (srcfilemiddlesql[k].contains(primaryKeyStr) || srcfilemiddlesql[k].contains(foreignKeyStr)
                            || srcfilemiddlesql[k].contains(usingBtreeStr)) && srcfilemiddlesql[k].contains("`" + field + "`")) {
                        String priKeySql = srcfilemiddlesql[k].trim() + ",";
                        String end_sbStr = end_sb.toString().replace(priKeySql,"");
                        end_sb = new StringBuffer(end_sbStr);
                    }
                }

                sb.append("`" + field + "`");

                sb.append(getModificationFiledType(fieldType, fieldFlag));

                sb.append(str).append(",\r\n");
            } else {
                sb.append(fieldsql).append(",\r\n");
            }

        }
        sb.append(end_sb.toString().trim());
        int lastIndex = sb.lastIndexOf(",");// 去掉最后一个逗号
        return sb.substring(0, lastIndex) + end_sql;
    }

    /**
     * @Description: 获取原有建表语句
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static String getSrcCreateTableSql(String tableName, String dburl, String username, String password) {
        String srccreatetablesql = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("show create table `" + tableName + "`;");
            if (rs != null) {
                while (rs.next()) {
                    srccreatetablesql = rs.getString("Create Table");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(rs, stmt, conn);
        return srccreatetablesql;
    }

    /**
     * 根据当前字段类别，获取脱敏字段类别（改变原有字段类型、扩充字段长度）
     *
     * @param fieldType 字段类别，示例：varchar(255)
     * @param fieldFlag 字段总长度是否超过建表限制
     * @return
     */
    private static String getModificationFiledType(String fieldType, boolean fieldFlag) {
        String returnType = "";
        //原本字段是text类型，原封不动
        if (fieldType.contains("text")) {
            returnType = " " + fieldType + " ";
            return returnType;
        }
        //原本字段是blob类型，原封不动
        if (fieldType.contains("blob")){
            returnType = " " + fieldType + " ";
            return returnType;
        }

        int begIndex = fieldType.indexOf("(") + 1;
        int endIndex = fieldType.indexOf(")");
        Integer filedExtent = 0;

        //fieldType可能没有长度，获取下标为-1时，脱敏后长度为100大都够用
        if (endIndex == -1) {
            filedExtent = 50;
        } else {
            //数据类型可能是 decimal(10,0) 这种，需要特殊处理长度，脱敏后长度为100大都够用
            int i = fieldType.substring(begIndex, endIndex).indexOf(",");
            if (i == -1) {
                filedExtent = Integer.valueOf(fieldType.substring(begIndex, endIndex));
            } else {
                filedExtent = 50;
            }
        }

        if (fieldFlag) {
            returnType = " text ";
        } else {
            //varchar类型，根据原本长度判断需要扩充多少长度
            if (fieldType.contains("varchar")) {
                //varchar长度不宜设置过长，最好不要超过255，过长的数据直接换成text
                if (filedExtent <= 100) {
                    returnType = " varchar(255)";
                } else if (filedExtent <= 127) {
                    returnType = " varchar(255) ";
                } else if (filedExtent <= 255) {
                    returnType = " varchar(500) ";
                } else {
                    returnType = " text ";
                }
            } else {
                //其他类型换成原本长度*2大部分情况都够用
                if (filedExtent <=100){
                    returnType = " varchar(255) ";
                } else {
                    returnType = " varchar(" + filedExtent * 2 + ") ";
                }
            }
        }
        return returnType;
    }

    /**
     * @Description: 获取字符串出现次数的位置
     * <AUTHOR>
     * @date 2020年2月27日
     */
    private static int appearNumber(String fieldsql, String s, int i) {
        Pattern pattern = Pattern.compile(s);
        Matcher findMatcher = pattern.matcher(fieldsql);
        int number = 0;
        while (findMatcher.find()) {
            number++;
            if (number == i) {//当“i”次出现时停止
                break;
            }
        }
        return findMatcher.start();
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT COLUMN_NAME,COLUMN_COMMENT,COLUMN_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "' ORDER BY ORDINAL_POSITION");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COLUMN_COMMENT"));
                    fieldInfoMap.put("fieldType", rs.getString("COLUMN_TYPE"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表信息
     * @Description 查询表信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:56
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TABLE_NAME,TABLE_COMMENT,TABLE_ROWS,ROUND(((data_length + index_length) / 1024 / 1024), 2) AS Size_MB FROM information_schema.TABLES WHERE TABLE_SCHEMA='" + dbName + "' and TABLE_NAME='" + tableName + "'");
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
                tableInfoMap.put("tableRows", rs.getString("TABLE_ROWS"));
                tableInfoMap.put("dataSize", rs.getString("Size_MB"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }

    /**
     *
     *
     * @param dbUrl    数据库连接url
     * @param username 用户名
     * @param password 密码
     * @param dbName   库名
     * @return
     */

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT DISTINCT CONCAT('`', routine_name, '`') AS procedure_name " +
                "FROM information_schema.routines " +
                "WHERE routine_type = 'PROCEDURE' AND routine_schema = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,dbName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String procedureName = rs.getString("procedure_name");
                // 对于每个存储过程名称，使用 SHOW CREATE PROCEDURE 获取创建语句
                PreparedStatement stCreateProcedure = conn.prepareStatement("SHOW CREATE PROCEDURE " + procedureName);
                ResultSet rsCreateProcedure = stCreateProcedure.executeQuery();
                if (rsCreateProcedure != null && rsCreateProcedure.next()) {
                    String createProcedureSql = rsCreateProcedure.getString("Create Procedure");
                    sqlList.add(createProcedureSql);
                }
                rsCreateProcedure.close();
                stCreateProcedure.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT DISTINCT CONCAT('`', routine_name, '`') AS procedure_name " +
                "FROM information_schema.routines " +
                "WHERE routine_type = 'FUNCTION' AND routine_schema = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,dbName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String procedureName = rs.getString("procedure_name");
                // 对于每个存储过程名称，使用 SHOW CREATE PROCEDURE 获取创建语句
                PreparedStatement stCreateProcedure = conn.prepareStatement("SHOW CREATE FUNCTION " + procedureName);
                ResultSet rsCreateProcedure = stCreateProcedure.executeQuery();
                if (rsCreateProcedure != null && rsCreateProcedure.next()) {
                    String createProcedureSql = rsCreateProcedure.getString("Create Function");
                    sqlList.add(createProcedureSql);
                }
                rsCreateProcedure.close();
                stCreateProcedure.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT TRIGGER_NAME AS 'Trigger_Name', EVENT_MANIPULATION AS 'Event', " +
                "EVENT_OBJECT_TABLE AS 'Table_Name', ACTION_STATEMENT AS 'Action_Statement', " +
                " ACTION_TIMING AS 'Timing', TRIGGER_SCHEMA AS 'Database_Name' " +
                "FROM information_schema.TRIGGERS WHERE TRIGGER_SCHEMA = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String triggerSqlStr = "CREATE TRIGGER %s %s %s ON %s FOR EACH ROW %s;";
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,dbName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String triggerName = rs.getString("Trigger_Name");
                String timing = rs.getString("Timing");
                String event = rs.getString("Event");
                String tableName = rs.getString("Table_Name");
                String actionStatement = rs.getString("Action_Statement");
                String triggerSql = String.format(triggerSqlStr, triggerName, timing, event,tableName,actionStatement);
                sqlList.add(triggerSql);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        List<String> sqlList = new ArrayList<>();
        String sql = "SELECT TABLE_NAME, VIEW_DEFINITION FROM information_schema.VIEWS " +
                "WHERE TABLE_SCHEMA = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1,inDBName);
            rs = stmt.executeQuery();
            while (rs.next()){
                String tableName = rs.getString("TABLE_NAME");
                // 对于每个存储过程名称，使用 SHOW CREATE PROCEDURE 获取创建语句
                PreparedStatement stCreate = conn.prepareStatement("SHOW CREATE VIEW `" + tableName + "`");
                ResultSet rsCreate = stCreate.executeQuery();
                if (rsCreate != null && rsCreate.next()) {
                    String createSql = rsCreate.getString(2);
                    createSql = createSql.replace("`"+inDBName+"`","`"+outDBName+"`");//库名替换
                    sqlList.add(createSql);
                }
                rsCreate.close();
                stCreate.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
            return sqlList;
        }
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        //Mysql没有序列的概念，自增ID建表时会自带
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        // 索引建表时会自带，也可额外设置
        List<String> sqlList = new ArrayList<>();
        // 用于拼接列名的临时 Map
        Map<String, List<String>> indexColumnMap = new HashMap<>();
        String sql = "SELECT TABLE_SCHEMA, TABLE_NAME, INDEX_NAME, COLUMN_NAME, SEQ_IN_INDEX " +
                "FROM information_schema.STATISTICS " +
                "WHERE TABLE_SCHEMA = ? " +
                "ORDER BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dbUrl, username, password);
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, inDBName);
            rs = stmt.executeQuery();
            while (rs.next()) {
                String tableSchema = rs.getString("TABLE_SCHEMA");
                String tableName = rs.getString("TABLE_NAME");
                String indexName = rs.getString("INDEX_NAME");
                String columnName = rs.getString("COLUMN_NAME");

                // 跳过主键索引，因为主键应该使用 ALTER TABLE 或在创建表时指定
                if ("PRIMARY".equalsIgnoreCase(indexName)) {
                    continue;
                }

                // 使用表模式、表名和索引名作为键，索引列名列表作为值
                String key = tableSchema + "." + tableName + "." + indexName;
                indexColumnMap.computeIfAbsent(key, k -> new ArrayList<>()).add("`" + columnName + "`");
            }

            // 拼接创建索引的 SQL 语句
            for (Map.Entry<String, List<String>> entry : indexColumnMap.entrySet()) {
                String[] parts = entry.getKey().split("\\.");
                String tableSchema = parts[0];
                String tableName = parts[1];
                String indexName = parts[2];
                List<String> columns = entry.getValue();

                String createIndexSql = "CREATE INDEX `" + indexName + "` ON `" + outDBName + "`.`" + tableName + "`" +
                        " (" + String.join(", ", columns) + ");";
                sqlList.add(createIndexSql);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, conn);
        }
        return sqlList;
    }

    /**
    * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
    * @param: map   连接数据库需要参数
    * @return:
    * @author: penglei
    * @date: 2024/11/14 9:46
    */
    public static Map<String, String> createDataBaseIfNoExist(Map<String,String> map) {
        Map<String, String> msgMap = new HashMap<>();
        //1. 连接数据库
        String driverprogram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String dbname = map.get("dbname");
        String srcport = map.get("srcport");
        String srcip = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            // 1. 注册JDBC驱动
            Class.forName(driverprogram);
            // 2. 打开连接
            String jdbcUrl = "jdbc:mysql://"+srcip+":"+srcport+"/mysql";
            connection = DriverManager.getConnection(jdbcUrl, username, password);
            // 3. 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            // 4. 检查数据库是否存在
            resultSet = metaData.getCatalogs();
            boolean databaseExists = false;
            while (resultSet.next()) {
                String databaseName = resultSet.getString(1);
                if (dbname.equals(databaseName)) {
                    databaseExists = true;
                    break;
                }
            }
            // 5. 如果数据库不存在，则创建它
            if (!databaseExists) {
                statement = connection.createStatement();
                String createDatabaseSQL = "CREATE DATABASE "+dbname;
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库"+dbname+"已创建！");
                msgMap.put("code",Const.DATABASE_CREATE);
                msgMap.put("msg","数据库"+dbname+"已创建");
                return msgMap;
            } else {
                System.out.println("数据库"+dbname+"已存在，跳过创建步骤！");
                msgMap.put("code",Const.DATABASE_EXIST);
                msgMap.put("msg","数据库"+dbname+"已存在");
                return msgMap;
            }
        } catch (Exception e) {
            e.printStackTrace();
            msgMap.put("code",Const.DATABASE_ERROR);
            msgMap.put("msg","连接数据库失败");
            return msgMap;
        } finally {
            // 6. 关闭资源
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static List<Map<String, String>> queryFieldInfoList(String dbName, String tableName, String dburl, String username,
                                                               String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SHOW FULL COLUMNS FROM `"+dbName+"`.`" + tableName + "`;");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();

                    //TODO 所有DBUtil字段信息尽量统一，以便支持不同数据源类型之间脱敏
                    fieldInfoMap.put("FieldEName", rs.getString("Field"));//字段名
                    fieldInfoMap.put("FieldCName", rs.getString("Comment"));//注释
                    fieldInfoMap.put("FieldType", rs.getString("Type"));//格式：varchar(255)

                    //TODO Mysql特有
                    fieldInfoMap.put("Key", rs.getString("Key"));//主外键
                    fieldInfoMap.put("Null", rs.getString("Null"));//是否为空
                    fieldInfoMap.put("Default", rs.getString("Default"));//默认值
                    fieldInfoMap.put("Extra", rs.getString("Extra"));// auto_increment为自增
                    fieldInfoMap.put("Privileges", rs.getString("Privileges"));//权限
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(rs, stmt, conn);
        return fieldInfoList;
    }


    public static void main(String[] args) {
//        String dbUrl = "*******************************************************************************************************";
//        String username = "root";
//        String password = "<EMAIL>";
//        String dbName = "01010";
//        MysqlUtil mysqlUtil = new MysqlUtil();
//        List<String> triggerSql = mysqlUtil.getIndexesSql(dbUrl, username, password, dbName,"");
//        System.out.println(JSON.toJSONString(triggerSql));
        // 数据库连接的URL，格式为：jdbc:mysql://<hostname>:<port>/<anyExistingDatabase>
        // 注意：这里我们连接到一个已经存在的数据库，比如mysql，以便能够执行SQL查询来检查其他数据库
    }
}
