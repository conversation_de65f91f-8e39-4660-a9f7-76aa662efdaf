package com.wzsec.modules.system.service.mapper;

import com.wzsec.modules.system.domain.Menu;
import com.wzsec.modules.system.service.dto.MenuDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MenuMapperImpl implements MenuMapper {

    @Override
    public MenuDto toDto(Menu entity) {
        if ( entity == null ) {
            return null;
        }

        MenuDto menuDto = new MenuDto();

        menuDto.setCache( entity.getCache() );
        menuDto.setComponent( entity.getComponent() );
        menuDto.setComponentName( entity.getComponentName() );
        menuDto.setCreateTime( entity.getCreateTime() );
        menuDto.setHidden( entity.getHidden() );
        menuDto.setIFrame( entity.getIFrame() );
        menuDto.setIcon( entity.getIcon() );
        menuDto.setId( entity.getId() );
        menuDto.setName( entity.getName() );
        menuDto.setPath( entity.getPath() );
        menuDto.setPermission( entity.getPermission() );
        menuDto.setPid( entity.getPid() );
        menuDto.setSort( entity.getSort() );
        menuDto.setType( entity.getType() );

        return menuDto;
    }

    @Override
    public List<MenuDto> toDto(List<Menu> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MenuDto> list = new ArrayList<MenuDto>( entityList.size() );
        for ( Menu menu : entityList ) {
            list.add( toDto( menu ) );
        }

        return list;
    }

    @Override
    public Menu toEntity(MenuDto dto) {
        if ( dto == null ) {
            return null;
        }

        Menu menu = new Menu();

        menu.setCache( dto.getCache() );
        menu.setComponent( dto.getComponent() );
        menu.setComponentName( dto.getComponentName() );
        menu.setCreateTime( dto.getCreateTime() );
        menu.setHidden( dto.getHidden() );
        menu.setIFrame( dto.getIFrame() );
        menu.setIcon( dto.getIcon() );
        menu.setId( dto.getId() );
        menu.setName( dto.getName() );
        menu.setPath( dto.getPath() );
        menu.setPermission( dto.getPermission() );
        menu.setPid( dto.getPid() );
        menu.setSort( dto.getSort() );
        menu.setType( dto.getType() );

        return menu;
    }

    @Override
    public List<Menu> toEntity(List<MenuDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Menu> list = new ArrayList<Menu>( dtoList.size() );
        for ( MenuDto menuDto : dtoList ) {
            list.add( toEntity( menuDto ) );
        }

        return list;
    }
}
