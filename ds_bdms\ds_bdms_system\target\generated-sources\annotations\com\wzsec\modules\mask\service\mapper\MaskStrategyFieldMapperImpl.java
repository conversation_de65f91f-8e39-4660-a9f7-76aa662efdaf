package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyField;
import com.wzsec.modules.mask.service.dto.MaskStrategyFieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskStrategyFieldMapperImpl implements MaskStrategyFieldMapper {

    @Override
    public MaskStrategyFieldDto toDto(MaskStrategyField entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFieldDto maskStrategyFieldDto = new MaskStrategyFieldDto();

        maskStrategyFieldDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyFieldDto.setDbname( entity.getDbname() );
        maskStrategyFieldDto.setExtractfield( entity.getExtractfield() );
        maskStrategyFieldDto.setFieldcname( entity.getFieldcname() );
        maskStrategyFieldDto.setFieldename( entity.getFieldename() );
        maskStrategyFieldDto.setFieldid( entity.getFieldid() );
        maskStrategyFieldDto.setFieldtype( entity.getFieldtype() );
        maskStrategyFieldDto.setId( entity.getId() );
        maskStrategyFieldDto.setParam( entity.getParam() );
        maskStrategyFieldDto.setRuleid( entity.getRuleid() );
        maskStrategyFieldDto.setSecretkey( entity.getSecretkey() );
        maskStrategyFieldDto.setSenLevel( entity.getSenLevel() );
        maskStrategyFieldDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFieldDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFieldDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFieldDto.setSparefield4( entity.getSparefield4() );
        maskStrategyFieldDto.setSparefield5( entity.getSparefield5() );
        maskStrategyFieldDto.setSparefield6( entity.getSparefield6() );
        maskStrategyFieldDto.setStategytableid( entity.getStategytableid() );
        maskStrategyFieldDto.setTabcname( entity.getTabcname() );
        maskStrategyFieldDto.setTabename( entity.getTabename() );
        maskStrategyFieldDto.setTableid( entity.getTableid() );

        return maskStrategyFieldDto;
    }

    @Override
    public List<MaskStrategyFieldDto> toDto(List<MaskStrategyField> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFieldDto> list = new ArrayList<MaskStrategyFieldDto>( entityList.size() );
        for ( MaskStrategyField maskStrategyField : entityList ) {
            list.add( toDto( maskStrategyField ) );
        }

        return list;
    }

    @Override
    public MaskStrategyField toEntity(MaskStrategyFieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyField maskStrategyField = new MaskStrategyField();

        maskStrategyField.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyField.setDbname( dto.getDbname() );
        maskStrategyField.setExtractfield( dto.getExtractfield() );
        maskStrategyField.setFieldcname( dto.getFieldcname() );
        maskStrategyField.setFieldename( dto.getFieldename() );
        maskStrategyField.setFieldid( dto.getFieldid() );
        maskStrategyField.setFieldtype( dto.getFieldtype() );
        maskStrategyField.setId( dto.getId() );
        maskStrategyField.setParam( dto.getParam() );
        maskStrategyField.setRuleid( dto.getRuleid() );
        maskStrategyField.setSecretkey( dto.getSecretkey() );
        maskStrategyField.setSenLevel( dto.getSenLevel() );
        maskStrategyField.setSparefield1( dto.getSparefield1() );
        maskStrategyField.setSparefield2( dto.getSparefield2() );
        maskStrategyField.setSparefield3( dto.getSparefield3() );
        maskStrategyField.setSparefield4( dto.getSparefield4() );
        maskStrategyField.setSparefield5( dto.getSparefield5() );
        maskStrategyField.setSparefield6( dto.getSparefield6() );
        maskStrategyField.setStategytableid( dto.getStategytableid() );
        maskStrategyField.setTabcname( dto.getTabcname() );
        maskStrategyField.setTabename( dto.getTabename() );
        maskStrategyField.setTableid( dto.getTableid() );

        return maskStrategyField;
    }

    @Override
    public List<MaskStrategyField> toEntity(List<MaskStrategyFieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyField> list = new ArrayList<MaskStrategyField>( dtoList.size() );
        for ( MaskStrategyFieldDto maskStrategyFieldDto : dtoList ) {
            list.add( toEntity( maskStrategyFieldDto ) );
        }

        return list;
    }
}
