package com.wzsec;

/**
 * Integer工具类
 *
 * <AUTHOR>
 * @date 2020-4-23
 */
public class IntegerUtils {

    /**
     * 不够位数的在前面补0，保留num的长度位数字
     *
     * <AUTHOR>
     * @date 2020-4-23
     */
    public static String autoGenericCode(int code, int num) {
        // 保留num的位数
        // 0 代表前面补充0
        // num 代表长度为4
        // d 代表参数为正数型
        String result = String.format("%0" + num + "d", code);
        return result;
    }

}
