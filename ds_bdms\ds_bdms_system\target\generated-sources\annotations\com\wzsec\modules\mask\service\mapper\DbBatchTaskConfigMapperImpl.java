package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DbBatchTaskConfig;
import com.wzsec.modules.mask.service.dto.DbBatchTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:00+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DbBatchTaskConfigMapperImpl implements DbBatchTaskConfigMapper {

    @Override
    public DbBatchTaskConfigDto toDto(DbBatchTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        DbBatchTaskConfigDto dbBatchTaskConfigDto = new DbBatchTaskConfigDto();

        dbBatchTaskConfigDto.setCreatetime( entity.getCreatetime() );
        dbBatchTaskConfigDto.setCreateuser( entity.getCreateuser() );
        dbBatchTaskConfigDto.setCron( entity.getCron() );
        dbBatchTaskConfigDto.setErrordirectory( entity.getErrordirectory() );
        dbBatchTaskConfigDto.setExecutionstate( entity.getExecutionstate() );
        dbBatchTaskConfigDto.setId( entity.getId() );
        dbBatchTaskConfigDto.setInputdatasourceid( entity.getInputdatasourceid() );
        dbBatchTaskConfigDto.setMasktablename( entity.getMasktablename() );
        dbBatchTaskConfigDto.setMaskway( entity.getMaskway() );
        dbBatchTaskConfigDto.setOutputdatasourceid( entity.getOutputdatasourceid() );
        dbBatchTaskConfigDto.setOutputdirectory( entity.getOutputdirectory() );
        dbBatchTaskConfigDto.setOutputtype( entity.getOutputtype() );
        dbBatchTaskConfigDto.setRemark( entity.getRemark() );
        dbBatchTaskConfigDto.setSparefield1( entity.getSparefield1() );
        dbBatchTaskConfigDto.setSparefield2( entity.getSparefield2() );
        dbBatchTaskConfigDto.setSparefield3( entity.getSparefield3() );
        dbBatchTaskConfigDto.setSparefield4( entity.getSparefield4() );
        dbBatchTaskConfigDto.setSparefield5( entity.getSparefield5() );
        dbBatchTaskConfigDto.setState( entity.getState() );
        dbBatchTaskConfigDto.setSubmittype( entity.getSubmittype() );
        dbBatchTaskConfigDto.setTaskexecuteengine( entity.getTaskexecuteengine() );
        dbBatchTaskConfigDto.setTaskname( entity.getTaskname() );
        dbBatchTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        dbBatchTaskConfigDto.setUpdateuser( entity.getUpdateuser() );

        return dbBatchTaskConfigDto;
    }

    @Override
    public List<DbBatchTaskConfigDto> toDto(List<DbBatchTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DbBatchTaskConfigDto> list = new ArrayList<DbBatchTaskConfigDto>( entityList.size() );
        for ( DbBatchTaskConfig dbBatchTaskConfig : entityList ) {
            list.add( toDto( dbBatchTaskConfig ) );
        }

        return list;
    }

    @Override
    public DbBatchTaskConfig toEntity(DbBatchTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        DbBatchTaskConfig dbBatchTaskConfig = new DbBatchTaskConfig();

        dbBatchTaskConfig.setCreatetime( dto.getCreatetime() );
        dbBatchTaskConfig.setCreateuser( dto.getCreateuser() );
        dbBatchTaskConfig.setCron( dto.getCron() );
        dbBatchTaskConfig.setErrordirectory( dto.getErrordirectory() );
        dbBatchTaskConfig.setExecutionstate( dto.getExecutionstate() );
        dbBatchTaskConfig.setId( dto.getId() );
        dbBatchTaskConfig.setInputdatasourceid( dto.getInputdatasourceid() );
        dbBatchTaskConfig.setMasktablename( dto.getMasktablename() );
        dbBatchTaskConfig.setMaskway( dto.getMaskway() );
        dbBatchTaskConfig.setOutputdatasourceid( dto.getOutputdatasourceid() );
        dbBatchTaskConfig.setOutputdirectory( dto.getOutputdirectory() );
        dbBatchTaskConfig.setOutputtype( dto.getOutputtype() );
        dbBatchTaskConfig.setRemark( dto.getRemark() );
        dbBatchTaskConfig.setSparefield1( dto.getSparefield1() );
        dbBatchTaskConfig.setSparefield2( dto.getSparefield2() );
        dbBatchTaskConfig.setSparefield3( dto.getSparefield3() );
        dbBatchTaskConfig.setSparefield4( dto.getSparefield4() );
        dbBatchTaskConfig.setSparefield5( dto.getSparefield5() );
        dbBatchTaskConfig.setState( dto.getState() );
        dbBatchTaskConfig.setSubmittype( dto.getSubmittype() );
        dbBatchTaskConfig.setTaskexecuteengine( dto.getTaskexecuteengine() );
        dbBatchTaskConfig.setTaskname( dto.getTaskname() );
        dbBatchTaskConfig.setUpdatetime( dto.getUpdatetime() );
        dbBatchTaskConfig.setUpdateuser( dto.getUpdateuser() );

        return dbBatchTaskConfig;
    }

    @Override
    public List<DbBatchTaskConfig> toEntity(List<DbBatchTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DbBatchTaskConfig> list = new ArrayList<DbBatchTaskConfig>( dtoList.size() );
        for ( DbBatchTaskConfigDto dbBatchTaskConfigDto : dtoList ) {
            list.add( toEntity( dbBatchTaskConfigDto ) );
        }

        return list;
    }
}
