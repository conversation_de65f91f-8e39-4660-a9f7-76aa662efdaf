package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DbBatchTaskConfig;
import com.wzsec.modules.mask.service.dto.DbBatchTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:19+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DbBatchTaskConfigMapperImpl implements DbBatchTaskConfigMapper {

    @Override
    public DbBatchTaskConfig toEntity(DbBatchTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        DbBatchTaskConfig dbBatchTaskConfig = new DbBatchTaskConfig();

        dbBatchTaskConfig.setId( dto.getId() );
        dbBatchTaskConfig.setTaskname( dto.getTaskname() );
        dbBatchTaskConfig.setInputdatasourceid( dto.getInputdatasourceid() );
        dbBatchTaskConfig.setState( dto.getState() );
        dbBatchTaskConfig.setOutputtype( dto.getOutputtype() );
        dbBatchTaskConfig.setOutputdatasourceid( dto.getOutputdatasourceid() );
        dbBatchTaskConfig.setOutputdirectory( dto.getOutputdirectory() );
        dbBatchTaskConfig.setErrordirectory( dto.getErrordirectory() );
        dbBatchTaskConfig.setSubmittype( dto.getSubmittype() );
        dbBatchTaskConfig.setCron( dto.getCron() );
        dbBatchTaskConfig.setTaskexecuteengine( dto.getTaskexecuteengine() );
        dbBatchTaskConfig.setMasktablename( dto.getMasktablename() );
        dbBatchTaskConfig.setMaskway( dto.getMaskway() );
        dbBatchTaskConfig.setExecutionstate( dto.getExecutionstate() );
        dbBatchTaskConfig.setRemark( dto.getRemark() );
        dbBatchTaskConfig.setCreateuser( dto.getCreateuser() );
        dbBatchTaskConfig.setCreatetime( dto.getCreatetime() );
        dbBatchTaskConfig.setUpdateuser( dto.getUpdateuser() );
        dbBatchTaskConfig.setUpdatetime( dto.getUpdatetime() );
        dbBatchTaskConfig.setSparefield1( dto.getSparefield1() );
        dbBatchTaskConfig.setSparefield2( dto.getSparefield2() );
        dbBatchTaskConfig.setSparefield3( dto.getSparefield3() );
        dbBatchTaskConfig.setSparefield4( dto.getSparefield4() );
        dbBatchTaskConfig.setSparefield5( dto.getSparefield5() );

        return dbBatchTaskConfig;
    }

    @Override
    public DbBatchTaskConfigDto toDto(DbBatchTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        DbBatchTaskConfigDto dbBatchTaskConfigDto = new DbBatchTaskConfigDto();

        dbBatchTaskConfigDto.setId( entity.getId() );
        dbBatchTaskConfigDto.setTaskname( entity.getTaskname() );
        dbBatchTaskConfigDto.setInputdatasourceid( entity.getInputdatasourceid() );
        dbBatchTaskConfigDto.setState( entity.getState() );
        dbBatchTaskConfigDto.setOutputtype( entity.getOutputtype() );
        dbBatchTaskConfigDto.setOutputdatasourceid( entity.getOutputdatasourceid() );
        dbBatchTaskConfigDto.setOutputdirectory( entity.getOutputdirectory() );
        dbBatchTaskConfigDto.setErrordirectory( entity.getErrordirectory() );
        dbBatchTaskConfigDto.setSubmittype( entity.getSubmittype() );
        dbBatchTaskConfigDto.setTaskexecuteengine( entity.getTaskexecuteengine() );
        dbBatchTaskConfigDto.setMasktablename( entity.getMasktablename() );
        dbBatchTaskConfigDto.setCron( entity.getCron() );
        dbBatchTaskConfigDto.setMaskway( entity.getMaskway() );
        dbBatchTaskConfigDto.setExecutionstate( entity.getExecutionstate() );
        dbBatchTaskConfigDto.setRemark( entity.getRemark() );
        dbBatchTaskConfigDto.setCreateuser( entity.getCreateuser() );
        dbBatchTaskConfigDto.setCreatetime( entity.getCreatetime() );
        dbBatchTaskConfigDto.setUpdateuser( entity.getUpdateuser() );
        dbBatchTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        dbBatchTaskConfigDto.setSparefield1( entity.getSparefield1() );
        dbBatchTaskConfigDto.setSparefield2( entity.getSparefield2() );
        dbBatchTaskConfigDto.setSparefield3( entity.getSparefield3() );
        dbBatchTaskConfigDto.setSparefield4( entity.getSparefield4() );
        dbBatchTaskConfigDto.setSparefield5( entity.getSparefield5() );

        return dbBatchTaskConfigDto;
    }

    @Override
    public List<DbBatchTaskConfig> toEntity(List<DbBatchTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DbBatchTaskConfig> list = new ArrayList<DbBatchTaskConfig>( dtoList.size() );
        for ( DbBatchTaskConfigDto dbBatchTaskConfigDto : dtoList ) {
            list.add( toEntity( dbBatchTaskConfigDto ) );
        }

        return list;
    }

    @Override
    public List<DbBatchTaskConfigDto> toDto(List<DbBatchTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DbBatchTaskConfigDto> list = new ArrayList<DbBatchTaskConfigDto>( entityList.size() );
        for ( DbBatchTaskConfig dbBatchTaskConfig : entityList ) {
            list.add( toDto( dbBatchTaskConfig ) );
        }

        return list;
    }
}
