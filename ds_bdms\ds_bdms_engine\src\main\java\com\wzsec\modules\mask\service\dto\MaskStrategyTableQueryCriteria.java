package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2020-11-09
*/
@Data
public class MaskStrategyTableQueryCriteria{

    /** 模糊 */
    @Query
    private String status;

    @Query(blurry = "strategyname,strategydesc,dbname,tabename,tabcname,createuser,createtime,updateuser,updatetime")
    private String blurry;
}
