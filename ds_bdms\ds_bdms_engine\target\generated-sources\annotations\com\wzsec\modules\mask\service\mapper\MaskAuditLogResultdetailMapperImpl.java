package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditLogResultdetail;
import com.wzsec.modules.mask.service.dto.MaskAuditLogResultdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskAuditLogResultdetailMapperImpl implements MaskAuditLogResultdetailMapper {

    @Override
    public MaskAuditLogResultdetailDto toDto(MaskAuditLogResultdetail entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditLogResultdetailDto maskAuditLogResultdetailDto = new MaskAuditLogResultdetailDto();

        maskAuditLogResultdetailDto.setApicode( entity.getApicode() );
        maskAuditLogResultdetailDto.setApimethod( entity.getApimethod() );
        maskAuditLogResultdetailDto.setApiname( entity.getApiname() );
        maskAuditLogResultdetailDto.setApitype( entity.getApitype() );
        maskAuditLogResultdetailDto.setAppid( entity.getAppid() );
        maskAuditLogResultdetailDto.setAppname( entity.getAppname() );
        maskAuditLogResultdetailDto.setCheckcount( entity.getCheckcount() );
        maskAuditLogResultdetailDto.setChecklinescount( entity.getChecklinescount() );
        maskAuditLogResultdetailDto.setCheckparam( entity.getCheckparam() );
        maskAuditLogResultdetailDto.setChecktime( entity.getChecktime() );
        maskAuditLogResultdetailDto.setCustSimplename( entity.getCustSimplename() );
        maskAuditLogResultdetailDto.setCustname( entity.getCustname() );
        maskAuditLogResultdetailDto.setExample( entity.getExample() );
        maskAuditLogResultdetailDto.setId( entity.getId() );
        maskAuditLogResultdetailDto.setLogsign( entity.getLogsign() );
        maskAuditLogResultdetailDto.setParamMean( entity.getParamMean() );
        maskAuditLogResultdetailDto.setParamNote( entity.getParamNote() );
        maskAuditLogResultdetailDto.setRatio( entity.getRatio() );
        maskAuditLogResultdetailDto.setResulttype( entity.getResulttype() );
        maskAuditLogResultdetailDto.setRisk( entity.getRisk() );
        maskAuditLogResultdetailDto.setSensitivedata( entity.getSensitivedata() );
        maskAuditLogResultdetailDto.setSparefield1( entity.getSparefield1() );
        maskAuditLogResultdetailDto.setSparefield2( entity.getSparefield2() );
        maskAuditLogResultdetailDto.setSparefield3( entity.getSparefield3() );
        maskAuditLogResultdetailDto.setSparefield4( entity.getSparefield4() );
        maskAuditLogResultdetailDto.setTaskname( entity.getTaskname() );
        maskAuditLogResultdetailDto.setTotalcount( entity.getTotalcount() );
        maskAuditLogResultdetailDto.setTotallinescount( entity.getTotallinescount() );
        maskAuditLogResultdetailDto.setUrl( entity.getUrl() );
        maskAuditLogResultdetailDto.setUserid( entity.getUserid() );

        return maskAuditLogResultdetailDto;
    }

    @Override
    public List<MaskAuditLogResultdetailDto> toDto(List<MaskAuditLogResultdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditLogResultdetailDto> list = new ArrayList<MaskAuditLogResultdetailDto>( entityList.size() );
        for ( MaskAuditLogResultdetail maskAuditLogResultdetail : entityList ) {
            list.add( toDto( maskAuditLogResultdetail ) );
        }

        return list;
    }

    @Override
    public MaskAuditLogResultdetail toEntity(MaskAuditLogResultdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditLogResultdetail maskAuditLogResultdetail = new MaskAuditLogResultdetail();

        maskAuditLogResultdetail.setApicode( dto.getApicode() );
        maskAuditLogResultdetail.setApimethod( dto.getApimethod() );
        maskAuditLogResultdetail.setApiname( dto.getApiname() );
        maskAuditLogResultdetail.setApitype( dto.getApitype() );
        maskAuditLogResultdetail.setAppid( dto.getAppid() );
        maskAuditLogResultdetail.setAppname( dto.getAppname() );
        maskAuditLogResultdetail.setCheckcount( dto.getCheckcount() );
        maskAuditLogResultdetail.setChecklinescount( dto.getChecklinescount() );
        maskAuditLogResultdetail.setCheckparam( dto.getCheckparam() );
        maskAuditLogResultdetail.setChecktime( dto.getChecktime() );
        maskAuditLogResultdetail.setCustSimplename( dto.getCustSimplename() );
        maskAuditLogResultdetail.setCustname( dto.getCustname() );
        maskAuditLogResultdetail.setExample( dto.getExample() );
        maskAuditLogResultdetail.setId( dto.getId() );
        maskAuditLogResultdetail.setLogsign( dto.getLogsign() );
        maskAuditLogResultdetail.setParamMean( dto.getParamMean() );
        maskAuditLogResultdetail.setParamNote( dto.getParamNote() );
        maskAuditLogResultdetail.setRatio( dto.getRatio() );
        maskAuditLogResultdetail.setResulttype( dto.getResulttype() );
        maskAuditLogResultdetail.setRisk( dto.getRisk() );
        maskAuditLogResultdetail.setSensitivedata( dto.getSensitivedata() );
        maskAuditLogResultdetail.setSparefield1( dto.getSparefield1() );
        maskAuditLogResultdetail.setSparefield2( dto.getSparefield2() );
        maskAuditLogResultdetail.setSparefield3( dto.getSparefield3() );
        maskAuditLogResultdetail.setSparefield4( dto.getSparefield4() );
        maskAuditLogResultdetail.setTaskname( dto.getTaskname() );
        maskAuditLogResultdetail.setTotalcount( dto.getTotalcount() );
        maskAuditLogResultdetail.setTotallinescount( dto.getTotallinescount() );
        maskAuditLogResultdetail.setUrl( dto.getUrl() );
        maskAuditLogResultdetail.setUserid( dto.getUserid() );

        return maskAuditLogResultdetail;
    }

    @Override
    public List<MaskAuditLogResultdetail> toEntity(List<MaskAuditLogResultdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditLogResultdetail> list = new ArrayList<MaskAuditLogResultdetail>( dtoList.size() );
        for ( MaskAuditLogResultdetailDto maskAuditLogResultdetailDto : dtoList ) {
            list.add( toEntity( maskAuditLogResultdetailDto ) );
        }

        return list;
    }
}
