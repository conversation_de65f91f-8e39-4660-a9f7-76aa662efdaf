package com.wzsec.utils.zlicense.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class VerifyUtil {

	public static void main(String[] args) {
	   String deadlineTime = "Fri Aug 29 00:00:00 CST 2019";
	   System.out.println("result:" + getIsValid(getNowDate(), getDateFromGMT(deadlineTime)));
	}

	public static boolean getIsValid(String nowTime, String deadlineTime) {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		try {
			Date date1 = format.parse(nowTime);
			Date date2 = format.parse(deadlineTime);
			int compareTo = date1.compareTo(date2);
			if (compareTo >= 0) {
				return false;
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return true;
	}

	/**
	 * 将格林威治的时间转为yyyy-MM-dd
	 * @param gmtTime
	 * @return
	 */
	public static String getDateFromGMT(String gmtTime) {
		SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.US);
		Date date = null;
		try {
			date = sdf.parse(gmtTime);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(date);
	}
	
	/**
	 * 将现在时间转为yyyy-MM-dd格式
	 * @param nowTime
	 * @return
	 */
	public static String getNowDate() {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		return format.format(new Date());
	}
}
