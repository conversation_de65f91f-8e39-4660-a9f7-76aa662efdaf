2025-07-31 11:30:41,168 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 16408 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-31 11:30:41,178 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-31 11:30:43,938 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-31 11:30:43,940 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-31 11:30:44,991 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1038 ms. Found 88 JPA repository interfaces.
2025-07-31 11:30:45,495 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:30:45,495 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:45,509 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-31 11:30:45,510 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-31 11:30:45,510 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:45,510 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:30:46,204 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-31 11:30:46,223 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-31 11:30:46,225 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-31 11:30:47,233 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-31 11:30:47,496 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-31 11:30:47,520 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-31 11:30:47,531 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-31 11:30:47,533 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-31 11:30:50,036 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-31 11:30:50,629 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-31 11:30:50,814 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-31 11:30:51,286 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-31 11:30:51,778 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-31 11:30:57,583 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-31 11:30:57,617 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-31 11:31:01,292 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-31 11:31:01,621 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 19633 ms
2025-07-31 11:31:05,621 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 11:31:06,330 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-31 11:31:06,511 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:31:06,598 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:31:06,600 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:31:06,620 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:31:06,625 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:31:06,625 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:31:06,625 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:31:06,625 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@b6ae271
2025-07-31 11:31:06,625 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:31:09,760 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@608cff9e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5de49e5a, org.springframework.security.web.context.SecurityContextPersistenceFilter@55de7b8d, org.springframework.security.web.header.HeaderWriterFilter@6d0acf9a, org.springframework.security.web.authentication.logout.LogoutFilter@246eba0, org.springframework.web.filter.CorsFilter@318f2e9d, com.wzsec.modules.security.security.TokenFilter@4497e084, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3df05259, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@72df13c3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5a4cf76c, org.springframework.security.web.session.SessionManagementFilter@4827711c, org.springframework.security.web.access.ExceptionTranslationFilter@623e8e88, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@729bdd9d]
2025-07-31 11:31:09,812 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-31 11:31:12,936 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-31 11:31:12,936 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 11:31:12,936 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-31 11:31:12,936 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-31 11:31:12,936 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 11:31:12,936 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 11:31:12,936 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-31 11:31:12,936 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3dcafc9c
2025-07-31 11:31:13,350 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-31 11:31:13,422 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-31 11:31:13,423 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:31:13,423 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-31 11:31:13,423 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-31 11:31:13,423 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-31 11:31:13,423 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 11:31:13,437 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 33.287 seconds (JVM running for 35.912)
2025-07-31 11:31:14,241 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-31 11:31:14,241 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
