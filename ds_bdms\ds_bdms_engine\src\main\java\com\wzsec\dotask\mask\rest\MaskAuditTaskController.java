package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoMaskAuditTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2021-1-07
 */
// @Api(tags = "脱敏日志审计任务管理")
@RestController
@RequestMapping("/engine/maskaudit/task")
public class MaskAuditTaskController {

    private final DoMaskAuditTaskService doMaskAuditTaskService;

    private final UserService userService;

    public MaskAuditTaskController(DoMaskAuditTaskService doMaskAuditTaskService, UserService userService) {
        this.doMaskAuditTaskService = doMaskAuditTaskService;
        this.userService = userService;
    }

    @Log("执行脱敏日志审计任务")
    // @ApiOperation("执行脱敏日志审计任务")
    @PostMapping(value = "/exec/{id}")
    @PreAuthorize("@el.check('maskAuditTaskV1:edit')")
    public ResponseEntity<Object> execution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doMaskAuditTaskService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("定时执行脱敏日志审计任务")
    // // @ApiOperation("定时执行脱敏日志审计任务")
    @PostMapping(value = "/timingexec/{id}")
    //@PreAuthorize("@el.check('maskAuditTaskV1:edit')")
    public ResponseEntity<Object> timingexecution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        doMaskAuditTaskService.execution(id, Const.TASK_SUBMITTYPE_AUTOSUBMIT);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
