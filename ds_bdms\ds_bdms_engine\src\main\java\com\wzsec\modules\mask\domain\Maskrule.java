package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-06
*/
@Entity
@Data
@Table(name="sdd_mask_rule")
public class Maskrule implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 规则名 */
    @Column(name = "rulename")
    //@NotBlank
    private String rulename;

    /** 规则中文名 */
    @Column(name = "rulecname")
    private String rulecname;

    /** 规范化字段中文名 */
    @Column(name = "standardcname")
    private String standardcname;

    /** 规范化字段英文名 */
    @Column(name = "standardename")
    private String standardename;

    /** 算法ID */
    @Column(name = "algorithmid")
    //@NotNull
    private Integer algorithmid;

    /** 参数 */
    @Column(name = "param")
    private String param;

    /** 状态 */
    @Column(name = "flag")
    private String flag;

    /** 备注 */
    @Column(name = "memo")
    private String memo;

    /** 创建人 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新人 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(Maskrule source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
