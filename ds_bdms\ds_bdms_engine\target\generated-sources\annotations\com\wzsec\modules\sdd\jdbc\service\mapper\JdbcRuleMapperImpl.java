package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcRule;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JdbcRuleMapperImpl implements JdbcRuleMapper {

    @Override
    public JdbcRuleDto toDto(JdbcRule entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcRuleDto jdbcRuleDto = new JdbcRuleDto();

        jdbcRuleDto.setAlgorithmid( entity.getAlgorithmid() );
        jdbcRuleDto.setCreatetime( entity.getCreatetime() );
        jdbcRuleDto.setCreateuser( entity.getCreateuser() );
        jdbcRuleDto.setId( entity.getId() );
        jdbcRuleDto.setNote( entity.getNote() );
        jdbcRuleDto.setParam( entity.getParam() );
        jdbcRuleDto.setRole( entity.getRole() );
        jdbcRuleDto.setSname( entity.getSname() );
        jdbcRuleDto.setSparefield1( entity.getSparefield1() );
        jdbcRuleDto.setSparefield2( entity.getSparefield2() );
        jdbcRuleDto.setSparefield3( entity.getSparefield3() );
        jdbcRuleDto.setSparefield4( entity.getSparefield4() );
        jdbcRuleDto.setSparefield5( entity.getSparefield5() );
        jdbcRuleDto.setState( entity.getState() );
        jdbcRuleDto.setUpdatetime( entity.getUpdatetime() );
        jdbcRuleDto.setUpdateuser( entity.getUpdateuser() );

        return jdbcRuleDto;
    }

    @Override
    public List<JdbcRuleDto> toDto(List<JdbcRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcRuleDto> list = new ArrayList<JdbcRuleDto>( entityList.size() );
        for ( JdbcRule jdbcRule : entityList ) {
            list.add( toDto( jdbcRule ) );
        }

        return list;
    }

    @Override
    public JdbcRule toEntity(JdbcRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcRule jdbcRule = new JdbcRule();

        jdbcRule.setAlgorithmid( dto.getAlgorithmid() );
        jdbcRule.setCreatetime( dto.getCreatetime() );
        jdbcRule.setCreateuser( dto.getCreateuser() );
        jdbcRule.setId( dto.getId() );
        jdbcRule.setNote( dto.getNote() );
        jdbcRule.setParam( dto.getParam() );
        jdbcRule.setRole( dto.getRole() );
        jdbcRule.setSname( dto.getSname() );
        jdbcRule.setSparefield1( dto.getSparefield1() );
        jdbcRule.setSparefield2( dto.getSparefield2() );
        jdbcRule.setSparefield3( dto.getSparefield3() );
        jdbcRule.setSparefield4( dto.getSparefield4() );
        jdbcRule.setSparefield5( dto.getSparefield5() );
        jdbcRule.setState( dto.getState() );
        jdbcRule.setUpdatetime( dto.getUpdatetime() );
        jdbcRule.setUpdateuser( dto.getUpdateuser() );

        return jdbcRule;
    }

    @Override
    public List<JdbcRule> toEntity(List<JdbcRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcRule> list = new ArrayList<JdbcRule>( dtoList.size() );
        for ( JdbcRuleDto jdbcRuleDto : dtoList ) {
            list.add( toEntity( jdbcRuleDto ) );
        }

        return list;
    }
}
