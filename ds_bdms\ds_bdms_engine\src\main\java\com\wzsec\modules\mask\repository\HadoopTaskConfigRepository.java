package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.HadoopTaskConfig;
import com.wzsec.modules.mask.domain.MaskStrategyTable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2020-11-17
*/
public interface HadoopTaskConfigRepository extends JpaRepository<HadoopTaskConfig, Integer>, JpaSpecificationExecutor<HadoopTaskConfig> {

    @Query(value = "select MAX(`taskname`) from sdd_mask_hadooptaskconfig where taskname like concat(?1,'%')", nativeQuery = true)
    String findMAXTaskNameByPrefix(String prefix);

    @Query(value = "select distinct dbname from sdd_meta_table where sourcetype = ?1 ORDER BY id DESC", nativeQuery = true)
    List<String> getDbnameByDbSourceType(String sourcetype);

    @Query(value = "select distinct tablename from sdd_meta_table where sourcetype = ?1 and dbname = ?2 ORDER BY id DESC", nativeQuery = true)
    List<String> getTabnameByDbname(String sourcetype,String tabdatabase);

    @Query(value = "select * from sdd_mask_strategy_table where sourcetype = ?1 and dbname = ?2 and tabename= ?3 ORDER BY strategyname", nativeQuery = true)
    List<Map<String,Object>> getStrategyByDbnameAndTabname(String sourcetype, String tabdatabase, String tabname);
}
