<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ds_bdms</artifactId>
        <groupId>com.wzsec</groupId>
        <version>3.2.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ds_bdms_system</artifactId>
    <name>ds_bdms_system</name>

    <properties>
        <!--  wangqi 2021-06-23 修改jwt版本为 0.11.1  -->
        <!--  <jjwt.version>0.10.6</jjwt.version>-->
        <jjwt.version>0.11.1</jjwt.version>
        <!-- oshi监控需要指定jna版本, 问题详见 https://github.com/oshi/oshi/issues/1040 -->
        <jna.version>5.8.0</jna.version>
        <bcprov-jdk15on.version>1.65</bcprov-jdk15on.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>5.6.1</version>
        </dependency>

        <!--    代码生成模块    -->
        <dependency>
            <groupId>com.wzsec</groupId>
            <artifactId>ds_bdms_generator</artifactId>
            <version>3.2.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wzsec</groupId>
                    <artifactId>ds_bdms_common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    tools 模块包含了 common 和 logging 模块    -->
        <dependency>
            <groupId>com.wzsec</groupId>
            <artifactId>ds_bdms_tools</artifactId>
            <version>3.2.6</version>
        </dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>

        <!--jwt-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
        </dependency>

        <!-- quartz -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>

		<dependency>
			<groupId>ch.ethz.ganymed</groupId>
			<artifactId>ganymed-ssh2</artifactId>
			<version>build210</version>
		</dependency>

		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

        <dependency>
            <groupId>com.aliyun.auth</groupId>
            <artifactId>dss-appjoint-auth</artifactId>
            <version>0.9.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/dss-appjoint-auth-0.9.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.ast</groupId>
            <artifactId>json4s-ast</artifactId>
            <version>2.11-3.5.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/json4s-ast_2.11-3.5.3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.core</groupId>
            <artifactId>json4s-core</artifactId>
            <version>2.11-3.5.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/json4s-core_2.11-3.5.3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.jackson</groupId>
            <artifactId>json4s-jackson</artifactId>
            <version>2.11-3.5.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/json4s-jackson_2.11-3.5.3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.scalap</groupId>
            <artifactId>json4s-scalap</artifactId>
            <version>2.11-3.5.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/json4s-scalap_2.11-3.5.3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.common</groupId>
            <artifactId>linkis-common</artifactId>
            <version>1.6.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/linkis-common-1.6.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.support</groupId>
            <artifactId>linkis-gateway-httpclient-support</artifactId>
            <version>0.9.4</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/linkis-gateway-httpclient-support-0.9.4.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.httpclient</groupId>
            <artifactId>linkis-httpclient</artifactId>
            <version>0.9.4</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/linkis-httpclient-0.9.4.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.storage</groupId>
            <artifactId>linkis-storage</artifactId>
            <version>0.9.4</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/linkis-storage-0.9.4.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.10</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/reflections-0.9.10.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.compiler</groupId>
            <artifactId>scala-compiler</artifactId>
            <version>2.11.8</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/scala-compiler-2.11.8.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.library</groupId>
            <artifactId>scala-library</artifactId>
            <version>2.11.8</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/scala-library-2.11.8.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.combinators</groupId>
            <artifactId>scala-parser-combinators</artifactId>
            <version>2.11-1.0.4</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/scala-parser-combinators_2.11-1.0.4.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.reflect</groupId>
            <artifactId>scala-reflect</artifactId>
            <version>2.11.8</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/scala-reflect-2.11.8.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.xml</groupId>
            <artifactId>scala-xml</artifactId>
            <version>2.11-1.0.6</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/scala-xml_2.11-1.0.6.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.aliyun.scalap</groupId>
            <artifactId>scalap</artifactId>
            <version>2.11.8</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/scalap-2.11.8.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.licenseVerify</groupId>
            <artifactId>licenseVerify</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/licenseVerify-1.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.wzsec.watermark</groupId>
            <artifactId>watermark</artifactId>
            <version>1.0.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/watermark-1.0.2.jar</systemPath>
        </dependency>

        <!--算法包-->
        <dependency>
            <groupId>cn.god.mask</groupId>
            <artifactId>maskalg</artifactId>
            <version>2.3.11</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/maskalglib-v2.3.11.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>0.3.2-patch11-ce-patch-v1</groupId>
            <artifactId>0.3.2-patch11-ce-patch-v1</artifactId>
            <version>0.3.2-patch11-ce-patch-v1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/clickhouse-jdbc-0.3.3-SNAPSHOT-ce-patch-v1-all.jar</systemPath>
            <classifier>all</classifier>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- 0.3.3-SNAPSHOT-ce-patch-v1 依赖 -->
            <groupId>org.roaringbitmap</groupId>
            <artifactId>RoaringBitmap</artifactId>
            <version>0.9.36</version>
        </dependency>

        <dependency>
            <groupId>com.groupdocs.watermark</groupId>
            <artifactId>watermark</artifactId>
            <version>18.8</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/groupdocs-watermark-18.8.jar</systemPath>
        </dependency>

        <!--elasticsearch-->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.9.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.9.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>7.9.1</version>
        </dependency>
        <dependency>
            <groupId>com.wzsec</groupId>
            <artifactId>ds_bdms_common</artifactId>
            <version>3.2.6</version>
            <scope>compile</scope>
        </dependency>

        <!--syslog-->
        <dependency>
            <groupId>org.graylog2</groupId>
            <artifactId>syslog4j</artifactId>
            <version>0.9.60</version>
        </dependency>

        <dependency>
            <groupId>org.hyperic</groupId>
            <artifactId>sigar</artifactId>
            <version>1.6.5.132</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.oceanbase.jdbc</groupId>
            <artifactId>oceanbase-client</artifactId>
            <version>2.4.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/oceanbase-client-2.4.1.jar</systemPath>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.wzsec</groupId>-->
<!--            <artifactId>ds_bdms_engine</artifactId>-->
<!--            <version>3.2.6</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

        <!-- Kafka Client -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka_2.11</artifactId>
            <version>0.11.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>jmxri</artifactId>
                    <groupId>com.sun.jmx</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jms</artifactId>
                    <groupId>javax.jms</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jmxtools</artifactId>
                    <groupId>com.sun.jdmk</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>${spring-kafka.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka-test</artifactId>
            <version>${spring-kafka.version}</version>
            <scope>test</scope>
        </dependency>

        <!--SM2 算法依赖包-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bcprov-jdk15on.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <!--配置本地包打入jar包-->
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
