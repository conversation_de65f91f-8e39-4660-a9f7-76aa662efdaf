package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2021-01-15
*/
@Entity
@Data
@Table(name="sdd_maskaudit_resultdetail")
public class MaskAuditResultdetailV1 implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名称 */
    @Column(name = "taskname")
    private String taskname;

    /** 表名 */
    @Column(name = "tablename")
    private String tablename;

    /** 文件名 */
    @Column(name = "filename")
    private String filename;

    /** 字段或者列数 */
    @Column(name = "field")
    private String field;

    /** 调用算法名 */
    @Column(name = "algorithm")
    private String algorithm;

    /** 脱敏行数 */
    @Column(name = "linenumber")
    private Integer linenumber;

    /** 是否与安全策略基线相同(0：是，1：否) */
    @Column(name = "issafesame")
    private String issafesame;

    /** 风险程度（0：无，1：低危，2：中危，3：高危） */
    @Column(name = "risk")
    private String risk;

    /** 样例 */
    @Column(name = "example")
    private String example;

    /** 检测时间 */
    @Column(name = "checktime")
    private String checktime;

    /** 提交人 */
    @Column(name = "submitter")
    private String submitter;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskAuditResultdetailV1 source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
