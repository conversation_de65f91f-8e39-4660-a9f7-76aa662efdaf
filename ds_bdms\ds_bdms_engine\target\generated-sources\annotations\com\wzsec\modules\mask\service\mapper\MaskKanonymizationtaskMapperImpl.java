package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskKanonymizationtask;
import com.wzsec.modules.mask.service.dto.MaskKanonymizationtaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskKanonymizationtaskMapperImpl implements MaskKanonymizationtaskMapper {

    @Override
    public MaskKanonymizationtaskDto toDto(MaskKanonymizationtask entity) {
        if ( entity == null ) {
            return null;
        }

        MaskKanonymizationtaskDto maskKanonymizationtaskDto = new MaskKanonymizationtaskDto();

        maskKanonymizationtaskDto.setCreatetime( entity.getCreatetime() );
        maskKanonymizationtaskDto.setCreateuserid( entity.getCreateuserid() );
        maskKanonymizationtaskDto.setDatabasename( entity.getDatabasename() );
        maskKanonymizationtaskDto.setDbname( entity.getDbname() );
        maskKanonymizationtaskDto.setErrordirectory( entity.getErrordirectory() );
        maskKanonymizationtaskDto.setId( entity.getId() );
        maskKanonymizationtaskDto.setOutputdirectory( entity.getOutputdirectory() );
        maskKanonymizationtaskDto.setPass( entity.getPass() );
        maskKanonymizationtaskDto.setRemark( entity.getRemark() );
        maskKanonymizationtaskDto.setSparefield1( entity.getSparefield1() );
        maskKanonymizationtaskDto.setSparefield2( entity.getSparefield2() );
        maskKanonymizationtaskDto.setSparefield3( entity.getSparefield3() );
        maskKanonymizationtaskDto.setSparefield4( entity.getSparefield4() );
        maskKanonymizationtaskDto.setSparefield5( entity.getSparefield5() );
        maskKanonymizationtaskDto.setState( entity.getState() );
        maskKanonymizationtaskDto.setStrategy( entity.getStrategy() );
        maskKanonymizationtaskDto.setTabname( entity.getTabname() );
        maskKanonymizationtaskDto.setTaskname( entity.getTaskname() );
        maskKanonymizationtaskDto.setUpdatetime( entity.getUpdatetime() );
        maskKanonymizationtaskDto.setUpdateuserid( entity.getUpdateuserid() );
        maskKanonymizationtaskDto.setUrl( entity.getUrl() );
        maskKanonymizationtaskDto.setUsername( entity.getUsername() );

        return maskKanonymizationtaskDto;
    }

    @Override
    public List<MaskKanonymizationtaskDto> toDto(List<MaskKanonymizationtask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskKanonymizationtaskDto> list = new ArrayList<MaskKanonymizationtaskDto>( entityList.size() );
        for ( MaskKanonymizationtask maskKanonymizationtask : entityList ) {
            list.add( toDto( maskKanonymizationtask ) );
        }

        return list;
    }

    @Override
    public MaskKanonymizationtask toEntity(MaskKanonymizationtaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskKanonymizationtask maskKanonymizationtask = new MaskKanonymizationtask();

        maskKanonymizationtask.setCreatetime( dto.getCreatetime() );
        maskKanonymizationtask.setCreateuserid( dto.getCreateuserid() );
        maskKanonymizationtask.setDatabasename( dto.getDatabasename() );
        maskKanonymizationtask.setDbname( dto.getDbname() );
        maskKanonymizationtask.setErrordirectory( dto.getErrordirectory() );
        maskKanonymizationtask.setId( dto.getId() );
        maskKanonymizationtask.setOutputdirectory( dto.getOutputdirectory() );
        maskKanonymizationtask.setPass( dto.getPass() );
        maskKanonymizationtask.setRemark( dto.getRemark() );
        maskKanonymizationtask.setSparefield1( dto.getSparefield1() );
        maskKanonymizationtask.setSparefield2( dto.getSparefield2() );
        maskKanonymizationtask.setSparefield3( dto.getSparefield3() );
        maskKanonymizationtask.setSparefield4( dto.getSparefield4() );
        maskKanonymizationtask.setSparefield5( dto.getSparefield5() );
        maskKanonymizationtask.setState( dto.getState() );
        maskKanonymizationtask.setStrategy( dto.getStrategy() );
        maskKanonymizationtask.setTabname( dto.getTabname() );
        maskKanonymizationtask.setTaskname( dto.getTaskname() );
        maskKanonymizationtask.setUpdatetime( dto.getUpdatetime() );
        maskKanonymizationtask.setUpdateuserid( dto.getUpdateuserid() );
        maskKanonymizationtask.setUrl( dto.getUrl() );
        maskKanonymizationtask.setUsername( dto.getUsername() );

        return maskKanonymizationtask;
    }

    @Override
    public List<MaskKanonymizationtask> toEntity(List<MaskKanonymizationtaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskKanonymizationtask> list = new ArrayList<MaskKanonymizationtask>( dtoList.size() );
        for ( MaskKanonymizationtaskDto maskKanonymizationtaskDto : dtoList ) {
            list.add( toEntity( maskKanonymizationtaskDto ) );
        }

        return list;
    }
}
