package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcDb;
import com.wzsec.modules.sdd.jdbc.domain.JdbcUser;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcDbDto;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcUserSmallDto;
import com.wzsec.modules.sdd.source.domain.Datasource;
import com.wzsec.modules.sdd.source.service.dto.DatasourceSmallDto;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JdbcDbMapperImpl implements JdbcDbMapper {

    @Override
    public JdbcDbDto toDto(JdbcDb entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcDbDto jdbcDbDto = new JdbcDbDto();

        jdbcDbDto.setDatasource( datasourceToDatasourceSmallDto( entity.getDatasource() ) );
        jdbcDbDto.setId( entity.getId() );
        jdbcDbDto.setJdbcUsers( jdbcUserSetToJdbcUserSmallDtoSet( entity.getJdbcUsers() ) );
        jdbcDbDto.setLogicdbcname( entity.getLogicdbcname() );
        jdbcDbDto.setLogicdbename( entity.getLogicdbename() );
        jdbcDbDto.setSparefield1( entity.getSparefield1() );
        jdbcDbDto.setSparefield2( entity.getSparefield2() );
        jdbcDbDto.setSparefield3( entity.getSparefield3() );
        jdbcDbDto.setSparefield4( entity.getSparefield4() );
        jdbcDbDto.setSparefield5( entity.getSparefield5() );

        return jdbcDbDto;
    }

    @Override
    public List<JdbcDbDto> toDto(List<JdbcDb> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcDbDto> list = new ArrayList<JdbcDbDto>( entityList.size() );
        for ( JdbcDb jdbcDb : entityList ) {
            list.add( toDto( jdbcDb ) );
        }

        return list;
    }

    @Override
    public JdbcDb toEntity(JdbcDbDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcDb jdbcDb = new JdbcDb();

        jdbcDb.setDatasource( datasourceSmallDtoToDatasource( dto.getDatasource() ) );
        jdbcDb.setId( dto.getId() );
        jdbcDb.setJdbcUsers( jdbcUserSmallDtoSetToJdbcUserSet( dto.getJdbcUsers() ) );
        jdbcDb.setLogicdbcname( dto.getLogicdbcname() );
        jdbcDb.setLogicdbename( dto.getLogicdbename() );
        jdbcDb.setSparefield1( dto.getSparefield1() );
        jdbcDb.setSparefield2( dto.getSparefield2() );
        jdbcDb.setSparefield3( dto.getSparefield3() );
        jdbcDb.setSparefield4( dto.getSparefield4() );
        jdbcDb.setSparefield5( dto.getSparefield5() );

        return jdbcDb;
    }

    @Override
    public List<JdbcDb> toEntity(List<JdbcDbDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcDb> list = new ArrayList<JdbcDb>( dtoList.size() );
        for ( JdbcDbDto jdbcDbDto : dtoList ) {
            list.add( toEntity( jdbcDbDto ) );
        }

        return list;
    }

    protected DatasourceSmallDto datasourceToDatasourceSmallDto(Datasource datasource) {
        if ( datasource == null ) {
            return null;
        }

        DatasourceSmallDto datasourceSmallDto = new DatasourceSmallDto();

        datasourceSmallDto.setDbname( datasource.getDbname() );
        datasourceSmallDto.setDriverprogram( datasource.getDriverprogram() );
        datasourceSmallDto.setId( datasource.getId() );
        datasourceSmallDto.setSrcname( datasource.getSrcname() );
        datasourceSmallDto.setSrcurl( datasource.getSrcurl() );
        datasourceSmallDto.setType( datasource.getType() );

        return datasourceSmallDto;
    }

    protected JdbcUserSmallDto jdbcUserToJdbcUserSmallDto(JdbcUser jdbcUser) {
        if ( jdbcUser == null ) {
            return null;
        }

        JdbcUserSmallDto jdbcUserSmallDto = new JdbcUserSmallDto();

        jdbcUserSmallDto.setId( jdbcUser.getId() );
        jdbcUserSmallDto.setNickName( jdbcUser.getNickName() );
        jdbcUserSmallDto.setUsername( jdbcUser.getUsername() );

        return jdbcUserSmallDto;
    }

    protected Set<JdbcUserSmallDto> jdbcUserSetToJdbcUserSmallDtoSet(Set<JdbcUser> set) {
        if ( set == null ) {
            return null;
        }

        Set<JdbcUserSmallDto> set1 = new HashSet<JdbcUserSmallDto>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( JdbcUser jdbcUser : set ) {
            set1.add( jdbcUserToJdbcUserSmallDto( jdbcUser ) );
        }

        return set1;
    }

    protected Datasource datasourceSmallDtoToDatasource(DatasourceSmallDto datasourceSmallDto) {
        if ( datasourceSmallDto == null ) {
            return null;
        }

        Datasource datasource = new Datasource();

        datasource.setDbname( datasourceSmallDto.getDbname() );
        datasource.setDriverprogram( datasourceSmallDto.getDriverprogram() );
        datasource.setId( datasourceSmallDto.getId() );
        datasource.setSrcname( datasourceSmallDto.getSrcname() );
        datasource.setSrcurl( datasourceSmallDto.getSrcurl() );
        datasource.setType( datasourceSmallDto.getType() );

        return datasource;
    }

    protected JdbcUser jdbcUserSmallDtoToJdbcUser(JdbcUserSmallDto jdbcUserSmallDto) {
        if ( jdbcUserSmallDto == null ) {
            return null;
        }

        JdbcUser jdbcUser = new JdbcUser();

        jdbcUser.setId( jdbcUserSmallDto.getId() );
        jdbcUser.setNickName( jdbcUserSmallDto.getNickName() );
        jdbcUser.setUsername( jdbcUserSmallDto.getUsername() );

        return jdbcUser;
    }

    protected Set<JdbcUser> jdbcUserSmallDtoSetToJdbcUserSet(Set<JdbcUserSmallDto> set) {
        if ( set == null ) {
            return null;
        }

        Set<JdbcUser> set1 = new HashSet<JdbcUser>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( JdbcUserSmallDto jdbcUserSmallDto : set ) {
            set1.add( jdbcUserSmallDtoToJdbcUser( jdbcUserSmallDto ) );
        }

        return set1;
    }
}
