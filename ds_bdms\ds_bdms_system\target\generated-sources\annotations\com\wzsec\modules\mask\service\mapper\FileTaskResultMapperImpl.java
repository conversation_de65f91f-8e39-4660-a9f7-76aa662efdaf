package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.FileTaskResult;
import com.wzsec.modules.mask.service.dto.FileTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class FileTaskResultMapperImpl implements FileTaskResultMapper {

    @Override
    public FileTaskResultDto toDto(FileTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        FileTaskResultDto fileTaskResultDto = new FileTaskResultDto();

        fileTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        fileTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        fileTaskResultDto.setCreatetime( entity.getCreatetime() );
        fileTaskResultDto.setFilenames( entity.getFilenames() );
        fileTaskResultDto.setId( entity.getId() );
        fileTaskResultDto.setInputfiletype( entity.getInputfiletype() );
        fileTaskResultDto.setInputpath( entity.getInputpath() );
        fileTaskResultDto.setJobendtime( entity.getJobendtime() );
        fileTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        fileTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        fileTaskResultDto.setMiniodata( entity.getMiniodata() );
        fileTaskResultDto.setOutputname( entity.getOutputname() );
        fileTaskResultDto.setOutputpath( entity.getOutputpath() );
        fileTaskResultDto.setOutputtype( entity.getOutputtype() );
        fileTaskResultDto.setRemark( entity.getRemark() );
        fileTaskResultDto.setSparefield1( entity.getSparefield1() );
        fileTaskResultDto.setSparefield2( entity.getSparefield2() );
        fileTaskResultDto.setSparefield3( entity.getSparefield3() );
        fileTaskResultDto.setSparefield4( entity.getSparefield4() );
        fileTaskResultDto.setSparefield5( entity.getSparefield5() );
        fileTaskResultDto.setSplitstr( entity.getSplitstr() );
        fileTaskResultDto.setStrategyname( entity.getStrategyname() );
        fileTaskResultDto.setTaskname( entity.getTaskname() );
        fileTaskResultDto.setTaskstatus( entity.getTaskstatus() );
        fileTaskResultDto.setTotallines( entity.getTotallines() );
        fileTaskResultDto.setUpdatetime( entity.getUpdatetime() );
        fileTaskResultDto.setUserid( entity.getUserid() );
        fileTaskResultDto.setUsername( entity.getUsername() );

        return fileTaskResultDto;
    }

    @Override
    public List<FileTaskResultDto> toDto(List<FileTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FileTaskResultDto> list = new ArrayList<FileTaskResultDto>( entityList.size() );
        for ( FileTaskResult fileTaskResult : entityList ) {
            list.add( toDto( fileTaskResult ) );
        }

        return list;
    }

    @Override
    public FileTaskResult toEntity(FileTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        FileTaskResult fileTaskResult = new FileTaskResult();

        fileTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        fileTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        fileTaskResult.setCreatetime( dto.getCreatetime() );
        fileTaskResult.setFilenames( dto.getFilenames() );
        fileTaskResult.setId( dto.getId() );
        fileTaskResult.setInputfiletype( dto.getInputfiletype() );
        fileTaskResult.setInputpath( dto.getInputpath() );
        fileTaskResult.setJobendtime( dto.getJobendtime() );
        fileTaskResult.setJobstarttime( dto.getJobstarttime() );
        fileTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        fileTaskResult.setMiniodata( dto.getMiniodata() );
        fileTaskResult.setOutputname( dto.getOutputname() );
        fileTaskResult.setOutputpath( dto.getOutputpath() );
        fileTaskResult.setOutputtype( dto.getOutputtype() );
        fileTaskResult.setRemark( dto.getRemark() );
        fileTaskResult.setSparefield1( dto.getSparefield1() );
        fileTaskResult.setSparefield2( dto.getSparefield2() );
        fileTaskResult.setSparefield3( dto.getSparefield3() );
        fileTaskResult.setSparefield4( dto.getSparefield4() );
        fileTaskResult.setSparefield5( dto.getSparefield5() );
        fileTaskResult.setSplitstr( dto.getSplitstr() );
        fileTaskResult.setStrategyname( dto.getStrategyname() );
        fileTaskResult.setTaskname( dto.getTaskname() );
        fileTaskResult.setTaskstatus( dto.getTaskstatus() );
        fileTaskResult.setTotallines( dto.getTotallines() );
        fileTaskResult.setUpdatetime( dto.getUpdatetime() );
        fileTaskResult.setUserid( dto.getUserid() );
        fileTaskResult.setUsername( dto.getUsername() );

        return fileTaskResult;
    }

    @Override
    public List<FileTaskResult> toEntity(List<FileTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FileTaskResult> list = new ArrayList<FileTaskResult>( dtoList.size() );
        for ( FileTaskResultDto fileTaskResultDto : dtoList ) {
            list.add( toEntity( fileTaskResultDto ) );
        }

        return list;
    }
}
