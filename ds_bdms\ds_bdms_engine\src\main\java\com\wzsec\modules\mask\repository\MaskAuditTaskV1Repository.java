package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskAuditTaskV1;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* <AUTHOR>
* @date 2021-01-14
*/
public interface MaskAuditTaskV1Repository extends JpaRepository<MaskAuditTaskV1, Integer>, JpaSpecificationExecutor<MaskAuditTaskV1> {
    @Query(value = "select MAX(`taskname`) from sdd_maskaudit_task where taskname like concat(?1,'%')", nativeQuery = true)
    String findMAXTaskNameByPrefix(String prefix);
}
