package com.wzsec.modules.correlation.service.mapper;

import com.wzsec.modules.correlation.domain.KeyCorrelationLog;
import com.wzsec.modules.correlation.service.dto.KeyCorrelationLogDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:00+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KeyCorrelationLogMapperImpl implements KeyCorrelationLogMapper {

    @Override
    public KeyCorrelationLogDto toDto(KeyCorrelationLog entity) {
        if ( entity == null ) {
            return null;
        }

        KeyCorrelationLogDto keyCorrelationLogDto = new KeyCorrelationLogDto();

        keyCorrelationLogDto.setDetails( entity.getDetails() );
        keyCorrelationLogDto.setFieldcname( entity.getFieldcname() );
        keyCorrelationLogDto.setFieldename( entity.getFieldename() );
        keyCorrelationLogDto.setId( entity.getId() );
        keyCorrelationLogDto.setOperatetime( entity.getOperatetime() );
        keyCorrelationLogDto.setOperatetype( entity.getOperatetype() );
        keyCorrelationLogDto.setOperateuser( entity.getOperateuser() );
        keyCorrelationLogDto.setTablecname( entity.getTablecname() );
        keyCorrelationLogDto.setTablename( entity.getTablename() );
        keyCorrelationLogDto.setTabletype( entity.getTabletype() );

        return keyCorrelationLogDto;
    }

    @Override
    public List<KeyCorrelationLogDto> toDto(List<KeyCorrelationLog> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KeyCorrelationLogDto> list = new ArrayList<KeyCorrelationLogDto>( entityList.size() );
        for ( KeyCorrelationLog keyCorrelationLog : entityList ) {
            list.add( toDto( keyCorrelationLog ) );
        }

        return list;
    }

    @Override
    public KeyCorrelationLog toEntity(KeyCorrelationLogDto dto) {
        if ( dto == null ) {
            return null;
        }

        KeyCorrelationLog keyCorrelationLog = new KeyCorrelationLog();

        keyCorrelationLog.setDetails( dto.getDetails() );
        keyCorrelationLog.setFieldcname( dto.getFieldcname() );
        keyCorrelationLog.setFieldename( dto.getFieldename() );
        keyCorrelationLog.setId( dto.getId() );
        keyCorrelationLog.setOperatetime( dto.getOperatetime() );
        keyCorrelationLog.setOperatetype( dto.getOperatetype() );
        keyCorrelationLog.setOperateuser( dto.getOperateuser() );
        keyCorrelationLog.setTablecname( dto.getTablecname() );
        keyCorrelationLog.setTablename( dto.getTablename() );
        keyCorrelationLog.setTabletype( dto.getTabletype() );

        return keyCorrelationLog;
    }

    @Override
    public List<KeyCorrelationLog> toEntity(List<KeyCorrelationLogDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KeyCorrelationLog> list = new ArrayList<KeyCorrelationLog>( dtoList.size() );
        for ( KeyCorrelationLogDto keyCorrelationLogDto : dtoList ) {
            list.add( toEntity( keyCorrelationLogDto ) );
        }

        return list;
    }
}
