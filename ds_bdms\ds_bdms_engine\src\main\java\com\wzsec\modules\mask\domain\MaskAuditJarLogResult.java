package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2021-03-18
*/
@Entity
@Data
@Table(name="sdd_maskaudit_jarlogresult")
public class MaskAuditJarLogResult implements Serializable {

    /** 自增ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 任务号 */
    @Column(name = "taskname")
    private String taskname;

    /** 脱敏策略 */
    @Column(name = "strategy")
    private String strategy;

    /** 调用算法包名称 */
    @Column(name = "algorithmpackagename")
    private String algorithmpackagename;

    /** 调用算法名 */
    @Column(name = "algorithmname")
    private String algorithmname;

    /** 主机ip地址 */
    @Column(name = "ipaddress")
    private String ipaddress;

    /** 调用账号 */
    @Column(name = "account")
    private String account;

    /** 行数 */
    @Column(name = "linenumber")
    private String linenumber;

    /** 调用是否成功 */
    @Column(name = "issuccessful")
    private String issuccessful;

    /** 开始时间 */
    @Column(name = "starttime")
    private String starttime;

    /** 结束时间 */
    @Column(name = "endtime")
    private String endtime;

    /** 耗时(毫秒) */
    @Column(name = "totaltime")
    private String totaltime;

    /** 检测时间 */
    @Column(name = "checktime")
    private String checktime;

    /** 备注 */
    @Column(name = "note")
    private String note;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskAuditJarLogResult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
