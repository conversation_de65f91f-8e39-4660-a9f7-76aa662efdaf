package com.wzsec.utils;

/**
 * 数组工具类
 *
 * <AUTHOR>
 * @date 2020-4-23
 */
public class ArrayUtil {

    /**
     * String数组转Long数组
     *
     * <AUTHOR>
     * @date 2020-4-23
     */
    public static String[] longToString(Long longArray[]) {
        if (longArray == null || longArray.length < 1) {
            return null;
        }
        String stringArray[] = new String[longArray.length];
        for (int i = 0; i < stringArray.length; i++) {
            try {
                stringArray[i] = String.valueOf(longArray[i]);
            } catch (NumberFormatException e) {
                stringArray[i] = null;
                continue;
            }
        }
        return stringArray;
    }

    /**
     * Long数组转String数组
     *
     * <AUTHOR>
     * @date 2020-4-23
     */
    public static Long[] stringToLong(String stringArray[]) {
        if (stringArray == null || stringArray.length < 1) {
            return null;
        }
        Long longArray[] = new Long[stringArray.length];
        for (int i = 0; i < stringArray.length; i++) {
            try {
                longArray[i] = Long.valueOf(stringArray[i]);
            } catch (NumberFormatException e) {
                longArray[i] = null;
                continue;
            }
        }
        return longArray;
    }


    /**
     * String数组转Integer数组
     *
     * <AUTHOR>
     * @date 2020-5-21
     */
    public static String[] integerToString(Integer integerArray[]) {
        if (integerArray == null || integerArray.length < 1) {
            return null;
        }
        String stringArray[] = new String[integerArray.length];
        for (int i = 0; i < stringArray.length; i++) {
            try {
                stringArray[i] = String.valueOf(integerArray[i]);
            } catch (NumberFormatException e) {
                stringArray[i] = null;
                continue;
            }
        }
        return stringArray;
    }

    /**
     * Integer数组转String数组
     *
     * <AUTHOR>
     * @date 2020-5-21
     */
    public static Integer[] stringToInteger(String stringArray[]) {
        if (stringArray == null || stringArray.length < 1) {
            return null;
        }
        Integer integerArray[] = new Integer[stringArray.length];
        for (int i = 0; i < stringArray.length; i++) {
            try {
                integerArray[i] = Integer.valueOf(stringArray[i]);
            } catch (NumberFormatException e) {
                integerArray[i] = null;
                continue;
            }
        }
        return integerArray;
    }

}

