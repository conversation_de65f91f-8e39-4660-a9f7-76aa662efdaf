package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:00+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskPictaskconfigMapperImpl implements MaskPictaskconfigMapper {

    @Override
    public MaskPictaskconfigDto toDto(MaskPictaskconfig entity) {
        if ( entity == null ) {
            return null;
        }

        MaskPictaskconfigDto maskPictaskconfigDto = new MaskPictaskconfigDto();

        maskPictaskconfigDto.setComputeresources( entity.getComputeresources() );
        maskPictaskconfigDto.setCreatetime( entity.getCreatetime() );
        maskPictaskconfigDto.setCreateuser( entity.getCreateuser() );
        maskPictaskconfigDto.setCron( entity.getCron() );
        maskPictaskconfigDto.setExecutionstate( entity.getExecutionstate() );
        maskPictaskconfigDto.setId( entity.getId() );
        maskPictaskconfigDto.setInputdirectory( entity.getInputdirectory() );
        maskPictaskconfigDto.setInputfileformat( entity.getInputfileformat() );
        maskPictaskconfigDto.setMaskobject( entity.getMaskobject() );
        maskPictaskconfigDto.setOutputdirectory( entity.getOutputdirectory() );
        maskPictaskconfigDto.setOutputfileformat( entity.getOutputfileformat() );
        maskPictaskconfigDto.setRemark( entity.getRemark() );
        maskPictaskconfigDto.setSparefield1( entity.getSparefield1() );
        maskPictaskconfigDto.setSparefield2( entity.getSparefield2() );
        maskPictaskconfigDto.setSparefield3( entity.getSparefield3() );
        maskPictaskconfigDto.setSparefield4( entity.getSparefield4() );
        maskPictaskconfigDto.setSparefield5( entity.getSparefield5() );
        maskPictaskconfigDto.setState( entity.getState() );
        maskPictaskconfigDto.setSubmitmethod( entity.getSubmitmethod() );
        maskPictaskconfigDto.setTaskname( entity.getTaskname() );
        maskPictaskconfigDto.setUpdatetime( entity.getUpdatetime() );
        maskPictaskconfigDto.setUpdateuser( entity.getUpdateuser() );

        return maskPictaskconfigDto;
    }

    @Override
    public List<MaskPictaskconfigDto> toDto(List<MaskPictaskconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskPictaskconfigDto> list = new ArrayList<MaskPictaskconfigDto>( entityList.size() );
        for ( MaskPictaskconfig maskPictaskconfig : entityList ) {
            list.add( toDto( maskPictaskconfig ) );
        }

        return list;
    }

    @Override
    public MaskPictaskconfig toEntity(MaskPictaskconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskPictaskconfig maskPictaskconfig = new MaskPictaskconfig();

        maskPictaskconfig.setComputeresources( dto.getComputeresources() );
        maskPictaskconfig.setCreatetime( dto.getCreatetime() );
        maskPictaskconfig.setCreateuser( dto.getCreateuser() );
        maskPictaskconfig.setCron( dto.getCron() );
        maskPictaskconfig.setExecutionstate( dto.getExecutionstate() );
        maskPictaskconfig.setId( dto.getId() );
        maskPictaskconfig.setInputdirectory( dto.getInputdirectory() );
        maskPictaskconfig.setInputfileformat( dto.getInputfileformat() );
        maskPictaskconfig.setMaskobject( dto.getMaskobject() );
        maskPictaskconfig.setOutputdirectory( dto.getOutputdirectory() );
        maskPictaskconfig.setOutputfileformat( dto.getOutputfileformat() );
        maskPictaskconfig.setRemark( dto.getRemark() );
        maskPictaskconfig.setSparefield1( dto.getSparefield1() );
        maskPictaskconfig.setSparefield2( dto.getSparefield2() );
        maskPictaskconfig.setSparefield3( dto.getSparefield3() );
        maskPictaskconfig.setSparefield4( dto.getSparefield4() );
        maskPictaskconfig.setSparefield5( dto.getSparefield5() );
        maskPictaskconfig.setState( dto.getState() );
        maskPictaskconfig.setSubmitmethod( dto.getSubmitmethod() );
        maskPictaskconfig.setTaskname( dto.getTaskname() );
        maskPictaskconfig.setUpdatetime( dto.getUpdatetime() );
        maskPictaskconfig.setUpdateuser( dto.getUpdateuser() );

        return maskPictaskconfig;
    }

    @Override
    public List<MaskPictaskconfig> toEntity(List<MaskPictaskconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskPictaskconfig> list = new ArrayList<MaskPictaskconfig>( dtoList.size() );
        for ( MaskPictaskconfigDto maskPictaskconfigDto : dtoList ) {
            list.add( toEntity( maskPictaskconfigDto ) );
        }

        return list;
    }
}
