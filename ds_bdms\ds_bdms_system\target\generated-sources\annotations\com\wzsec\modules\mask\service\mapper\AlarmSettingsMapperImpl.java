package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.AlarmSettings;
import com.wzsec.modules.mask.service.dto.AlarmSettingsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:02+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AlarmSettingsMapperImpl implements AlarmSettingsMapper {

    @Override
    public AlarmSettingsDto toDto(AlarmSettings entity) {
        if ( entity == null ) {
            return null;
        }

        AlarmSettingsDto alarmSettingsDto = new AlarmSettingsDto();

        alarmSettingsDto.setAlarmlevel( entity.getAlarmlevel() );
        alarmSettingsDto.setCreatetime( entity.getCreatetime() );
        alarmSettingsDto.setCreateuser( entity.getCreateuser() );
        alarmSettingsDto.setFaultname( entity.getFaultname() );
        alarmSettingsDto.setId( entity.getId() );
        alarmSettingsDto.setMessage( entity.getMessage() );
        alarmSettingsDto.setNote( entity.getNote() );
        alarmSettingsDto.setSparefield1( entity.getSparefield1() );
        alarmSettingsDto.setSparefield2( entity.getSparefield2() );
        alarmSettingsDto.setSparefield3( entity.getSparefield3() );
        alarmSettingsDto.setSparefield4( entity.getSparefield4() );
        alarmSettingsDto.setSparefield5( entity.getSparefield5() );
        alarmSettingsDto.setState( entity.getState() );
        alarmSettingsDto.setThreshold( entity.getThreshold() );
        alarmSettingsDto.setUpdatetime( entity.getUpdatetime() );
        alarmSettingsDto.setUpdateuser( entity.getUpdateuser() );

        return alarmSettingsDto;
    }

    @Override
    public List<AlarmSettingsDto> toDto(List<AlarmSettings> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AlarmSettingsDto> list = new ArrayList<AlarmSettingsDto>( entityList.size() );
        for ( AlarmSettings alarmSettings : entityList ) {
            list.add( toDto( alarmSettings ) );
        }

        return list;
    }

    @Override
    public AlarmSettings toEntity(AlarmSettingsDto dto) {
        if ( dto == null ) {
            return null;
        }

        AlarmSettings alarmSettings = new AlarmSettings();

        alarmSettings.setAlarmlevel( dto.getAlarmlevel() );
        alarmSettings.setCreatetime( dto.getCreatetime() );
        alarmSettings.setCreateuser( dto.getCreateuser() );
        alarmSettings.setFaultname( dto.getFaultname() );
        alarmSettings.setId( dto.getId() );
        alarmSettings.setMessage( dto.getMessage() );
        alarmSettings.setNote( dto.getNote() );
        alarmSettings.setSparefield1( dto.getSparefield1() );
        alarmSettings.setSparefield2( dto.getSparefield2() );
        alarmSettings.setSparefield3( dto.getSparefield3() );
        alarmSettings.setSparefield4( dto.getSparefield4() );
        alarmSettings.setSparefield5( dto.getSparefield5() );
        alarmSettings.setState( dto.getState() );
        alarmSettings.setThreshold( dto.getThreshold() );
        alarmSettings.setUpdatetime( dto.getUpdatetime() );
        alarmSettings.setUpdateuser( dto.getUpdateuser() );

        return alarmSettings;
    }

    @Override
    public List<AlarmSettings> toEntity(List<AlarmSettingsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<AlarmSettings> list = new ArrayList<AlarmSettings>( dtoList.size() );
        for ( AlarmSettingsDto alarmSettingsDto : dtoList ) {
            list.add( toEntity( alarmSettingsDto ) );
        }

        return list;
    }
}
