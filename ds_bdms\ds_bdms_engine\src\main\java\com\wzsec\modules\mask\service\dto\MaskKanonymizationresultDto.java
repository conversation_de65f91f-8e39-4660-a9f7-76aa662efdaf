package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Data
public class MaskKanonymizationresultDto implements Serializable {

    /** 主键 */
    private Integer id;

    /** 任务名 */
    private String taskname;

    /** 库名 */
    private String dbname;

    /** 表名 */
    private String tabname;

    /** 脱敏行数 */
    private String maskline;

    /** 数据总行数 */
    private String totalline;

    /** 任务状态 */
    private String taskstatus;

    /** 任务结果评估(良好,一般,差) */
    private String taskresultestimate;

    /** 输出目录(1到库，0到文件) */
    private String outputdirectory;

    /** 脱敏前数据 */
    private String beforemaskdata;

    /** 脱敏后数据 */
    private String aftermaskdata;

    /** 开始时间 */
    private String starttime;

    /** 结束时间 */
    private String endtime;

    /** 备注 */
    private String remark;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;

    /** 备用字段5 */
    private String sparefield6;
}
