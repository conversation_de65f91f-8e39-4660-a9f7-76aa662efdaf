package com.wzsec.modules.sdd.metadata.service.mapper;

import com.wzsec.modules.sdd.metadata.domain.MetaTable;
import com.wzsec.modules.sdd.metadata.service.dto.MetaTableDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MetaTableMapperImpl implements MetaTableMapper {

    @Override
    public MetaTableDto toDto(MetaTable entity) {
        if ( entity == null ) {
            return null;
        }

        MetaTableDto metaTableDto = new MetaTableDto();

        metaTableDto.setCategory( entity.getCategory() );
        metaTableDto.setCreatetime( entity.getCreatetime() );
        metaTableDto.setCreateuser( entity.getCreateuser() );
        metaTableDto.setDbname( entity.getDbname() );
        metaTableDto.setDbstatus( entity.getDbstatus() );
        metaTableDto.setEvaluationmethod( entity.getEvaluationmethod() );
        metaTableDto.setEvaluationstatus( entity.getEvaluationstatus() );
        metaTableDto.setGetway( entity.getGetway() );
        metaTableDto.setId( entity.getId() );
        metaTableDto.setNote( entity.getNote() );
        metaTableDto.setSenlevel( entity.getSenlevel() );
        metaTableDto.setSourceid( entity.getSourceid() );
        metaTableDto.setSourcename( entity.getSourcename() );
        metaTableDto.setSourcetype( entity.getSourcetype() );
        metaTableDto.setSparefield1( entity.getSparefield1() );
        metaTableDto.setSparefield2( entity.getSparefield2() );
        metaTableDto.setSparefield3( entity.getSparefield3() );
        metaTableDto.setSparefield4( entity.getSparefield4() );
        metaTableDto.setStorepath( entity.getStorepath() );
        metaTableDto.setSubjectdomain( entity.getSubjectdomain() );
        metaTableDto.setTablecname( entity.getTablecname() );
        metaTableDto.setTablename( entity.getTablename() );
        metaTableDto.setTablestatus( entity.getTablestatus() );
        metaTableDto.setTaskname( entity.getTaskname() );
        metaTableDto.setUpdatetime( entity.getUpdatetime() );
        metaTableDto.setUpdateuser( entity.getUpdateuser() );

        return metaTableDto;
    }

    @Override
    public List<MetaTableDto> toDto(List<MetaTable> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetaTableDto> list = new ArrayList<MetaTableDto>( entityList.size() );
        for ( MetaTable metaTable : entityList ) {
            list.add( toDto( metaTable ) );
        }

        return list;
    }

    @Override
    public MetaTable toEntity(MetaTableDto dto) {
        if ( dto == null ) {
            return null;
        }

        MetaTable metaTable = new MetaTable();

        metaTable.setCategory( dto.getCategory() );
        metaTable.setCreatetime( dto.getCreatetime() );
        metaTable.setCreateuser( dto.getCreateuser() );
        metaTable.setDbname( dto.getDbname() );
        metaTable.setDbstatus( dto.getDbstatus() );
        metaTable.setEvaluationmethod( dto.getEvaluationmethod() );
        metaTable.setEvaluationstatus( dto.getEvaluationstatus() );
        metaTable.setGetway( dto.getGetway() );
        metaTable.setId( dto.getId() );
        metaTable.setNote( dto.getNote() );
        metaTable.setSenlevel( dto.getSenlevel() );
        metaTable.setSourceid( dto.getSourceid() );
        metaTable.setSourcename( dto.getSourcename() );
        metaTable.setSourcetype( dto.getSourcetype() );
        metaTable.setSparefield1( dto.getSparefield1() );
        metaTable.setSparefield2( dto.getSparefield2() );
        metaTable.setSparefield3( dto.getSparefield3() );
        metaTable.setSparefield4( dto.getSparefield4() );
        metaTable.setStorepath( dto.getStorepath() );
        metaTable.setSubjectdomain( dto.getSubjectdomain() );
        metaTable.setTablecname( dto.getTablecname() );
        metaTable.setTablename( dto.getTablename() );
        metaTable.setTablestatus( dto.getTablestatus() );
        metaTable.setTaskname( dto.getTaskname() );
        metaTable.setUpdatetime( dto.getUpdatetime() );
        metaTable.setUpdateuser( dto.getUpdateuser() );

        return metaTable;
    }

    @Override
    public List<MetaTable> toEntity(List<MetaTableDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MetaTable> list = new ArrayList<MetaTable>( dtoList.size() );
        for ( MetaTableDto metaTableDto : dtoList ) {
            list.add( toEntity( metaTableDto ) );
        }

        return list;
    }
}
