package com.wzsec.modules.api.algo;

import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 改写结果脱敏算法_地址掩码
 *
 */
public class ADDRESSMask {

    /**
     * 地址掩码（参数：掩码字符）
     *
     * @param hideCode 掩码字符
     */
    public static String encrypt(String strData, String hideCode) {
        if (strData != null && !strData.equals("")) {
            String strMaskResult = strData;
            if (isContainDigit(strData)) { // 如果包含数字，改写
                strMaskResult = strData.replaceAll("\\d", hideCode);
            }
            String[] strMaskResultArr = strMaskResult.split("");
//            for (int i = 0; i < strMaskResultArr.length; i++) {
//                for (int j = 0; j < NumberChar.length; j++) {
//                    if (strMaskResultArr[i].equals(NumberChar[j])) {
//                        strMaskResultArr[i] = hideCode;
//                    }
//                }
//            }
            return StringUtils.join(strMaskResultArr, "");
        }
        return null;
    }

    /**
     * 判断一个字符串是否含有数字
     *
     * @param content
     * @return
     */
    public static boolean isContainDigit(String content) {
        boolean flag = false;
        Pattern p = Pattern.compile(".*\\d+.*");
        Matcher m = p.matcher(content);
        if (m.matches()) {
            flag = true;
        }
        return flag;
    }

    public static void main(String[] args) {
        String strData = "上海市上海市龙梆卜路956号瑰广私小区十一单元1067室";
        System.out.println(encrypt(strData, "*"));
        System.out.println(encrypt(strData, "%"));
        System.out.println(encrypt(strData, "#"));
        System.out.println(encrypt(strData, "@"));
        System.out.println(encrypt(strData, "!"));
        System.out.println(encrypt(strData, "^"));
    }

}
