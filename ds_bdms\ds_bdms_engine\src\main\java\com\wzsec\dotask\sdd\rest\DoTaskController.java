package com.wzsec.dotask.sdd.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.sdd.service.DoTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2020-06-28
 */
// @Api(tags = "敏感数据发现任务管理")
@RestController
@RequestMapping("/engine/task")
public class DoTaskController {

    private final DoTaskService doTaskService;

    private final UserService userService;

    public DoTaskController(DoTaskService doTaskService, UserService userService) {
        this.doTaskService = doTaskService;
        this.userService = userService;
    }

    @Log("执行敏感数据发现任务")
    // // @ApiOperation("执行敏感数据发现任务")
    @PostMapping(value = "/exec/{id}")
//    @PreAuthorize("@el.check('task:edit')")
    public ResponseEntity<Object> execution(@PathVariable Long id, HttpServletRequest request) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doTaskService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("定时执行敏感数据发现任务")
    //@ApiOperation("定时执行敏感数据发现任务")
    @PostMapping(value = "/timingexec/{id}")
    //@PreAuthorize("@el.check('task:edit')")
    public ResponseEntity<Object> timingexecution(@PathVariable Long id) {
        System.out.println("开始执行：" + id);
        doTaskService.execution(id, Const.TASK_SUBMITTYPE_AUTOSUBMIT);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }


}
