package com.wzsec.dotask.mask.service.excute.hdfs;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

/**
 *@Description:Reducer
 *<AUTHOR>
 *@date 2017-1-3
 */
public class DataMaskingReducer extends Reducer<NullWritable, Text, NullWritable, Text> {

	public void reduce(NullWritable key, Iterable<Text> value, Context context) throws java.io.IOException, InterruptedException {
	   for(Text val : value) {
		  context.write(NullWritable.get(), val);
	   }
	}
}
