package com.wzsec.dotask.mask.service.excute.kafka;

import com.wzsec.utils.ConstEngine;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @Description: kafka工具类
 * @date 2020年12月3日
 */
public class KafkaUtil {

    private final static Logger log = LoggerFactory.getLogger(KafkaUtil.class);

    public static final AtomicBoolean isRunning = new AtomicBoolean(true);

    public static Map<Long, Boolean> isRunningMap = new HashMap<>();

    /**
     * 初始化kafka配置
     *
     * @return
     */
    public static Properties initConfig() {
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                ConstEngine.sddEngineConfs.get("kafka.consumer.bootstrap-servers").trim());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, ConstEngine.sddEngineConfs.get("kafka.consumer.group-id").trim());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,
                ConstEngine.sddEngineConfs.get("kafka.consumer.enable.auto.commit").trim()); // 自动commit
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,
                ConstEngine.sddEngineConfs.get("kafka.consumer.auto.commit.interval.ms").trim()); // 定时commit的周期
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
                ConstEngine.sddEngineConfs.get("kafka.consumer.session.timeout.ms").trim()); // consumer活性超时时间
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                ConstEngine.sddEngineConfs.get("kafka.consumer.key.deserializer").trim());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                ConstEngine.sddEngineConfs.get("kafka.consumer.value.deserializer").trim());
        return props;
    }


    /**
     * 消费kafka消息
     *
     * @return
     */
    public static void consumeKafkaMessage(String topic) {
        Properties props = initConfig();
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(Arrays.asList(topic));
        long time = System.currentTimeMillis();
        long cycle = 10 * 1000;// 10秒打印一次正在消费的topic
        long count = 0;
        try {
            while (isRunning.get()) {
                ConsumerRecords<String, String> records = consumer.poll(5);
                if (records != null && records.count() > 0) {
                    for (ConsumerRecord<String, String> record : records) {
                        System.out.println("输出1：topic=" + record.topic() + ", partition=" + record.partition());
                        System.out.println("输出2：key=" + record.key() + ", value=" + record.value());
                        count++;
                    }
                }
                if (System.currentTimeMillis() >= time + cycle) {
                    time += cycle;
                    System.out.println("正在消费topic:" + topic + "，次数：" + count + "，Kafka脱敏正在执行中...");
                    count = 0;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            consumer.close();
        }
    }

    public static void main(String[] args) {
//        String row =
//                "{\"accelSet\":{\"lat\":315,\"long_\":1202,\"vert\":120,\"yaw\":343},\"brakes\":{\"abs\":\"engaged\",\"auxBrakes\":\"off\",\"brakeBoost\":\"off\",\"brakePadel\":\"on\",\"scs\":\"engaged\",\"traction\":\"engaged\",\"wheelBrakes\":{\"bitSize\":5,\"bytes\":\"50\",\"leftFront\":true,\"leftRear\":false,\"rightFront\":true,\"rightRear\":false,\"unavailable\":false,\"unusedBits\":3}},\"heading\":2148,\"id\":\"4000743400980006\",\"msgCnt\":0,\"pos\":{\"elevation\":44426,\"lat\":314219765,\"long_\":1206352991},\"safetyExt\":{\"pathHistory\":{\"crumbData\":[{\"llvOffset\":{\"offsetLL\":{\"choiceID\":6,\"position_LatLon\":{\"lat\":1,\"lon\":2}}},\"speed\":8191,\"timeOffset\":108}]}},\"secMark\":6301,\"size\":{\"height\":2,\"length\":6,\"width\":3},\"speed\":6,\"transmission\":\"neutral\",\"vehicleClass\":{\"classification\":0}}";
//        Properties props = initConfig();
//        KafkaProducer<String, String> producer = new KafkaProducer<String,
//                String>(props);
//        for (int i = 0; i < 5000000; i++) {
//            producer.send(new ProducerRecord<String, String>("test-data",
//                    "key111", row));
//        }

//         consumeKafkaMessage("test-data");
    }
}
