package com.wzsec.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.apache.log4j.Logger;


/**
 *@Description:SHA256 64个字符
 *<AUTHOR> by xiongpf
 *@date 2018-01-29
 */
public class SHA256 {
	private static Logger log = Logger.getLogger(SHA256.class);
	 
	static{
	   log.info("调用算法:"+ SHA256.class.getName());
	}

    public static String sha256(String data) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
        messageDigest.update(data.getBytes());
        byte[] byteBuffer = messageDigest.digest();
        //将byte转为16进制
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i=0;i<byteBuffer.length;i++){
            temp = Integer.toHexString(byteBuffer[i] & 0xFF);
            if(temp.length()==1){
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    public String encrypt(String paramString) throws Exception {
        String returnstr = "";
        if(paramString != null && !"".equals(paramString.trim()) && !paramString.matches("^[\\u0020\\u3000]*$")) {
            returnstr = sha256(paramString);
            return returnstr;
        }
        else{
            return paramString;
        }
    }

    public static void main(String[] args) {
        SHA256 sha256 = new SHA256();
        try{
            System.out.println(sha256.encrypt("chinactyun"));
            System.out.println(sha256.encrypt("110108000000016"));
            System.out.println(sha256.encrypt("11010800000001"));
            System.out.println(sha256.encrypt("11010800000001@"));
            System.out.println(sha256.encrypt("atool.org"));
        }
        catch(Exception var3) {
            var3.printStackTrace();
        }
    }
}
