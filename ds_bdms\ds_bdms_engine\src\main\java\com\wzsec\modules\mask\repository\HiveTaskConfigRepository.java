package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.HiveTaskConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2020-12-11
*/
public interface HiveTaskConfigRepository extends JpaRepository<HiveTaskConfig, Integer>, JpaSpecificationExecutor<HiveTaskConfig> {

    @Query(value = "select MAX(`taskname`) from sdd_mask_hivetaskconfig where taskname like concat(?1,'%')", nativeQuery = true)
    String findMAXTaskNameByPrefix(String prefix);

    @Query(value = "select distinct dbname from sdd_meta_table where sourceid = ?1 ORDER BY id DESC", nativeQuery = true)
    List<String> getDbnameBySourceid(String sourceid);

    @Query(value = "select distinct tablename from sdd_meta_table where sourceid = ?1 and dbname = ?2 ORDER BY id DESC", nativeQuery = true)
    List<String> getTabnameByDbname(String sourceid, String dbname);

    @Query(value = "select * from sdd_mask_strategy_table where sourcetype = ?1 and dbname = ?2 and tabename= ?3 ORDER BY strategyname", nativeQuery = true)
    List<Map<String,Object>> getStrategyByDbnameAndTabname(String sourceid, String dbname, String tabname);

    @Query(value = "select a.algenglishname,f.param,f.secretkey from sdd_mask_strategy_field f JOIN sdd_algorithm a ON f.algorithmid = a.id WHERE f.fieldename =?1 AND f.stategytableid = ?2",nativeQuery = true)
    Map<String,Object> getMaskAlgoInfoByAlgoCname(String algocname,Integer strategyid);

    @Query(value = "select COALESCE(MAX(id),0) from sdd_mask_hivetaskresult",nativeQuery = true)
    Integer getNewestHiveJobId();
}
