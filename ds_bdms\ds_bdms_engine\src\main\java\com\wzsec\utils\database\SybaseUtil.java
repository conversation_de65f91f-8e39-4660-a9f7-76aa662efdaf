package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName SybaseUtil
 * @Description sybase操作类
 * <AUTHOR>
 * @Date 2024/11/14 16:52
 */
@Slf4j
public class SybaseUtil {

    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 16:58
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        String driverProgram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");

        // 如果密码经过加密，则进行解密
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败");
                return msgMap;
            }
        }

        String dbname = map.get("dbname");
        String srcport = map.get("srcport");
        String srcip = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 加载Sybase JDBC驱动
            Class.forName(driverProgram);
            String jdbcUrl = "jdbc:sybase:Tds:" + srcip + ":" + srcport + "/" + dbname;
            // 建立连接
            connection = DriverManager.getConnection(jdbcUrl, username, password);
            // 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            String checkDatabaseSQL = "SELECT * FROM master.sysdatabases WHERE name = '" + dbname + "'";
            statement = connection.createStatement();
            resultSet = statement.executeQuery(checkDatabaseSQL);

            boolean databaseExists = resultSet.next();
            // 如果数据库不存在，则创建数据库
            if (!databaseExists) {
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "驱动类未找到：" + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 关闭资源
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return msgMap;
    }
}
