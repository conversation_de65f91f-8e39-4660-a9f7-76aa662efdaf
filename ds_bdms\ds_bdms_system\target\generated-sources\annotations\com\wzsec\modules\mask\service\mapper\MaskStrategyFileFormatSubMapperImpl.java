package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyFileFormatSub;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileFormatSubDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskStrategyFileFormatSubMapperImpl implements MaskStrategyFileFormatSubMapper {

    @Override
    public MaskStrategyFileFormatSubDto toDto(MaskStrategyFileFormatSub entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFileFormatSubDto maskStrategyFileFormatSubDto = new MaskStrategyFileFormatSubDto();

        maskStrategyFileFormatSubDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyFileFormatSubDto.setColumndesc( entity.getColumndesc() );
        maskStrategyFileFormatSubDto.setColumnnum( entity.getColumnnum() );
        maskStrategyFileFormatSubDto.setId( entity.getId() );
        maskStrategyFileFormatSubDto.setParam( entity.getParam() );
        maskStrategyFileFormatSubDto.setRuleid( entity.getRuleid() );
        maskStrategyFileFormatSubDto.setSecretkey( entity.getSecretkey() );
        maskStrategyFileFormatSubDto.setSenLevel( entity.getSenLevel() );
        maskStrategyFileFormatSubDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFileFormatSubDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFileFormatSubDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFileFormatSubDto.setSparefield4( entity.getSparefield4() );
        maskStrategyFileFormatSubDto.setStrategyid( entity.getStrategyid() );

        return maskStrategyFileFormatSubDto;
    }

    @Override
    public List<MaskStrategyFileFormatSubDto> toDto(List<MaskStrategyFileFormatSub> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFileFormatSubDto> list = new ArrayList<MaskStrategyFileFormatSubDto>( entityList.size() );
        for ( MaskStrategyFileFormatSub maskStrategyFileFormatSub : entityList ) {
            list.add( toDto( maskStrategyFileFormatSub ) );
        }

        return list;
    }

    @Override
    public MaskStrategyFileFormatSub toEntity(MaskStrategyFileFormatSubDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyFileFormatSub maskStrategyFileFormatSub = new MaskStrategyFileFormatSub();

        maskStrategyFileFormatSub.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyFileFormatSub.setColumndesc( dto.getColumndesc() );
        maskStrategyFileFormatSub.setColumnnum( dto.getColumnnum() );
        maskStrategyFileFormatSub.setId( dto.getId() );
        maskStrategyFileFormatSub.setParam( dto.getParam() );
        maskStrategyFileFormatSub.setRuleid( dto.getRuleid() );
        maskStrategyFileFormatSub.setSecretkey( dto.getSecretkey() );
        maskStrategyFileFormatSub.setSenLevel( dto.getSenLevel() );
        maskStrategyFileFormatSub.setSparefield1( dto.getSparefield1() );
        maskStrategyFileFormatSub.setSparefield2( dto.getSparefield2() );
        maskStrategyFileFormatSub.setSparefield3( dto.getSparefield3() );
        maskStrategyFileFormatSub.setSparefield4( dto.getSparefield4() );
        maskStrategyFileFormatSub.setStrategyid( dto.getStrategyid() );

        return maskStrategyFileFormatSub;
    }

    @Override
    public List<MaskStrategyFileFormatSub> toEntity(List<MaskStrategyFileFormatSubDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyFileFormatSub> list = new ArrayList<MaskStrategyFileFormatSub>( dtoList.size() );
        for ( MaskStrategyFileFormatSubDto maskStrategyFileFormatSubDto : dtoList ) {
            list.add( toEntity( maskStrategyFileFormatSubDto ) );
        }

        return list;
    }
}
