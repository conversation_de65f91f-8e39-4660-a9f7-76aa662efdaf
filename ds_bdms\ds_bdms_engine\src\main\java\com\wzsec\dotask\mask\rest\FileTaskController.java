package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoFileTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020-11-13
 */
// @Api(tags = "格式化文件脱敏任务管理")
@RestController
@RequestMapping("/engine/file/task")
public class FileTaskController {

    private final DoFileTaskService doFileTaskService;

    private final UserService userService;

    public FileTaskController(DoFileTaskService doFileTaskService, UserService userService) {
        this.doFileTaskService = doFileTaskService;
        this.userService = userService;
    }

    @Log("执行文件脱敏任务")
    // @ApiOperation("执行文件脱敏任务")
    @PostMapping(value = "/exec/{id}")
    public ResponseEntity<Object> execution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doFileTaskService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("定时执行弱密码发现任务")
    // @ApiOperation("定时执行弱密码发现任务")
    @PostMapping(value = "/timingexec/{id}")
    //@PreAuthorize("@el.check('wptask:edit')")
    public ResponseEntity<Object> timingexecution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        doFileTaskService.execution(id, Const.TASK_SUBMITTYPE_AUTOSUBMIT);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("脱敏结果预览")
    @GetMapping(value = "/maskresultrreview/{id}")
    public ResponseEntity<Object> getMaskResultPreview(@PathVariable Integer id) {
        Object data = doFileTaskService.getMaskResultPreview(id);
        return new ResponseEntity<>(data,HttpStatus.OK);
    }
}
