package com.wzsec.dotask.sdd.service.excute.common;

import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.ClassUtil;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 敏感数据发现规则管理类
 *
 * <AUTHOR>
 * @date 2020-4-26
 */
@Slf4j
public class RuleManager {

    /**
     * 敏感数据发现检测(用于文件和数据库通用)
     *
     * <AUTHOR>
     * @date 2020-4-26
     */
    public static boolean checkDataByRuleDto(String data, RuleDto ruleDto, Map<String, Object> fieldMap) throws Exception {
        boolean checkSensResult = false;
        if (ruleDto.getRuletype().equals(Const.RULE_RULETYPE_REGEXPS)) {//1正则表达式
            // checkSensResult = data.matches(ruleDto.getRegexps());
            Pattern pattern = Pattern.compile(ruleDto.getRegexps());
            Matcher matcher = pattern.matcher(data);
            checkSensResult = matcher.find();
        } else if (ruleDto.getRuletype().equals(Const.RULE_RULETYPE_ALGORITHM_PROGRAM)) {//2程序算法
            String classpath = ruleDto.getClasspath();  //类路径
            String methodname = ruleDto.getMethodname();  //方法名
            // 指定类要求：给一个要检测的文本数据内容，返回的是敏感（true），不敏感（false），true进行加密
            try {
                checkSensResult = ClassUtil.getClassReturnBoolean(classpath, methodname, data);   //Rule4IdentifierUtil
            } catch (Exception e) {
                log.error("加载类:" + classpath + "方法:" + methodname + "失败");
                throw e;//出现异常
            }
        } else if (ruleDto.getRuletype().equals(Const.RULE_RULETYPE_ALGORITHM_CUSTOM)) {//3自定义算法
            String classpath = ruleDto.getClasspath();
            String methodname = ruleDto.getMethodname();
            log.info("暂不支持自定义算法");
        } else if (ruleDto.getRuletype().equals(Const.RULE_RULETYPE_ALGORITHM_PARTICIPLE)) {//4分词（姓名、地址、地名）
            //按常用分隔符 ,;|分词查询 姓名、地址、地名
            if (StringUtils.isNotEmpty(data)){
                checkSensResult = identifyData(data.split(","),(Map<String, String>) fieldMap.get("participleMap"));
                if (!checkSensResult){
                    checkSensResult = identifyData(data.split(";"),(Map<String, String>) fieldMap.get("participleMap"));
                    if (!checkSensResult){
                        checkSensResult = identifyData(data.split("|"),(Map<String, String>) fieldMap.get("participleMap"));
                    }
                }
            }
        } else if (ruleDto.getRuletype().equals(Const.RULE_RULETYPE_ALGORITHM_DATA_DICTIONARY_FIELD_CHINESE_NAME)) {//5数据字典-字段中文名
            String fieldCName = fieldMap.get("fieldCName").toString();
            if (StringUtils.isNotEmpty(fieldCName)){
                Map<String, String> dataDictionaryFieldChineseNameMap = (Map<String, String>) fieldMap.get("dataDictionaryFieldChineseNameMap");
                if (ObjectUtils.isNotEmpty(dataDictionaryFieldChineseNameMap)){
                    Set<String> keySet = dataDictionaryFieldChineseNameMap.keySet();
                    for (String key : keySet) {
                        String value = dataDictionaryFieldChineseNameMap.get(key);
                        if (value.equals(fieldCName)){
                            checkSensResult = true;
                            break;
                        }
                    }
                }
            }
        } else if (ruleDto.getRuletype().equals(Const.RULE_RULETYPE_ALGORITHM_DATA_DICTIONARY_FIELD_ENGLISH_NAME)) {//6数据字典-字段英文名
            String fieldEName = fieldMap.get("fieldEName").toString();
            if (StringUtils.isNotEmpty(fieldEName)){
                Map<String, String> dataDictionaryFieldEnglishNameMap = (Map<String, String>) fieldMap.get("dataDictionaryFieldEnglishNameMap");
                if (ObjectUtils.isNotEmpty(dataDictionaryFieldEnglishNameMap)){
                    Set<String> keySet = dataDictionaryFieldEnglishNameMap.keySet();
                    for (String key : keySet) {
                        String value = dataDictionaryFieldEnglishNameMap.get(key);
                        if (value.equals(fieldEName)){
                            checkSensResult = true;
                            break;
                        }
                    }
                }
            }
        } else if (ruleDto.getRuletype().equals(Const.RULE_RULETYPE_ALGORITHM_DATA_DICTIONARY_FIELD_CONTENT)) {//7数据字典-字段内容
            if (StringUtils.isNotEmpty(data)){
                Map<String, String> dataDictionaryFieldContentMap = (Map<String, String>) fieldMap.get("dataDictionaryFieldContentMap");
                if (ObjectUtils.isNotEmpty(dataDictionaryFieldContentMap)){
                    Set<String> keySet = dataDictionaryFieldContentMap.keySet();
                    for (String key : keySet) {
                        String value = dataDictionaryFieldContentMap.get(key);
                        if (value.equals(data)){
                            checkSensResult = true;
                            break;
                        }
                    }
                }
            }
        }
        return checkSensResult;
    }

    /**
     * 分词 姓名、地址、地名 识别敏感数据
     * @param split
     * <AUTHOR>
     * @date 2025-03-17
     * @return
     */
    private static boolean identifyData(String[] split,Map<String, String> participleMap) {
        boolean checkSensResult = false;
        for (String str : split) {
            //姓名识别
            String fullNameClasspath = participleMap.getOrDefault("fullNameClasspath", "");
            String fullNameMethodname = participleMap.getOrDefault("fullNameMethodname", "");
            try {
                checkSensResult = ClassUtil.getClassReturnBoolean(fullNameClasspath, fullNameMethodname, str);   //Rule4IdentifierUtil
            } catch (Exception e) {
                log.error("加载类:" + fullNameClasspath + "方法:" + fullNameMethodname + "失败");
                throw new RuntimeException(e);
            }
            // 如果姓名识别为敏感数据，结束识别
            if (checkSensResult){
                return checkSensResult;
            }

            //地址识别
            String addressClasspath = participleMap.getOrDefault("addressClasspath", "");
            String addressMethodname = participleMap.getOrDefault("addressMethodname", "");
            try {
                checkSensResult = ClassUtil.getClassReturnBoolean(addressClasspath, addressMethodname, str);   //Rule4IdentifierUtil
            } catch (Exception e) {
                log.error("加载类:" + addressClasspath + "方法:" + addressMethodname + "失败");
                throw new RuntimeException(e);
            }
            // 如果地址识别为敏感数据，结束识别
            if (checkSensResult){
                return checkSensResult;
            }

            //地名识别
            String placeNameClasspath = participleMap.getOrDefault("placeNameClasspath", "");
            String placeNameMethodname = participleMap.getOrDefault("placeNameMethodname", "");
            try {
                checkSensResult = ClassUtil.getClassReturnBoolean(placeNameClasspath, placeNameMethodname, str);   //Rule4IdentifierUtil
            } catch (Exception e) {
                log.error("加载类:" + placeNameClasspath + "方法:" + placeNameMethodname + "失败");
                throw new RuntimeException(e);
            }
        }
        return checkSensResult;
    }

    public static void main(String[] args) throws Exception {
        //p_checkNoSpecificAddress
        String data = "13401784582";
        boolean checkSensResult = ClassUtil.getClassReturnBoolean("com.wzsec.detectrule.rule.Rule4IdentifierUtil",
                "p_checkQQ", data);
        boolean checkSensResult2 = ClassUtil.getClassReturnBoolean("com.wzsec.detectrule.rule.Rule4IdentifierUtil",
                "p_checkMobilePhone", data);
        boolean checkSensResult3 = ClassUtil.getClassReturnBoolean("com.wzsec.detectrule.rule.Rule4IdentifierUtil",
                "p_checkLongitude", data);
        System.out.println(checkSensResult);
        System.out.println(checkSensResult2);
        System.out.println(checkSensResult3);
    }
}
