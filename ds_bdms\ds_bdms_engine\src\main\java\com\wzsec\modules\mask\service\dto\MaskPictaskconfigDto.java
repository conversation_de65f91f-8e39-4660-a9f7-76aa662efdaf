package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2022-04-18
*/
@Data
public class MaskPictaskconfigDto implements Serializable {

    /** ID */
    private Integer id;

    /** 任务名 */
    private String taskname;

    /** 原始图片目录 */
    private String inputdirectory;

    /** 输入文件格式 */
    private String inputfileformat;

    /** 计算资源 */
    private String computeresources;

    /** 脱敏对象 */
    private String maskobject;

    /** 任务状态(0禁用,1启用) */
    private String state;

    /** 输出文件格式 */
    private String outputfileformat;

    /** 脱敏后图片目录 */
    private String outputdirectory;

    /** 执行状态：0 :未提交1:执行中2:执行成功3:执行失败4:提交失败 */
    private String executionstate;

    /** 执行方式（1手动提交，2定时执行） */
    private String submitmethod;

    /** 执行时间cron表达式 */
    private String cron;

    /** 创建用户 */
    private String createuser;

    /** 创建时间 */
    private Timestamp createtime;

    /** 更新用户 */
    private String updateuser;

    /** 更新时间 */
    private Timestamp updatetime;

    /** 备注 */
    private String remark;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;
}