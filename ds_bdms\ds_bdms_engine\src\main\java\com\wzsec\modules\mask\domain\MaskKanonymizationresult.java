package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

//import javax.validation.constraints.*;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Entity
@Data
@Table(name="sdd_mask_kanonymizationresult")
public class MaskKanonymizationresult implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 表名 */
    @Column(name = "tabname")
    private String tabname;

    /** 脱敏行数 */
    @Column(name = "maskline")
    private String maskline;

    /** 数据总行数 */
    @Column(name = "totalline")
    private String totalline;

    /** 任务状态 */
    @Column(name = "taskstatus")
    private String taskstatus;

    /** 任务结果评估(良好,一般,差) */
    @Column(name = "taskresultestimate")
    private String taskresultestimate;

    /** 输出目录(1到库，0到文件) */
    @Column(name = "outputdirectory")
    private String outputdirectory;

    /** 脱敏前数据 */
    @Column(name = "beforemaskdata")
    private String beforemaskdata;

    /** 脱敏后数据 */
    @Column(name = "aftermaskdata")
    private String aftermaskdata;

    /** 开始时间 */
    @Column(name = "starttime")
    private String starttime;

    /** 结束时间 */
    @Column(name = "endtime")
    private String endtime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    /** 备用字段5 */
    @Column(name = "sparefield6")
    private String sparefield6;

    public void copy(MaskKanonymizationresult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
