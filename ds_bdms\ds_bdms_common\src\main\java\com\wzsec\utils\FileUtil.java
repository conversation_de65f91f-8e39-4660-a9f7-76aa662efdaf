package com.wzsec.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.wzsec.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.MimetypesFileTypeMap;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.*;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * File工具类，扩展 hutool 工具包
 *
 * <AUTHOR> Jie
 * @date 2018-12-27
 */
@Slf4j
public class FileUtil extends cn.hutool.core.io.FileUtil {

    /**
     * 定义GB的计算常量
     */
    private static final int GB = 1024 * 1024 * 1024;
    /**
     * 定义MB的计算常量
     */
    private static final int MB = 1024 * 1024;
    /**
     * 定义KB的计算常量
     */
    private static final int KB = 1024;

    /**
     * 格式化小数
     */
    private static final DecimalFormat DF = new DecimalFormat("0.00");

    /**
     * MultipartFile转File
     */
    public static File toFile(MultipartFile multipartFile) {
        // 获取文件名
        String fileName = multipartFile.getOriginalFilename();
        // 获取文件后缀
        String prefix = "." + getExtensionName(fileName);
        File file = null;
        try {
            // 用uuid作为文件名，防止生成的临时文件重复
            file = File.createTempFile(IdUtil.simpleUUID(), prefix);
            // MultipartFile to File
            multipartFile.transferTo(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    /**
     * 获取文件扩展名，不带 .
     */
    public static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }

    /**
     * Java文件操作 获取不带扩展名的文件名
     */
    public static String getFileNameNoEx(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length()))) {
                return filename.substring(0, dot);
            }
        }
        return filename;
    }

    /**
     * 文件大小转换
     */
    public static String getSize(long size) {
        String resultSize;
        if (size / GB >= 1) {
            //如果当前Byte的值大于等于1GB
            resultSize = DF.format(size / (float) GB) + "GB   ";
        } else if (size / MB >= 1) {
            //如果当前Byte的值大于等于1MB
            resultSize = DF.format(size / (float) MB) + "MB   ";
        } else if (size / KB >= 1) {
            //如果当前Byte的值大于等于1KB
            resultSize = DF.format(size / (float) KB) + "KB   ";
        } else {
            resultSize = size + "B   ";
        }
        return resultSize;
    }

    /**
     * inputStream 转 File
     */
    static File inputStreamToFile(InputStream ins, String name) throws Exception {
        File file = new File(System.getProperty("java.io.tmpdir") + File.separator + name);
        if (file.exists()) {
            return file;
        }
        OutputStream os = new FileOutputStream(file);
        int bytesRead;
        int len = 8192;
        byte[] buffer = new byte[len];
        while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
            os.write(buffer, 0, bytesRead);
        }
        os.close();
        ins.close();
        return file;
    }

    /**
     * 将文件名解析成文件的上传路径
     */
    public static File upload(MultipartFile file, String filePath) {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
        String name = getFileNameNoEx(file.getOriginalFilename());
        String suffix = getExtensionName(file.getOriginalFilename());
        String nowStr = "-" + format.format(date);
        try {
            String fileName = name + nowStr + "." + suffix;
            String path = filePath + fileName;
            // getCanonicalFile 可解析正确各种路径
            File dest = new File(path).getCanonicalFile();
            // 检测是否存在目录
            if (!dest.getParentFile().exists()) {
                dest.getParentFile().mkdirs();
            }
            // 文件写入
            file.transferTo(dest);
            return dest;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String fileToBase64(File file) throws Exception {
        FileInputStream inputFile = new FileInputStream(file);
        String base64;
        byte[] buffer = new byte[(int) file.length()];
        inputFile.read(buffer);
        inputFile.close();
        base64 = Base64.encode(buffer);
        return base64.replaceAll("[\\s*\t\n\r]", "");
    }

    /*    *//**
     * 导出excel
     *//*
    public static void downloadExcel(List<Map<String, Object>> list, HttpServletResponse response) throws IOException {
        String tempPath = System.getProperty("java.io.tmpdir") + IdUtil.fastSimpleUUID() + ".xlsx";
        File file = new File(tempPath);
        BigExcelWriter writer = ExcelUtil.getBigWriter(file);

        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(list, true);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=file.xlsx");
        ServletOutputStream out = response.getOutputStream();
        // 终止后删除临时文件
        file.deleteOnExit();
        writer.flush(out, true);
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }*/


    /**
     * 导出excel
     */
    public static void downloadExcel(List<Map<String, Object>> list, HttpServletResponse response) throws IOException {

        String tempPath = System.getProperty("java.io.tmpdir") + IdUtil.fastSimpleUUID() + ".xlsx";
        File file = new File(tempPath);

        BigExcelWriter writer = ExcelUtil.getBigWriter(file);

        //跳过当前行，既第一行，非必须，在此演示用
        //writer.passCurrentRow();

        // 合并单元格后的标题行，使用默认标题样式
        //writer.merge(list.size() - 1, "titleName");

        // list.remove(list.size());

        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(list, true);

        //response为HttpServletResponse对象
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=file.xlsx");
        ServletOutputStream out = response.getOutputStream();
        // 终止后删除临时文件
        file.deleteOnExit();
        writer.flush(out, true);
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    /**
     * 导出excel
     */
    public static void downloadExcel(List<Map<String, Object>> list, HttpServletResponse response, String titleName, Integer size) throws IOException {

        String tempPath = System.getProperty("java.io.tmpdir") + IdUtil.fastSimpleUUID() + ".xlsx";
        File file = new File(tempPath);

        BigExcelWriter writer = ExcelUtil.getBigWriter(file);

        //跳过当前行，既第一行，非必须，在此演示用
        //writer.passCurrentRow();

        // 合并单元格后的标题行，使用默认标题样式
        writer.merge(size - 1, titleName);

        // list.remove(list.size());

        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(list, true);

        //response为HttpServletResponse对象
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=file.xlsx");
        ServletOutputStream out = response.getOutputStream();
        // 终止后删除临时文件
        file.deleteOnExit();
        writer.flush(out, true);
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }


    public static String getFileType(String type) {
        String documents = "txt doc pdf ppt pps xlsx xls docx";
        String music = "mp3 wav wma mpa ram ra aac aif m4a";
        String video = "avi mpg mpe mpeg asf wmv mov qt rm mp4 flv m4v webm ogv ogg";
        String image = "bmp dib pcp dif wmf gif jpg tif eps psd cdr iff tga pcd mpt png jpeg";
        if (image.contains(type)) {
            return "图片";
        } else if (documents.contains(type)) {
            return "文档";
        } else if (music.contains(type)) {
            return "音乐";
        } else if (video.contains(type)) {
            return "视频";
        } else {
            return "其他";
        }
    }

    public static String getFileTypeByMimeType(String type) {
        String mimeType = new MimetypesFileTypeMap().getContentType("." + type);
        return mimeType.split("/")[0];
    }

    public static void checkSize(long maxSize, long size) {
        // 1M
        int len = 1024 * 1024;
        if (size > (maxSize * len)) {
            throw new BadRequestException("文件超出规定大小");
        }
    }

    /**
     * 判断两个文件是否相同
     */
    public static boolean check(File file1, File file2) {
        String img1Md5 = getMd5(file1);
        String img2Md5 = getMd5(file2);
        return img1Md5.equals(img2Md5);
    }

    /**
     * 判断两个文件是否相同
     */
    public static boolean check(String file1Md5, String file2Md5) {
        return file1Md5.equals(file2Md5);
    }

    private static byte[] getByte(File file) {
        // 得到文件长度
        byte[] b = new byte[(int) file.length()];
        try {
            InputStream in = new FileInputStream(file);
            try {
                in.read(b);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        }
        return b;
    }

    private static String getMd5(byte[] bytes) {
        // 16进制字符
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(bytes);
            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            // 移位 输出字符串
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 下载文件
     *
     * @param request  /
     * @param response /
     * @param file     /
     */
    public static void downloadFile(HttpServletRequest request, HttpServletResponse response, File file, boolean deleteOnExit) {
        response.setCharacterEncoding(request.getCharacterEncoding());
        response.setContentType("application/octet-stream");
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
            IOUtils.copy(fis, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                    if (deleteOnExit) {
                        file.deleteOnExit();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static String getMd5(File file) {
        return getMd5(getByte(file));
    }

    /**
     * 下载Jar类型文件
     * @param filePath
     * @param response
     * @throws IOException
     * <AUTHOR>
     * @date 2025-2-25
     */
    public static void downloadJarFile(String filePath, HttpServletResponse response) throws IOException {
        String fileName = Const.SDK_FILE_NAME;
        File directory = new File(filePath);

        // 确保是有效的目录
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                // 遍历目录中的文件，找到匹配的 jar 文件
                for (File file : files) {
                    if (file.isFile() && file.getName().startsWith(fileName) && file.getName().endsWith(".jar")) {
                        fileName = file.getName();
                        break;
                    }
                }
            } else {
                throw new BadRequestException("指定路径不是一个有效的目录，或目录为空。");
            }
        } else {
            throw new BadRequestException("指定路径不是一个有效的目录。");
        }
        System.out.println("Downloading file: " + fileName);
        // 设置响应内容类型为二进制流，或者使用 application/java-archive 也可
        response.setContentType("application/java-archive"); // MIME类型
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        // 获取输出流
        ServletOutputStream out = response.getOutputStream();
        File file = new File(filePath,fileName);
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                out.write(buffer, 0, length);
            }
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        } finally {
            // 确保关闭输出流
            IoUtil.close(out);
        }
    }

    /**
     * 下载zip类型文件
     * @param filepath1
     * @param filepath2
     * @param response
     * @throws IOException
     * <AUTHOR>
     * @date 2025-2-25
     */
    public static void downloadZipFile(String filepath1, String filepath2, HttpServletResponse response) throws IOException {
        // 设置下载文件的名字和文件类型
        String zipFileName = "ds_bdms-sdk-documents.zip";
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);

        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            addFileToZip(filepath1, zos, new File(filepath1).getName());
            addFileToZip(filepath2, zos, new File(filepath2).getName());
        } catch (IOException e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error while generating ZIP file");
        }
    }

    /**
     * 下载zip类型文件
     * @param filepath1
     * @param filepath2
     * @param response
     * @throws IOException
     * <AUTHOR>
     * @date 2025-2-27
     */
    public static void downloadSDKZipFile(String filepath1, String filepath2,String filepath3,String filepath4,String filepath5, HttpServletResponse response,String sdkname) throws IOException {
        // 设置下载文件的名字和文件类型
        String zipFileName = "ds_bdms-sdk-package.zip";
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);

        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            addFileToZip(filepath1, zos, new File(filepath1).getName());
            addFileToZip(filepath2, zos, new File(filepath2).getName());
            addFileToZip(filepath3, zos, new File(filepath3).getName());
            addFileToZip(filepath4, zos, new File(filepath4).getName());
            addFileToZip(filepath5, zos, sdkname);
        } catch (IOException e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error while generating ZIP file");
        }
    }

    /**
     * 将文件压缩到zip文件中
     * @param filepath
     * @param zos
     * @throws IOException
     * <AUTHOR>
     * @date 2025-2-25
     */
    private static void addFileToZip(String filepath, ZipOutputStream zos, String zipFileName) throws IOException {
        File file = new File(filepath);
        try (FileInputStream fis = new FileInputStream(file)) {
            ZipEntry zipEntry = new ZipEntry(zipFileName);
            zos.putNextEntry(zipEntry);
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) >= 0) {
                zos.write(buffer, 0, length);
            }
            zos.closeEntry();
        }
    }


    /**
     * 获取文件夹下所有文件对象（包含子目录）
     *
     * @param path
     * <AUTHOR>
     * @date 2020年4月26日
     */
    public static void getFileList(String path, List<File> fileList) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        } else {
            fileList.add(file);
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    fileList.add(f);
                } else {
                    getFileList(f.getAbsolutePath(), fileList);
                }
            }
        }
    }

    /**
     * 获取文件夹下所有文件完整路径（包含子目录）
     *
     * @param path
     * <AUTHOR>
     * @date 2020年4月27日
     */
    public static void getFilePathList(String path, List<String> filePathList) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    filePathList.add(f.getAbsolutePath());
                } else {
                    getFilePathList(f.getAbsolutePath(), filePathList);
                }
            }
        }
    }

    /**
     * 写出文件到本地目录,目录不存在创建目录
     *
     * @param path
     * <AUTHOR>
     * @date 2020年5月21日
     */
    public static void outputFile(String path, String content, Boolean isAppend) {
        try {
            File file = new File(path);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fileWriter = new FileWriter(file.getAbsoluteFile(), isAppend);
            BufferedWriter bw = new BufferedWriter(fileWriter);
            bw.write(content);
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 读取资源路径下文件，优先读取外部文件
     *
     * @param filePath  文件路径
     * @param sparePath 备用路径
     * @throws IOException
     */
    public static InputStream getResourceAsStream(String filePath, String sparePath) throws IOException {
        // 将配置文件放在jar包外部，优先使用外部配置文件
        File object = new File(filePath);
        InputStream inputStream = null;
        String status = "外部";
        if (!object.exists()) {
            status = "内部";
            if (sparePath != null && !"".equals(sparePath)) {
                filePath = sparePath;
            }
            inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath);
        } else {
            inputStream = new FileInputStream(object);
        }
        log.info("读取资源文件路径为：" + status + " 的 " + filePath);
        return inputStream;
    }

    /**
     * 获取清洗后所有文件和文件名
     *
     * @param path
     * @param fileNameMap
     * <AUTHOR>
     * @date 2021年7月6日10:21:26
     */
    public static void getAllFileName(String path, Map<String, String> fileNameMap) {
        File file = new File(path);
        File[] files = file.listFiles();

        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    fileNameMap.put(f.getAbsolutePath(), f.getName());
                } else if (f.isDirectory()) {
                    getAllFileName(f.getAbsolutePath(), fileNameMap);
                }
            }
        }
    }

    /**
     * 删除文件(递归删除目录下所有文件与文件夹，包括目录本身)
     * @param directory
     */
    public static void deleteFiles(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteFiles(file);
                }
            }
        }
        directory.delete();
    }

    /**
     * 获取文件路径集合，key为文件绝对路径，value为文件名
     * @param path
     * @return
     */
    public static Map<String,String> getFilePathList(String path){
        Map<String,String> filePath = new LinkedHashMap<>();
        File file = new File(path);
        if (file.exists()) {
            if (file.isDirectory()) {
                File[] files = file.listFiles(); // 获取目录下所有文件
                if (files != null) {
                    for (File f : files) {
                        if (f.isFile()){
                            filePath.put(f.getAbsolutePath(),f.getName());
                        }
                    }
                }
            } else if (file.isFile()) {
                filePath.put(file.getAbsolutePath(),file.getName());
            }
        } else {
            log.info("路径不存在,path:"+path);
        }
        return filePath;
    }

    /**
     * 通过读取文件并获取其width及height的方式，来判断判断当前文件是否图片
     * @param imageFile
     * @return
     */
    public static boolean isImage(File imageFile) {
        if (!imageFile.exists()) {
            return false;
        }
        Image img = null;
        try {
            img = ImageIO.read(imageFile);
            if (img == null || img.getWidth(null) <= 0 || img.getHeight(null) <= 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        } finally {
            img = null;
        }
    }
}
