package com.wzsec.modules.alarm.service;

import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.dto.DmAlarmdisposalDto;
import com.wzsec.modules.alarm.service.dto.DmAlarmdisposalQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface DmAlarmdisposalService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(DmAlarmdisposalQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<dmAlarmdisposalDto>
     */
    List<DmAlarmdisposalDto> queryAll(DmAlarmdisposalQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return dmAlarmdisposalDto
     */
    DmAlarmdisposalDto findById(Integer id);

    /**
     * 创建
     *
     * @param resources /
     * @return dmAlarmdisposalDto
     */
    DmAlarmdisposalDto create(DmAlarmdisposal resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(DmAlarmdisposal resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);


}