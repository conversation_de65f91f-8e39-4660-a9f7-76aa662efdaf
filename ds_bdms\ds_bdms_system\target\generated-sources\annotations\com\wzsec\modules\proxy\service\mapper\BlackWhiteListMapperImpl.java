package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.BlackWhiteList;
import com.wzsec.modules.proxy.service.dto.BlackWhiteListDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:04+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class BlackWhiteListMapperImpl implements BlackWhiteListMapper {

    @Override
    public BlackWhiteListDto toDto(BlackWhiteList entity) {
        if ( entity == null ) {
            return null;
        }

        BlackWhiteListDto blackWhiteListDto = new BlackWhiteListDto();

        blackWhiteListDto.setContent( entity.getContent() );
        blackWhiteListDto.setCreatetime( entity.getCreatetime() );
        blackWhiteListDto.setCreateuser( entity.getCreateuser() );
        blackWhiteListDto.setId( entity.getId() );
        blackWhiteListDto.setMemo( entity.getMemo() );
        blackWhiteListDto.setPurpose( entity.getPurpose() );
        blackWhiteListDto.setSparefield1( entity.getSparefield1() );
        blackWhiteListDto.setSparefield2( entity.getSparefield2() );
        blackWhiteListDto.setSparefield3( entity.getSparefield3() );
        blackWhiteListDto.setSparefield4( entity.getSparefield4() );
        blackWhiteListDto.setSparefield5( entity.getSparefield5() );
        blackWhiteListDto.setSrcid( entity.getSrcid() );
        blackWhiteListDto.setState( entity.getState() );
        blackWhiteListDto.setType( entity.getType() );
        blackWhiteListDto.setUpdatetime( entity.getUpdatetime() );
        blackWhiteListDto.setUpdateuser( entity.getUpdateuser() );

        return blackWhiteListDto;
    }

    @Override
    public List<BlackWhiteListDto> toDto(List<BlackWhiteList> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BlackWhiteListDto> list = new ArrayList<BlackWhiteListDto>( entityList.size() );
        for ( BlackWhiteList blackWhiteList : entityList ) {
            list.add( toDto( blackWhiteList ) );
        }

        return list;
    }

    @Override
    public BlackWhiteList toEntity(BlackWhiteListDto dto) {
        if ( dto == null ) {
            return null;
        }

        BlackWhiteList blackWhiteList = new BlackWhiteList();

        blackWhiteList.setContent( dto.getContent() );
        blackWhiteList.setCreatetime( dto.getCreatetime() );
        blackWhiteList.setCreateuser( dto.getCreateuser() );
        blackWhiteList.setId( dto.getId() );
        blackWhiteList.setMemo( dto.getMemo() );
        blackWhiteList.setPurpose( dto.getPurpose() );
        blackWhiteList.setSparefield1( dto.getSparefield1() );
        blackWhiteList.setSparefield2( dto.getSparefield2() );
        blackWhiteList.setSparefield3( dto.getSparefield3() );
        blackWhiteList.setSparefield4( dto.getSparefield4() );
        blackWhiteList.setSparefield5( dto.getSparefield5() );
        blackWhiteList.setSrcid( dto.getSrcid() );
        blackWhiteList.setState( dto.getState() );
        blackWhiteList.setType( dto.getType() );
        blackWhiteList.setUpdatetime( dto.getUpdatetime() );
        blackWhiteList.setUpdateuser( dto.getUpdateuser() );

        return blackWhiteList;
    }

    @Override
    public List<BlackWhiteList> toEntity(List<BlackWhiteListDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<BlackWhiteList> list = new ArrayList<BlackWhiteList>( dtoList.size() );
        for ( BlackWhiteListDto blackWhiteListDto : dtoList ) {
            list.add( toEntity( blackWhiteListDto ) );
        }

        return list;
    }
}
