package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2021-01-14
*/
@Entity
@Data
@Table(name="sdd_maskaudit_task")
public class MaskAuditTaskV1 implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名称 */
    @Column(name = "taskname")
    private String taskname;

    /** 系统标识 */
    @Column(name = "systemid")
    private String systemid;

    /** 执行方式（1手动提交，2定时执行） */
    @Column(name = "submitmethod")
    private String submitmethod;

    /** 定时时间 */
    @Column(name = "cron")
    private String cron;

    /** 检测类型(1.库表，2.文件，3.日志) */
    @Column(name = "checktype")
    private String checktype;

    /** 识别策略 */
    @Column(name = "disstrategy")
    private String disstrategy;

    /** 脱敏策略 */
    @Column(name = "maskstrategy")
    private String maskstrategy;

    /** 输出数据源id */
    @Column(name = "outsourceid")
    private Integer outsourceid;

    /** 表名 */
    @Column(name = "tablename")
    private String tablename;

    /** 输出文件路径 */
    @Column(name = "outfilepath")
    private String outfilepath;

    /** 文件类型（1.txt、2.excel、3.csv） */
    @Column(name = "filetype")
    private String filetype;

    /** 文件切分符 */
    @Column(name = "filespilt")
    private String filespilt;

    /** 任务状态：0：启用，1：禁用 */
    @Column(name = "status")
    private String status;

    /** 执行状态：0 :未提交1:提交失败2:执行中3:执行成功败4:执行交失败 */
    @Column(name = "executionstate")
    private String executionstate;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 对应脱敏任务号 */
    @Column(name = "masktask")
    private String masktask;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    public void copy(MaskAuditTaskV1 source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
