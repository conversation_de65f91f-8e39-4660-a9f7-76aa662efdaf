package com.wzsec.modules.sdk.service.mapper;

import com.wzsec.modules.sdk.domain.SdkDownloadrecord;
import com.wzsec.modules.sdk.service.dto.SdkDownloadrecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:06+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SdkDownloadrecordMapperImpl implements SdkDownloadrecordMapper {

    @Override
    public SdkDownloadrecordDto toDto(SdkDownloadrecord entity) {
        if ( entity == null ) {
            return null;
        }

        SdkDownloadrecordDto sdkDownloadrecordDto = new SdkDownloadrecordDto();

        sdkDownloadrecordDto.setApplysystemname( entity.getApplysystemname() );
        sdkDownloadrecordDto.setDownloadtime( entity.getDownloadtime() );
        sdkDownloadrecordDto.setDownloadusername( entity.getDownloadusername() );
        sdkDownloadrecordDto.setId( entity.getId() );
        sdkDownloadrecordDto.setSdkid( entity.getSdkid() );
        sdkDownloadrecordDto.setSdkname( entity.getSdkname() );
        sdkDownloadrecordDto.setSparefield1( entity.getSparefield1() );
        sdkDownloadrecordDto.setSparefield2( entity.getSparefield2() );
        sdkDownloadrecordDto.setSparefield3( entity.getSparefield3() );
        sdkDownloadrecordDto.setSparefield4( entity.getSparefield4() );
        sdkDownloadrecordDto.setVersion( entity.getVersion() );

        return sdkDownloadrecordDto;
    }

    @Override
    public List<SdkDownloadrecordDto> toDto(List<SdkDownloadrecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SdkDownloadrecordDto> list = new ArrayList<SdkDownloadrecordDto>( entityList.size() );
        for ( SdkDownloadrecord sdkDownloadrecord : entityList ) {
            list.add( toDto( sdkDownloadrecord ) );
        }

        return list;
    }

    @Override
    public SdkDownloadrecord toEntity(SdkDownloadrecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        SdkDownloadrecord sdkDownloadrecord = new SdkDownloadrecord();

        sdkDownloadrecord.setApplysystemname( dto.getApplysystemname() );
        sdkDownloadrecord.setDownloadtime( dto.getDownloadtime() );
        sdkDownloadrecord.setDownloadusername( dto.getDownloadusername() );
        sdkDownloadrecord.setId( dto.getId() );
        sdkDownloadrecord.setSdkid( dto.getSdkid() );
        sdkDownloadrecord.setSdkname( dto.getSdkname() );
        sdkDownloadrecord.setSparefield1( dto.getSparefield1() );
        sdkDownloadrecord.setSparefield2( dto.getSparefield2() );
        sdkDownloadrecord.setSparefield3( dto.getSparefield3() );
        sdkDownloadrecord.setSparefield4( dto.getSparefield4() );
        sdkDownloadrecord.setVersion( dto.getVersion() );

        return sdkDownloadrecord;
    }

    @Override
    public List<SdkDownloadrecord> toEntity(List<SdkDownloadrecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SdkDownloadrecord> list = new ArrayList<SdkDownloadrecord>( dtoList.size() );
        for ( SdkDownloadrecordDto sdkDownloadrecordDto : dtoList ) {
            list.add( toEntity( sdkDownloadrecordDto ) );
        }

        return list;
    }
}
