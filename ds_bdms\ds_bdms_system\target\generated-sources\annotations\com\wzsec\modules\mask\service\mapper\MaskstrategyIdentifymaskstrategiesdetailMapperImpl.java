package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskstrategyIdentifymaskstrategiesdetail;
import com.wzsec.modules.mask.service.dto.MaskstrategyIdentifymaskstrategiesdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:00+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskstrategyIdentifymaskstrategiesdetailMapperImpl implements MaskstrategyIdentifymaskstrategiesdetailMapper {

    @Override
    public MaskstrategyIdentifymaskstrategiesdetailDto toDto(MaskstrategyIdentifymaskstrategiesdetail entity) {
        if ( entity == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategiesdetailDto maskstrategyIdentifymaskstrategiesdetailDto = new MaskstrategyIdentifymaskstrategiesdetailDto();

        maskstrategyIdentifymaskstrategiesdetailDto.setAlgorithmid( entity.getAlgorithmid() );
        maskstrategyIdentifymaskstrategiesdetailDto.setDataname( entity.getDataname() );
        maskstrategyIdentifymaskstrategiesdetailDto.setId( entity.getId() );
        maskstrategyIdentifymaskstrategiesdetailDto.setMaskruleid( entity.getMaskruleid() );
        maskstrategyIdentifymaskstrategiesdetailDto.setParam( entity.getParam() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSecretkey( entity.getSecretkey() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSenruleid( entity.getSenruleid() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield1( entity.getSparefield1() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield2( entity.getSparefield2() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield3( entity.getSparefield3() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield4( entity.getSparefield4() );
        maskstrategyIdentifymaskstrategiesdetailDto.setSparefield5( entity.getSparefield5() );
        maskstrategyIdentifymaskstrategiesdetailDto.setStrategyid( entity.getStrategyid() );

        return maskstrategyIdentifymaskstrategiesdetailDto;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategiesdetailDto> toDto(List<MaskstrategyIdentifymaskstrategiesdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategiesdetailDto> list = new ArrayList<MaskstrategyIdentifymaskstrategiesdetailDto>( entityList.size() );
        for ( MaskstrategyIdentifymaskstrategiesdetail maskstrategyIdentifymaskstrategiesdetail : entityList ) {
            list.add( toDto( maskstrategyIdentifymaskstrategiesdetail ) );
        }

        return list;
    }

    @Override
    public MaskstrategyIdentifymaskstrategiesdetail toEntity(MaskstrategyIdentifymaskstrategiesdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategiesdetail maskstrategyIdentifymaskstrategiesdetail = new MaskstrategyIdentifymaskstrategiesdetail();

        maskstrategyIdentifymaskstrategiesdetail.setAlgorithmid( dto.getAlgorithmid() );
        maskstrategyIdentifymaskstrategiesdetail.setDataname( dto.getDataname() );
        maskstrategyIdentifymaskstrategiesdetail.setId( dto.getId() );
        maskstrategyIdentifymaskstrategiesdetail.setMaskruleid( dto.getMaskruleid() );
        maskstrategyIdentifymaskstrategiesdetail.setParam( dto.getParam() );
        maskstrategyIdentifymaskstrategiesdetail.setSecretkey( dto.getSecretkey() );
        maskstrategyIdentifymaskstrategiesdetail.setSenruleid( dto.getSenruleid() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield1( dto.getSparefield1() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield2( dto.getSparefield2() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield3( dto.getSparefield3() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield4( dto.getSparefield4() );
        maskstrategyIdentifymaskstrategiesdetail.setSparefield5( dto.getSparefield5() );
        maskstrategyIdentifymaskstrategiesdetail.setStrategyid( dto.getStrategyid() );

        return maskstrategyIdentifymaskstrategiesdetail;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategiesdetail> toEntity(List<MaskstrategyIdentifymaskstrategiesdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategiesdetail> list = new ArrayList<MaskstrategyIdentifymaskstrategiesdetail>( dtoList.size() );
        for ( MaskstrategyIdentifymaskstrategiesdetailDto maskstrategyIdentifymaskstrategiesdetailDto : dtoList ) {
            list.add( toEntity( maskstrategyIdentifymaskstrategiesdetailDto ) );
        }

        return list;
    }
}
