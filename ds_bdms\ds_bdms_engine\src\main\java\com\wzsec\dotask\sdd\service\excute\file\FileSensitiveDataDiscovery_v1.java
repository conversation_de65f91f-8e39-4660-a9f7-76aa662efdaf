package com.wzsec.dotask.sdd.service.excute.file;

import cn.hutool.core.util.ObjectUtil;
import com.wzsec.dotask.sdd.service.excute.common.CheckFileManager;
import com.wzsec.modules.sdd.discover.domain.Detailresult;
import com.wzsec.modules.sdd.discover.domain.Outlineresult;
import com.wzsec.modules.sdd.discover.service.DetailresultService;
import com.wzsec.modules.sdd.discover.service.OutlineresultService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.proxy.common.utils.StringUtil;
import com.wzsec.utils.*;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;


/**
 * 文件敏感数据发现，下载一个检测一个删除一个
 *
 * <AUTHOR>
 * @date 2020-4-27
 */
@Slf4j
@Transactional
public class FileSensitiveDataDiscovery_v1 {


    private final OutlineresultService outlineresultService;

    private final DetailresultService detailresultService;

    public FileSensitiveDataDiscovery_v1(OutlineresultService outlineresultService, DetailresultService detailresultService) {
        this.outlineresultService = outlineresultService;
        this.detailresultService = detailresultService;
    }

    private String taskname = null;
    private String submituser = null;
    private Long dataSourceId = null;
    private String type = null;
    private String srcurl = null;
    private String username = null;
    private String password = null;
    private String datapath = null;//远程目录
    private List<RuleDto> ruleDtoList = null;


    private String ip = null;
    private Integer port = null;


    /**
     * 执行文件敏感数据发现任务
     *
     * @param taskname    任务名
     * @param submituser  提交人
     * @param type        数据源类型
     * @param srcurl      数据源地址(包含IP和端口)
     * @param username    用户名
     * @param datapath    数据目录
     * @param ruleDtoList 规则
     * <AUTHOR>
     * @date 2020年4月26日
     */
    public boolean doFileSensitiveDataDiscovery(String taskname, Long dataSourceId, String submituser, String srcname, String type, String srcurl, String username,
                                                String password, String datapath, List<RuleDto> ruleDtoList, String datastructure,String srcip,String srcport) throws Exception {
        this.taskname = taskname;
        this.submituser = submituser;
        this.dataSourceId = dataSourceId;
        this.type = type;
        this.srcurl = srcurl;
        this.username = username;
        this.password = password;
        this.datapath = datapath;
        this.ruleDtoList = ruleDtoList;
        if (StringUtils.isNotEmpty(srcip) && StringUtils.isNotEmpty(srcport)){
            this.ip = srcip;
            this.port = Integer.valueOf(srcport);
        }
        // 1.转换文件路径拼接符为通用拼接符“/”
        datapath = datapath.replace(Const.SEPARATOR_WINDOWS, Const.SEPARATOR_LINUX);//替换windows环境路径分割符号为linux分隔符，用于本地路径与远程路径对应处理
        boolean result = false;
        String localPath = null;
        // 2.获取文件源IP和端口，以及文件本地缓存路径
        if (type.equalsIgnoreCase(Const.FILE_LOCAL)) {//1、本地文件检测
            try {
                InetAddress addr = InetAddress.getLocalHost();
                ip = addr.getHostAddress();
            } catch (UnknownHostException e) {
                ip = Const.LOCALHOST;
            }
            localPath = datapath;//需要检测目录
        } else if (type.equalsIgnoreCase(Const.FILE_FTP) || type.equalsIgnoreCase(Const.FILE_SFTP)) {//2、FTP服务器文件检测
            String[] hostNameAndPort = srcurl.split(":|：");
            ip = hostNameAndPort[0];
            port = Integer.valueOf(hostNameAndPort[1]);
            localPath = ConstEngine.sddEngineConfs.get("ftp.download.save.localpath");
        } else if (type.equalsIgnoreCase(Const.FILE_HDFS)) {//3、HDFS集群文件检测
            if (srcurl.contains("：")) {
                String[] hostNameAndPort = srcurl.split(":|：");
                ip = hostNameAndPort[0];
                port = Integer.valueOf(hostNameAndPort[1]);
            } else {
                ip = srcurl;
                port = 0;
            }
            localPath = ConstEngine.sddEngineConfs.get("hdfs.download.save.localpath");
        } else if (type.equalsIgnoreCase(Const.FILE_LINUX)) {//4、Linux系统文件检测
            String[] hostNameAndPort = srcurl.split(":|：");
            ip = hostNameAndPort[0];
            port = Integer.valueOf(hostNameAndPort[1]);
            localPath = ConstEngine.sddEngineConfs.get("linux.download.save.localpath");
        } else if (type.equalsIgnoreCase(Const.FILE_MINIO)) {//5、minio文件检测
            localPath = ConstEngine.sddEngineConfs.get("minio.download.save.localpath");
        } else {
            log.info("暂不支持的文件源：" + type);
        }
        if (localPath != null) {
            // 3.获取文件源指定目录下所有文件路径
            List<String> filePathList = getFilePathList();

            // 4.检测检测
            result = checkLocalFile(localPath, filePathList, datastructure);
        }
        return result;
    }

    /**
     * 从数据源获取远程目录下所有文件路径
     *
     * @return
     * <AUTHOR>
     * @date 2020年4月27日
     */
    private List<String> getFilePathList() throws Exception {
        List<String> filePathList = new ArrayList<>();
        //1、本地文件获取
        if (type.equalsIgnoreCase(Const.FILE_LOCAL)) {
            FileUtil.getFilePathList(datapath, filePathList);
            //2、FTP服务器文件获取
        } else if (type.equalsIgnoreCase(Const.FILE_FTP)) {
            FTPUtil.getAllFilePathByDirectory(ip, port, username, password, datapath, filePathList);
            //3、HDFS集群文件获取
        } else if (type.equalsIgnoreCase(Const.FILE_HDFS)) {
            //HDFSUtil.getHDFSDirFile("hdfs://" + srcurl, srcurl, datapath, filePathList);
            HDFSUtil.getHDFSDirFile(srcurl, srcurl, datapath, filePathList);
            //4、Linux系统文件获取
        } else if (type.equalsIgnoreCase(Const.FILE_LINUX)) {
            ScpClient.getInstance(ip, port, username, password).getAllFilePathByRemotePath(datapath, filePathList);
        } else if (type.equalsIgnoreCase(Const.FILE_MINIO)) {
            //minio文件获取
            List<String> fileList = MinioUtil.queryMinioFileList(srcurl, username, password, datapath);
            if (ObjectUtil.isNotEmpty(fileList)){
                filePathList.addAll(fileList);
            }
        } else {
            log.info("暂不支持的文件源：" + type);
        }
        log.info("获取所有的文件名：" + filePathList.toString());
        return filePathList;
    }

    /**
     * 从数据源下载文件到本地(单个文件)
     *
     * @param remotePath     远程目录（含子目录）
     * @param remoteFileName 远程文件名
     * @param localFilePath  本地保存文件路径（含子目录）
     * @return
     * <AUTHOR>
     * @date 2020年4月27日
     */
    private boolean downloadFile(String remotePath, String remoteFileName, String localFilePath) throws Exception {
        boolean result = false;
        //1、本地文件检测
        if (type.equalsIgnoreCase(Const.FILE_LOCAL)) {
            result = true;
            //2、FTP服务器文件下载
        } else if (type.equalsIgnoreCase(Const.FILE_FTP)) {
            String localPath = localFilePath.substring(0, localFilePath.lastIndexOf(Const.SEPARATOR_LINUX));
            result = FTPUtil.downloadFileByFilePath(ip, port, username, password, remotePath, remoteFileName, localPath);
            //3、HDFS集群文件下载
        } else if (type.equalsIgnoreCase(Const.FILE_HDFS)) {
            String localPath = localFilePath.substring(0, localFilePath.lastIndexOf(Const.SEPARATOR_LINUX));
            //result = HDFSUtil.downloadFileByFilePath("hdfs://" + srcurl, remotePath, remoteFileName, localPath);
            result = HDFSUtil.downloadFileByFilePath(srcurl, remotePath, remoteFileName, localPath);
            //4、Linux系统文件下载
        } else if (type.equalsIgnoreCase(Const.FILE_LINUX)) {
            String localPath = localFilePath.substring(0, localFilePath.lastIndexOf(Const.SEPARATOR_LINUX));
            result = ScpClient.getInstance(ip, port, username, password).downloadFile(remotePath, remoteFileName, localPath);
            //SFTP服务器文件下载
        }
//        else if (type.equalsIgnoreCase(Const.FILE_SFTP)) {
//            String localPath = localFilePath.substring(0, localFilePath.lastIndexOf(Const.SEPARATOR_LINUX));
//            result = SFTPUtil.downloadFile(ip, port, username, password, remotePath, remoteFileName, localPath);
//        }
        else if (type.equalsIgnoreCase(Const.FILE_MINIO)) {
            String bucketName = MinioUtil.queryBucketName(srcurl);
            remoteFileName = remotePath + "/" +remoteFileName;
            result = MinioUtil.downloadFileByFilePath(srcurl, username, password,bucketName,remotePath,remoteFileName,localFilePath);
        }
        else {
            log.info("暂不支持的文件源：" + type);
        }
        return result;
    }


    /**
     * 检测文件源中所有文件敏感数据(通用)
     * <p>
     * 保存时对路径进行处理，本地检测为本地路径，文件服务器检测为远程路径
     *
     * @param localPath    本地保存路径
     * @param filePathList 需要检测的文件远程路径（子目录）
     * <AUTHOR>
     * @date 2020年4月27日
     */
    private boolean checkLocalFile(String localPath, List<String> filePathList, String datastructure) {
        try {
            int count = 0;
            int sensitive = 0;
            Outlineresult outlineresult = new Outlineresult();
            long startTime = System.currentTimeMillis();
            outlineresult.setTaskname(taskname);
            outlineresult.setSubmituser(submituser);
            outlineresult.setStarttime(TimestampUtil.getNowTime());
            outlineresult.setLocation(ip);
            outlineresult.setSparefield1(dataSourceId);
            outlineresult.setDatasourcetype(Const.OUTLINERESULT_DATASOURCETYPE_FILE);//文件
            outlineresultService.create(outlineresult);
            List<String> dataTypeList = new ArrayList<>();
            for (String remoteFilePath : filePathList) {
                int fileLineNum = 0;
                File file = null;
                List<String> fileDataList = null;

                try {
                    String remotePath = null;
                    String localFilePath = null;
                    try {
                        if (type.equals(Const.FILE_MINIO)){
                            if (remoteFilePath.contains(Const.SEPARATOR_LINUX)){
                                remotePath = remoteFilePath.substring(0, remoteFilePath.lastIndexOf(Const.SEPARATOR_LINUX));
                            }else {
                                remotePath = "/";
                            }
                        }else {
                            remotePath = remoteFilePath.substring(0, remoteFilePath.lastIndexOf(Const.SEPARATOR_LINUX));
                        }
                        if ("".equals(remotePath)) {
                            remotePath = Const.SEPARATOR_LINUX;
                        }
                        String remoteFileName = remoteFilePath.substring(remoteFilePath.lastIndexOf(Const.SEPARATOR_LINUX) + 1);
                        if (type.equalsIgnoreCase(Const.FILE_MINIO)) {
                            localFilePath = localPath;
                        }else {
                            localFilePath = localPath + Const.SEPARATOR_LINUX + remoteFilePath.replace(datapath, Const.STRING_EMPTY);
                        }
//                    String localFilePath = localPath + Const.SEPARATOR_LINUX + remoteFileName;
                        System.out.println("开始下载" + remoteFilePath + "文件时间：" + TimeUtils.getNowTime());
                        log.info("开始下载" + remoteFilePath + "文件时间：" + TimeUtils.getNowTime());
                        // 1.下载指定文件到本地
                        if (!downloadFile(remotePath, remoteFileName, localFilePath)) {
                            log.error("下载" + remoteFilePath + "文件失败！");
                            throw new Exception("下载" + remoteFilePath + "文件失败！");
                        }
                        if (type.equalsIgnoreCase(Const.FILE_MINIO)) {
                            if (remotePath.equals(Const.SEPARATOR_LINUX)){
                                localFilePath = localPath + Const.SEPARATOR_LINUX + remoteFileName;
                            }else {
                                localFilePath = localPath + Const.SEPARATOR_LINUX + remotePath + Const.SEPARATOR_LINUX + remoteFileName;
                            }
                        }
                    } catch (Exception e) {
                        //windows环境检测
                        if (type.equalsIgnoreCase(Const.FILE_MINIO)) {
                            localFilePath = localPath + "/" + remoteFilePath.substring(remoteFilePath.lastIndexOf(Const.SEPARATOR_LINUX) + 1);
                        }else {
                            localFilePath = remoteFilePath;  //TODO
                        }
                        remotePath = remoteFilePath; //TODO
                    }

                    // 2.读取缓存在本地的文件
                    file = new File(localFilePath);
                    //任务敏感数据规则检测信息
                    Map<String, Map<String, String>> fileDataTypeExampleMap = new HashMap<>();
                    log.info("====================对" + remoteFilePath + "文件分析开始====================");

                    // 3.获取文件中数据
                    if (Const.SOURCE_STRUCTURED.equals(datastructure)) {
                        fileDataList = CheckFileManager.getExtractFileData(file, fileLineNum);
                    } else if (Const.SOURCE__NOT_STRUCTURED.equals(datastructure)) {
                        fileDataList = CheckFileManager.getFileData(file);
                    } else {
                        fileDataList = CheckFileManager.getFileData(file);
                    }

                    //List<String> fileDataList = CheckFileManager.getFileData(file);
                    //System.out.println("拆分后的字符串：" + JSON.toJSONString(fileDataList));
                    System.out.println("检测文件中数据是否敏感时间：" + TimeUtils.getNowTime());
                    log.info("检测文件中数据是否敏感时间：" + TimeUtils.getNowTime());
                    // 4.检测文件中数据是否敏感
                    fileDataTypeExampleMap = CheckFileManager.getFileDataTypeMap(ruleDtoList, fileDataList);
                    log.info("====================对" + remoteFilePath + "文件分析结束====================");
                    Map<String, String> dataTypeMap = fileDataTypeExampleMap.get("fileDataType");
                    Map<String, String> dataExampleMap = fileDataTypeExampleMap.get("dataExample");

                    // 5.统计敏感数据数量、样本，记录详情结果
                    if (dataTypeMap.size() > 0) {
                        sensitive++;
                        //存在敏感数据
                        for (String dataType : dataTypeMap.keySet()) {
                            if (!dataTypeList.contains(dataType)) {
                                dataTypeList.add(dataType);
                            }
                            Detailresult detailresult = new Detailresult();
                            detailresult.setSparefield1(Integer.valueOf(String.valueOf(dataSourceId)));
                            detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_FILE);//文件
                            detailresult.setTaskname(taskname);
                            //拼接子目录，用于本地路径与远程路径对应处理
                            detailresult.setDborpath(remotePath);//源文件所在路径
                            detailresult.setTableorfile(file.getName());
                            detailresult.setDatatype(dataType);
                            if (fileLineNum == 0) {
                                if (fileDataList == null) {
                                    detailresult.setDatacount(fileLineNum);
                                } else {
                                    detailresult.setDatacount(Integer.valueOf(dataTypeMap.get(dataType)));
                                }
                            } else {
                                detailresult.setDatacount(fileLineNum);
                            }
                            detailresult.setExample(dataExampleMap.get(dataType));
                            detailresultService.create(detailresult);
                        }
                    } else {
                        //没有敏感数据
                        Detailresult detailresult = new Detailresult();
                        detailresult.setSparefield1(Integer.valueOf(String.valueOf(dataSourceId)));
                        detailresult.setSourcetype(Const.DETAILRESULT_SOURCETYPE_FILE);//文件
                        detailresult.setTaskname(taskname);
                        //拼接子目录，用于本地路径与远程路径对应处理
                        detailresult.setDborpath(remotePath);//源文件所在路径    TODO
                        detailresult.setTableorfile(file.getName());
                        if (fileLineNum == 0) {
                            if (fileDataList == null) {
//                                detailresult.setDatacount(String.valueOf(fileDataList));
                                detailresult.setDatacount(fileLineNum);
                            } else {
                                detailresult.setDatacount(fileDataList.size());
                            }
                        } else {
                            detailresult.setDatacount(fileLineNum);
                        }
                        //detailresult.setDatatype();
                        //detailresult.setDatacount(dataTypeMap.get(dataType));
                        //detailresult.setExample(dataExampleMap.get(dataType));
                        detailresultService.create(detailresult);
                    }
                } catch (Exception e) {
                    log.error("检测" + remoteFilePath + "文件出现异常！", e);
                } finally {
                    // 5.删除缓存在本地的文件
                    /*if (file != null) {
                        file.delete();
                    }*/
                    count++;
                    log.info("已处理文件数量： " + count + " 总文件数量:" + filePathList.size());
                    System.out.println("已处理文件数量： " + count + " 总文件数量:" + filePathList.size());
                }

            }
            outlineresult.setEndtime(TimestampUtil.getNowTime());
            //用时计算
            long endTime = System.currentTimeMillis();
            outlineresult.setUsetime(String.valueOf(Math.round(endTime - startTime) / 1000.000));
            outlineresult.setTypenum(String.valueOf(dataTypeList.size()));
            outlineresult.setSparefield2(String.valueOf(filePathList.size()));
            outlineresult.setSparefield3(String.valueOf(sensitive));
            outlineresultService.update(outlineresult);
            return true;
        } catch (Exception e) {
            log.error("检测" + datapath + "目录出现异常！", e);
            return false;
        }
    }

}
