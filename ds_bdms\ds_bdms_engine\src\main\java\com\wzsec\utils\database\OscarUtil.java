package com.wzsec.utils.database;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2024/12/12 13:53
 */
@Slf4j
public class OscarUtil extends DatabaseUtil{

    private static String JDBC_DRIVER = "com.oscar.Driver";

    /**
     * 创建表sql
     * @param dbName    库名
     * @param tableName 表名
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @return
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username, String password) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        String schema = null;
        Connection conn = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();
            rs = stmt.executeQuery("SELECT current_schema()");
            if (rs.next()) {
                schema = rs.getString(1);
            }
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT DISTINCT c.column_name AS COLUMN_NAME,com.comments AS COLUMN_COMMENT,c.DATA_TYPE,c.DATA_LENGTH FROM all_tab_columns c LEFT JOIN all_col_comments com ON c.owner = com.owner AND c.table_name = com.table_name AND c.column_name = com.column_name WHERE c.owner = '"+schema+"' AND c.table_name = '"+tableName+"' ORDER BY c.column_id");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("FieldEName", rs.getString("COLUMN_NAME"));
                    if (rs.getString("DATA_TYPE").contains("VARCHAR")){
                        fieldInfoMap.put("FieldType", rs.getString("DATA_TYPE")+"("+rs.getString("DATA_LENGTH")+")");
                    }else {
                        fieldInfoMap.put("FieldType", rs.getString("DATA_TYPE"));
                    }

                    fieldInfoMap.put("FieldCName", rs.getString("COLUMN_COMMENT"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs,stmt,null);
        }
        return fieldInfoList;
    }

    /**
     * 创建表sql
     * @param objList   Map数据库集合
     * @param dbName    库名
     * @param tableName 表名
     * @return
     */
    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbName, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 新增表sql语句
     * @param start     批量插入开始位
     * @param end       批量插入结束位
     * @param objList   Map数据库集合
     * @param dbname    库名
     * @param tableName 表名
     * @return
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 新增表sql语句
     * @param start      批量插入开始位
     * @param end        批量插入结束位
     * @param objList    Map数据库集合
     * @param dbname     库名
     * @param tableName  表名
     * @param fieldnames 字段名
     * @return
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @description: 生成创建表sql语句
     * @param fieldInfoList 字段信息列表
     * @param obj           Map对象
     * @param tableName     表名
     * @param maskfields    脱敏字段
     * @return:
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append(fieldInfo.get("FieldEName"));// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        sb.append(" DEFAULT NULL");
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r);\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * <AUTHOR>
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            // boolean firstId = true;
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append(fieldInfo.get("FieldEName"));// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        sb.append(" DEFAULT NULL");
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("Comment") != null && !"".equals(fieldInfo.get("Comment"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("Comment")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r);\r\n";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    /***
     * 获取数据库中所有的库名表名
     * @param: conn     连接
     * @param: dbnames  库名
     * <AUTHOR>
     * @date: 2024/12/12 13:55
     * @return:
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        String schema = null;
        try {
            stmt = conn.prepareStatement("SELECT current_schema()");
            rs = stmt.executeQuery();
            if (rs.next()) {
                schema = rs.getString(1);
            }
            String strSQL = "SELECT DISTINCT TABLE_NAME FROM ALL_TABLES WHERE OWNER = '"+schema+"' AND TABLE_NAME NOT LIKE 'SYS_%' AND TABLE_NAME NOT LIKE 'USER_LOGIN_%' AND TABLE_NAME NOT LIKE 'V_SYS_%' AND TABLE_NAME NOT LIKE '_OBJ%' AND TABLE_NAME NOT LIKE 'LOGIN_%' AND TABLE_NAME NOT LIKE 'DBMS_LOCK_%' AND TABLE_NAME NOT LIKE 'AQ$_%' AND TABLE_NAME NOT LIKE 'DUAL%' ORDER BY TABLE_NAME";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String table_name = rs.getString(1);
                if (dbTabMap.containsKey(dbnames)) {
                    dbTabMap.put(dbnames, dbTabMap.get(dbnames) + "," + table_name);
                } else {
                    dbTabMap.put(dbnames, table_name);
                }
            }
        } catch (Exception ex) {
            log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        }finally {
            closeCon(rs,stmt,null);
        }
        return dbTabMap;
    }

    /**
     * 获取数据库表数据
     *
     * @param conn    连接
     * @param dbname  dbname
     * @param tabname tabname
     * @param lineNum 行num
     * @return {@link List}<{@link String[]}>
     * @throws SQLException sqlexception异常
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        String schema = null;
        try {
            stmt = conn.prepareStatement("SELECT current_schema()");
            rs = stmt.executeQuery();
            if (rs.next()) {
                schema = rs.getString(1);
            }
            String strSQL = "select * from " + schema + "." + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        }finally {
            closeCon(rs,stmt,null);
        }
        return tabDataList;
    }


    /**
     * @Description 获取数据库表中所有字段名
     * @param conn：conn
     * @param dbname：库名
     * @param tabname：表名
     * @return List<String>: List集合
     * <AUTHOR>
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        String schema = null;
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery("SELECT current_schema()");
            if (rs.next()) {
                schema = rs.getString(1);
            }
            String strSQL = "select DISTINCT COLUMN_NAME from INFO_SCHEM.ALL_TAB_COLUMNS where TABLE_NAME = '"+tabname+"' and OWNER = '"+schema+"' ORDER BY COLUMN_ID";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } catch (Exception ex) {
            log.error("获取库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
            throw ex;
        }finally {
            closeCon(rs,stmt,null);
        }
        return list;
    }

    /**
     * @Description 获取数据库表中前100条数据
     * @param conn：conn
     * @param dbname：库名
     * @param tabname：表名
     * @return int: int
     * <AUTHOR>
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return count;
    }

    /***
     *
     * 查询表信息
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表信息
     * @Description 查询表信息
     * <AUTHOR>
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        String schema = null;
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery("SELECT current_schema()");
            if (rs.next()) {
                schema = rs.getString(1);
            }
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT TMP.RELNAME AS TABLE_NAME, TMP.COMMENTS AS TABLE_COMMENT, TMP.ROW_COUNT AS TABLE_ROWS, SUM(TMP.SIZE) / 1024 / 1024 AS Size_MB " +
                    "FROM (SELECT N.NSPNAME,C.RELNAME,C.RELKIND,S.SIZE,T.COMMENTS,(SELECT COUNT(*) FROM USER_TABLES) AS ROW_COUNT " +
                    "FROM V_SEGMENT_INFO S JOIN SYS_CLASS C ON S.RELID = C.OID JOIN SYS_NAMESPACE N ON C.RELNAMESPACE = N.OID JOIN INFO_SCHEM.ALL_TAB_COMMENTS T ON C.RELNAME = T.TABLE_NAME " +
                    "WHERE N.NSPNAME = '"+schema+"' AND C.RELKIND = 'r' AND C.RELNAME = '"+tableName+"')TMP GROUP BY TMP.NSPNAME, TMP.RELNAME, TMP.COMMENTS, TMP.ROW_COUNT");
            if (rs != null && rs.next()) {
                tableInfoMap.put("tableName", rs.getString("TABLE_NAME"));
                tableInfoMap.put("tableCName", rs.getString("TABLE_COMMENT"));
                tableInfoMap.put("tableRows", rs.getString("TABLE_ROWS"));
                tableInfoMap.put("dataSize", rs.getString("Size_MB"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs,stmt,null);
        }
        return tableInfoMap;
    }

    /***
     * 查询表字段信息
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        String schema = null;
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery("SELECT current_schema()");
            if (rs.next()) {
                schema = rs.getString(1);
            }
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT DISTINCT c.column_name AS COLUMN_NAME,com.comments AS COLUMN_COMMENT FROM all_tab_columns c LEFT JOIN all_col_comments com ON c.owner = com.owner AND c.table_name = com.table_name AND c.column_name = com.column_name WHERE c.owner = '"+schema+"' AND c.table_name = '"+tableName+"' ORDER BY c.column_id");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("COLUMN_NAME"));
                    fieldInfoMap.put("fieldCName", rs.getString("COLUMN_COMMENT"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs,stmt,null);
        }
        return fieldInfoList;
    }
}
