package com.wzsec.modules.sdd.discover.service.mapper;

import com.wzsec.base.BaseMapper;
import com.wzsec.modules.sdd.discover.domain.Task;
import com.wzsec.modules.sdd.discover.service.dto.TaskDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* <AUTHOR>
* @date 2020-04-03
*/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskMapper extends BaseMapper<TaskDto, Task> {

}