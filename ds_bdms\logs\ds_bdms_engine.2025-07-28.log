2025-07-28 13:54:43,647 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 13824 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 13:54:43,653 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 13:54:46,453 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 13:54:46,454 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 13:54:47,595 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1121 ms. Found 88 JPA repository interfaces.
2025-07-28 13:54:48,207 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 13:54:48,208 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 13:54:48,211 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:54:48,211 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:54:48,214 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:54:48,216 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 13:54:48,217 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 13:54:48,218 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:54:48,218 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:54:49,161 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 13:54:49,179 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 13:54:49,182 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 13:54:50,477 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 13:54:50,831 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 13:54:50,866 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 13:54:50,867 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 13:54:50,867 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 13:54:50,868 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 13:54:50,868 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 13:54:50,868 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 13:54:50,872 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 13:54:50,875 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 13:54:54,236 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 13:54:54,975 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 13:54:55,220 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 13:54:55,816 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 13:54:56,393 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 13:55:05,060 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 13:55:05,130 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 13:55:13,999 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 13:55:14,615 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 30311 ms
2025-07-28 13:55:22,814 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 13:55:23,093 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 13:55:23,242 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 13:55:23,242 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 13:55:23,266 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 13:55:23,273 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 13:55:23,273 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 13:55:23,274 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 13:55:23,275 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@844e2fb
2025-07-28 13:55:23,275 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 13:55:27,383 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@41db1728, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2a69158a, org.springframework.security.web.context.SecurityContextPersistenceFilter@7352de9c, org.springframework.security.web.header.HeaderWriterFilter@61523c3, org.springframework.security.web.authentication.logout.LogoutFilter@48cd97f1, org.springframework.web.filter.CorsFilter@1296284f, com.wzsec.modules.security.security.TokenFilter@1b5a471d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1e792043, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@532f5c5d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ff49da3, org.springframework.security.web.session.SessionManagementFilter@4ea0719e, org.springframework.security.web.access.ExceptionTranslationFilter@564c9626, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5ff00e9a]
2025-07-28 13:55:27,431 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 13:55:31,545 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 13:55:31,546 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 13:55:31,547 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 13:55:31,547 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 13:55:31,547 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 13:55:31,547 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 13:55:31,547 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 13:55:31,547 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@65a288e7
2025-07-28 13:55:31,762 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 13:55:31,865 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 13:55:31,869 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 13:55:31,870 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 13:55:31,870 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 13:55:31,870 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 13:55:31,871 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 13:55:31,871 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 13:55:31,871 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 13:55:31,872 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 13:55:31,872 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 13:55:31,876 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 13:55:31,877 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 13:55:31,877 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 13:55:31,891 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 49.054 seconds (JVM running for 51.682)
2025-07-28 13:55:32,733 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 13:55:32,733 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:06:36,199 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:06:36,435 INFO (FrameworkServlet.java:547)- Completed initialization in 225 ms
2025-07-28 14:06:44,334 INFO (DoPictureTaskServiceImpl.java:58)- 开始执行图片脱敏任务
2025-07-28 14:06:45,397 WARN (DoPictureTaskServiceImpl.java:320)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:06:45,398 INFO (DoPictureTaskServiceImpl.java:93)- 处理前收集到输入文件数量: 0
2025-07-28 14:06:45,428 INFO (DockerRunner.java:34)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input/idcard.jpg:/app/input -v /data/joy/p_output:/app/output mosaic_app:latest -type image -input /app/input/idcard.jpg -output /app/output/idcard.jpg -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:08:10,447 ERROR (DockerRunner.java:54)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:37)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:135)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:08:10,448 WARN (DoPictureTaskServiceImpl.java:147)- Docker 处理失败，退出码: -1
2025-07-28 14:08:10,933 WARN (DoPictureTaskServiceImpl.java:320)- 目录不存在: /data/joy/p_output
2025-07-28 14:08:10,951 INFO (DoPictureTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:08:12,294 INFO (DoPictureTaskServiceImpl.java:251)- 执行图片脱敏总时长为: 86秒
2025-07-28 14:08:12,295 INFO (DoPictureTaskServiceImpl.java:252)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:08:12,296 INFO (DoPictureTaskServiceImpl.java:253)- 任务ID【22】,执行完毕
2025-07-28 14:10:43,816 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:10:44,803 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 14:10:44,804 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:10:44,804 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:10:44,806 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:10:44,858 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:10:44,859 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:10:44,859 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:10:44,884 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:10:44,952 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-28 14:10:55,895 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 18912 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:10:55,901 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:10:58,827 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:10:58,828 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:10:59,944 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1100 ms. Found 88 JPA repository interfaces.
2025-07-28 14:11:00,539 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:11:00,540 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:11:00,542 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:11:00,543 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:11:00,545 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:11:00,547 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:11:00,548 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:11:00,548 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:11:00,548 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:11:01,422 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:11:01,439 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:11:01,444 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:11:02,620 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:11:02,894 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:11:02,920 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:11:02,920 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:11:02,922 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:11:02,922 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:11:02,922 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:11:02,923 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:11:02,925 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:11:02,928 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:11:05,707 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:11:06,379 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:11:06,590 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:11:07,132 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:11:07,683 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:11:17,780 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:11:17,827 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:11:22,138 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 14:11:22,568 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 26068 ms
2025-07-28 14:11:27,914 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 14:11:28,116 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:11:28,228 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:11:28,228 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:11:28,249 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:11:28,255 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:11:28,255 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:11:28,255 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:11:28,256 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@4086b547
2025-07-28 14:11:28,256 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:11:31,922 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@16abeca6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@475add19, org.springframework.security.web.context.SecurityContextPersistenceFilter@51d502a6, org.springframework.security.web.header.HeaderWriterFilter@44ebf052, org.springframework.security.web.authentication.logout.LogoutFilter@6b45907f, org.springframework.web.filter.CorsFilter@55f20d7e, com.wzsec.modules.security.security.TokenFilter@7136d1b3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@143cb21, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f880f30, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19a78a4f, org.springframework.security.web.session.SessionManagementFilter@5b95c0ba, org.springframework.security.web.access.ExceptionTranslationFilter@18011a4a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5f3c97c9]
2025-07-28 14:11:31,985 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:11:36,666 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:11:36,667 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:11:36,667 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:11:36,667 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:11:36,667 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:11:36,668 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:11:36,668 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:11:36,668 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1683d77d
2025-07-28 14:11:36,897 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:11:37,025 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 14:11:37,029 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:11:37,029 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:11:37,030 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:11:37,030 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:11:37,030 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:11:37,030 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:11:37,031 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:11:37,031 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:11:37,031 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:11:37,034 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:11:37,034 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:11:37,034 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:11:37,049 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 42.0 seconds (JVM running for 44.59)
2025-07-28 14:11:37,879 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 14:11:37,879 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:11:44,897 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:11:44,900 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-28 14:11:50,626 INFO (DoPictureTaskServiceImpl.java:58)- 开始执行图片脱敏任务
2025-07-28 14:11:51,429 WARN (DoPictureTaskServiceImpl.java:320)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:11:51,429 INFO (DoPictureTaskServiceImpl.java:93)- 处理前收集到输入文件数量: 0
2025-07-28 14:11:51,452 INFO (DockerRunner.java:34)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input:/app/input -v /data/joy:/app/output mosaic_app:latest -type image -input /app/input/idcard.jpg -output /app/output/idcard.jpg -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:14:22,420 ERROR (DockerRunner.java:54)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:37)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:135)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:14:22,420 WARN (DoPictureTaskServiceImpl.java:147)- Docker 处理失败，退出码: -1
2025-07-28 14:14:22,646 WARN (DoPictureTaskServiceImpl.java:320)- 目录不存在: /data/joy/p_output
2025-07-28 14:14:22,663 INFO (DoPictureTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:14:23,650 INFO (DoPictureTaskServiceImpl.java:251)- 执行图片脱敏总时长为: 152秒
2025-07-28 14:14:23,651 INFO (DoPictureTaskServiceImpl.java:252)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:14:23,651 INFO (DoPictureTaskServiceImpl.java:253)- 任务ID【22】,执行完毕
2025-07-28 14:20:47,024 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:20:48,122 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 14:20:48,123 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:20:48,123 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:20:48,125 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:20:48,185 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:20:48,186 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:20:48,186 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:20:48,220 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:20:48,295 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-28 14:21:01,860 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 25576 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:21:01,865 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:21:04,632 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:21:04,633 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:21:05,759 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1106 ms. Found 88 JPA repository interfaces.
2025-07-28 14:21:06,467 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:21:06,469 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:21:06,472 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:21:06,474 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:21:06,477 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:21:06,481 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:21:06,483 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:21:06,483 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:21:06,483 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:21:07,451 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:21:07,472 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:21:07,474 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:21:08,657 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:21:08,930 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:21:08,957 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:21:08,957 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:21:08,958 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:21:08,958 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:21:08,958 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:21:08,959 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:21:08,962 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:21:08,964 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:21:11,965 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:21:12,922 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:21:13,245 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:21:13,986 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:21:14,681 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:21:23,433 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:21:23,485 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:21:28,792 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 14:21:29,288 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 26834 ms
2025-07-28 14:21:37,219 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 14:21:37,463 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:21:37,608 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:21:37,608 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:21:37,636 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:21:37,646 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:21:37,646 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:21:37,647 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:21:37,648 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@46379aea
2025-07-28 14:21:37,648 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:21:42,049 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6a0ce603, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@361c3c1, org.springframework.security.web.context.SecurityContextPersistenceFilter@7f8ccfcc, org.springframework.security.web.header.HeaderWriterFilter@54c17694, org.springframework.security.web.authentication.logout.LogoutFilter@d55b14b, org.springframework.web.filter.CorsFilter@4d002c5c, com.wzsec.modules.security.security.TokenFilter@4f383036, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3e445475, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@39ba0bff, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3bfc7d53, org.springframework.security.web.session.SessionManagementFilter@238709e6, org.springframework.security.web.access.ExceptionTranslationFilter@1da0256f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@31786c4c]
2025-07-28 14:21:42,105 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:21:48,827 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:21:48,828 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:21:48,829 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:21:48,829 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:21:48,829 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:21:48,829 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:21:48,829 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:21:48,829 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4a126898
2025-07-28 14:21:49,242 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:21:49,362 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 14:21:49,367 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:21:49,368 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:21:49,368 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:21:49,368 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:21:49,368 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:21:49,369 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:21:49,369 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:21:49,369 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:21:49,369 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:21:49,375 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:21:49,375 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:21:49,375 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:21:49,390 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 48.413 seconds (JVM running for 52.109)
2025-07-28 14:21:50,231 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 14:21:50,232 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:21:58,948 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:21:58,951 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-28 14:22:04,527 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 14:22:05,380 WARN (DoPictureTaskServiceImpl.java:322)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:22:05,380 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 14:22:05,404 INFO (DockerRunner.java:36)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input\idcard.jpg:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:24:34,041 ERROR (DockerRunner.java:56)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:39)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:137)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:24:34,042 WARN (DoPictureTaskServiceImpl.java:149)- Docker 处理失败，退出码: -1
2025-07-28 14:24:34,301 WARN (DoPictureTaskServiceImpl.java:322)- 目录不存在: /data/joy/p_output
2025-07-28 14:24:34,319 INFO (DoPictureTaskServiceImpl.java:224)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:24:35,140 INFO (DoPictureTaskServiceImpl.java:253)- 执行图片脱敏总时长为: 150秒
2025-07-28 14:24:35,141 INFO (DoPictureTaskServiceImpl.java:254)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:24:35,141 INFO (DoPictureTaskServiceImpl.java:255)- 任务ID【22】,执行完毕
2025-07-28 14:24:40,356 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 14:24:40,631 WARN (DoPictureTaskServiceImpl.java:322)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:24:40,631 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 14:24:40,632 INFO (DockerRunner.java:36)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input\idcard.jpg:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:24:47,339 ERROR (DockerRunner.java:56)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:39)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:137)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:24:47,340 WARN (DoPictureTaskServiceImpl.java:149)- Docker 处理失败，退出码: -1
2025-07-28 14:24:47,398 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:24:47,511 WARN (DoPictureTaskServiceImpl.java:322)- 目录不存在: /data/joy/p_output
2025-07-28 14:24:47,512 INFO (DoPictureTaskServiceImpl.java:224)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:24:48,051 INFO (DoPictureTaskServiceImpl.java:253)- 执行图片脱敏总时长为: 7秒
2025-07-28 14:24:48,051 INFO (DoPictureTaskServiceImpl.java:254)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:24:48,052 INFO (DoPictureTaskServiceImpl.java:255)- 任务ID【22】,执行完毕
2025-07-28 14:24:48,369 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 14:24:48,369 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:24:48,369 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:24:48,370 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:24:48,412 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:24:48,412 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:24:48,412 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:24:48,432 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:24:48,483 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-28 14:24:54,490 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 18788 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:24:54,497 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:24:57,299 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:24:57,301 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:24:58,348 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1031 ms. Found 88 JPA repository interfaces.
2025-07-28 14:24:58,907 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:24:58,908 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:24:58,909 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:24:58,910 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:24:58,912 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:24:58,913 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:24:58,915 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:24:58,915 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:24:58,915 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:24:59,719 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:24:59,735 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:24:59,737 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:25:00,797 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:25:01,079 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:25:01,106 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:25:01,107 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:25:01,107 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:25:01,108 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:25:01,108 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:25:01,109 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:25:01,111 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:25:01,114 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:25:04,045 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:25:04,685 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:25:04,896 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:25:05,536 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:25:06,238 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:25:16,161 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:25:16,234 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:25:21,130 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 14:25:21,567 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 26368 ms
2025-07-28 14:25:27,004 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 14:25:27,212 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:25:27,325 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:25:27,325 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:25:27,345 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:25:27,350 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:25:27,350 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:25:27,350 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:25:27,351 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@4f847b48
2025-07-28 14:25:27,351 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:25:31,598 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@f8f9ce0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@117554c6, org.springframework.security.web.context.SecurityContextPersistenceFilter@e9fcd1e, org.springframework.security.web.header.HeaderWriterFilter@293b7870, org.springframework.security.web.authentication.logout.LogoutFilter@74ea5db2, org.springframework.web.filter.CorsFilter@14cc127b, com.wzsec.modules.security.security.TokenFilter@5fda1853, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@68a7ea1e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c6062bd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@df0efb4, org.springframework.security.web.session.SessionManagementFilter@2fd2cf1f, org.springframework.security.web.access.ExceptionTranslationFilter@3631bac6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5ac5df4b]
2025-07-28 14:25:31,666 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:25:36,393 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:25:36,393 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:25:36,394 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:25:36,394 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:25:36,394 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:25:36,394 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:25:36,394 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:25:36,394 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7a204f57
2025-07-28 14:25:36,654 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:25:36,763 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 14:25:36,768 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:25:36,769 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:25:36,770 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:25:36,770 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:25:36,770 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:25:36,770 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:25:36,770 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:25:36,770 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:25:36,770 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:25:36,774 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:25:36,775 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:25:36,775 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:25:36,792 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 43.165 seconds (JVM running for 46.408)
2025-07-28 14:25:37,726 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 14:25:37,726 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:25:43,617 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:25:43,619 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-28 14:25:48,888 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 14:25:49,718 WARN (DoPictureTaskServiceImpl.java:322)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:25:49,720 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 14:28:47,012 INFO (DockerRunner.java:36)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input\idcard.jpg:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:28:47,070 ERROR (DockerRunner.java:56)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:39)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:137)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:28:47,071 WARN (DoPictureTaskServiceImpl.java:149)- Docker 处理失败，退出码: -1
2025-07-28 14:28:47,293 WARN (DoPictureTaskServiceImpl.java:322)- 目录不存在: /data/joy/p_output
2025-07-28 14:28:47,339 INFO (DoPictureTaskServiceImpl.java:224)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:28:48,299 INFO (DoPictureTaskServiceImpl.java:253)- 执行图片脱敏总时长为: 179秒
2025-07-28 14:28:48,299 INFO (DoPictureTaskServiceImpl.java:254)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:28:48,299 INFO (DoPictureTaskServiceImpl.java:255)- 任务ID【22】,执行完毕
2025-07-28 14:29:09,409 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:29:10,298 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 14:29:10,298 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:29:10,298 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:29:10,299 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:29:10,320 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:29:10,321 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:29:10,321 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:29:10,334 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:29:10,370 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-28 14:29:21,101 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 24624 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:29:21,107 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:29:23,727 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:29:23,729 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:29:24,857 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1109 ms. Found 88 JPA repository interfaces.
2025-07-28 14:29:25,506 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:29:25,508 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:29:25,512 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:29:25,512 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:29:25,517 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:29:25,520 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:29:25,521 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:29:25,521 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:29:25,521 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:29:26,428 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:29:26,449 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:29:26,454 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:29:27,701 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:29:28,014 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:29:28,049 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:29:28,050 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:29:28,050 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:29:28,051 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:29:28,051 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:29:28,051 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:29:28,055 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:29:28,059 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:29:30,866 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:29:31,547 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:29:31,770 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:29:32,330 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:29:32,951 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:29:41,251 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:29:41,319 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:29:46,040 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 14:29:46,467 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 24742 ms
2025-07-28 14:29:52,281 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 14:29:52,516 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:29:52,625 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:29:52,625 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:29:52,648 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:29:52,655 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:29:52,656 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:29:52,657 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:29:52,658 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@77c60c78
2025-07-28 14:29:52,658 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:29:56,573 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7f8ccfcc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a7a1c19, org.springframework.security.web.context.SecurityContextPersistenceFilter@34da2b4a, org.springframework.security.web.header.HeaderWriterFilter@2e897323, org.springframework.security.web.authentication.logout.LogoutFilter@25a5d43a, org.springframework.web.filter.CorsFilter@5db553f9, com.wzsec.modules.security.security.TokenFilter@3e445475, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4c47eb9b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@40d2b219, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2e7ce7b, org.springframework.security.web.session.SessionManagementFilter@6bf666e0, org.springframework.security.web.access.ExceptionTranslationFilter@50406145, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6cc8ad62]
2025-07-28 14:29:56,633 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:30:01,608 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:30:01,610 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:30:01,610 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:30:01,610 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:30:01,610 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:30:01,610 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:30:01,610 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:30:01,610 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2960f500
2025-07-28 14:30:01,837 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:30:01,977 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 14:30:01,992 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:30:01,994 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:30:01,995 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:30:01,995 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:30:01,995 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:30:01,995 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:30:01,995 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:30:01,995 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:30:01,995 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:30:02,004 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:30:02,005 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:30:02,005 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:30:02,034 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 41.63 seconds (JVM running for 44.087)
2025-07-28 14:30:03,075 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 14:30:03,076 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:30:23,165 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:30:23,168 INFO (FrameworkServlet.java:547)- Completed initialization in 3 ms
2025-07-28 14:30:28,646 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 14:30:29,654 WARN (DoPictureTaskServiceImpl.java:302)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:30:29,654 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 14:30:58,242 INFO (DockerRunner.java:36)- 准备执行 Docker 命令:
docker run --rm -v /data/joy/p_input/idcard.jpg:/app/input -v /data/joy/p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:30:58,268 ERROR (DockerRunner.java:56)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:39)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:117)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:30:58,269 WARN (DoPictureTaskServiceImpl.java:129)- Docker 处理失败，退出码: -1
2025-07-28 14:30:58,463 WARN (DoPictureTaskServiceImpl.java:302)- 目录不存在: /data/joy/p_output
2025-07-28 14:30:58,480 INFO (DoPictureTaskServiceImpl.java:204)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:30:59,241 INFO (DoPictureTaskServiceImpl.java:233)- 执行图片脱敏总时长为: 30秒
2025-07-28 14:30:59,241 INFO (DoPictureTaskServiceImpl.java:234)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:30:59,241 INFO (DoPictureTaskServiceImpl.java:235)- 任务ID【22】,执行完毕
2025-07-28 14:35:25,071 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:35:26,067 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 14:35:26,067 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:35:26,067 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:35:26,068 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:35:26,098 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:35:26,099 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:35:26,099 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:35:26,122 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:35:26,184 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-28 14:35:36,892 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 20812 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:35:36,899 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:35:39,461 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:35:39,463 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:35:40,502 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1024 ms. Found 88 JPA repository interfaces.
2025-07-28 14:35:41,050 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:35:41,051 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:35:41,054 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:35:41,055 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:35:41,057 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:35:41,059 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:35:41,060 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:35:41,060 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:35:41,061 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:35:41,856 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:35:41,873 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:35:41,876 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:35:42,999 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:35:43,267 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:35:43,294 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:35:43,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:35:43,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:35:43,295 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:35:43,296 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:35:43,296 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:35:43,299 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:35:43,301 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:35:46,174 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:35:46,847 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:35:47,066 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:35:47,584 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:35:48,080 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:35:54,892 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:35:54,943 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:35:59,203 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 14:35:59,639 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 22175 ms
2025-07-28 14:36:04,886 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 14:36:05,084 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:36:05,190 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:36:05,191 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:36:05,213 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:36:05,218 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:36:05,218 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:36:05,218 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:36:05,219 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@5b96272e
2025-07-28 14:36:05,219 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:36:08,745 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@82cd68d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@31102e84, org.springframework.security.web.context.SecurityContextPersistenceFilter@459dea3e, org.springframework.security.web.header.HeaderWriterFilter@1ec5ade, org.springframework.security.web.authentication.logout.LogoutFilter@371c8e30, org.springframework.web.filter.CorsFilter@69b74dc2, com.wzsec.modules.security.security.TokenFilter@729bdd9d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@26825fbb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3c1ddea3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3b8bb9a0, org.springframework.security.web.session.SessionManagementFilter@18011a4a, org.springframework.security.web.access.ExceptionTranslationFilter@21970f32, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4262a8d2]
2025-07-28 14:36:08,794 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:36:12,755 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:36:12,756 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:36:12,756 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:36:12,756 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:36:12,756 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:36:12,756 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:36:12,756 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:36:12,756 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2bdb0d42
2025-07-28 14:36:13,000 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:36:13,119 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 14:36:13,123 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:36:13,125 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:36:13,125 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:36:13,125 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:36:13,125 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:36:13,125 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:36:13,125 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:36:13,125 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:36:13,125 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:36:13,129 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:36:13,130 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:36:13,130 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:36:13,148 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 36.998 seconds (JVM running for 39.279)
2025-07-28 14:36:13,953 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 14:36:13,954 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:36:35,020 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:36:35,022 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-28 14:36:40,550 INFO (DoPictureTaskServiceImpl.java:60)- 开始执行图片脱敏任务
2025-07-28 14:36:41,465 WARN (DoPictureTaskServiceImpl.java:308)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:36:41,466 INFO (DoPictureTaskServiceImpl.java:95)- 处理前收集到输入文件数量: 0
2025-07-28 14:36:41,466 INFO (DoPictureTaskServiceImpl.java:120)- Batch mode: inputPath = D:\data\joy\p_input\idcard.jpg
2025-07-28 14:36:41,471 INFO (DockerRunner.java:93)- Batch mode: inputDir = D:\data\joy\p_input\idcard.jpg
2025-07-28 14:36:41,471 INFO (DockerRunner.java:143)- Generated Docker command: docker run --rm -v D:/data/joy/p_input/idcard.jpg:/app/input -v D:/data/joy/p_output:/app/output mosaic_app:latest -type video -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:36:41,471 INFO (DockerRunner.java:36)- 准备执行 Docker 命令:
docker run --rm -v D:/data/joy/p_input/idcard.jpg:/app/input -v D:/data/joy/p_output:/app/output mosaic_app:latest -type video -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:36:41,497 ERROR (DockerRunner.java:56)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:39)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:123)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:36:41,497 WARN (DoPictureTaskServiceImpl.java:135)- Docker 处理失败，退出码: -1
2025-07-28 14:36:41,718 WARN (DoPictureTaskServiceImpl.java:308)- 目录不存在: /data/joy/p_output
2025-07-28 14:36:41,736 INFO (DoPictureTaskServiceImpl.java:210)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:36:42,502 INFO (DoPictureTaskServiceImpl.java:239)- 执行图片脱敏总时长为: 1秒
2025-07-28 14:36:42,502 INFO (DoPictureTaskServiceImpl.java:240)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:36:42,504 INFO (DoPictureTaskServiceImpl.java:241)- 任务ID【22】,执行完毕
2025-07-28 14:48:58,995 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:49:00,090 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 14:49:00,091 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:49:00,092 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:49:00,093 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:49:00,166 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 14:49:00,168 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:49:00,168 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 14:49:00,201 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:49:00,319 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-28 14:49:12,042 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 7552 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:49:12,048 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:49:14,621 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:49:14,623 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:49:15,666 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1026 ms. Found 88 JPA repository interfaces.
2025-07-28 14:49:16,245 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:49:16,246 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:49:16,248 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:49:16,248 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:49:16,251 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:49:16,252 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:49:16,254 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:49:16,254 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:49:16,254 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:49:17,072 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:49:17,094 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:49:17,097 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:49:18,284 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:49:18,575 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:49:18,606 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:49:18,607 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:49:18,607 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:49:18,607 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:49:18,608 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:49:18,608 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:49:18,613 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:49:18,615 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:49:21,567 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:49:22,254 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:49:22,489 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:49:23,037 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:49:23,628 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:49:31,571 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:49:31,684 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:49:36,931 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 14:49:37,387 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 24763 ms
2025-07-28 14:49:43,079 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 14:49:43,313 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:49:43,426 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:49:43,426 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:49:43,450 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:49:43,456 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:49:43,456 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:49:43,456 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:49:43,458 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@64d23a83
2025-07-28 14:49:43,458 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:49:47,718 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@a9781d8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@238709e6, org.springframework.security.web.context.SecurityContextPersistenceFilter@6f6ce71d, org.springframework.security.web.header.HeaderWriterFilter@6291cdb4, org.springframework.security.web.authentication.logout.LogoutFilter@31eb7dd4, org.springframework.web.filter.CorsFilter@7c9e2e07, com.wzsec.modules.security.security.TokenFilter@15d87a4a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@28a16598, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4c47eb9b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5a5115be, org.springframework.security.web.session.SessionManagementFilter@65fcdaeb, org.springframework.security.web.access.ExceptionTranslationFilter@4ced30b9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@439e956c]
2025-07-28 14:49:47,787 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:49:52,086 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:49:52,087 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:49:52,087 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:49:52,087 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:49:52,087 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:49:52,087 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:49:52,087 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:49:52,087 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@c45e4d9
2025-07-28 14:49:52,310 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:49:52,422 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 14:49:52,427 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:49:52,428 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:49:52,428 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:49:52,428 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:49:52,429 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:49:52,429 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:49:52,429 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:49:52,429 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:49:52,429 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:49:52,433 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:49:52,433 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:49:52,433 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:49:52,447 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 41.227 seconds (JVM running for 43.654)
2025-07-28 14:49:53,288 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 14:49:53,288 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:50:28,035 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:50:28,036 INFO (FrameworkServlet.java:547)- Completed initialization in 1 ms
2025-07-28 14:50:32,985 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 14:50:33,745 WARN (DoPictureTaskServiceImpl.java:304)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:50:33,745 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 14:51:55,319 INFO (DockerRunner.java:68)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input\idcard.jpg:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:51:55,366 ERROR (DockerRunner.java:88)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:71)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:118)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:51:55,366 WARN (DoPictureTaskServiceImpl.java:131)- Docker 处理失败，退出码: -1
2025-07-28 14:51:55,588 WARN (DoPictureTaskServiceImpl.java:304)- 目录不存在: /data/joy/p_output
2025-07-28 14:51:55,603 INFO (DoPictureTaskServiceImpl.java:206)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:51:56,426 INFO (DoPictureTaskServiceImpl.java:235)- 执行图片脱敏总时长为: 83秒
2025-07-28 14:51:56,426 INFO (DoPictureTaskServiceImpl.java:236)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:51:56,427 INFO (DoPictureTaskServiceImpl.java:237)- 任务ID【22】,执行完毕
2025-07-28 14:51:57,401 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 14:52:57,018 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 21840 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 14:52:57,025 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 14:52:59,902 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 14:52:59,904 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 14:53:01,794 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1864 ms. Found 88 JPA repository interfaces.
2025-07-28 14:53:03,397 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 14:53:03,399 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:53:03,405 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:53:03,405 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:53:03,414 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:53:03,420 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 14:53:03,424 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 14:53:03,424 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:53:03,424 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:53:04,792 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 14:53:04,827 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 14:53:04,832 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 14:53:06,248 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 14:53:06,604 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 14:53:06,651 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 14:53:06,652 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 14:53:06,653 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 14:53:06,653 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 14:53:06,654 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 14:53:06,655 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 14:53:06,660 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 14:53:06,666 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 14:53:10,554 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 14:53:11,464 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 14:53:11,793 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 14:53:12,559 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 14:53:13,313 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 14:53:23,018 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 14:53:23,185 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 14:53:32,732 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 14:53:33,360 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 35665 ms
2025-07-28 14:53:41,364 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 14:53:41,627 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:53:41,747 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:53:41,748 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:53:41,772 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:53:41,783 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:53:41,784 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:53:41,784 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:53:41,784 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@d863a65
2025-07-28 14:53:41,785 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:53:46,327 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4d1bbad3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d850252, org.springframework.security.web.context.SecurityContextPersistenceFilter@3894400, org.springframework.security.web.header.HeaderWriterFilter@25847567, org.springframework.security.web.authentication.logout.LogoutFilter@6716d006, org.springframework.web.filter.CorsFilter@6fc1a561, com.wzsec.modules.security.security.TokenFilter@36881f62, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@71f55a59, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4046b407, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c791c14, org.springframework.security.web.session.SessionManagementFilter@5f7abc9e, org.springframework.security.web.access.ExceptionTranslationFilter@a858d36, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@26714a4a]
2025-07-28 14:53:46,385 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 14:53:51,725 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 14:53:51,727 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:53:51,727 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 14:53:51,727 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 14:53:51,727 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:53:51,727 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:53:51,727 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 14:53:51,727 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@43567fca
2025-07-28 14:53:51,991 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 14:53:52,103 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 14:53:52,109 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 14:53:52,110 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 14:53:52,110 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 14:53:52,111 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 14:53:52,111 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:53:52,111 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 14:53:52,111 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 14:53:52,111 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 14:53:52,111 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 14:53:52,115 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 14:53:52,115 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 14:53:52,116 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:53:52,133 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 56.283 seconds (JVM running for 59.49)
2025-07-28 14:53:53,449 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 14:53:53,449 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 14:54:42,021 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 14:54:42,023 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-28 14:54:47,122 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 14:54:47,969 WARN (DoPictureTaskServiceImpl.java:304)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 14:54:47,969 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 14:58:50,681 INFO (DockerRunner.java:68)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input\idcard.jpg:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 14:58:50,733 ERROR (DockerRunner.java:88)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:71)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:118)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 14:58:50,734 WARN (DoPictureTaskServiceImpl.java:131)- Docker 处理失败，退出码: -1
2025-07-28 14:58:50,967 WARN (DoPictureTaskServiceImpl.java:304)- 目录不存在: /data/joy/p_output
2025-07-28 14:58:50,991 INFO (DoPictureTaskServiceImpl.java:206)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 14:58:51,849 INFO (DoPictureTaskServiceImpl.java:235)- 执行图片脱敏总时长为: 243秒
2025-07-28 14:58:51,849 INFO (DoPictureTaskServiceImpl.java:236)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 14:58:51,849 INFO (DoPictureTaskServiceImpl.java:237)- 任务ID【22】,执行完毕
2025-07-28 14:58:54,826 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 15:00:44,428 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 24384 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 15:00:44,436 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 15:00:46,926 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 15:00:46,928 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 15:00:47,973 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1025 ms. Found 88 JPA repository interfaces.
2025-07-28 15:00:48,543 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 15:00:48,544 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 15:00:48,546 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:00:48,547 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:00:48,549 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:00:48,551 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 15:00:48,552 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 15:00:48,552 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:00:48,553 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:00:49,329 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 15:00:49,346 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 15:00:49,349 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 15:00:50,367 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 15:00:50,631 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 15:00:50,660 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 15:00:50,661 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 15:00:50,661 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 15:00:50,662 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 15:00:50,662 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 15:00:50,662 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 15:00:50,666 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 15:00:50,668 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 15:00:53,455 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 15:00:54,137 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 15:00:54,353 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 15:00:54,933 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 15:00:55,469 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 15:01:01,982 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 15:01:02,039 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 15:01:06,249 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 15:01:06,700 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 21720 ms
2025-07-28 15:01:12,173 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 15:01:12,372 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 15:01:12,477 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 15:01:12,477 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 15:01:12,499 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 15:01:12,504 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 15:01:12,504 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 15:01:12,504 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 15:01:12,505 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@179273ff
2025-07-28 15:01:12,505 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 15:01:16,048 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2e724561, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44352a38, org.springframework.security.web.context.SecurityContextPersistenceFilter@2c1e93ff, org.springframework.security.web.header.HeaderWriterFilter@3590ccd, org.springframework.security.web.authentication.logout.LogoutFilter@3e445475, org.springframework.web.filter.CorsFilter@3501c1de, com.wzsec.modules.security.security.TokenFilter@6afafefa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6bf1cb6f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@199f957, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5e90de14, org.springframework.security.web.session.SessionManagementFilter@4415b9fa, org.springframework.security.web.access.ExceptionTranslationFilter@3cb49d5e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5abf00f2]
2025-07-28 15:01:16,094 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 15:01:19,972 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 15:01:19,973 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 15:01:19,973 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 15:01:19,973 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 15:01:19,973 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 15:01:19,974 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 15:01:19,974 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 15:01:19,974 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@60357655
2025-07-28 15:01:20,204 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 15:01:20,315 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 15:01:20,323 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 15:01:20,325 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 15:01:20,325 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 15:01:20,325 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 15:01:20,325 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 15:01:20,325 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 15:01:20,325 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:01:20,325 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 15:01:20,325 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:01:20,329 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 15:01:20,329 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 15:01:20,329 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 15:01:20,344 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 36.671 seconds (JVM running for 39.041)
2025-07-28 15:01:21,181 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 15:01:21,181 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 15:01:25,541 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 15:01:25,542 INFO (FrameworkServlet.java:547)- Completed initialization in 1 ms
2025-07-28 15:01:30,893 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 15:01:31,678 WARN (DoPictureTaskServiceImpl.java:314)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 15:01:31,679 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 15:01:31,679 INFO (DoPictureTaskServiceImpl.java:111)- 检测到目录: \data\joy\p_input\idcard.jpg
2025-07-28 15:01:31,679 INFO (DoPictureTaskServiceImpl.java:125)- 批处理模式 - inputPath: \data\joy\p_input\idcard.jpg, isBatch: true
2025-07-28 15:01:31,706 INFO (DockerRunner.java:68)- DockerRunner 参数 - inputPath: \data\joy\p_input\idcard.jpg, isBatch: true, fileName: null
2025-07-28 15:01:38,359 INFO (DockerRunner.java:72)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input\idcard.jpg:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 15:01:38,395 ERROR (DockerRunner.java:92)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:75)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:128)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 15:01:38,395 WARN (DoPictureTaskServiceImpl.java:141)- Docker 处理失败，退出码: -1
2025-07-28 15:01:38,603 WARN (DoPictureTaskServiceImpl.java:314)- 目录不存在: /data/joy/p_output
2025-07-28 15:01:38,624 INFO (DoPictureTaskServiceImpl.java:216)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 15:01:39,413 INFO (DoPictureTaskServiceImpl.java:245)- 执行图片脱敏总时长为: 8秒
2025-07-28 15:01:39,414 INFO (DoPictureTaskServiceImpl.java:246)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 15:01:39,414 INFO (DoPictureTaskServiceImpl.java:247)- 任务ID【22】,执行完毕
2025-07-28 15:05:33,284 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 15:05:34,120 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-28 15:05:34,121 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 15:05:34,121 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 15:05:34,121 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 15:05:34,140 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 15:05:34,141 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 15:05:34,141 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 15:05:34,154 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 15:05:34,185 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-28 15:05:44,789 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 4820 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-28 15:05:44,797 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-28 15:05:47,256 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-28 15:05:47,258 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-28 15:05:48,303 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1027 ms. Found 88 JPA repository interfaces.
2025-07-28 15:05:48,982 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-28 15:05:48,982 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 15:05:48,987 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:05:48,987 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:05:48,993 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:05:48,995 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-28 15:05:48,997 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-28 15:05:48,997 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:05:48,997 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:05:50,126 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-28 15:05:50,146 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-28 15:05:50,150 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-28 15:05:51,358 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-28 15:05:51,685 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-28 15:05:51,729 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-28 15:05:51,729 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-28 15:05:51,730 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-28 15:05:51,730 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-28 15:05:51,731 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-28 15:05:51,731 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-28 15:05:51,736 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-28 15:05:51,739 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-28 15:05:55,094 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-28 15:05:55,755 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-28 15:05:55,983 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-28 15:05:56,546 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-28 15:05:57,079 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-28 15:06:05,680 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-28 15:06:05,740 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-28 15:06:10,506 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-28 15:06:10,914 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 25593 ms
2025-07-28 15:06:16,241 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-28 15:06:16,434 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 15:06:16,537 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 15:06:16,538 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 15:06:16,559 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 15:06:16,566 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 15:06:16,566 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-28 15:06:16,566 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 15:06:16,567 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@68817cd7
2025-07-28 15:06:16,567 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-28 15:06:20,230 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@168d5f34, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@122a6877, org.springframework.security.web.context.SecurityContextPersistenceFilter@67196a65, org.springframework.security.web.header.HeaderWriterFilter@438e8297, org.springframework.security.web.authentication.logout.LogoutFilter@31102e84, org.springframework.web.filter.CorsFilter@2ae032c4, com.wzsec.modules.security.security.TokenFilter@35e246b2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@63730298, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@586e39e1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c4b2c69, org.springframework.security.web.session.SessionManagementFilter@6d0c8cd0, org.springframework.security.web.access.ExceptionTranslationFilter@5ff00e9a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5cd16573]
2025-07-28 15:06:20,281 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-28 15:06:24,267 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-28 15:06:24,268 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 15:06:24,268 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-28 15:06:24,268 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-28 15:06:24,268 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 15:06:24,268 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 15:06:24,268 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-28 15:06:24,268 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@562c6d5c
2025-07-28 15:06:24,462 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-28 15:06:24,558 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-28 15:06:24,564 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-28 15:06:24,565 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-28 15:06:24,565 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-28 15:06:24,565 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-28 15:06:24,565 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 15:06:24,565 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-28 15:06:24,567 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-28 15:06:24,567 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-28 15:06:24,567 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-28 15:06:24,575 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-28 15:06:24,575 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-28 15:06:24,575 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 15:06:24,596 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 40.6 seconds (JVM running for 43.136)
2025-07-28 15:06:25,428 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-28 15:06:25,428 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-28 15:06:29,809 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-28 15:06:29,811 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-28 15:06:35,166 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 15:06:37,284 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_input/idcard.jpg
2025-07-28 15:06:37,285 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 15:06:37,285 INFO (DoPictureTaskServiceImpl.java:120)- 根据扩展名判断为文件: /data/joy/p_input/idcard.jpg, 目录: \data\joy\p_input, 文件名: idcard.jpg
2025-07-28 15:06:37,285 INFO (DoPictureTaskServiceImpl.java:141)- 单文件模式 - inputPath: \data\joy\p_input, isBatch: false, fileName: idcard.jpg
2025-07-28 15:06:37,308 INFO (DockerRunner.java:68)- DockerRunner 参数 - inputPath: \data\joy\p_input, isBatch: false, fileName: idcard.jpg
2025-07-28 15:06:51,554 INFO (DockerRunner.java:72)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input/idcard.jpg -output /app/output/idcard.jpg -target yolov8_license_plate -target yolov8n-face
2025-07-28 15:06:51,580 ERROR (DockerRunner.java:92)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:75)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:148)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 15:06:51,581 WARN (DoPictureTaskServiceImpl.java:161)- Docker 处理失败，退出码: -1
2025-07-28 15:06:51,784 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_output
2025-07-28 15:06:51,810 INFO (DoPictureTaskServiceImpl.java:236)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 15:06:52,575 INFO (DoPictureTaskServiceImpl.java:265)- 执行图片脱敏总时长为: 16秒
2025-07-28 15:06:52,577 INFO (DoPictureTaskServiceImpl.java:266)- 任务ID【22】,执行图片脱敏任务结束
2025-07-28 15:06:52,577 INFO (DoPictureTaskServiceImpl.java:267)- 任务ID【22】,执行完毕
2025-07-28 15:07:06,215 INFO (DoPictureTaskServiceImpl.java:59)- 开始执行图片脱敏任务
2025-07-28 15:07:06,495 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_input
2025-07-28 15:07:06,496 INFO (DoPictureTaskServiceImpl.java:94)- 处理前收集到输入文件数量: 0
2025-07-28 15:07:06,496 INFO (DoPictureTaskServiceImpl.java:130)- 根据路径特征判断为目录: \data\joy\p_input
2025-07-28 15:07:06,496 INFO (DoPictureTaskServiceImpl.java:145)- 批处理模式 - inputPath: \data\joy\p_input, isBatch: true
2025-07-28 15:07:06,496 INFO (DockerRunner.java:68)- DockerRunner 参数 - inputPath: \data\joy\p_input, isBatch: true, fileName: null
2025-07-28 15:07:37,429 INFO (DockerRunner.java:72)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\p_input:/app/input -v \data\joy\p_output:/app/output mosaic_app:latest -type image -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 15:07:37,436 ERROR (DockerRunner.java:92)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:75)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl.execution(DoPictureTaskServiceImpl.java:148)
	at com.wzsec.dotask.mask.service.impl.DoPictureTaskServiceImpl$$FastClassBySpringCGLIB$$b3a27dc1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 15:07:37,436 WARN (DoPictureTaskServiceImpl.java:161)- Docker 处理失败，退出码: -1
2025-07-28 15:07:37,593 WARN (DoPictureTaskServiceImpl.java:353)- 目录不存在: /data/joy/p_output
2025-07-28 15:07:37,593 INFO (DoPictureTaskServiceImpl.java:236)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 15:07:38,095 INFO (DoPictureTaskServiceImpl.java:265)- 执行图片脱敏总时长为: 31秒
2025-07-28 15:07:38,095 INFO (DoPictureTaskServiceImpl.java:266)- 任务ID【21】,执行图片脱敏任务结束
2025-07-28 15:07:38,095 INFO (DoPictureTaskServiceImpl.java:267)- 任务ID【21】,执行完毕
2025-07-28 15:08:02,817 INFO (DoVideoTaskServiceImpl.java:58)- 开始执行视频脱敏任务
2025-07-28 15:08:03,129 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_input/v1.mp4
2025-07-28 15:08:03,130 INFO (DoVideoTaskServiceImpl.java:91)- 处理前收集到输入文件数量: 0
2025-07-28 15:08:03,130 INFO (DoVideoTaskServiceImpl.java:116)- 根据扩展名判断为视频文件: /data/joy/v_input/v1.mp4, 目录: \data\joy\v_input, 文件名: v1.mp4
2025-07-28 15:08:03,130 INFO (DockerRunner.java:68)- DockerRunner 参数 - inputPath: \data\joy\v_input, isBatch: false, fileName: v1.mp4
2025-07-28 15:08:27,722 INFO (DockerRunner.java:72)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\v_input:/app/input -v \data\joy\v_output:/app/output mosaic_app:latest -type video -input /app/input/v1.mp4 -output /app/output/v1.mp4 -target yolov8_license_plate -target yolov8n-face
2025-07-28 15:08:27,730 ERROR (DockerRunner.java:92)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:75)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl.execution(DoVideoTaskServiceImpl.java:142)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl$$FastClassBySpringCGLIB$$d85a2c64.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 15:08:27,731 WARN (DoVideoTaskServiceImpl.java:155)- Docker 处理失败，退出码: -1
2025-07-28 15:08:27,885 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_output
2025-07-28 15:08:27,885 INFO (DoVideoTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 15:08:28,458 INFO (DoVideoTaskServiceImpl.java:251)- 执行视频脱敏总时长为: 25秒
2025-07-28 15:08:28,458 INFO (DoVideoTaskServiceImpl.java:252)- 任务ID【10】,执行视频脱敏任务结束
2025-07-28 15:08:28,458 INFO (DoVideoTaskServiceImpl.java:253)- 任务ID【10】,执行完毕
2025-07-28 15:09:10,520 INFO (DoVideoTaskServiceImpl.java:58)- 开始执行视频脱敏任务
2025-07-28 15:09:10,818 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_input
2025-07-28 15:09:10,818 INFO (DoVideoTaskServiceImpl.java:91)- 处理前收集到输入文件数量: 0
2025-07-28 15:09:10,818 INFO (DoVideoTaskServiceImpl.java:126)- 根据路径特征判断为目录: \data\joy\v_input
2025-07-28 15:09:10,818 INFO (DockerRunner.java:68)- DockerRunner 参数 - inputPath: \data\joy\v_input, isBatch: true, fileName: null
2025-07-28 15:12:19,467 INFO (DockerRunner.java:72)- 准备执行 Docker 命令:
docker run --rm -v \data\joy\v_input:/app/input -v \data\joy\v_output:/app/output mosaic_app:latest -type video -input /app/input -output /app/output -target yolov8_license_plate -target yolov8n-face
2025-07-28 15:12:19,484 ERROR (DockerRunner.java:92)- Docker 执行异常: 
java.io.IOException: Cannot run program "docker": CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at com.wzsec.utils.DockerRunner.runDocker(DockerRunner.java:75)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl.execution(DoVideoTaskServiceImpl.java:142)
	at com.wzsec.dotask.mask.service.impl.DoVideoTaskServiceImpl$$FastClassBySpringCGLIB$$d85a2c64.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: CreateProcess error=2, 系统找不到指定的文件。
	at java.lang.ProcessImpl.create(Native Method)
	at java.lang.ProcessImpl.<init>(ProcessImpl.java:386)
	at java.lang.ProcessImpl.start(ProcessImpl.java:137)
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1029)
	... 17 common frames omitted
2025-07-28 15:12:19,485 WARN (DoVideoTaskServiceImpl.java:155)- Docker 处理失败，退出码: -1
2025-07-28 15:12:19,715 WARN (DoVideoTaskServiceImpl.java:290)- 目录不存在: /data/joy/v_output
2025-07-28 15:12:19,716 INFO (DoVideoTaskServiceImpl.java:222)- 文件信息收集完成，输入文件数量: 0, 成功处理: 0, 处理失败: 0
2025-07-28 15:12:20,202 INFO (DoVideoTaskServiceImpl.java:251)- 执行视频脱敏总时长为: 189秒
2025-07-28 15:12:20,202 INFO (DoVideoTaskServiceImpl.java:252)- 任务ID【9】,执行视频脱敏任务结束
2025-07-28 15:12:20,203 INFO (DoVideoTaskServiceImpl.java:253)- 任务ID【9】,执行完毕
