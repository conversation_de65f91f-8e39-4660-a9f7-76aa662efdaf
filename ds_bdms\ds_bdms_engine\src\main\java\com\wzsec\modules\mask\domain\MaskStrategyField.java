package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-09
*/
@Entity
@Data
@Table(name="sdd_mask_strategy_field")
public class MaskStrategyField implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 策略表id */
    @Column(name = "stategytableid")
    private Integer stategytableid;

    /** 表ID */
    @Column(name = "tableid")
    private String tableid;

    private Boolean extractfield;

    /** 表名 */
    @Column(name = "tabename")
    private String tabename;

    /** 表中文名 */
    @Column(name = "tabcname")
    private String tabcname;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 字段ID */
    @Column(name = "fieldid")
    private String fieldid;

    /** 字段名 */
    @Column(name = "fieldename")
    private String fieldename;

    /** 字段中文名 */
    @Column(name = "fieldcname")
    private String fieldcname;

    /** 字段类型 */
    @Column(name = "fieldtype")
    private String fieldtype;

    /** 敏感度 */
    @Column(name = "sen_level")
    private String senLevel;

    /** 规则id */
    @Column(name = "ruleid")
    private Integer ruleid;

    /** 算法ID */
    @Column(name = "algorithmid")
    private Integer algorithmid;

    /** 参数 */
    @Column(name = "param")
    private String param;

    /** 密钥 */
    @Column(name = "secretkey")
    private String secretkey;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    /** 备用字段6 */
    @Column(name = "sparefield6")
    private String sparefield6;

    public void copy(MaskStrategyField source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
