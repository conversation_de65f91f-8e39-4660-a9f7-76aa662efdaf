package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.FileTaskConfig;
import com.wzsec.modules.mask.service.dto.FileTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class FileTaskConfigMapperImpl implements FileTaskConfigMapper {

    @Override
    public FileTaskConfigDto toDto(FileTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        FileTaskConfigDto fileTaskConfigDto = new FileTaskConfigDto();

        fileTaskConfigDto.setApprovalstatus( entity.getApprovalstatus() );
        fileTaskConfigDto.setApprovaltime( entity.getApprovaltime() );
        fileTaskConfigDto.setApprover( entity.getApprover() );
        fileTaskConfigDto.setCreatetime( entity.getCreatetime() );
        fileTaskConfigDto.setCreateuser( entity.getCreateuser() );
        fileTaskConfigDto.setCron( entity.getCron() );
        fileTaskConfigDto.setDataprovider( entity.getDataprovider() );
        fileTaskConfigDto.setDatause( entity.getDatause() );
        fileTaskConfigDto.setEnablemoderation( entity.getEnablemoderation() );
        fileTaskConfigDto.setExecutionstate( entity.getExecutionstate() );
        fileTaskConfigDto.setExtractfield( entity.getExtractfield() );
        fileTaskConfigDto.setId( entity.getId() );
        fileTaskConfigDto.setInputdatasourceid( entity.getInputdatasourceid() );
        fileTaskConfigDto.setInputfiletype( entity.getInputfiletype() );
        fileTaskConfigDto.setInputpath( entity.getInputpath() );
        fileTaskConfigDto.setIswatermark( entity.getIswatermark() );
        fileTaskConfigDto.setMaskstrategystr( entity.getMaskstrategystr() );
        fileTaskConfigDto.setOutputdatasourceid( entity.getOutputdatasourceid() );
        fileTaskConfigDto.setOutputdirectory( entity.getOutputdirectory() );
        fileTaskConfigDto.setOutputfiletype( entity.getOutputfiletype() );
        fileTaskConfigDto.setOutputtype( entity.getOutputtype() );
        fileTaskConfigDto.setRemark( entity.getRemark() );
        fileTaskConfigDto.setSparefield1( entity.getSparefield1() );
        fileTaskConfigDto.setSparefield2( entity.getSparefield2() );
        fileTaskConfigDto.setSparefield3( entity.getSparefield3() );
        fileTaskConfigDto.setSparefield4( entity.getSparefield4() );
        fileTaskConfigDto.setSparefield5( entity.getSparefield5() );
        fileTaskConfigDto.setSplitstr( entity.getSplitstr() );
        fileTaskConfigDto.setState( entity.getState() );
        fileTaskConfigDto.setStrategyid( entity.getStrategyid() );
        fileTaskConfigDto.setSubmitmethod( entity.getSubmitmethod() );
        fileTaskConfigDto.setTablename( entity.getTablename() );
        fileTaskConfigDto.setTaskexecuteengine( entity.getTaskexecuteengine() );
        fileTaskConfigDto.setTaskname( entity.getTaskname() );
        fileTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        fileTaskConfigDto.setUpdateuser( entity.getUpdateuser() );

        return fileTaskConfigDto;
    }

    @Override
    public List<FileTaskConfigDto> toDto(List<FileTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FileTaskConfigDto> list = new ArrayList<FileTaskConfigDto>( entityList.size() );
        for ( FileTaskConfig fileTaskConfig : entityList ) {
            list.add( toDto( fileTaskConfig ) );
        }

        return list;
    }

    @Override
    public FileTaskConfig toEntity(FileTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        FileTaskConfig fileTaskConfig = new FileTaskConfig();

        fileTaskConfig.setApprovalstatus( dto.getApprovalstatus() );
        fileTaskConfig.setApprovaltime( dto.getApprovaltime() );
        fileTaskConfig.setApprover( dto.getApprover() );
        fileTaskConfig.setCreatetime( dto.getCreatetime() );
        fileTaskConfig.setCreateuser( dto.getCreateuser() );
        fileTaskConfig.setCron( dto.getCron() );
        fileTaskConfig.setDataprovider( dto.getDataprovider() );
        fileTaskConfig.setDatause( dto.getDatause() );
        fileTaskConfig.setEnablemoderation( dto.getEnablemoderation() );
        fileTaskConfig.setExecutionstate( dto.getExecutionstate() );
        fileTaskConfig.setExtractfield( dto.getExtractfield() );
        fileTaskConfig.setId( dto.getId() );
        fileTaskConfig.setInputdatasourceid( dto.getInputdatasourceid() );
        fileTaskConfig.setInputfiletype( dto.getInputfiletype() );
        fileTaskConfig.setInputpath( dto.getInputpath() );
        fileTaskConfig.setIswatermark( dto.getIswatermark() );
        fileTaskConfig.setMaskstrategystr( dto.getMaskstrategystr() );
        fileTaskConfig.setOutputdatasourceid( dto.getOutputdatasourceid() );
        fileTaskConfig.setOutputdirectory( dto.getOutputdirectory() );
        fileTaskConfig.setOutputfiletype( dto.getOutputfiletype() );
        fileTaskConfig.setOutputtype( dto.getOutputtype() );
        fileTaskConfig.setRemark( dto.getRemark() );
        fileTaskConfig.setSparefield1( dto.getSparefield1() );
        fileTaskConfig.setSparefield2( dto.getSparefield2() );
        fileTaskConfig.setSparefield3( dto.getSparefield3() );
        fileTaskConfig.setSparefield4( dto.getSparefield4() );
        fileTaskConfig.setSparefield5( dto.getSparefield5() );
        fileTaskConfig.setSplitstr( dto.getSplitstr() );
        fileTaskConfig.setState( dto.getState() );
        fileTaskConfig.setStrategyid( dto.getStrategyid() );
        fileTaskConfig.setSubmitmethod( dto.getSubmitmethod() );
        fileTaskConfig.setTablename( dto.getTablename() );
        fileTaskConfig.setTaskexecuteengine( dto.getTaskexecuteengine() );
        fileTaskConfig.setTaskname( dto.getTaskname() );
        fileTaskConfig.setUpdatetime( dto.getUpdatetime() );
        fileTaskConfig.setUpdateuser( dto.getUpdateuser() );

        return fileTaskConfig;
    }

    @Override
    public List<FileTaskConfig> toEntity(List<FileTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FileTaskConfig> list = new ArrayList<FileTaskConfig>( dtoList.size() );
        for ( FileTaskConfigDto fileTaskConfigDto : dtoList ) {
            list.add( toEntity( fileTaskConfigDto ) );
        }

        return list;
    }
}
