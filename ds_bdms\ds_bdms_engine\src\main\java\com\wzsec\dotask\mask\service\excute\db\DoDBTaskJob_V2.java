package com.wzsec.dotask.mask.service.excute.db;

import cn.god.mask.common.Algorithm;
import cn.god.mask.common.MaskAlgFactory;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.mongodb.client.MongoClient;
import com.wzsec.config.Log;
import com.wzsec.modules.alarm.config.MonitorRiskAlarmData;
import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.mask.domain.DBTaskConfig;
import com.wzsec.modules.mask.domain.DBTaskResult;
import com.wzsec.modules.mask.domain.TaskProgressModel;
import com.wzsec.modules.mask.repository.AlgorithmRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.DBTaskConfigDto;
import com.wzsec.modules.mask.service.dto.DBTaskResultDto;
import com.wzsec.modules.mask.service.dto.MaskStrategyTableDto;
import com.wzsec.modules.mask.service.mapper.DbtaskconfigMapper;
import com.wzsec.modules.mask.service.mapper.DbtaskresultMapper;
import com.wzsec.modules.sdd.metadata.domain.MetaField;
import com.wzsec.modules.sdd.metadata.service.MetaFieldService;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.statistics.domain.MaskTaskresultrecords;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.proxy.common.rule.ProRuleFactory;
import com.wzsec.proxy.common.utils.SpringUtil;
import com.wzsec.utils.*;
import com.wzsec.utils.database.DBMaskUtils;
import com.wzsec.utils.database.DatabaseUtil;
import com.wzsec.utils.database.HiveUtil;
import com.wzsec.utils.database.MongoDBUtil;
import com.wzsec.utils.rule.Rule4AlgorithmUtil;
import com.wzsec.watermark.alg.WaterMarkAlgFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.hive.jdbc.HiveStatement;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.*;
import java.util.Date;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 执行数据库脱敏，设置获取分批获取数据版本(MYSQL-OK)，会创建临时表导致数据库服务器临时空间占满，影响业务使用
 * @date 2021年2月4日 下午2:21:31
 */
public class DoDBTaskJob_V2 {

    private static DmAlarmdisposalService maskAlarmdisposalService = SpringUtil.getApplicationContext().getBean(DmAlarmdisposalService.class);

    private final static Logger log = LoggerFactory.getLogger(DoDBTaskJob_V2.class);

    private DBTaskConfigService dbTaskConfigService;

    private DBTaskResultService dBTaskResultService;

    private DatasourceService dataSourceService;

    private MaskStrategyFieldService maskStrategyFieldService;

    private MaskStrategyTableService maskStrategyTableService;

    private UserService userService;

    private AlgorithmService algorithmService;

    private TaskProgressModel taskProgressModel;

    private DbtaskconfigMapper dbtaskconfigMapper;

    private DbtaskresultMapper dbtaskresultMapper;

    private DmAlarmdisposalService dmAlarmdisposalService;

    private SystemLogService logService;

    private MetaFieldService metaFieldService;

    private SdkApplyconfigRepository sdkApplyconfigRepository;

    private SdkOperationrecordRepository sdkOperationrecordRepository;

    private Integer taskid;
    private String submituser;

    private MaskTaskresultrecordsService maskTaskresultrecordsService;

    @Autowired
    private AlgorithmRepository algorithmRepository;

    Long pageSize = Long.parseLong(ConstEngine.sddEngineConfs.get("dbtask.readdatacount"));// 一次读取1万条数据再写入到数据库
    Integer writeDataCount = Integer.parseInt(ConstEngine.sddEngineConfs.get("dbtask.writedatacount"));

    String startTime = null;
    String endTime = null;
    String totalTime = null;
    String updateTime = null;

    String taskname = null;
    String strategyname = null;
    String out_type = null;

    String in_dbtype = null;
    String in_fieldNameStr = null;
    String in_dbname = null;
    String in_tabname = null;
    String in_drivername = null;
    String in_url = null;
    String in_username = null;
    String in_password = null;
    String in_limitingcondition = null;

    String out_dbtype = null;
    String out_dbname = null;
    String out_tabname = null;
    String out_drivername = null;
    String out_url = null;
    String out_username = null;
    String out_password = null;
    String out_path = null;
    String out_ip = null;
    String original_table_rewriting = null;

    Integer resultid = null;
    Long totalLineCount = 0l;
    Long maskLineCount = 0l;
    int limitStart = 0;
    int limitEnd = 0;

    Long saveLineCount = 0l;
    int exampleLineCount = 5;
    String beforemaskdata = "";
    String aftermaskdata = "";

    boolean isSuccess = false;
    boolean flag = false;
    Map<String, Object> maskAlgoInfoMap = null;

    Map<String, String> abnormalHandleMap = null; //异常数据处理

    Map<String, String> abnormalHandlingAlarm = null; //异常数据处理告警

    List<String> fieldNameStrList = new ArrayList<>();
    DBTaskConfig dbTaskConfig = null;
    DBTaskResult dBTaskResult = null;
    HashMap<String, cn.god.mask.common.Algorithm> algorithmMap = null;

    Boolean isWatermark = null;
    String watermarkField = null;
    String waterMarkInfo = null;

    MongoClient mongoClient = null;
    MongoClient outMongoClient = null;


    public DoDBTaskJob_V2(DBTaskConfigService dbTaskConfigService, DatasourceService datasourceService, MaskStrategyFieldService maskStrategyFieldService,
                          MaskStrategyTableService maskStrategyTableService, DBTaskResultService dBTaskResultService, UserService userService,
                          AlgorithmService algorithmService, DbtaskconfigMapper dbtaskconfigMapper, DbtaskresultMapper dbtaskresultMapper,
                          TaskProgressModel taskProgressModel, Integer id, String submituser, DmAlarmdisposalService dmAlarmdisposalService,
                          SystemLogService logService, MaskTaskresultrecordsService maskTaskresultrecordsService, MetaFieldService metaFieldService,SdkOperationrecordRepository sdkOperationrecordRepository
                          ,SdkApplyconfigRepository sdkApplyconfigRepository) {
        this.dbTaskConfigService = dbTaskConfigService;
        this.dataSourceService = datasourceService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.maskStrategyTableService = maskStrategyTableService;
        this.dBTaskResultService = dBTaskResultService;
        this.userService = userService;
        this.algorithmService = algorithmService;
        this.taskProgressModel = taskProgressModel;
        this.dbtaskconfigMapper = dbtaskconfigMapper;
        this.dbtaskresultMapper = dbtaskresultMapper;
        this.taskid = id;
        this.submituser = submituser;
        this.dmAlarmdisposalService = dmAlarmdisposalService;
        this.logService = logService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.metaFieldService = metaFieldService;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }

    public void exec(String execip,String execport) {
        // 任务开始时间
        startTime = DateUtil.getNowTime();
        long startTimeLong = System.currentTimeMillis();
        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(execip+":"+execport);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();
        try {
            initParam();

            initResult();

//            initJar();

            // 更新任务表
            dbTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTING);
            dbTaskConfigService.update(dbTaskConfig);

            // 数据库传入限制条件字符处理(id<10或limit 10),WHERE 或 LIMIT
            if (StringUtils.isNotBlank(in_limitingcondition)) {
                // LIMIT
                if (in_limitingcondition.toLowerCase().contains("limit")) {
                    String limit = in_limitingcondition.toLowerCase().replaceAll("limit", "").trim();
                    if (in_limitingcondition.contains(",")) {
                        String[] split = limit.split(",");
                        limitStart = Integer.parseInt(split[0].trim());
                        limitEnd = Integer.parseInt(split[1].trim());
                        in_limitingcondition = "LIMIT " + limitStart + "," + limitEnd;
                    } else {
                        limitEnd = Integer.parseInt(limit.trim());
                        in_limitingcondition = "LIMIT " + limitStart + "," + limitEnd;
                    }
                } else {  //WHERE
                    in_limitingcondition = "WHERE " + in_limitingcondition;
                }
            } else {
                in_limitingcondition = "";
            }

            // 获取查询总条数
            totalLineCount = limitEnd > 0 ? limitEnd : getDataCount();

            algorithmMap = algorithmService.getAlgorithmByEName();

            taskProgressModel = new TaskProgressModel();
            taskProgressModel.setTaskid(taskid);
            taskProgressModel.setStarttime(startTime);
            taskProgressModel.setTotalcount(totalLineCount.intValue());

            // 数据库脱敏执行流程
            log.info("开始执行url: {}, 库: {}, 表: {}, 数据库脱敏任务总脱敏行数{}", in_url, in_dbname, in_tabname, totalLineCount);

            if (Const.DB_MONGODB.equalsIgnoreCase(in_dbtype)) {   // MongoDB
                doMaskDataByMongoDB();
            } else {
                doMaskData(in_fieldNameStr, in_dbname, in_tabname, in_url, in_dbname, in_password, in_drivername,
                        in_limitingcondition);
            }


            // 基于原库表进行脱敏,备份原库表,脱敏表改为原表名
            if (Const.STATE_ON.equals(original_table_rewriting)) {  //原表脱敏,需更换表名
                // 原表作为备份表,脱敏表改为原表名
                String originalTableBackupSql = DatabaseUtil.modifyingTableName(in_dbtype, in_dbname, in_tabname, in_tabname + "_bak");
                Boolean originalTable = false;
                try {
                    originalTable = DatabaseUtil.createTable(originalTableBackupSql, in_drivername, in_url, in_username, in_password);
                } catch (Exception e) {
                    log.error("执行原表备份出现异常: {}", e.getStackTrace());
                }

                if (originalTable) {
                    log.info("原表备份成功,生成备份表为: {}", in_tabname + "_bak");
                    String maskToOriginalNameSql = DatabaseUtil.modifyingTableName(in_dbtype, in_dbname, out_tabname, in_tabname);
                    Boolean maskTable = false;
                    try {
                        maskTable = DatabaseUtil.createTable(maskToOriginalNameSql, in_drivername, in_url, in_username, in_password);
                    } catch (Exception e) {
                        log.error("执行脱敏表还原出现异常: {}", e.getStackTrace());
                    }

                    if (maskTable) {
                        log.info("==> 基于原库表进行脱敏完成");
                    }
                }
            }

            // 结束时间
            endTime = DateUtil.getNowTime();
            // 执行总时间
            totalTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(startTime, endTime));
            // 更新时间
            dBTaskResult.setJobendtime(endTime);
            dBTaskResult.setJobtotaltime(totalTime);
            dBTaskResult.setOutputipaddress(out_ip);
            dBTaskResult.setOutputpath(out_path);
            dBTaskResult.setOutputname(out_tabname);
            dBTaskResult.setTotallines(totalLineCount.intValue());
            dBTaskResult.setMasklines(maskLineCount.intValue());
            dBTaskResult.setBeforemaskdata(beforemaskdata);
            dBTaskResult.setAftermaskdata(aftermaskdata);
            if (flag) {
                log.info("任务ID【" + taskid + "】,保存到" + ("1".equals(out_type) ? "库表" : "文件") + "成功");
                // 更新结果
                dBTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_SUCCESS);
                dBTaskResultService.update(dBTaskResult);
                // 更新任务表
                dbTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS);
                dbTaskConfigService.update(dbTaskConfig);
                //插入任务结果记录表
                MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
                maskTaskresultrecords.setTaskname(dBTaskResult.getTaskname());
                maskTaskresultrecords.setTasktype(Const.MASK_TASK_DB);
                maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS_MESSAGE);
                maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
                maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
                maskTaskresultrecordsService.create(maskTaskresultrecords);

                sdkOperationrecord.setObjectname(dBTaskResult.getTaskname());
                sdkOperationrecord.setOperation("数据库脱敏任务执行成功");
            } else {
                log.info("任务ID【" + taskid + "】,保存到" + ("1".equals(out_type) ? "库表" : "文件") + "失败");
                // 更新结果
                dBTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_FAIL);
                dBTaskResultService.update(dBTaskResult);
                // 更新任务表
                dbTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
                dbTaskConfigService.update(dbTaskConfig);
                //插入任务结果记录表
                MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
                maskTaskresultrecords.setTaskname(dBTaskResult.getTaskname());
                maskTaskresultrecords.setTasktype(Const.MASK_TASK_DB);
                maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE);
                maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
                maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", endTime));
                maskTaskresultrecordsService.create(maskTaskresultrecords);

                sdkOperationrecord.setObjectname(dBTaskResult.getTaskname());
                sdkOperationrecord.setOperation("数据库脱敏任务执行失败");
            }
        } catch (Exception e) {
            e.printStackTrace();

            // 更新结果
            dBTaskResult.setTaskstatus(Const.TASK_RESULT_EXECUTE_FAIL);
            dBTaskResultService.update(dBTaskResult);
            // 更新任务表
            dbTaskConfig.setExecutionstate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
            dbTaskConfigService.update(dbTaskConfig);
            //插入任务结果记录表
            MaskTaskresultrecords maskTaskresultrecords = new MaskTaskresultrecords();
            maskTaskresultrecords.setTaskname(dBTaskResult.getTaskname());
            maskTaskresultrecords.setTasktype(Const.MASK_TASK_DB);
            maskTaskresultrecords.setTaskstatus(Const.TASK_EXECUTESTATE_EXECUTE_FAIL_MESSAGE);
            maskTaskresultrecords.setStarttime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", startTime));
            maskTaskresultrecords.setEndtime(DateUtil.str2Timestamp("yyyy-MM-dd HH:mm:SS", DateUtil.getNowTime()));
            maskTaskresultrecordsService.create(maskTaskresultrecords);
            log.error("数据库脱敏任务执行失败");

            sdkOperationrecord.setObjectname(dBTaskResult.getTaskname());
            sdkOperationrecord.setOperation("数据库脱敏任务执行失败");


            //TODO 写入系统监控-异常日志 且首页告警统计
            Log log = new Log();
            String description = "任务号:" + taskname + ",表名：" + in_tabname + ",执行数据库单表脱敏任务出现异常";
            log.setDescription(description);
            StackTraceElement[] stackTrace = e.getStackTrace();
            String method = stackTrace[0].getClassName() + "." + stackTrace[0].getMethodName();
            log.setMethod(method);
            String parameters = "{taskname:" + taskname + "}";
            log.setParams(parameters);
            long endTime = System.currentTimeMillis();
            int time = (int) ((endTime - startTimeLong) / 1000);
            log.setTime((long) time);
            log.setCreateTime(new Timestamp(System.currentTimeMillis()));

            //将打印到控制台的内容，转为字符串,存放到日志里
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            String exInfo = sw.toString();

            //前面需拼接com.wzsec.exception.BadRequestException:  格式，以免影响之前逻辑
            log.setExceptionDetail("com.wzsec.exception.BadRequestException: " + e.getMessage() + "\n\t" +
                    "at" + exInfo);
            logService.saveLogs(log);

        } finally {
            //插入SDK操作记录
            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
            sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_DBMASK);
            sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
            sdkOperationrecordRepository.save(sdkOperationrecord);

            //将执行数据库脱敏任务失败告警推送至syslog
            if (Const.TASK_EXECUTESTATE_EXECUTE_FAIL.equals(dbTaskConfig.getExecutionstate())) {
                DmAlarmdisposal dmAlarmdisposal = new DmAlarmdisposal();
                // TODO 事件详情定义
                String str = "数据脱敏：任务ID：{},表名：{},库名：{},数据源url：{},执行时间：{}，库表数据脱敏任务执行失败";
                String eventDetails = cn.hutool.core.util.StrUtil.format(str, dBTaskResult.getTaskname(), dBTaskResult.getTabname(), dBTaskResult.getDbname(), dBTaskResult.getIpaddress(), startTime);

                dmAlarmdisposal.setCircumstantiality(eventDetails); //事件详情
                dmAlarmdisposal.setChecktime(cn.hutool.core.date.DateUtil.now());
                dmAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED); //处置状态

                dmAlarmdisposal.setEventrule(Const.DICT_DATABASETABLE_EXECUTION_TASK_ERROR);//规则

                String url = dBTaskResult.getIpaddress();
                String ip = null;
                String port = null;
                if (url.contains("//")) {
                    String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringBetween(url, "//", "/");
                    String[] ips = hostNameAndPort1.split(":");
                    ip = ips[0];
                    port = ips[1];
                    dmAlarmdisposal.setSourceip(ip);//源ip
                    dmAlarmdisposal.setSourceport(port);//源端口
                } else if (url.contains("@")) {
                    String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringAfter(url, "@");
                    String hostNameAndPort2 = com.wzsec.utils.StringUtils.substringBeforeLast(hostNameAndPort1, ":");
                    String[] ips = hostNameAndPort2.split(":");
                    ip = ips[0];
                    port = ips[1];
                    dmAlarmdisposal.setSourceip(ip);//源ip
                    dmAlarmdisposal.setSourceport(port);//源端口
                } else {
                    String[] ips = url.split(":");
                    ip = ips[0];
                    port = ips[1];
                    dmAlarmdisposal.setSourceip(ip);//源ip
                    dmAlarmdisposal.setSourceport(port);//源端口
                }
                dmAlarmdisposal.setAccount(dBTaskResult.getOutputtype());//事件相关用户名
                dmAlarmdisposal.setReservefield2(dBTaskResult.getOutputtype());
                dmAlarmdisposal.setReservefield3("2");

                String outUrl = dBTaskResult.getOutputipaddress();
                if (com.wzsec.utils.StringUtils.isNotEmpty(outUrl)) {
                    if (outUrl.contains("//")) {
                        String hostNameAndPort = com.wzsec.utils.StringUtils.substringBetween(outUrl, "//", "/");
                        String[] outIps = hostNameAndPort.split(":");
                        String outIp = outIps[0];
                        String outPort = outIps[1];
                        dmAlarmdisposal.setDestinationip(outIp);//目标IP
                        dmAlarmdisposal.setDestinationport(port);//目标端口
                    } else if (outUrl.contains("@")) {
                        String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringAfter(url, "@");
                        String hostNameAndPort2 = com.wzsec.utils.StringUtils.substringBeforeLast(hostNameAndPort1, ":");
                        String[] outIps = hostNameAndPort2.split(":");
                        String outIp = outIps[0];
                        String outPort = outIps[1];
                        dmAlarmdisposal.setDestinationip(outIp);//目标IP
                        dmAlarmdisposal.setDestinationport(port);//目标端口
                    } else {
                        dmAlarmdisposal.setDestinationip(outUrl);//目标IP
                        dmAlarmdisposal.setDestinationport(port);//目标端口
                    }
                }
                dmAlarmdisposalService.create(dmAlarmdisposal);

                //告警推送syslog
                new MonitorRiskAlarmData().sendDmExample(dmAlarmdisposal);
            }
            log.info("任务ID【" + taskid + "】,执行脱敏任务结束");
            log.info("任务ID【" + taskid + "】,执行完毕");

            // TODO 脱敏字段异常处置告警,写入告警记录表
            if (!abnormalHandlingAlarm.isEmpty()) {
                desensitizationAbnormalAlarm();
            }
        }
    }


    public static void main(String[] args) {
        try {
            if (true) {
                throw new Exception("自定义异常");
            }
        } catch (Exception ex) {
            //将打印到控制台的内容，转为字符串
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            ex.printStackTrace(pw);
            String exInfo = sw.toString();
            System.out.println("=====" + exInfo + "======");
        }

//        String cityInfo = com.wzsec.utils.StringUtils.getCityInfo("12");
//        System.out.println(cityInfo);
    }

    private void doMaskData(String fieldNameStr, String dbname, String tabname, String url, String username,
                            String password, String jdbcname, String limitingcondition) {

        long initialPosition = 0L;
        if (totalLineCount > pageSize) { // 查询总数据量大于设定每页读取行数,即进行分页处理
            initialPosition = (totalLineCount / pageSize);
            long tmpPage = 0;
            tmpPage = totalLineCount % pageSize == 0 ? 0 : 1;
            initialPosition = initialPosition + tmpPage - 1;   //获取分页数量
        }

        Connection con = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;

        try {
            boolean isCreateTable = true;

            for (int i = 0; i <= initialPosition; i++) {

                // 获取分页获取结果集 (有限制条件,先查询限制条件结果集,再进行分页处理)
                resultSet = getResultSet(con, preparedStatement, i * pageSize, pageSize);

                String[] fieldNameStrs = in_fieldNameStr.split(",");
                List<Map<String, Object>> fieldDataList = new ArrayList<>();

                if (resultSet != null) {
                    // 遍历查询结果集
                    while (resultSet.next()) {

                        Map<String, Object> fieldDataMap = new LinkedHashMap<String, Object>();
                        for (String fieldName : fieldNameStrs) {
                            String fieldNameReplace = fieldName.replaceAll("`", "");
                            fieldDataMap.put(fieldNameReplace, resultSet.getString(fieldNameReplace));
                        }

                        // 行数据写出
                        fieldDataMap = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap);

                        fieldDataList.add(fieldDataMap);

                        boolean isUpdateExample = false;
                        //TODO 因传参原因，单独处理 "保持数值均值特征脱敏" NUMKEEPAVERAGE
                        for (Map.Entry<String, Object> entry : maskAlgoInfoMap.entrySet()) {
                            if ("NUMKEEPAVERAGE$".equals(entry.getValue())) {
                                String fieldName = entry.getKey();
                                List<Object> numDataList = new ArrayList<>();
                                for (Map<String, Object> stringMap : fieldDataList) {
                                    String data = stringMap.get(fieldName) == null?"":stringMap.get(fieldName).toString();
                                    numDataList.add(data);
                                }
                                List<Object> averageData = Rule4AlgorithmUtil.p_average(numDataList);
                                for (int j = 0; j < fieldDataList.size(); j++) {
                                    fieldDataList.get(j).put(fieldName, averageData.get(j).toString());
                                }
                                isUpdateExample = true;
                            }
                        }

                        // TODO 数据样例更新
                        if (isUpdateExample) {
                            StringBuffer maskdataline = new StringBuffer();
                            aftermaskdata = "";
                            for (int z = 0; z < fieldDataList.size(); z++) {
                                Map<String, Object> stringStringMap = fieldDataList.get(z);
                                for (Map.Entry<String, Object> maskDataEntey : stringStringMap.entrySet()) {
                                    maskdataline.append(maskDataEntey.getValue() + ",");
                                }
                                aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
                                maskdataline.setLength(0); //拼接完一行后，清空数据
                                //只取采5条数据样例
                                if ((z + 1) == 5) {
                                    break;
                                }
                            }
                        }

                        if (fieldDataList.size() == pageSize) {// 写出结果
                            saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入
                            if (flag == false) {
                                throw new Exception("分批写入失败");
                            }
                            fieldDataList.clear();
                            isCreateTable = false;
                        }
                    }

                    if (fieldDataList != null && fieldDataList.size() > 0) {// 写出结果
                        saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入
                        if (flag == false) {
                            throw new Exception("分批写入失败");
                        }
                        fieldDataList.clear();
                        isCreateTable = false;
                    }

                }
                if (totalLineCount - (i * pageSize + pageSize) >= 0) {
                    Console.log("==> 完成脱敏 {} 数据落盘 {} 行 ,剩余 {} 行", out_tabname, i * pageSize + pageSize, totalLineCount - (i * pageSize + pageSize));
                }
            }


        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("连接数据库失败或获取抽取字段的数据出现异常");
            //throw ex;
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 执行MongoDB脱敏任务
     */
    private void doMaskDataByMongoDB() {

        long initialPosition = 1L;
        //没有条件限制参数，正常分页查询
        if (limitEnd == 0 && limitStart == 0) {
            if (totalLineCount > pageSize) { // 查询总数据量大于设定每页读取行数,即进行分页处理
                initialPosition = totalLineCount % pageSize == 0 ?
                        totalLineCount / pageSize : totalLineCount / pageSize + 1;
            }
        } else {
            initialPosition = limitEnd % pageSize.intValue() == 0 ?
                    limitEnd / pageSize.intValue() : limitEnd / pageSize.intValue() + 1;
        }

        try {
            mongoClient = MongoDBUtil.getConnect(in_url, in_username, in_password, in_dbname);
            outMongoClient = MongoDBUtil.getConnect(out_url, out_username, out_password, out_dbname);

            String[] fieldNameStrs = in_fieldNameStr.split(",");

            // 任务开始时间
            startTime = DateUtil.getNowTime();

            boolean isCreateTable = true;

            for (int i = 0; i < initialPosition; i++) {

                // 读取MongoDB结果集
                String getResultSetStartTime = DateUtil.getNowTime();

                int skip = 0, limit = 0;
                if (limitEnd == 0 && limitStart == 0) {
                    // 计算要跳过的文档数量
                    skip = (int) (i * pageSize);
                    limit = pageSize.intValue();
                } else {
                    // 1.limitEnd >= 分页数量，按正常分页逻辑走，最后一次分页数量取余数
                    // 2.limitEnd < 分页数量，一次性读完写完
                    if (limitEnd >= pageSize.intValue()) {
                        skip = ((i * pageSize.intValue())) + limitStart; // 从指定limit开始处进行读取
                        //最后一次分页取余数，没有余数取分页数
                        if (i == initialPosition) {
                            limit = limitEnd % pageSize.intValue() == 0 ? pageSize.intValue() : limitEnd % pageSize.intValue();
                        } else {
                            limit = pageSize.intValue();
                        }
                    } else {
                        skip = limitStart;
                        limit = limitEnd;
                    }
                }
                List<Document> documentList = MongoDBUtil.batchReadDocument(in_dbname, in_tabname, mongoClient, skip, limit);

                String getResultSetEndTime = DateUtil.getNowTime();
                String getResultSetTime = String.valueOf(DateUtil.getTimeSecondsByBothDate(getResultSetStartTime, getResultSetEndTime));

                if (pageSize > totalLineCount) {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, totalLineCount, getResultSetTime);
                } else {
                    log.info("获取结果集 {}-{} 时长: {}", i * pageSize, pageSize + i * pageSize, getResultSetTime);
                }

                List<Map<String, Object>> fieldDataList = new ArrayList<>();

                if (documentList.size() > 0) {
                    // 遍历查询结果集
                    for (Document document : documentList) {

                        JSONObject jsonObject = JSONUtil.parseObj(document.toJson());

                        Map<String, Object> fieldDataMap = new LinkedHashMap<>();
                        for (String fieldName : fieldNameStrs) {
                            String fieldValue = jsonObject.getStr(fieldName);
                            if (fieldValue != null && fieldValue.contains("$numberLong")) {
                                fieldValue = JSONUtil.parseObj(fieldValue).getStr("$numberLong");
                            }
                            fieldDataMap.put(fieldName, fieldValue);
                        }

                        fieldDataMap = getMaskRowData(maskAlgoInfoMap, algorithmMap, fieldDataMap);
                        fieldDataList.add(fieldDataMap);
                    }

                    boolean isUpdateExample = false;
                    //TODO 因传参原因，单独处理 "保持数值均值特征脱敏" NUMKEEPAVERAGE
                    for (Map.Entry<String, Object> entry : maskAlgoInfoMap.entrySet()) {
                        if ("NUMKEEPAVERAGE$".equals(entry.getValue())) {
                            String fieldName = entry.getKey();
                            List<Object> numDataList = new ArrayList<>();
                            for (Map<String, Object> stringMap : fieldDataList) {
                                String data = stringMap.get(fieldName) == null?"":stringMap.get(fieldName).toString();
                                numDataList.add(data);
                            }
                            List<Object> averageData = Rule4AlgorithmUtil.p_average(numDataList);
                            for (int j = 0; j < fieldDataList.size(); j++) {
                                fieldDataList.get(j).put(fieldName, averageData.get(j).toString());
                            }
                            isUpdateExample = true;
                        }
                    }

                    // TODO 数据样例更新
                    if (isUpdateExample) {
                        StringBuffer maskdataline = new StringBuffer();
                        aftermaskdata = "";
                        for (int z = 0; z < fieldDataList.size(); z++) {
                            Map<String, Object> stringStringMap = fieldDataList.get(z);
                            for (Map.Entry<String, Object> maskDataEntey : stringStringMap.entrySet()) {
                                maskdataline.append(maskDataEntey.getValue() + ",");
                            }
                            aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
                            maskdataline.setLength(0); //拼接完一行后，清空数据
                            //只取采5条数据样例
                            if ((z + 1) == 5) {
                                break;
                            }
                        }
                    }

                    if (fieldDataList.size() == pageSize) {// 写出结果
                        /*saveencdecRowData(databaseUtil, driversByType, isCreateTable, fieldDataList, fieldNameStrs,
                                i * pageSize, i * pageSize + pageSize, writeDataCount);// 批量保存数据，里面设置有批量写入*/
                        saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入

                        if (pageSize > totalLineCount) {
                            log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                        } else {
                            log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                        }
                        fieldDataList.clear();
                        isCreateTable = false;
                    } else if (fieldDataList.size() != pageSize) {// 写出结果
                        saveMaskRowData(isCreateTable, fieldDataList, fieldNameStrs, i * pageSize, i * pageSize + pageSize);// 批量保存数据，里面设置有批量写入


                        if (pageSize > totalLineCount) {
                            log.info("〖已写入数据量 {} 行, 已全部写入〗", totalLineCount);
                        } else {
                            log.info("〖已写入数据量 {} 行, 剩余 {} 行〗", pageSize + i * pageSize, totalLineCount - (i * pageSize + pageSize));
                        }

                        fieldDataList.clear();
                        isCreateTable = false;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            mongoClient.close();
            outMongoClient.close();
        }
    }

    private Map<String, Object> getMaskRowData(Map<String, Object> maskAlgoInfoMap,
                                               HashMap<String, cn.god.mask.common.Algorithm> algorithmMap,
                                               Map<String, Object> fieldDataMap) {
        // 行数据脱敏
        StringBuffer dataline = new StringBuffer();
        StringBuffer maskdataline = new StringBuffer();
        for (String filedName : fieldDataMap.keySet()) {
            String data = fieldDataMap.get(filedName) == null?"":fieldDataMap.get(filedName).toString();
            String result = data;
            // 该字段有数据才进行脱敏
            if (data != null && !"".equals(data)) {
                // 获取字段的脱敏算法信息 MD5$123,412
                String maskAlgoInfo = maskAlgoInfoMap.get(filedName) == null?"":maskAlgoInfoMap.get(filedName).toString();
                // 该字段有脱敏算法才进行脱敏
                if (maskAlgoInfo != null && !"".equals(maskAlgoInfo) && !"NUMKEEPAVERAGE$".equals(maskAlgoInfo)) {
                    String[] AlgoParamInfo = maskAlgoInfo.split("\\$");
                    String algo = AlgoParamInfo[0];
                    String param = "";
                    if (AlgoParamInfo.length > 1) {
                        param = AlgoParamInfo[1];
                    }
                    try {
                        // 联系方式复合类型脱敏 (CONTACT_INFORMATION_COMBINATION)
                        // "HIDECODE"  "*,3,4" (手机号通用)  "HIDECODE"  "*,3,3" (固话通用)
                        if (algo.equalsIgnoreCase("CONTACT_INFORMATION_COMBINATION")) {
                            if (ProRuleFactory.checkDataByProRule("P_PHONENUMBER", data)) {
                                result = MaskAlgFactory.getMaskData(data, "HIDECODE", "*,3,4", null);
                            } else if (ProRuleFactory.checkDataByProRule("P_FIXPHONE", data)) {
                                result = MaskAlgFactory.getMaskData(data, "HIDECODE", "*,3,3", null);
                            } else {
                                result = MaskAlgFactory.getMaskData(data, "HIDECODE", "*,3,0", null);
                            }
                        } else {
                            // 数据:data算法名:algo参数:param
                            Algorithm algorithm = algorithmMap.get(algo);
                            String sparefield1 = algorithm.getSparefield1();
                            String middleData = "";
                            if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                                result = MaskAlgFactory.getMaskData(data, algo, param, algorithmMap);
                            }else {
                                //自定义算法
                                String jarname = algorithm.getJarname();//jar包名称
                                String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                                String methodname = algorithm.getMethodname();//方法名
                                Map<String, String> paramMap = new HashMap<>();
                                paramMap.put("jarname",jarname);
                                paramMap.put("funcnamepath",funcnamepath);
                                paramMap.put("methodname",methodname);
                                paramMap.put("maskData",data);
                                result = AlgorithmUtils.invokeJarMethod(paramMap);
                            }

                        }

                    } catch (Exception e) {
                        log.error("执行脱敏出现异常,数据:" + data + "算法名:" + algo + "参数:" + param);
                        // TODO 异常数据处置
                        String abnormalDataHandling = abnormalHandlingAlarm.get(filedName);
                        log.warn("异常数据处置算法名为: {}", abnormalDataHandling);
                        result = abnormalDataHandling(filedName, result, data, abnormalDataHandling);

                        e.printStackTrace();
                    }
                    fieldDataMap.put(filedName, result);
                }
            }
            if (exampleLineCount > 0) {
                dataline.append(data + ",");
                maskdataline.append(result + ",");
            }
        }
        if (exampleLineCount > 0) {
            beforemaskdata += dataline.toString().substring(0, dataline.toString().length() - 1) + "\n";
            aftermaskdata += maskdataline.toString().substring(0, maskdataline.toString().length() - 1) + "\n";
            exampleLineCount--;
        }
        maskLineCount++;
        taskProgressModel.setFinishedcount(maskLineCount.intValue());
        taskProgressModel.setProgress(calculateUtils.getPercentage(maskLineCount.intValue(), totalLineCount.intValue()));
        return fieldDataMap;
    }

    /**
     * 异常数据处理
     *
     * @param result 结果
     * @param data   数据
     * @return {@link String }
     */
    private String abnormalDataHandling(String filedName, String result, String data, String abnormalDataHandling) {
        // TODO ---------- 异常数据处理 ----------
        try {
            if (StringUtils.isNotBlank(abnormalDataHandling)) {
                if (!abnormalDataHandling.equalsIgnoreCase("IGNORE")) { //置空,替换
                    com.wzsec.modules.mask.domain.Algorithm algorithm = algorithmRepository.findByAlgorithmName(abnormalDataHandling);
                    String sparefield1 = algorithm.getSparefield1();
                    if (!Const.CUSTOM_ALGORITHM.equals(sparefield1)){
                        result = MaskAlgFactory.getMaskData(data, abnormalDataHandling, Const.AES_SECRET_KEY, null);
                    }else {
                        //自定义算法
                        String jarname = algorithm.getJarname();//jar包名称
                        String funcnamepath = algorithm.getFuncnamepath(); //方法路径
                        String methodname = algorithm.getMethodname();//方法名
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("jarname",jarname);
                        paramMap.put("funcnamepath",funcnamepath);
                        paramMap.put("methodname",methodname);
                        paramMap.put("maskData",data);
                        result = AlgorithmUtils.invokeJarMethod(paramMap);
                    }

                    String cNameAlgorithm = abnormalDataHandling.equals(Const.ABNORMAL_DATA_HANDLING_EMPTY) ?
                            Const.ABNORMAL_DATA_HANDLING_EMPTY_CNAME : Const.ABNORMAL_DATA_HANDLING_SUBSTITUTION_CNAME;
                    abnormalHandlingAlarm.put(filedName, cNameAlgorithm);
                } else { // 忽略
                    abnormalHandlingAlarm.put(filedName, Const.ABNORMAL_DATA_HANDLING_IGNORE_CNAME);
                }
            }
        } catch (Exception ex) {
            int length = data.length();
            StringBuilder masked = new StringBuilder();
            for (int i = 0; i < length; i++) {
                masked.append('*');
            }
            result = masked.toString();
        }
        return result;
        // TODO ---------- 异常数据处理 ----------
    }


    /**
     * 脱敏字段异常处置告警,写入告警记录表
     */
    private void desensitizationAbnormalAlarm() {
        // 拼接字段处置方式
        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, String> entry : abnormalHandlingAlarm.entrySet()) {
            result.append("字段:{").append(entry.getKey()).append("}, 异常数据处置方式:{").append(entry.getValue()).append("}; ");
        }

        if (result.length() > 0) {
            result.setLength(result.length() - 2);
        }

        String template = "库名:{}, 表名:{}, {} ";
        String alarmDetails = StrUtil.format(template, in_dbname, in_tabname, result);
        String url = dBTaskResult.getIpaddress();

        String in_ip = "";
        String in_port = "";
        if (url.contains("//")) {
            String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringBetween(url, "//", "/");
            String[] ips = hostNameAndPort1.split(":");
            in_ip = ips[0];
            in_port = ips[1];
        } else if (url.contains("@")) {
            String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringAfter(url, "@");
            String hostNameAndPort2 = com.wzsec.utils.StringUtils.substringBeforeLast(hostNameAndPort1, ":");
            String[] ips = hostNameAndPort2.split(":");
            in_ip = ips[0];
            in_port = ips[1];
        } else {
            String[] ips = url.split(":");
            in_ip = ips[0];
            in_port = ips[1];
        }

        String out_ip = "";
        String out_port = "";
        String outUrl = dBTaskResult.getOutputipaddress();
        if (com.wzsec.utils.StringUtils.isNotEmpty(outUrl)) {
            if (outUrl.contains("//")) {
                String hostNameAndPort = com.wzsec.utils.StringUtils.substringBetween(outUrl, "//", "/");
                String[] outIps = hostNameAndPort.split(":");
                out_ip = outIps[0];
                out_port = outIps[1];
            } else if (outUrl.contains("@")) {
                String hostNameAndPort1 = com.wzsec.utils.StringUtils.substringAfter(url, "@");
                String hostNameAndPort2 = com.wzsec.utils.StringUtils.substringBeforeLast(hostNameAndPort1, ":");
                String[] outIps = hostNameAndPort2.split(":");
                out_ip = outIps[0];
                out_port = outIps[1];
            }
        }

        DmAlarmdisposal maskAlarmdisposal = new DmAlarmdisposal();
        maskAlarmdisposal.setCircumstantiality(alarmDetails);
        maskAlarmdisposal.setChecktime(DateUtil.getNowTime());
        maskAlarmdisposal.setReservefield2(dBTaskResult.getUsername()); //事件类型-库表
        maskAlarmdisposal.setReservefield3(Const.RISK_MIDDLE); //事件等级-中
        maskAlarmdisposal.setSourceip(in_ip);
        maskAlarmdisposal.setSourceport(in_port);
        maskAlarmdisposal.setDestinationip(out_ip);
        maskAlarmdisposal.setDestinationport(out_port);
        maskAlarmdisposal.setAccount(dBTaskResult.getUsername());
        maskAlarmdisposal.setEventrule(Const.DICT_DESENSITIZATION_ABNORMAL_DATA_HANDLING); //事件名称
        maskAlarmdisposalService.create(maskAlarmdisposal);
        //告警推送syslog
        MonitorRiskAlarmData.sendDmExample(maskAlarmdisposal);
    }

    private void saveMaskRowData(boolean isCreateTable, List<Map<String, Object>> fieldDataList, String[] fieldnamaearr, long startLine, long endLine) throws Exception {

        //TODO 原设置水印逻辑，数据最后一位拼接不可见字符
        //如果水印下标不为空，将对应下标位的数据设置水印
        if (isWatermark) {
            Integer waterLineSpacing = Integer.valueOf(ConstEngine.sddEngineConfs.get("waterLineSpacing"));
            //数据行数大于设置的水印行间距，就按照间隔数注入水印
            if (fieldDataList.size() >= waterLineSpacing) {
                for (int i = 0; i < fieldDataList.size(); i++) {
                    Integer index = i + 1;
                    if (index % waterLineSpacing == 0) {//该行等于水印行间距的倍数(例如100、200、300等)
                        String fieldName = fieldDataList.get(i).get(watermarkField) == null?"":fieldDataList.get(i).get(watermarkField).toString();
                        String data = fieldName + waterMarkInfo;
                        fieldDataList.get(i).put(watermarkField, data);
                    }
                }
            } else {
                // 小于水印行间隔，随机抽取一行注入水印
                Random random = new Random();
                int randomNumber = random.nextInt(fieldDataList.size());
                String fieldName = fieldDataList.get(randomNumber).get(watermarkField) == null?"":fieldDataList.get(randomNumber).get(watermarkField).toString();
                String data = fieldName + waterMarkInfo;
                fieldDataList.get(randomNumber).put(watermarkField, data);
            }
        }

        // 6.保存脱敏后的数据到的位置
        if (Const.DB_TASK_OUTPUTTYPE_DB.equals(out_type)) {
            if (Const.DB_MONGODB.equalsIgnoreCase(in_dbtype)) {
                flag = DatabaseUtil.mongoDBDataNewTable(isCreateTable, out_dbname, out_tabname, writeDataCount, outMongoClient, fieldDataList);
            } else {
                flag = DatabaseUtil.insertData2NewTable(isCreateTable, in_dbname, in_tabname, in_url, in_username, in_password, in_dbtype,
                        fieldDataList, out_tabname, out_url, out_username, out_password, fieldNameStrList, out_dbname, out_dbtype, watermarkField);
            }
            if (flag) {
                //脱敏结果更新 脱敏行数、当前脱敏行数、更新时间
                dBTaskResult.setTotallines(totalLineCount.intValue());
                dBTaskResult.setMasklines(maskLineCount.intValue());
                dBTaskResult.setSparefield1(DateUtil.getNowTime());
                dBTaskResultService.update(dBTaskResult);
            }
        } else {// 此处不能再写到excel，写到txt或者csv
            // 保存到文件
            // flag = CreateExcel.writedata(out_path, out_tabname, in_tabname,
            // fieldDataList);
            try {
                String line = System.getProperty("line.separator");// 平台换行!
                if (!out_path.endsWith(File.separator)) {
                    out_path = out_path + File.separator;
                }
                if (Const.DB_TASK_OUTPUTTYPE_EXCEL.equals(out_type)) {

                    List<List<?>> rowList = new ArrayList<>();

                    //List<String> maskDataList = new ArrayList<>();
                    //转换数据
                    for (Map<String, Object> m : fieldDataList) {
                        List<Object> rowDataList = new ArrayList<>();
                        String md = "";
                        for (String k : m.keySet()) {
                            rowDataList.add(m.get(k));
                            // 1.原有拼接
                            //if ("".equals(md)) {
                            //    md = m.get(k);
                            //} else {
                            //    md = md + "," + m.get(k);
                            //}
                        }
                        // 2.原有添加到脱敏后行字段集合
                        //maskDataList.add(md);
                        rowList.add(rowDataList);
                    }

                    // 3.原有获取字段名称集合
                    //ArrayList<String> filedNameList = new ArrayList<>();
                    //for (int i = 0; i < fieldnamaearr.length; i++) {
                    //    filedNameList.add(fieldnamaearr[i].replaceAll("`", "").trim());
                    //}

                    if (fieldDataList.size() > 0) {

                        if (fieldDataList.size() < endLine) {
                            endLine = startLine + fieldDataList.size();
                        }

                        String path = out_path + taskname + File.separator + out_tabname + "(" + startLine + "-" + endLine + ")" + ".xlsx";
                        BigExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getBigWriter(path);
                        writer.write(rowList);
                        writer.close();
                        flag = true;

                        // 4.原有写入excel逻辑
                        //WirteFileService.wirteXSSFWorkbook(maskDataList, fieldnamaearr.length,
                        //        out_path + taskname + File.separator,
                        //        out_tabname + "(" + startLine + "-" + endLine + ")" + ".xlsx",
                        //        ",");
                    }

                } else {
                    if (fieldDataList.size() > 0) {
                        if (fieldDataList.size() < endLine) {
                            endLine = startLine + fieldDataList.size();
                        }
                        String path = out_path + taskname + File.separator + out_tabname + "(" + startLine + "-" + endLine + ")";
                        if (Const.DB_TASK_OUTPUTTYPE_TXT.equals(out_type)) {
                            path = path + ".txt";
                        } else if (Const.DB_TASK_OUTPUTTYPE_CSV.equals(out_type)) {
                            path = path + ".csv";
                        } else {
                            throw new Exception("暂不支持的输出类型：" + out_type);
                        }
                        File file = new File(path);
                        // 如果没有文件就创建
                        if (!file.isFile()) {
                            // 获取父目录
                            File fileParent = file.getParentFile();
                            // 判断是否存在
                            if (!fileParent.exists()) {
                                // 创建父目录文件
                                fileParent.mkdirs();
                            }
                            if (!file.exists()) {
                                file.createNewFile();
                            }
                        }

                        FileWriter write = new FileWriter(file, true);
                        // 将数据写入文件中
                        for (Map<String, Object> map : fieldDataList) {
                            write.write(StringUtils.join(map.values(), ",") + line);
                        }
                        write.close();
                    }
                    flag = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
                flag = false;
            }
        }
    }

    private void initParam() {
        DBTaskConfigDto dbTaskConfigDto = dbTaskConfigService.findById(taskid);
        dbTaskConfig = dbtaskconfigMapper.toEntity(dbTaskConfigDto);

        taskname = dbTaskConfig.getTaskname();
        in_tabname = dbTaskConfig.getTablename();
        in_limitingcondition = dbTaskConfig.getSparefield1();
        out_type = dbTaskConfig.getOutputtype();
        out_path = dbTaskConfig.getOutputdirectory();

        original_table_rewriting = dbTaskConfig.getSparefield5();

        // 输入数据源信息
        Integer in_dataSourceId = dbTaskConfig.getInputdatasourceid();
        DatasourceDto in_datasourceDto = dataSourceService.findById(Long.valueOf(in_dataSourceId));
        in_dbtype = in_datasourceDto.getType();
        in_dbname = in_datasourceDto.getDbname();
        in_drivername = in_datasourceDto.getDriverprogram();
        in_url = in_datasourceDto.getSrcurl();
        in_username = in_datasourceDto.getUsername();
        in_password = in_datasourceDto.getPassword();
        //AES解密
        if (StringUtils.isNotEmpty(in_password)) {
            in_password = AES.decrypt(in_password, Const.AES_SECRET_KEY);
        }

        String strategyid = dbTaskConfigDto.getStrategyid();
        MaskStrategyTableDto maskStrategyTableDto = maskStrategyTableService.findById(Integer.valueOf(strategyid));

        List<Map<String, Object>> list = null;

        if (Const.STRATEGY_TYPE_SINGLETABLE.equals(maskStrategyTableDto.getStrategytype())) {
            list = maskStrategyFieldService.getMaskAlgoInfoByStrategyId(strategyid);

            for (Map<String, Object> maskStrategyField : list) {
                String fieldename = (String) maskStrategyField.get("fieldename");
                String abnormalHandle = maskStrategyField.get("abnormalhandle") != null ?
                        maskStrategyField.get("abnormalhandle").toString() : "";
                abnormalHandleMap.put(fieldename, abnormalHandle);
            }

        } else {
            List<MetaField> metaFieldsList = metaFieldService.findInfoFieldInfoByTabNameSourceId(in_tabname, Long.valueOf(in_dataSourceId));
            List<Map<String, Object>> algoInfo = maskStrategyTableService.getAllStrategyAlgoInfoByStrategyId(strategyid);
            list = new ArrayList<>();
            for (MetaField metaField : metaFieldsList) {
                String fieldName = metaField.getFieldname();
                String algoName = null;
                String key = null;
                String param = null;
                String abnormalHandle = null;
                String fieldSCname = metaField.getFieldscname();
                if (StringUtils.isNotEmpty(fieldSCname)) {
                    for (Map<String, Object> map : algoInfo) {
                        String dataName = map.get("dataname").toString();
                        if (dataName.equals(fieldSCname)) {
                            algoName = map.get("algenglishname") == null ? null : map.get("algenglishname").toString();
                            key = map.get("secretkey") == null ? null : map.get("secretkey").toString();
                            param = map.get("param") == null ? null : map.get("param").toString();
                            abnormalHandle = map.get("abnormalhandle") == null ? null : map.get("abnormalhandle").toString();
                            abnormalHandleMap.put(fieldName, abnormalHandle);
                            break;
                        }
                    }
                }
                Map<String, Object> map = new HashMap<>();
                map.put("fieldename", fieldName);
                map.put("algenglishname", algoName);
                map.put("secretkey", key);
                map.put("param", param);
                list.add(map);
            }
        }


        maskAlgoInfoMap = DBMaskUtils.getMaskAlgoInfo(list, null);
        in_fieldNameStr = "";
        for (String fieldName : maskAlgoInfoMap.keySet()) {
            if (Const.MYSQL_SIGN.equalsIgnoreCase(in_dbtype)) {
                in_fieldNameStr += "`" + fieldName + "`,";
            } else {
                in_fieldNameStr += fieldName + ",";
            }

            String algo = maskAlgoInfoMap.get(fieldName) == null?"":maskAlgoInfoMap.get(fieldName).toString();
            if (!"".equals(algo)) {
                fieldNameStrList.add(fieldName);
            }
        }
        if (in_fieldNameStr.endsWith(",")) { // 如果以","结尾，去除","
            in_fieldNameStr = in_fieldNameStr.substring(0, in_fieldNameStr.lastIndexOf(","));
        }

        Integer out_dataSourceId = dbTaskConfig.getOutputdatasourceid();
        DatasourceDto out_datasourceDto = dataSourceService.findById(Long.valueOf(out_dataSourceId));

        // 输出数据源信息
        if (Const.SOURCE_DB.equals(out_type)) {
            out_dbtype = out_datasourceDto.getType();
            out_dbname = out_datasourceDto.getDbname();
            out_drivername = out_datasourceDto.getDriverprogram();
            out_url = out_datasourceDto.getSrcurl();
            /*if (!out_url.contains("?") && !out_url.contains("characterEncoding")) {
                out_url += "?useCursorFetch=true&characterEncoding=utf8";
            }*/
            out_username = out_datasourceDto.getUsername();
            out_password = out_datasourceDto.getPassword();
            //AES解密
            if (StringUtils.isNotEmpty(out_password)) {
                out_password = AES.decrypt(out_password, Const.AES_SECRET_KEY);
            }
        }
        // 文件名或表名
        out_tabname = in_tabname + "_MASK_" + DateUtil.formatDate(new Date());
        if (in_dbtype.toLowerCase().contains("dm")) {
            out_tabname = in_tabname + "_MASK_" + DateUtil.formatDate(new Date());
        } else {
            out_tabname = in_tabname + "_mask_" + DateUtil.formatDate(new Date());
        }
        //初始化所有算法
        algorithmMap = algorithmService.getAlgorithmByEName();

        //如果没有输出源ip，就从url里截取
        out_ip = out_datasourceDto.getSrcip();
        out_path = out_datasourceDto.getDbname();
        if (out_ip == null) {
            setoutputIpAddress();
        }

        //是否设置水印
        isWatermark = Const.ISWATERMARK_YES.equals(dbTaskConfigDto.getIswatermark());
        if (isWatermark) {
            waterMarkInfo = WaterMarkAlgFactory.getWaterMarkInfo(dbTaskConfigDto.getDataprovider(), dbTaskConfigDto.getDatause(), true);

            //输出类型是库表，且抽取列不为空，将抽取列作为水印加注列
            if (Const.DB_TASK_OUTTYPE_DB.equals(dbTaskConfigDto.getOutputtype()) && StringUtils.isNotEmpty(dbTaskConfigDto.getWatermarkcol())) {
                watermarkField = dbTaskConfigDto.getWatermarkcol();
            } else {
                //设置水印，默认在最后一位添加；如若最后一位使用了可逆算法，改为倒数第二位，以此类推
                for (int i = list.size() - 1; i >= 0; i--) {
                    //脱敏算法
                    String algoName = list.get(i).get("algenglishname") == null ? null : list.get(i).get("algenglishname").toString();
                    String fieldName = list.get(i).get("fieldename") == null ? null : list.get(i).get("fieldename").toString();
                    Boolean isWatermarkFlag;
                    if (com.wzsec.utils.StringUtils.isEmpty(algoName)) {
                        isWatermarkFlag = true;
                    } else {
                        com.wzsec.modules.mask.domain.Algorithm algorithm = algorithmService.findByAlgorithmName(algoName);
                        isWatermarkFlag = Const.ALGORITHM_IS_REVERSIBLE_NO.equals(algorithm.getIsreversible());
                    }

                    //如果这一列没有选择脱敏算法，或所选的算法不可逆，选择这一列数据写入水印
                    if (isWatermarkFlag) {
                        watermarkField = fieldName;
                        break;
                    }
                }
            }
        }
    }

    private void setoutputIpAddress() {

        if (Const.DB_TASK_OUTPUTTYPE_DB.equals(out_type)) {
            if (in_dbtype.toLowerCase().contains("mysql") || out_url.contains("MYSQL") || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
                String temp1 = out_url.substring(out_url.indexOf("3306/") + 5);
                // outputPath = temp1.substring(0,temp.indexOf("?"));
                if (temp1.contains("?")) {
                    out_path = temp1.substring(0, temp1.indexOf("?"));
                } else {
                    out_path = temp1;
                }
            } else if (in_dbtype.toLowerCase().contains("oracle")) {
                String temp = out_url.substring(out_url.indexOf("@") + 1);
                out_ip = temp.substring(0, temp.indexOf(":"));
                out_path = out_url.substring(out_url.indexOf(":1521:") + 4, out_url.lastIndexOf(":") - 1);
            } else if (in_dbtype.toLowerCase().equals("gbase")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("db2")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("hive2")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {

            } else if (in_dbtype.toLowerCase().equals("gbase8s")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().equals("informix")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            } else if (in_dbtype.toLowerCase().equals("mariadb")) {
                String temp = out_url.substring(out_url.indexOf("//") + 2);
                out_ip = temp.substring(0, temp.indexOf(":"));
            }
        } else {
            out_ip = "127.0.0.1";
        }
    }

    private void initResult() {
        DBTaskResult dBTaskResuleDB = new DBTaskResult();
        dBTaskResuleDB.setTaskname(taskname);
        dBTaskResuleDB.setTabname(in_tabname);
        dBTaskResuleDB.setDbname(in_dbname);
        dBTaskResuleDB.setIpaddress(in_url);// 数据源主机或Ip地址
        dBTaskResuleDB.setTaskstatus(Const.TASK_RESULT_EXECUTING);
        dBTaskResuleDB.setOutputtype(out_type);
        dBTaskResuleDB.setUsername(submituser);
        dBTaskResuleDB.setJobstarttime(startTime);
        dBTaskResuleDB.setJobstarttime(startTime);
        DBTaskResultDto dbTaskResultDto = dBTaskResultService.create(dBTaskResuleDB);
        dBTaskResult = dbtaskresultMapper.toEntity(dbTaskResultDto);
        resultid = dBTaskResult.getId();
    }

    /**
     * 分页获取结果集
     *
     * @param con             连接
     * @param stmt            发送
     * @param initialPosition 初始位置
     * @param recordCount     记录数
     * @return {@link ResultSet}
     */
    private ResultSet getResultSet(Connection con, Statement stmt, long initialPosition, long recordCount) {

        ResultSet resultSet = null;
        try {
            // TODO oracle
            if (in_dbtype.toLowerCase().contains("oracle")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //log.info("连接oracle数据库成功");
                // 查抽取字段数据(分页通过子查询实现)
                String sql = "";
                if (StringUtils.isNotBlank(in_limitingcondition) && in_limitingcondition.contains("WHERE")) { //仅支持WHERE,无LIMIT
                    String template = "select {} from (select rownum r,t.* from (select * from {}.{} {}) t where rownum<={}) where r>{} ";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, recordCount + initialPosition, initialPosition);
                } else {
                    String template = "select {} from (select rownum r,t.* from {}.{} t where rownum<={}) where r>{} ";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, recordCount + initialPosition, initialPosition);
                }
                Console.log("oracle分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO MySQL
            } else if (in_dbtype.toLowerCase().contains("mysql") || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                //Console.log("MySQL分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO dm
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //log.info("连接dm数据库成功");
                String sql = "";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                Console.log("dm分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO informix
            } else if (in_dbtype.toLowerCase().equals("informix")) {
                Class.forName(in_drivername);
                //jdbc:informix-sqli://192.168.1.137:9003/mask:informixserver=tramsserver;
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接informix数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = in_fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String sql = "";
                if (StringUtils.isNotBlank(in_limitingcondition) && in_limitingcondition.contains("WHERE")) {
                    String template = "select skip {} first {} {} from ( select from {} {} )";
                    sql = StrUtil.format(template, initialPosition, recordCount, in_fieldNameStr, in_tabname, in_limitingcondition);
                } else {
                    String template = "select skip {} first {} {} from {}";
                    sql = StrUtil.format(template, initialPosition, recordCount, in_fieldNameStr, in_tabname);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO MariaDB
            } else if (in_dbtype.toLowerCase().contains("mariadb")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                Console.log("MariaDB分页查询语句: {}", sql);
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().equals("gbase")) {// 2021年2月24日18:24:46已测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(
                        in_url + "?useCursorFetch=true&defaultFetchSize=" + pageSize, in_username, in_password);
                log.info("连接gbase数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                Console.log("gbase分页查询语句: {}", sql);
                //if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                //    sql += " " + in_limitingcondition;
                //}
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {// 2021年2月24日18:24:46未测试
//				sqlserver表有两种权限，分为dbo和guest权限,dbo权限的下的表可以用表名直接查询,guest权限的下的表必须使用用户.表名进行查询,
//				dbo
//				database owner
//				数据库的创建者,创建该对象的用户
//				DBO是每个数据库的默认用户，具有所有者权限，即DbOwner ，通过用DBO作为所有者来定义对象，能够使数据库中的任何用户引用而不必提供所有者名称。
//				比如：你以User1登录进去并建表Table，而未指定DBO， 当用户User2登进去想访问Table时就得知道这个Table是你User1建立的，要写上User1.Table，如果他不知道是你建的，则访问会有问题。
//				如果你建表时把所有者指给了Dbo，则别的用户进来时写上Dbo.Table就行了，不必知道User1。
//				不光表是如此，视图等等数据库对象建立时也要如此才算是好。
//				guest
//				顾客   能够访问数据库中对象的数据,要求dbo分配权限给guest,一般给他查看的权限select
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接sqlserver数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname + " order by uid offset " + initialPosition + " rows fetch next " + recordCount + " rows only";
                Console.log("sqlserver分页查询语句: {}", sql);

                //if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                //    sql += " " + in_limitingcondition;
                //}
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("db2")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接db2数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                //if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                //    sql += " " + in_limitingcondition;
                //}
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("hive2")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                Connection conn = null;
                HiveStatement hstmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", in_username, in_password, in_url);
                    hstmt = HiveUtil.getStmt(conn);
                    if (null != in_dbname && !in_dbname.equals("")) {
                        String strUseBase = "use " + in_dbname;
                        hstmt.execute(strUseBase);
                    }
                    String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                    if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                        sql += " " + in_limitingcondition;
                    }
                    resultSet = hstmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(hstmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {// 2021年2月24日18:24:46未测试
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接postgresql数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains(Const.DB_GAUSS)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接Gauss数据库成功");
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {// 2021年2月24日18:24:46不支持

            } else if (in_dbtype.toLowerCase().equals("gbase8s")) {
                Class.forName(in_drivername);
                // jdbc:gbasedbt-sqli://192.168.1.71:9088/jdbcdb:GBASEDBTSERVER=gbase01;DB_LOCALE=zh_CN.utf8;CLIENT_LOCALE=zh_CN.utf8;IFX_LOCK_MODE_WAIT=30
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase8s数据库成功");
                StringBuffer findfieldnamestr = new StringBuffer();
                String[] fieldnamarr = in_fieldNameStr.split(",");
                for (String fieldname : fieldnamarr) {
                    findfieldnamestr.append("\"" + fieldname + "\"");
                    findfieldnamestr.append(",");
                }
                // 查抽取字段数据
                String sql = "select " + in_fieldNameStr + " from " + in_tabname;
                if (null != in_limitingcondition && !"".equals(in_limitingcondition)) {
                    sql += " " + in_limitingcondition;
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
            } else if (in_dbtype.toLowerCase().contains("bytehouse")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                //Console.log("连接mysql数据库成功");
                // 查抽取字段数据
                String sql = "";
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select * from (select {} from {}.{} {}) as a limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, in_limitingcondition, initialPosition, recordCount);
                } else {
                    String template = "select {} from {}.{} limit {},{}";
                    sql = StrUtil.format(template, in_fieldNameStr, in_dbname, in_tabname, initialPosition, recordCount);
                }
                stmt = con.createStatement();
                resultSet = stmt.executeQuery(sql);
                // TODO dm
            }
            resultSet.setFetchSize(pageSize.intValue());// oracle支持，mysql和gbase不支持(已通过改写url支持)。
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultSet;
    }

    private Long getDataCount() {
        Connection con = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        long count = 0;
        String sql = "";
        try {
            sql = "select count(*) from " + in_dbname + "." + in_tabname;
            // TODO oracle
            if (in_dbtype.toLowerCase().contains("oracle")) {
                if (StringUtils.isNotBlank(in_limitingcondition) && in_limitingcondition.contains("WHERE")) { //仅支持WHERE,无LIMIT
                    String template = "select count(*) from (select * from {}.{} {}) ";
                    sql = StrUtil.format(template, in_dbname, in_tabname, in_limitingcondition);
                } else {
                    String template = "select count(*) from {}.{}";
                    sql = StrUtil.format(template, in_dbname, in_tabname);
                }
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接oracle数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
                //TODO MySQL
            } else if (in_dbtype.toLowerCase().contains("mysql") || in_dbtype.equalsIgnoreCase(Const.DB_UNIDB)) {
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select count(*) from (select * from {}.{} {}) as a";
                    sql = StrUtil.format(template, in_dbname, in_tabname, in_limitingcondition);
                } else {
                    String template = "select count(*) from {}.{}";
                    sql = StrUtil.format(template, in_dbname, in_tabname);
                }
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接mysql数据库成功");
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
                // TODO dm
            } else if (in_dbtype.toLowerCase().contains("dm")) {
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select count(*) from (select * from {}.{} {}) as a";
                    sql = StrUtil.format(template, in_dbname, in_tabname, in_limitingcondition);
                } else {
                    String template = "select count(*) from {}.{}";
                    sql = StrUtil.format(template, in_dbname, in_tabname);
                }
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接dm数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
                // TODO informix
            } else if (in_dbtype.toLowerCase().equals("informix")) {
                if (StringUtils.isNotBlank(in_limitingcondition) && in_limitingcondition.contains("WHERE")) {
                    String template = "select count(*) from (select * from {} {}) ";
                    sql = StrUtil.format(template, in_tabname, in_limitingcondition);
                } else {
                    String template = "select count(*) from {}";
                    sql = StrUtil.format(template, in_tabname, in_tabname);
                }
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接informix数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
                //TODO MariaDB
            } else if (in_dbtype.toLowerCase().contains("mariadb")) {
                if (StringUtils.isNotBlank(in_limitingcondition)) {
                    String template = "select count(*) from (select * from {}.{} {}) as a";
                    sql = StrUtil.format(template, in_dbname, in_tabname, in_limitingcondition);
                } else {
                    String template = "select count(*) from {}.{}";
                    sql = StrUtil.format(template, in_dbname, in_tabname);
                }
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接MariaDB数据库成功");
                System.out.println(sql);
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().equals("gbase")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("sqlserver")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接sqlserver数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("db2")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接db2数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("hive2")) {
                Class.forName(in_drivername);
                Connection conn = null;
                HiveStatement stmt = null;
                try {
                    conn = HiveUtil.getConn("org.apache.hive.jdbc.HiveDriver", in_username, in_password, in_url);
                    stmt = HiveUtil.getStmt(conn);
                    if (null != in_dbname && !in_dbname.equals("")) {
                        String strUseBase = "use " + in_dbname;
                        stmt.execute(strUseBase);
                    }
                    resultSet = stmt.executeQuery(sql);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("获取数据库中所有的库名表名出现异常");
                    // log.error("获取数据库中所有的库名表名出现异常");
                } finally {
                    HiveUtil.closeStmt(stmt);
                    HiveUtil.closeConn(conn);
                }
            } else if (in_dbtype.toLowerCase().contains("postgresql")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接postgresql数据库成功");
                sql = "select count(*) from " + in_tabname;//postgresql数据库不支持 db.table 的写法，重写一下
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains(Const.DB_GAUSS)) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接Gauss数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().contains("mongodb")) {
                MongoClient mongoClient = MongoDBUtil.getConnect(in_url, in_username, in_password, in_dbname);
                count = MongoDBUtil.countDocuments(in_dbname, in_tabname, mongoClient);
                mongoClient.close();
            } else if (in_dbtype.toLowerCase().equals("gbase8s")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接gbase8s数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            } else if (in_dbtype.toLowerCase().equals("bytehouse")) {
                Class.forName(in_drivername);
                con = (Connection) DriverManager.getConnection(in_url, in_username, in_password);
                log.info("连接bytehouse数据库成功");
                preparedStatement = con.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();
            }
            if (resultSet != null) {
                while (resultSet.next()) {
                    count = resultSet.getLong(1);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

//    private void initJar() {
//        try {
//            List<String> jarpathlist = algorithmService.getJavaJarPathList();
//            // 动态将目录下jar包加载到java环境
//            Method add = URLClassLoader.class.getDeclaredMethod("addURL", new Class[] { URL.class });
//            add.setAccessible(true);
//            String algorithm_jarpath = ConfigurationManager.getProperty("algorithm_jarpath");
//            if ("".equals(algorithm_jarpath)) {
//                for (String jarpath : algorithm_jarpath.split(";")) {
//                    URLClassLoader classloader = (URLClassLoader) ClassLoader.getSystemClassLoader();
//                    URL url = new File(jarpath).toURI().toURL();
//                    add.invoke(classloader, new Object[] { url });
//                }
//            }
//            if (jarpathlist != null && jarpathlist.size() > 0) {
//                for (String jarpath : jarpathlist) {
//                    URLClassLoader classloader = (URLClassLoader) ClassLoader.getSystemClassLoader();
//                    URL url = new File(jarpath).toURI().toURL();
//                    add.invoke(classloader, new Object[] { url });
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
}
