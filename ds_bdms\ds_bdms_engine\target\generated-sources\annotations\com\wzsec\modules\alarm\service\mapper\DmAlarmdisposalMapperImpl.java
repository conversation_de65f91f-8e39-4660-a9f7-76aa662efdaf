package com.wzsec.modules.alarm.service.mapper;

import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.dto.DmAlarmdisposalDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DmAlarmdisposalMapperImpl implements DmAlarmdisposalMapper {

    @Override
    public DmAlarmdisposalDto toDto(DmAlarmdisposal entity) {
        if ( entity == null ) {
            return null;
        }

        DmAlarmdisposalDto dmAlarmdisposalDto = new DmAlarmdisposalDto();

        dmAlarmdisposalDto.setAccount( entity.getAccount() );
        dmAlarmdisposalDto.setChecktime( entity.getChecktime() );
        dmAlarmdisposalDto.setCircumstantiality( entity.getCircumstantiality() );
        dmAlarmdisposalDto.setDestinationip( entity.getDestinationip() );
        dmAlarmdisposalDto.setDestinationport( entity.getDestinationport() );
        dmAlarmdisposalDto.setEventhandlingtime( entity.getEventhandlingtime() );
        dmAlarmdisposalDto.setEventrule( entity.getEventrule() );
        dmAlarmdisposalDto.setId( entity.getId() );
        dmAlarmdisposalDto.setIncidenthandler( entity.getIncidenthandler() );
        dmAlarmdisposalDto.setNote( entity.getNote() );
        dmAlarmdisposalDto.setPushnumber( entity.getPushnumber() );
        dmAlarmdisposalDto.setReservefield1( entity.getReservefield1() );
        dmAlarmdisposalDto.setReservefield2( entity.getReservefield2() );
        dmAlarmdisposalDto.setReservefield3( entity.getReservefield3() );
        dmAlarmdisposalDto.setReservefield4( entity.getReservefield4() );
        dmAlarmdisposalDto.setSourceip( entity.getSourceip() );
        dmAlarmdisposalDto.setSourceport( entity.getSourceport() );
        dmAlarmdisposalDto.setTreatmentstate( entity.getTreatmentstate() );

        return dmAlarmdisposalDto;
    }

    @Override
    public List<DmAlarmdisposalDto> toDto(List<DmAlarmdisposal> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DmAlarmdisposalDto> list = new ArrayList<DmAlarmdisposalDto>( entityList.size() );
        for ( DmAlarmdisposal dmAlarmdisposal : entityList ) {
            list.add( toDto( dmAlarmdisposal ) );
        }

        return list;
    }

    @Override
    public DmAlarmdisposal toEntity(DmAlarmdisposalDto dto) {
        if ( dto == null ) {
            return null;
        }

        DmAlarmdisposal dmAlarmdisposal = new DmAlarmdisposal();

        dmAlarmdisposal.setAccount( dto.getAccount() );
        dmAlarmdisposal.setChecktime( dto.getChecktime() );
        dmAlarmdisposal.setCircumstantiality( dto.getCircumstantiality() );
        dmAlarmdisposal.setDestinationip( dto.getDestinationip() );
        dmAlarmdisposal.setDestinationport( dto.getDestinationport() );
        dmAlarmdisposal.setEventhandlingtime( dto.getEventhandlingtime() );
        dmAlarmdisposal.setEventrule( dto.getEventrule() );
        dmAlarmdisposal.setId( dto.getId() );
        dmAlarmdisposal.setIncidenthandler( dto.getIncidenthandler() );
        dmAlarmdisposal.setNote( dto.getNote() );
        dmAlarmdisposal.setPushnumber( dto.getPushnumber() );
        dmAlarmdisposal.setReservefield1( dto.getReservefield1() );
        dmAlarmdisposal.setReservefield2( dto.getReservefield2() );
        dmAlarmdisposal.setReservefield3( dto.getReservefield3() );
        dmAlarmdisposal.setReservefield4( dto.getReservefield4() );
        dmAlarmdisposal.setSourceip( dto.getSourceip() );
        dmAlarmdisposal.setSourceport( dto.getSourceport() );
        dmAlarmdisposal.setTreatmentstate( dto.getTreatmentstate() );

        return dmAlarmdisposal;
    }

    @Override
    public List<DmAlarmdisposal> toEntity(List<DmAlarmdisposalDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DmAlarmdisposal> list = new ArrayList<DmAlarmdisposal>( dtoList.size() );
        for ( DmAlarmdisposalDto dmAlarmdisposalDto : dtoList ) {
            list.add( toEntity( dmAlarmdisposalDto ) );
        }

        return list;
    }
}
