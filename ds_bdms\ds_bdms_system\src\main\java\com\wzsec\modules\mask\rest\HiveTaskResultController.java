package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.HiveTaskResult;
import com.wzsec.modules.mask.service.HiveTaskResultService;
import com.wzsec.modules.mask.service.dto.HiveTaskResultDto;
import com.wzsec.modules.mask.service.dto.HiveTaskResultQueryCriteria;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-01-26
*/
// @Api(tags = "Hive脱敏结果管理")
@RestController
@RequestMapping("/api/hiveTaskResult")
public class HiveTaskResultController {

    private final HiveTaskResultService hiveTaskResultService;

    public HiveTaskResultController(HiveTaskResultService hiveTaskResultService) {
        this.hiveTaskResultService = hiveTaskResultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('hiveTaskResult:list')")
    public void download(HttpServletResponse response, HiveTaskResultQueryCriteria criteria) throws IOException {
        hiveTaskResultService.download(hiveTaskResultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询Hive脱敏结果")
    // @ApiOperation("查询Hive脱敏结果")
    @PreAuthorize("@el.check('hiveTaskResult:list')")
    public ResponseEntity<Object> getHiveTaskResults(HiveTaskResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(hiveTaskResultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增Hive脱敏结果")
    // @ApiOperation("新增Hive脱敏结果")
    @PreAuthorize("@el.check('hiveTaskResult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody HiveTaskResult resources){
        return new ResponseEntity<>(hiveTaskResultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改Hive脱敏结果")
    // @ApiOperation("修改Hive脱敏结果")
    @PreAuthorize("@el.check('hiveTaskResult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody HiveTaskResult resources){
        hiveTaskResultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除Hive脱敏结果")
    // @ApiOperation("删除Hive脱敏结果")
    @PreAuthorize("@el.check('hiveTaskResult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        hiveTaskResultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/getHiveTaskResultDetails")
    @Log("获取hive批量任务结果详情")
    public ResponseEntity<Object> getBatchTaskResultDetails(@RequestParam Integer id){
        HiveTaskResultDto byId = hiveTaskResultService.findById(id);
        String sparefield1 = byId.getSparefield1();
        String[] before = sparefield1.split("\n");
        String sparefield2 = byId.getSparefield2();
        String[] after = sparefield2.split("\n");
        List<HiveTaskResultDto> hiveTaskResultDtoList = new ArrayList<HiveTaskResultDto>();
        if (before.length == after.length) {
            HiveTaskResultDto hiveTaskResultDto =null;
            for (int i = 0; i < before.length; i++) {
                hiveTaskResultDto = new HiveTaskResultDto();
                hiveTaskResultDto.setSparefield1(before[i]);
                hiveTaskResultDto.setSparefield2(after[i]);
                hiveTaskResultDtoList.add(hiveTaskResultDto);
            }
        }
        return new ResponseEntity<>(PageUtil.toPage(hiveTaskResultDtoList,hiveTaskResultDtoList.size()),HttpStatus.OK);
    }
}
