package com.wzsec.modules.alarm.service.mapper;

import com.wzsec.base.BaseMapper;
import com.wzsec.modules.alarm.domain.DmAlarmdisposal;
import com.wzsec.modules.alarm.service.dto.DmAlarmdisposalDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-04-07
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DmAlarmdisposalMapper extends BaseMapper<DmAlarmdisposalDto, DmAlarmdisposal> {

}