package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcSQLRecord;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcSQLRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JdbcSQLRecordMapperImpl implements JdbcSQLRecordMapper {

    @Override
    public JdbcSQLRecordDto toDto(JdbcSQLRecord entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcSQLRecordDto jdbcSQLRecordDto = new JdbcSQLRecordDto();

        jdbcSQLRecordDto.setDbname( entity.getDbname() );
        jdbcSQLRecordDto.setExecstate( entity.getExecstate() );
        jdbcSQLRecordDto.setHandlestate( entity.getHandlestate() );
        jdbcSQLRecordDto.setInserttime( entity.getInserttime() );
        jdbcSQLRecordDto.setRequestip( entity.getRequestip() );
        jdbcSQLRecordDto.setRequestparam( entity.getRequestparam() );
        jdbcSQLRecordDto.setRequestsql( entity.getRequestsql() );
        jdbcSQLRecordDto.setRequesttime( entity.getRequesttime() );
        jdbcSQLRecordDto.setRequestuser( entity.getRequestuser() );
        jdbcSQLRecordDto.setResponserownum( entity.getResponserownum() );
        jdbcSQLRecordDto.setRewriteparam( entity.getRewriteparam() );
        jdbcSQLRecordDto.setRewritesql( entity.getRewritesql() );
        jdbcSQLRecordDto.setRewritestate( entity.getRewritestate() );
        jdbcSQLRecordDto.setSourceurl( entity.getSourceurl() );
        jdbcSQLRecordDto.setSparefield1( entity.getSparefield1() );
        jdbcSQLRecordDto.setSparefield2( entity.getSparefield2() );
        jdbcSQLRecordDto.setSparefield3( entity.getSparefield3() );
        jdbcSQLRecordDto.setSparefield4( entity.getSparefield4() );
        jdbcSQLRecordDto.setSparefield5( entity.getSparefield5() );
        jdbcSQLRecordDto.setSqltype( entity.getSqltype() );

        return jdbcSQLRecordDto;
    }

    @Override
    public List<JdbcSQLRecordDto> toDto(List<JdbcSQLRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcSQLRecordDto> list = new ArrayList<JdbcSQLRecordDto>( entityList.size() );
        for ( JdbcSQLRecord jdbcSQLRecord : entityList ) {
            list.add( toDto( jdbcSQLRecord ) );
        }

        return list;
    }

    @Override
    public JdbcSQLRecord toEntity(JdbcSQLRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcSQLRecord jdbcSQLRecord = new JdbcSQLRecord();

        jdbcSQLRecord.setDbname( dto.getDbname() );
        jdbcSQLRecord.setExecstate( dto.getExecstate() );
        jdbcSQLRecord.setHandlestate( dto.getHandlestate() );
        jdbcSQLRecord.setInserttime( dto.getInserttime() );
        jdbcSQLRecord.setRequestip( dto.getRequestip() );
        jdbcSQLRecord.setRequestparam( dto.getRequestparam() );
        jdbcSQLRecord.setRequestsql( dto.getRequestsql() );
        jdbcSQLRecord.setRequesttime( dto.getRequesttime() );
        jdbcSQLRecord.setRequestuser( dto.getRequestuser() );
        jdbcSQLRecord.setResponserownum( dto.getResponserownum() );
        jdbcSQLRecord.setRewriteparam( dto.getRewriteparam() );
        jdbcSQLRecord.setRewritesql( dto.getRewritesql() );
        jdbcSQLRecord.setRewritestate( dto.getRewritestate() );
        jdbcSQLRecord.setSourceurl( dto.getSourceurl() );
        jdbcSQLRecord.setSparefield1( dto.getSparefield1() );
        jdbcSQLRecord.setSparefield2( dto.getSparefield2() );
        jdbcSQLRecord.setSparefield3( dto.getSparefield3() );
        jdbcSQLRecord.setSparefield4( dto.getSparefield4() );
        jdbcSQLRecord.setSparefield5( dto.getSparefield5() );
        jdbcSQLRecord.setSqltype( dto.getSqltype() );

        return jdbcSQLRecord;
    }

    @Override
    public List<JdbcSQLRecord> toEntity(List<JdbcSQLRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcSQLRecord> list = new ArrayList<JdbcSQLRecord>( dtoList.size() );
        for ( JdbcSQLRecordDto jdbcSQLRecordDto : dtoList ) {
            list.add( toEntity( jdbcSQLRecordDto ) );
        }

        return list;
    }
}
