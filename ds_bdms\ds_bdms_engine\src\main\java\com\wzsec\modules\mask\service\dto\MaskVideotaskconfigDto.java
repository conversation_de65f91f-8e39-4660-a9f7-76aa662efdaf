package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2022-04-21
*/
@Data
public class MaskVideotaskconfigDto implements Serializable {

    /** ID */
    private Integer id;

    /** 任务名 */
    private String taskname;

    /** 原始视频目录 */
    private String inputdirectory;

    /** 输入文件格式 */
    private String inputfileformat;

    /** 计算资源 */
    private String computeresources;

    /** 脱敏对象 */
    private String maskobject;

    /** 任务状态 */
    private String state;

    /** 输出文件格式 */
    private String outputfileformat;

    /** 脱敏后视频或图片目录 */
    private String outputdirectory;

    /** 执行状态 */
    private String executionstate;

    /** 创建用户 */
    private String createuser;

    /** 创建时间 */
    private Timestamp createtime;

    /** 更新用户 */
    private String updateuser;

    /** 更新时间 */
    private Timestamp updatetime;

    /** 备注 */
    private String remark;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;
}