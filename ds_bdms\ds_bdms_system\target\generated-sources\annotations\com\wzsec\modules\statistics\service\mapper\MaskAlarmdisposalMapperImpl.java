package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.MaskAlarmdisposal;
import com.wzsec.modules.statistics.service.dto.MaskAlarmdisposalDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:04+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskAlarmdisposalMapperImpl implements MaskAlarmdisposalMapper {

    @Override
    public MaskAlarmdisposalDto toDto(MaskAlarmdisposal entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAlarmdisposalDto maskAlarmdisposalDto = new MaskAlarmdisposalDto();

        maskAlarmdisposalDto.setAccount( entity.getAccount() );
        maskAlarmdisposalDto.setChecktime( entity.getChecktime() );
        maskAlarmdisposalDto.setCircumstantiality( entity.getCircumstantiality() );
        maskAlarmdisposalDto.setDestinationip( entity.getDestinationip() );
        maskAlarmdisposalDto.setDestinationport( entity.getDestinationport() );
        maskAlarmdisposalDto.setEventhandlingtime( entity.getEventhandlingtime() );
        maskAlarmdisposalDto.setEventrule( entity.getEventrule() );
        maskAlarmdisposalDto.setId( entity.getId() );
        maskAlarmdisposalDto.setIncidenthandler( entity.getIncidenthandler() );
        maskAlarmdisposalDto.setNote( entity.getNote() );
        maskAlarmdisposalDto.setPushnumber( entity.getPushnumber() );
        maskAlarmdisposalDto.setReservefield1( entity.getReservefield1() );
        maskAlarmdisposalDto.setReservefield2( entity.getReservefield2() );
        maskAlarmdisposalDto.setReservefield3( entity.getReservefield3() );
        maskAlarmdisposalDto.setReservefield4( entity.getReservefield4() );
        maskAlarmdisposalDto.setSourceip( entity.getSourceip() );
        maskAlarmdisposalDto.setSourceport( entity.getSourceport() );
        maskAlarmdisposalDto.setTreatmentstate( entity.getTreatmentstate() );

        return maskAlarmdisposalDto;
    }

    @Override
    public List<MaskAlarmdisposalDto> toDto(List<MaskAlarmdisposal> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAlarmdisposalDto> list = new ArrayList<MaskAlarmdisposalDto>( entityList.size() );
        for ( MaskAlarmdisposal maskAlarmdisposal : entityList ) {
            list.add( toDto( maskAlarmdisposal ) );
        }

        return list;
    }

    @Override
    public MaskAlarmdisposal toEntity(MaskAlarmdisposalDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAlarmdisposal maskAlarmdisposal = new MaskAlarmdisposal();

        maskAlarmdisposal.setAccount( dto.getAccount() );
        maskAlarmdisposal.setChecktime( dto.getChecktime() );
        maskAlarmdisposal.setCircumstantiality( dto.getCircumstantiality() );
        maskAlarmdisposal.setDestinationip( dto.getDestinationip() );
        maskAlarmdisposal.setDestinationport( dto.getDestinationport() );
        maskAlarmdisposal.setEventhandlingtime( dto.getEventhandlingtime() );
        maskAlarmdisposal.setEventrule( dto.getEventrule() );
        maskAlarmdisposal.setId( dto.getId() );
        maskAlarmdisposal.setIncidenthandler( dto.getIncidenthandler() );
        maskAlarmdisposal.setNote( dto.getNote() );
        maskAlarmdisposal.setPushnumber( dto.getPushnumber() );
        maskAlarmdisposal.setReservefield1( dto.getReservefield1() );
        maskAlarmdisposal.setReservefield2( dto.getReservefield2() );
        maskAlarmdisposal.setReservefield3( dto.getReservefield3() );
        maskAlarmdisposal.setReservefield4( dto.getReservefield4() );
        maskAlarmdisposal.setSourceip( dto.getSourceip() );
        maskAlarmdisposal.setSourceport( dto.getSourceport() );
        maskAlarmdisposal.setTreatmentstate( dto.getTreatmentstate() );

        return maskAlarmdisposal;
    }

    @Override
    public List<MaskAlarmdisposal> toEntity(List<MaskAlarmdisposalDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAlarmdisposal> list = new ArrayList<MaskAlarmdisposal>( dtoList.size() );
        for ( MaskAlarmdisposalDto maskAlarmdisposalDto : dtoList ) {
            list.add( toEntity( maskAlarmdisposalDto ) );
        }

        return list;
    }
}
