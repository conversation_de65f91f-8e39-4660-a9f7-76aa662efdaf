package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.MaskAlarmdisposal;
import com.wzsec.modules.statistics.service.dto.MaskAlarmdisposalDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:20+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskAlarmdisposalMapperImpl implements MaskAlarmdisposalMapper {

    @Override
    public MaskAlarmdisposal toEntity(MaskAlarmdisposalDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAlarmdisposal maskAlarmdisposal = new MaskAlarmdisposal();

        maskAlarmdisposal.setId( dto.getId() );
        maskAlarmdisposal.setCircumstantiality( dto.getCircumstantiality() );
        maskAlarmdisposal.setChecktime( dto.getChecktime() );
        maskAlarmdisposal.setTreatmentstate( dto.getTreatmentstate() );
        maskAlarmdisposal.setNote( dto.getNote() );
        maskAlarmdisposal.setReservefield1( dto.getReservefield1() );
        maskAlarmdisposal.setReservefield2( dto.getReservefield2() );
        maskAlarmdisposal.setReservefield3( dto.getReservefield3() );
        maskAlarmdisposal.setReservefield4( dto.getReservefield4() );
        maskAlarmdisposal.setIncidenthandler( dto.getIncidenthandler() );
        maskAlarmdisposal.setEventhandlingtime( dto.getEventhandlingtime() );
        maskAlarmdisposal.setSourceip( dto.getSourceip() );
        maskAlarmdisposal.setSourceport( dto.getSourceport() );
        maskAlarmdisposal.setDestinationip( dto.getDestinationip() );
        maskAlarmdisposal.setDestinationport( dto.getDestinationport() );
        maskAlarmdisposal.setAccount( dto.getAccount() );
        maskAlarmdisposal.setEventrule( dto.getEventrule() );
        maskAlarmdisposal.setPushnumber( dto.getPushnumber() );

        return maskAlarmdisposal;
    }

    @Override
    public MaskAlarmdisposalDto toDto(MaskAlarmdisposal entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAlarmdisposalDto maskAlarmdisposalDto = new MaskAlarmdisposalDto();

        maskAlarmdisposalDto.setId( entity.getId() );
        maskAlarmdisposalDto.setCircumstantiality( entity.getCircumstantiality() );
        maskAlarmdisposalDto.setChecktime( entity.getChecktime() );
        maskAlarmdisposalDto.setTreatmentstate( entity.getTreatmentstate() );
        maskAlarmdisposalDto.setNote( entity.getNote() );
        maskAlarmdisposalDto.setReservefield1( entity.getReservefield1() );
        maskAlarmdisposalDto.setReservefield2( entity.getReservefield2() );
        maskAlarmdisposalDto.setReservefield3( entity.getReservefield3() );
        maskAlarmdisposalDto.setReservefield4( entity.getReservefield4() );
        maskAlarmdisposalDto.setIncidenthandler( entity.getIncidenthandler() );
        maskAlarmdisposalDto.setEventhandlingtime( entity.getEventhandlingtime() );
        maskAlarmdisposalDto.setSourceip( entity.getSourceip() );
        maskAlarmdisposalDto.setSourceport( entity.getSourceport() );
        maskAlarmdisposalDto.setDestinationip( entity.getDestinationip() );
        maskAlarmdisposalDto.setDestinationport( entity.getDestinationport() );
        maskAlarmdisposalDto.setAccount( entity.getAccount() );
        maskAlarmdisposalDto.setEventrule( entity.getEventrule() );
        maskAlarmdisposalDto.setPushnumber( entity.getPushnumber() );

        return maskAlarmdisposalDto;
    }

    @Override
    public List<MaskAlarmdisposal> toEntity(List<MaskAlarmdisposalDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAlarmdisposal> list = new ArrayList<MaskAlarmdisposal>( dtoList.size() );
        for ( MaskAlarmdisposalDto maskAlarmdisposalDto : dtoList ) {
            list.add( toEntity( maskAlarmdisposalDto ) );
        }

        return list;
    }

    @Override
    public List<MaskAlarmdisposalDto> toDto(List<MaskAlarmdisposal> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAlarmdisposalDto> list = new ArrayList<MaskAlarmdisposalDto>( entityList.size() );
        for ( MaskAlarmdisposal maskAlarmdisposal : entityList ) {
            list.add( toDto( maskAlarmdisposal ) );
        }

        return list;
    }
}
