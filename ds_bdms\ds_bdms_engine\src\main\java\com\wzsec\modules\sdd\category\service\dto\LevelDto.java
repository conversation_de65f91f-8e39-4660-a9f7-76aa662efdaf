package com.wzsec.modules.sdd.category.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2020-09-14
*/
@Data
public class LevelDto implements Serializable {

    /** ID */
    private Long id;

    /** 级别 */
    private String level;

    /** 级别含义 */
    private String levelmeaning;

    /** 创建用户名 */
    private String createuser;

    /** 创建时间 */
    private Timestamp createtime;

    /** 更新用户名 */
    private String updateuser;

    /** 更新时间 */
    private Timestamp updatetime;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;

    /** 级别编号 */
    private String levelcode;
}
