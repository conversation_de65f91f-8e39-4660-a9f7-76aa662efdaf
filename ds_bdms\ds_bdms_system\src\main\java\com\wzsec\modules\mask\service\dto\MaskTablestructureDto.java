package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-14
*/
@Data
public class MaskTablestructureDto implements Serializable {

    private Integer id;

    /** 字段id */
    private String fieldid;

    /** 字段名 */
    private String fieldename;

    /** 字段中文名 */
    private String fieldcname;

    /** 表id */
    private String tableid;

    /** 表名 */
    private String tablename;

    /** 表中文名 */
    private String tablecname;

    /** 库名 */
    private String dbname;

    /** 数据属性(1标识符，2，准标识，3，敏感，4，非敏感） */
    private String attribute;

    /** 数据源 */
    private String source;

    /** 创建时间 */
    private String createtime;

    /** 更新时间 */
    private String updatetime;

    /** K匿名算法 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;
}