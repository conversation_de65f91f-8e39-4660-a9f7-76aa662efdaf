package com.wzsec.read.nio;

import com.wzsec.read.readfilethread.FileProcessHandler;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Method;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.Arrays;
import java.util.Map;

/**
 * 读取文件，NIO读取大文件
 *
 * <AUTHOR>
 * @version 2020年2月21日14:38:50
 */
public class NioMappedByteBuffer {

    final int BUFFER_SIZE = 1024 * 10;// 缓冲区大小
    byte[] left = new byte[0];// 第二部分
    final int LF = 10;// 换行符 ASCII
    final int CR = 13;// 回车符 ASCII

    // public static void main(String[] args) throws Exception {
    // long start = System.currentTimeMillis();
    // String filePath = "D://auditlog/新建文本文档.txt";
    // readFile(new File(filePath));
    // long end = System.currentTimeMillis();
    // System.out.println("用时:" + (end - start) + " ms");//
    // 如果想看MappedByteBuffer的读取速度，可以将两个deal注释掉
    // }

    @SuppressWarnings("resource")
    public void readFile(File readfile, Map<String, Object> params, FileProcessHandler fileProcessHandler)
            throws Exception {
        // A：使用IO
        // BufferedReader br = new BufferedReader(new FileReader(readfile));
        // String sline = null;
        // while ((sline = br.readLine()) != null) {
        // dealString(sline, o);
        // }
        // br.close();

        // B：使用NIO
        FileChannel channel = new FileInputStream(readfile).getChannel();
        MappedByteBuffer buffer = channel.map(FileChannel.MapMode.READ_ONLY, 0, channel.size());
        byte[] byteArray = new byte[BUFFER_SIZE];
        while (buffer.hasRemaining()) {
            if (buffer.limit() - buffer.position() > BUFFER_SIZE) {
                buffer.get(byteArray);
                dealByteArray(byteArray, false, params, fileProcessHandler, readfile.getAbsolutePath());
            } else {
                byte[] byteArray2 = new byte[buffer.limit() - buffer.position()];
                buffer.get(byteArray2);
                dealByteArray(byteArray2, true, params, fileProcessHandler, readfile.getAbsolutePath());
            }
        }
        buffer.clear();// 清空缓冲
        // 关闭文件打开状态，两个同时使用，使用new File(filePath).delete()验证
        clean(buffer);// 关闭，否则占用资源
        channel.close();
    }

    /**
     * 生硬地截取BUFFER_SIZE字节，必然会导致开头和末尾有问题，所以可以将byteArray分为2部分来处理。
     * 从末尾逆向寻找最后一个分隔符坐标endIndex，将endIndex-末尾作为第二部分。 将0-endIndex作为第一部分。
     * 每次deal的数据是：上一次deal的第二部分+本次deal的第一部分，同时保存本次deal的第二部分，以供下次deal使用。
     *
     * @param byteArray
     * @param EOF
     */
    public void dealByteArray(byte[] byteArray, boolean EOF, Map<String, Object> params, FileProcessHandler fileProcessHandler, String currentFilePath) {
        String input;// 多行数据，以换行或回车结尾
        if (!EOF && findLastDelimiter(byteArray) > 0) {
            int endIndex = findLastDelimiter(byteArray);
            byte[] firstPart = Arrays.copyOfRange(byteArray, 0, endIndex);
            byte[] secondPart = Arrays.copyOfRange(byteArray, endIndex, byteArray.length);
            byte[] mergeArray = arrayMerge(left, firstPart);
            input = new String(mergeArray);
            left = secondPart;
        } else if (!EOF && findLastDelimiter(byteArray) < 0) {// 如果读取的字节数组里面没有分隔符，则该次读取的整个字节数组与left合并后保存至left供下次deal使用。
            byte[] mergeArray = arrayMerge(left, byteArray);
            left = mergeArray;
            return;
        } else {// 如果是读到文件末尾了，则该次读取的整个字节数组与left合并后并处理
            byte[] mergeArray = arrayMerge(left, byteArray);
            input = new String(mergeArray);
        }
        fileProcessHandler.processLineString(input, params, currentFilePath);
    }


    /**
     * 寻找最后一个换行符或回车index
     *
     * @param byteArray
     * @return
     */
    private int findLastDelimiter(byte[] byteArray) {
        for (int i = byteArray.length - 1; i >= 0; i--) {
            if (byteArray[i] == LF || byteArray[i] == CR) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 数组合并
     *
     * @param first
     * @param second
     * @return
     */
    private byte[] arrayMerge(byte[] first, byte[] second) {
        byte[] result = Arrays.copyOf(first, first.length + second.length);
        System.arraycopy(second, 0, result, first.length, second.length);
        return result;
    }

    /**
     * 关闭缓冲通道
     *
     * @param buffer
     * @throws Exception
     */
    private void clean(final Object buffer) throws Exception {
        AccessController.doPrivileged(new PrivilegedAction<Object>() {
            @Override
            public Object run() {
                try {
                    Method getCleanerMethod = buffer.getClass().getMethod("cleaner", new Class[0]);
                    getCleanerMethod.setAccessible(true);
                    sun.misc.Cleaner cleaner = (sun.misc.Cleaner) getCleanerMethod.invoke(buffer, new Object[0]);
                    cleaner.clean();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return null;
            }
        });
    }
}
