package com.wzsec.modules.sdk.service.mapper;

import com.wzsec.modules.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdk.service.dto.SdkApplyconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SdkApplyconfigMapperImpl implements SdkApplyconfigMapper {

    @Override
    public SdkApplyconfigDto toDto(SdkApplyconfig entity) {
        if ( entity == null ) {
            return null;
        }

        SdkApplyconfigDto sdkApplyconfigDto = new SdkApplyconfigDto();

        sdkApplyconfigDto.setApplyname( entity.getApplyname() );
        sdkApplyconfigDto.setApplysystemname( entity.getApplysystemname() );
        sdkApplyconfigDto.setContactphone( entity.getContactphone() );
        sdkApplyconfigDto.setCreatetime( entity.getCreatetime() );
        sdkApplyconfigDto.setCreateuser( entity.getCreateuser() );
        sdkApplyconfigDto.setId( entity.getId() );
        sdkApplyconfigDto.setSdkid( entity.getSdkid() );
        sdkApplyconfigDto.setSdkname( entity.getSdkname() );
        sdkApplyconfigDto.setSparefield1( entity.getSparefield1() );
        sdkApplyconfigDto.setSparefield2( entity.getSparefield2() );
        sdkApplyconfigDto.setSparefield3( entity.getSparefield3() );
        sdkApplyconfigDto.setSparefield4( entity.getSparefield4() );
        sdkApplyconfigDto.setState( entity.getState() );
        sdkApplyconfigDto.setUpdatetime( entity.getUpdatetime() );
        sdkApplyconfigDto.setUpdateuser( entity.getUpdateuser() );
        sdkApplyconfigDto.setUseway( entity.getUseway() );
        sdkApplyconfigDto.setVersion( entity.getVersion() );

        return sdkApplyconfigDto;
    }

    @Override
    public List<SdkApplyconfigDto> toDto(List<SdkApplyconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SdkApplyconfigDto> list = new ArrayList<SdkApplyconfigDto>( entityList.size() );
        for ( SdkApplyconfig sdkApplyconfig : entityList ) {
            list.add( toDto( sdkApplyconfig ) );
        }

        return list;
    }

    @Override
    public SdkApplyconfig toEntity(SdkApplyconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        SdkApplyconfig sdkApplyconfig = new SdkApplyconfig();

        sdkApplyconfig.setApplyname( dto.getApplyname() );
        sdkApplyconfig.setApplysystemname( dto.getApplysystemname() );
        sdkApplyconfig.setContactphone( dto.getContactphone() );
        sdkApplyconfig.setCreatetime( dto.getCreatetime() );
        sdkApplyconfig.setCreateuser( dto.getCreateuser() );
        sdkApplyconfig.setId( dto.getId() );
        sdkApplyconfig.setSdkid( dto.getSdkid() );
        sdkApplyconfig.setSdkname( dto.getSdkname() );
        sdkApplyconfig.setSparefield1( dto.getSparefield1() );
        sdkApplyconfig.setSparefield2( dto.getSparefield2() );
        sdkApplyconfig.setSparefield3( dto.getSparefield3() );
        sdkApplyconfig.setSparefield4( dto.getSparefield4() );
        sdkApplyconfig.setState( dto.getState() );
        sdkApplyconfig.setUpdatetime( dto.getUpdatetime() );
        sdkApplyconfig.setUpdateuser( dto.getUpdateuser() );
        sdkApplyconfig.setUseway( dto.getUseway() );
        sdkApplyconfig.setVersion( dto.getVersion() );

        return sdkApplyconfig;
    }

    @Override
    public List<SdkApplyconfig> toEntity(List<SdkApplyconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SdkApplyconfig> list = new ArrayList<SdkApplyconfig>( dtoList.size() );
        for ( SdkApplyconfigDto sdkApplyconfigDto : dtoList ) {
            list.add( toEntity( sdkApplyconfigDto ) );
        }

        return list;
    }
}
