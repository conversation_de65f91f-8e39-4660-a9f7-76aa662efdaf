package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-09
*/
@Data
public class MaskStrategyFieldDto implements Serializable {

    /** 主键 */
    private Integer id;

    /** 策略表id */
    private Integer stategytableid;

    /** 表ID */
    private String tableid;

    /** 抽取字段 */
    private Boolean extractfield;

    /** 表名 */
    private String tabename;

    /** 表中文名 */
    private String tabcname;

    /** 库名 */
    private String dbname;

    /** 字段ID */
    private String fieldid;

    /** 字段名 */
    private String fieldename;

    /** 字段中文名 */
    private String fieldcname;

    /** 字段类型 */
    private String fieldtype;

    /** 敏感度 */
    private String senLevel;

    /** 规则id */
    private Integer ruleid;

    /** 算法ID */
    private Integer algorithmid;

    /** 参数 */
    private String param;

    /** 密钥 */
    private String secretkey;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;

    /** 备用字段6 */
    private String sparefield6;
}
