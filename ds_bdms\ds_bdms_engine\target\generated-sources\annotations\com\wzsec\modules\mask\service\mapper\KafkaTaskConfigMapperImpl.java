package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.KafkaTaskConfig;
import com.wzsec.modules.mask.service.dto.KafkaTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KafkaTaskConfigMapperImpl implements KafkaTaskConfigMapper {

    @Override
    public KafkaTaskConfigDto toDto(KafkaTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        KafkaTaskConfigDto kafkaTaskConfigDto = new KafkaTaskConfigDto();

        kafkaTaskConfigDto.setCreatetime( entity.getCreatetime() );
        kafkaTaskConfigDto.setCreateuser( entity.getCreateuser() );
        kafkaTaskConfigDto.setDatatopic( entity.getDatatopic() );
        kafkaTaskConfigDto.setDatatype( entity.getDatatype() );
        kafkaTaskConfigDto.setDbname( entity.getDbname() );
        kafkaTaskConfigDto.setId( entity.getId() );
        kafkaTaskConfigDto.setIsvalid( entity.getIsvalid() );
        kafkaTaskConfigDto.setNote( entity.getNote() );
        kafkaTaskConfigDto.setRediskey( entity.getRediskey() );
        kafkaTaskConfigDto.setSparefield1( entity.getSparefield1() );
        kafkaTaskConfigDto.setSparefield2( entity.getSparefield2() );
        kafkaTaskConfigDto.setSparefield3( entity.getSparefield3() );
        kafkaTaskConfigDto.setSparefield4( entity.getSparefield4() );
        kafkaTaskConfigDto.setStatus( entity.getStatus() );
        kafkaTaskConfigDto.setStrategyid( entity.getStrategyid() );
        kafkaTaskConfigDto.setTaskname( entity.getTaskname() );
        kafkaTaskConfigDto.setTbname( entity.getTbname() );
        kafkaTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        kafkaTaskConfigDto.setUpdateuser( entity.getUpdateuser() );

        return kafkaTaskConfigDto;
    }

    @Override
    public List<KafkaTaskConfigDto> toDto(List<KafkaTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KafkaTaskConfigDto> list = new ArrayList<KafkaTaskConfigDto>( entityList.size() );
        for ( KafkaTaskConfig kafkaTaskConfig : entityList ) {
            list.add( toDto( kafkaTaskConfig ) );
        }

        return list;
    }

    @Override
    public KafkaTaskConfig toEntity(KafkaTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        KafkaTaskConfig kafkaTaskConfig = new KafkaTaskConfig();

        kafkaTaskConfig.setCreatetime( dto.getCreatetime() );
        kafkaTaskConfig.setCreateuser( dto.getCreateuser() );
        kafkaTaskConfig.setDatatopic( dto.getDatatopic() );
        kafkaTaskConfig.setDatatype( dto.getDatatype() );
        kafkaTaskConfig.setDbname( dto.getDbname() );
        kafkaTaskConfig.setId( dto.getId() );
        kafkaTaskConfig.setIsvalid( dto.getIsvalid() );
        kafkaTaskConfig.setNote( dto.getNote() );
        kafkaTaskConfig.setRediskey( dto.getRediskey() );
        kafkaTaskConfig.setSparefield1( dto.getSparefield1() );
        kafkaTaskConfig.setSparefield2( dto.getSparefield2() );
        kafkaTaskConfig.setSparefield3( dto.getSparefield3() );
        kafkaTaskConfig.setSparefield4( dto.getSparefield4() );
        kafkaTaskConfig.setStatus( dto.getStatus() );
        kafkaTaskConfig.setStrategyid( dto.getStrategyid() );
        kafkaTaskConfig.setTaskname( dto.getTaskname() );
        kafkaTaskConfig.setTbname( dto.getTbname() );
        kafkaTaskConfig.setUpdatetime( dto.getUpdatetime() );
        kafkaTaskConfig.setUpdateuser( dto.getUpdateuser() );

        return kafkaTaskConfig;
    }

    @Override
    public List<KafkaTaskConfig> toEntity(List<KafkaTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KafkaTaskConfig> list = new ArrayList<KafkaTaskConfig>( dtoList.size() );
        for ( KafkaTaskConfigDto kafkaTaskConfigDto : dtoList ) {
            list.add( toEntity( kafkaTaskConfigDto ) );
        }

        return list;
    }
}
