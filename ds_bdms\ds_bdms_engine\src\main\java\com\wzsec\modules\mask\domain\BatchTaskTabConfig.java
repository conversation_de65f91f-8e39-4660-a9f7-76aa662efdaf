package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-02-07
 */
@Entity
@Data
@Table(name="sdd_mask_batchtasktabconfig")
public class BatchTaskTabConfig implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 批量任务id */
    @Column(name = "batchtaskid")
    private Long batchtaskid;

    /** 表id */
    @Column(name = "tableid")
    private Long tableid;

    /** 表名 */
    @Column(name = "tabename")
    private String tabename;

    /** 表中文名 */
    @Column(name = "tabcname")
    private String tabcname;

    /**脱敏策略id */
    @Column(name = "strategyid")
    private Long strategyid;

    /** 限制条件 */
    @Column(name = "conlimit")
    private String conlimit;

    /** 是否基于原表改写 */
    @Column(name = "isrewritetab")
    private String isrewritetab;

    /** 是否添加水印(1是，2否) */
    @Column(name = "iswatermark")
    private String iswatermark;

    /** 数据提供方 */
    @Column(name = "dataprovider")
    private String dataprovider;

    /** 数据使用方 */
    @Column(name = "datause")
    private String datause;

    /** 水印列(水印字段) */
    @Column(name = "watermarkcol")
    private String watermarkcol;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    public void copy(BatchTaskTabConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}