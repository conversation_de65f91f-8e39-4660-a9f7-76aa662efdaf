2025-07-24 08:25:51,838 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 11992 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-24 08:25:51,850 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-24 08:25:57,023 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-24 08:25:57,026 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 08:25:59,513 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 2337 ms. Found 111 JPA repository interfaces.
2025-07-24 08:26:01,583 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-24 08:26:01,587 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-24 08:26:01,595 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 08:26:01,595 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 08:26:01,603 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 08:26:01,608 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-24 08:26:01,611 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-24 08:26:01,612 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 08:26:01,613 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 08:26:04,219 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-24 08:26:04,259 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-24 08:26:04,266 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-24 08:26:05,833 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-24 08:26:05,958 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-24 08:26:05,976 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-24 08:26:05,976 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-24 08:26:06,239 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.4276606219574310260] which is part of the web application []
2025-07-24 08:26:06,733 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-24 08:26:06,734 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 13890 ms
2025-07-24 08:26:10,336 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-24 08:26:10,955 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-24 08:26:11,034 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-24 08:26:11,036 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-24 08:26:11,037 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-24 08:26:11,037 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-24 08:26:11,038 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-24 08:26:11,040 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-24 08:26:11,047 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-24 08:26:11,057 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-24 08:26:15,587 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-24 08:26:17,663 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 08:26:18,170 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-24 08:26:20,130 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 08:26:21,771 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-24 08:26:31,233 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 08:26:31,337 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 08:26:36,035 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-24 08:26:43,474 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-24 08:26:43,640 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 08:26:43,640 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-24 08:26:43,671 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-24 08:26:43,679 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 08:26:43,679 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-24 08:26:43,679 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-24 08:26:43,680 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@1c97ede3
2025-07-24 08:26:43,681 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-24 08:27:13,238 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b4340f8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@64a12381, org.springframework.security.web.context.SecurityContextPersistenceFilter@241c6c56, org.springframework.security.web.header.HeaderWriterFilter@3aea0fc2, org.springframework.security.web.authentication.logout.LogoutFilter@2b217392, org.springframework.web.filter.CorsFilter@7672960e, com.wzsec.modules.security.security.TokenFilter@53162e9c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4330f93c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a2ae0f2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@375575e2, org.springframework.security.web.session.SessionManagementFilter@695d66ce, org.springframework.security.web.access.ExceptionTranslationFilter@54d6130, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@54a0f09d]
2025-07-24 08:27:13,413 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-24 08:27:26,706 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-24 08:27:26,708 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 08:27:26,708 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-24 08:27:26,708 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-24 08:27:26,708 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 08:27:26,709 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 08:27:26,709 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-24 08:27:26,709 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@35c7c5e
2025-07-24 08:27:27,151 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-24 08:27:27,332 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-24 08:27:27,687 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-24 08:27:27,698 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-24 08:27:27,701 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-24 08:27:27,701 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-24 08:27:27,701 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-24 08:27:27,701 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-24 08:27:27,701 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-24 08:27:27,701 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 08:27:27,701 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-24 08:27:27,703 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 08:27:27,711 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-24 08:27:27,712 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-24 08:27:27,712 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 08:27:27,724 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-24 08:27:29,692 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-24 08:27:30,183 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-24 08:27:30,442 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-24 08:27:30,680 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-24 08:27:30,928 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-24 08:27:30,928 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-24 08:27:30,945 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 100.986 seconds (JVM running for 106.396)
2025-07-24 08:27:32,011 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-24 08:47:02,439 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 08:47:02,439 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-24 08:47:02,579 INFO (FrameworkServlet.java:547)- Completed initialization in 140 ms
2025-07-24 09:35:02,939 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 09:46:09,304 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 24328 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-24 09:46:09,310 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-24 09:46:11,905 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-24 09:46:11,905 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 09:46:13,026 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1001 ms. Found 111 JPA repository interfaces.
2025-07-24 09:46:13,578 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-24 09:46:13,578 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-24 09:46:13,591 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 09:46:13,591 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 09:46:13,593 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 09:46:13,598 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-24 09:46:13,598 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-24 09:46:13,599 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 09:46:13,599 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 09:46:14,613 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-24 09:46:14,630 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-24 09:46:14,633 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-24 09:46:15,157 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-24 09:46:15,191 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-24 09:46:15,196 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-24 09:46:15,196 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-24 09:46:15,252 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.2663720437771080939] which is part of the web application []
2025-07-24 09:46:15,532 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-24 09:46:15,532 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5718 ms
2025-07-24 09:46:17,128 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-24 09:46:17,545 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-24 09:46:17,578 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-24 09:46:17,580 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-24 09:46:17,580 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-24 09:46:17,580 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-24 09:46:17,580 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-24 09:46:17,580 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-24 09:46:17,585 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-24 09:46:17,585 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-24 09:46:20,205 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-24 09:46:21,168 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 09:46:21,421 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-24 09:46:22,009 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 09:46:22,578 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-24 09:46:26,841 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 09:46:26,892 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 09:46:29,512 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-24 09:46:33,141 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-24 09:46:33,227 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 09:46:33,228 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-24 09:46:33,253 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-24 09:46:33,261 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 09:46:33,261 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-24 09:46:33,261 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-24 09:46:33,261 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6cc57f26
2025-07-24 09:46:33,263 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-24 09:46:42,940 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@47e1255c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5de29c1e, org.springframework.security.web.context.SecurityContextPersistenceFilter@387759f7, org.springframework.security.web.header.HeaderWriterFilter@319f655b, org.springframework.security.web.authentication.logout.LogoutFilter@518cd198, org.springframework.web.filter.CorsFilter@2862271a, com.wzsec.modules.security.security.TokenFilter@b4e52a4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@42f55913, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25684391, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c66040f, org.springframework.security.web.session.SessionManagementFilter@30c6bd76, org.springframework.security.web.access.ExceptionTranslationFilter@73413f9a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@666d83ed]
2025-07-24 09:46:42,989 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-24 09:46:47,564 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-24 09:46:47,565 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 09:46:47,565 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-24 09:46:47,565 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-24 09:46:47,566 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 09:46:47,566 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 09:46:47,566 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-24 09:46:47,566 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@41b5ad5f
2025-07-24 09:46:47,777 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-24 09:46:47,864 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-24 09:46:47,950 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-24 09:46:47,954 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-24 09:46:47,956 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-24 09:46:47,956 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-24 09:46:47,956 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-24 09:46:47,956 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-24 09:46:47,956 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-24 09:46:47,956 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 09:46:47,956 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-24 09:46:47,956 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 09:46:47,959 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-24 09:46:47,959 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-24 09:46:47,959 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 09:46:47,964 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-24 09:46:49,156 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-24 09:46:49,362 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-24 09:46:49,648 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-24 09:46:49,866 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-24 09:46:50,105 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-24 09:46:50,108 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-24 09:46:50,110 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 41.516 seconds (JVM running for 44.271)
2025-07-24 09:46:50,867 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-24 09:47:12,451 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 09:47:12,451 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-24 09:47:12,451 INFO (FrameworkServlet.java:547)- Completed initialization in 0 ms
2025-07-24 10:29:24,182 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 16276 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-24 10:29:24,189 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-24 10:29:27,925 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-24 10:29:27,928 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-24 10:29:29,192 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1131 ms. Found 111 JPA repository interfaces.
2025-07-24 10:29:30,088 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-24 10:29:30,090 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-24 10:29:30,093 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 10:29:30,093 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 10:29:30,097 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 10:29:30,099 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-24 10:29:30,101 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-24 10:29:30,101 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 10:29:30,101 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 10:29:31,193 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-24 10:29:31,216 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-24 10:29:31,222 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-24 10:29:31,796 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-24 10:29:31,824 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-24 10:29:31,828 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-24 10:29:31,828 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-24 10:29:31,879 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.1748694333055139041] which is part of the web application []
2025-07-24 10:29:32,215 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-24 10:29:32,215 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 7362 ms
2025-07-24 10:29:34,054 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-24 10:29:34,465 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-24 10:29:34,494 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-24 10:29:34,497 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-24 10:29:34,498 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-24 10:29:34,498 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-24 10:29:34,498 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-24 10:29:34,498 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-24 10:29:34,502 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-24 10:29:34,506 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-24 10:29:37,360 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-24 10:29:38,296 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-24 10:29:38,507 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-24 10:29:39,223 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-24 10:29:39,920 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-24 10:29:44,665 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-24 10:29:44,717 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 10:29:47,654 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-24 10:29:51,665 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-24 10:29:51,774 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 10:29:51,777 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-24 10:29:51,805 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-24 10:29:51,812 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 10:29:51,815 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-24 10:29:51,815 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-24 10:29:51,817 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@7a234d98
2025-07-24 10:29:51,817 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-24 10:30:02,950 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@139596c2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3e4c4911, org.springframework.security.web.context.SecurityContextPersistenceFilter@5c273b3e, org.springframework.security.web.header.HeaderWriterFilter@486c3d3d, org.springframework.security.web.authentication.logout.LogoutFilter@11fe2600, org.springframework.web.filter.CorsFilter@93f15f6, com.wzsec.modules.security.security.TokenFilter@6ab1860, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6079a082, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@31e376e4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4261dc39, org.springframework.security.web.session.SessionManagementFilter@f0ca75, org.springframework.security.web.access.ExceptionTranslationFilter@238e788e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3f832bd]
2025-07-24 10:30:02,999 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-24 10:30:07,760 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-24 10:30:07,763 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 10:30:07,763 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-24 10:30:07,763 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-24 10:30:07,763 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 10:30:07,763 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 10:30:07,763 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-24 10:30:07,763 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4bc261a7
2025-07-24 10:30:07,943 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-24 10:30:08,037 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-24 10:30:08,169 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-24 10:30:08,174 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-24 10:30:08,176 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-24 10:30:08,176 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-24 10:30:08,176 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-24 10:30:08,176 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-24 10:30:08,176 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-24 10:30:08,176 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-24 10:30:08,176 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-24 10:30:08,176 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-24 10:30:08,180 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-24 10:30:08,180 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-24 10:30:08,180 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 10:30:08,188 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-24 10:30:09,501 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-24 10:30:09,703 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-24 10:30:09,933 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-24 10:30:10,153 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-24 10:30:10,365 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-24 10:30:10,365 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-24 10:30:10,379 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 47.323 seconds (JVM running for 51.791)
2025-07-24 10:30:10,991 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-24 10:32:08,663 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 10:32:08,663 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-24 10:32:08,665 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-24 10:33:36,714 ERROR (DruidDataSource.java:2471)- create connection SQLException, url: ****************************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:829)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:449)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:242)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at net.sf.log4jdbc.sql.jdbcapi.DriverSpy.connect(DriverSpy.java:401)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:156)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:218)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1560)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1623)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2468)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:949)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:819)
	... 10 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:156)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 13 common frames omitted
2025-07-24 11:38:01,095 ERROR (JobRunShell.java:211)- Job DEFAULT.19 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl.executionFromEngine(MaskAuditTaskV1ServiceImpl.java:179)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$FastClassBySpringCGLIB$$a4bd1e4d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$EnhancerBySpringCGLIB$$c07f6dcc.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskAuditTaskConfigJob.execute(MaskAuditTaskConfigJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 11:38:01,106 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.19 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl.executionFromEngine(MaskAuditTaskV1ServiceImpl.java:179)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$FastClassBySpringCGLIB$$a4bd1e4d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.MaskAuditTaskV1ServiceImpl$$EnhancerBySpringCGLIB$$c07f6dcc.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.MaskAuditTaskConfigJob.execute(MaskAuditTaskConfigJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 14:45:02,023 ERROR (JobRunShell.java:211)- Job DEFAULT.14 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 14:45:02,033 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.14 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 16:06:49,860 ERROR (DirectJDKLog.java:175)- Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:271)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1062)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:919)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:665)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker.just(LettuceInvoker.java:94)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:55)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:267)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.wzsec.utils.RedisUtils.get(RedisUtils.java:169)
	at com.wzsec.modules.security.service.OnlineUserService.getOne(OnlineUserService.java:134)
	at com.wzsec.modules.security.security.TokenFilter.doFilter(TokenFilter.java:56)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:361)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:225)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:190)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1793)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1060)
	... 60 common frames omitted
Caused by: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:256)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-07-24 16:06:49,926 INFO (AbstractInternalLogger.java:171)- Reconnecting, last destination was /150.223.22.107:9001
2025-07-24 16:06:50,367 INFO (ReconnectionHandler.java:174)- Reconnected to 150.223.22.107:9001
2025-07-24 16:11:00,603 ERROR (JobRunShell.java:211)- Job DEFAULT.131 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 16:11:00,604 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.131 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 17:04:27,444 ERROR (JobRunShell.java:211)- Job DEFAULT.174 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$598ae3d0.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 17:04:27,446 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.174 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$598ae3d0.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 17:29:01,342 ERROR (JobRunShell.java:211)- Job DEFAULT.27 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl.executionFromEngine(FileTaskConfigServiceImpl.java:234)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$FastClassBySpringCGLIB$$5968d26b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$49ad0ce6.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.FileTaskConfigJob.execute(FileTaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 17:29:01,343 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.27 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl.executionFromEngine(FileTaskConfigServiceImpl.java:234)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$FastClassBySpringCGLIB$$5968d26b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.FileTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$49ad0ce6.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.FileTaskConfigJob.execute(FileTaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 17:36:26,842 ERROR (JobRunShell.java:211)- Job DEFAULT.175 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$598ae3d0.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 17:36:26,846 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.175 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl.executionFromEngine(DbBatchTaskConfigServiceImpl.java:280)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$FastClassBySpringCGLIB$$e9aadc79.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.mask.service.impl.DbBatchTaskConfigServiceImpl$$EnhancerBySpringCGLIB$$598ae3d0.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.DbBatchTaskConfigJob.execute(DbBatchTaskConfigJob.java:36)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 17:54:24,119 ERROR (JobRunShell.java:211)- Job DEFAULT.256 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 17:54:24,119 ERROR (JobRunShell.java:211)- Job DEFAULT.258 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 17:54:24,125 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.256 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 17:54:24,125 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.258 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 17:54:24,210 ERROR (JobRunShell.java:211)- Job DEFAULT.257 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-24 17:54:24,211 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.257 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$5d3d80a9.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-24 18:07:22,746 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 18:07:23,863 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-24 18:07:23,865 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 18:07:23,865 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 18:07:23,867 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 18:07:23,982 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 18:07:23,982 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 18:07:23,983 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 18:07:24,000 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-24 18:07:24,141 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
