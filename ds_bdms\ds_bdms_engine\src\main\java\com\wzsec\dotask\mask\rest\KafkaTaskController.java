package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoKafkaTaskConfigService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2021年2月26日15:26:04
 */
// @Api(tags = "Kafka脱敏任务管理")
@RestController
@RequestMapping("/engine/kafka/task")
public class KafkaTaskController {

    private final DoKafkaTaskConfigService doKafkaTaskConfigService;

    private final UserService userService;

    public KafkaTaskController(DoKafkaTaskConfigService doKafkaTaskConfigService, UserService userService) {
        this.doKafkaTaskConfigService = doKafkaTaskConfigService;
        this.userService = userService;
    }

    @Log("执行Kafka脱敏任务")
    // @ApiOperation("执行Kafka脱敏任务")
    @PostMapping(value = "/exec/{id}")
    @PreAuthorize("@el.check('kafkataskconfig:edit')")
    public ResponseEntity<Object> execution(@PathVariable Long id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doKafkaTaskConfigService.execution(id, byName.getNickName());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("停止Kafka脱敏任务")
    // @ApiOperation("停止Kafka脱敏任务")
    @PostMapping(value = "/stop/{id}")
    @PreAuthorize("@el.check('kafkataskconfig:edit')")
    public ResponseEntity<Object> stop(@PathVariable Long id) {
        System.out.println("开始执行：" + id);
        doKafkaTaskConfigService.stopTask(id, Const.TASK_SUBMITTYPE_AUTOSUBMIT);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
