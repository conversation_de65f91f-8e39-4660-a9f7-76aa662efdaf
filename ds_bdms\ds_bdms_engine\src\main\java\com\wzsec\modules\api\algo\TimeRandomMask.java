package com.wzsec.modules.api.algo;

import org.apache.commons.lang.StringUtils;

import java.util.Random;

/**
 * 改写结果脱敏算法_随机时间
 */
public class TimeRandomMask {
    /**
     * 随机时间
     */
    public static String encrypt(String strData) {

        String time = randomDate();

        if (strData != null && !strData.equals("")) {
            String strMaskResult = time;
            String[] strMaskResultArr = strMaskResult.split("");
            return StringUtils.join(strMaskResultArr, "");
        }
        return null;
    }


    private static String randomDate() {
        Random rndYear = new Random();
        int year = rndYear.nextInt(18) + 2000;
        Random rndMonth = new Random();
        int month = rndMonth.nextInt(12) + 1;
        Random rndDay = new Random();
        int Day = rndDay.nextInt(30) + 1;
        Random rndHour = new Random();
        int hour = rndHour.nextInt(23);
        Random rndMinute = new Random();
        int minute = rndMinute.nextInt(60);
        Random rndSecond = new Random();
        int second = rndSecond.nextInt(60);
        return year + "-" + cp(month) + "-" + cp(Day) + " " + cp(hour) + ":" + cp(minute) + ":" + cp(second);
    }

    private static String cp(int num) {
        String Num = num + "";
        if (Num.length() == 1) {
            return "0" + Num;
        } else {
            return Num;
        }
    }


    public static void main(String[] args) {
        String strData = "2019-11-19 00:34:57";
        System.out.println(encrypt(strData));
        System.out.println(encrypt(strData));
        System.out.println(encrypt(strData));
    }

}
