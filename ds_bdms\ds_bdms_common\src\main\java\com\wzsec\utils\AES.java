package com.wzsec.utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 *  AES加解密算法
 *  1.需要密钥（如12349876）,可逆算法
 *  备注: 解决在Linux下解密出现异常:
 *  javax.crypto.BadPaddingException: Given final block not properly padded 
 */
public class AES {
	
    private static Key initKeyForAES(String key) throws NoSuchAlgorithmException {
        if(null == key || key.length() == 0) {
            throw new NullPointerException("key not is null");
        }
        SecretKeySpec key2 = null;
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        random.setSeed(key.getBytes());
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128, random);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            key2 = new SecretKeySpec(enCodeFormat, "AES");
        } 
        catch(NoSuchAlgorithmException ex) {
            throw new NoSuchAlgorithmException();
        }
        return key2;
    }
    
    /**
     * 加密
     * @param content 明文字符串
     * @param strKey 密钥
     * @return 密文字节数组
     * @throws Exception
     */
    public static byte[] enCrypt(String content, String strKey) throws Exception {
        /*//实例化AES密钥生成器
        KeyGenerator keygen = KeyGenerator.getInstance("AES");
        //使用用户提供的密钥初始化此密钥生成器，使其具有确定的密钥长度128位。
        keygen.init(128, new SecureRandom(strKey.getBytes()));
        SecretKey desKey = keygen.generateKey();//生成一个密钥  */
    	
        //实例化加密算法对象
        Cipher cipher = Cipher.getInstance("AES");
        //使用密钥初始化，设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, initKeyForAES(strKey));
        byte[] cByte = cipher.doFinal(content.getBytes("UTF-8")); //加密算法对象对明文字节数组进行加密
        return cByte;
    }

    /**
     * 解密 (解密过程中，报错了： javax.crypto.BadPaddingException: Given final block not properly padded） 
     * @param src 密文字节数组
     * @param strKey 密钥
     * @return 字符串
     * @throws Exception
     */
    public static String deCrypt(byte[] src, String strKey) throws Exception {
        /* //实例化AES密钥生成器
        KeyGenerator keygen = KeyGenerator.getInstance("AES");
        keygen.init(128, new SecureRandom(strKey.getBytes()));
        SecretKey desKey = keygen.generateKey();*/
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, initKeyForAES(strKey));
        byte[] cByte = cipher.doFinal(src);
        return new String(cByte, "UTF-8");
    }

    /**
     * 字节数组转换为十六进制字符串
     * @param buf
     * @return
     */
    public static String parseByte2HexStr(byte[] buf) {
        StringBuffer sb = new StringBuffer();

        for(int i = 0; i < buf.length; ++i) {
            String hex = Integer.toHexString(buf[i] & 255);
            if(hex.length() == 1) {
                hex = '0' + hex;
            }

            sb.append(hex.toUpperCase());
        }

        return sb.toString();
    }

    /**
     *十六进制字符串转换为字节数组
     * @param hexStr
     * @return
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if(hexStr.length() < 1) {
            return null;
        }
        else{
            byte[] result = new byte[hexStr.length() / 2];
            for(int i = 0; i < hexStr.length() / 2; ++i) {
                int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
                int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
                result[i] = (byte)(high * 16 + low);
            }

            return result;
        }
    }

    /**
     * 加密
     * @param paramString  明文字符串
     * @param key 密钥
     * @return 密文字符串
     * @throws Exception
     */
    public static String encrypt(String paramString, String key) {
        String returnstr = "";
        byte[] byteRe = new byte[0];
        try {
            byteRe = enCrypt(paramString, key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        returnstr = parseByte2HexStr(byteRe);
        return returnstr;
    }

    /**
     * 解密
     * @param data 密文字符串
     * @param key  密钥
     * @return  明文字符串
     * @throws Exception
     */
    public static String decrypt(String data, String key){
        String rdata = "";
        if(data != null && !data.trim().equals("") && !data.matches("^[\\u0020\\u3000]*$")) {
            byte[] encrytByte = parseHexStr2Byte(data);
            try {
                rdata = deCrypt(encrytByte, key);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return rdata;
        }
        else{
            return data;
        }
    }

    public static void main(String[] args) {
        AES aes = new AES();
        try {

            System.out.println(AES.encrypt("123456",Const.AES_SECRET_KEY));
            System.out.println(AES.encrypt("root",Const.AES_SECRET_KEY));
            System.out.println(AES.encrypt("wz001",Const.AES_SECRET_KEY));
        }catch (Exception e){
            e.printStackTrace();
        }

//        try{
//            System.out.println(AES.encrypt("chinactyun", "12349876")); //明文大于16时 64个字符
//            System.out.println(AES.encrypt("123456789012", "1234a876")); //明文小于16时 32个字符
//            System.out.println(AES.encrypt("123456789012", "1234")); //加解密中每轮的密钥分别由初始密钥扩展得到
//            System.out.println(AES.encrypt("11010800000001大甜甜", "12349876"));
//            System.out.println(AES.encrypt("11010800000001@", "12349876"));
//            System.out.println(AES.encrypt("中华大", "12349876"));
//            System.out.println(AES.encrypt("", "12349876"));
//            System.out.println(AES.decrypt(AES.encrypt("chinactyun", "12349876"), "12349876"));
//            System.out.println(AES.decrypt(AES.encrypt("11010800000001大甜甜", "12349876"), "12349876"));
//            System.out.println(AES.decrypt(AES.encrypt("11010800000001@", "12349876"), "12349876"));
//            System.out.println(AES.decrypt(AES.encrypt("中华大", "12349876"), "12349876"));
//            System.out.println(AES.encrypt("", "12349876"));
//        }
//        catch(Exception var3) {
//            var3.printStackTrace();
//        }
    }
}
