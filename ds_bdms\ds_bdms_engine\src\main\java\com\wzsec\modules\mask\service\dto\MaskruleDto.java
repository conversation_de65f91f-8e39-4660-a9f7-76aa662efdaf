package com.wzsec.modules.mask.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2020-11-06
*/
@Data
public class MaskruleDto implements Serializable {

    /** 主键 */
    private Integer id;

    /** 规则名 */
    private String rulename;

    /** 规则中文名 */
    private String rulecname;

    /** 规范化字段中文名 */
    private String standardcname;

    /** 规范化字段英文名 */
    private String standardename;

    /** 算法ID */
    private Integer algorithmid;

    /** 参数 */
    private String param;

    /** 状态 */
    private String flag;

    /** 备注 */
    private String memo;

    /** 创建人 */
    private String createuser;

    /** 创建时间 */
    private Timestamp createtime;

    /** 更新人 */
    private String updateuser;

    /** 更新时间 */
    private Timestamp updatetime;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 备用字段5 */
    private String sparefield5;
}