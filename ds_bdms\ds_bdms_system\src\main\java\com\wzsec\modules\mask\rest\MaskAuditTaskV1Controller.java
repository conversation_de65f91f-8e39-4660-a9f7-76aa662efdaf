package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskAuditTaskV1;
import com.wzsec.modules.mask.service.MaskAuditTaskV1Service;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1Dto;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1QueryCriteria;
import com.wzsec.modules.quartz.config.MaskAuditTaskScanConfig;
import com.wzsec.utils.Const;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021-01-14
 */
// @Api(tags = "脱敏审计任务管理")
@RestController
@RequestMapping("/api/maskAuditTaskV1")
public class MaskAuditTaskV1Controller {

    private final MaskAuditTaskV1Service maskAuditTaskV1Service;
    private final MaskAuditTaskScanConfig maskAuditTaskScanConfig;

    public MaskAuditTaskV1Controller(MaskAuditTaskV1Service maskAuditTaskV1Service, MaskAuditTaskScanConfig maskAuditTaskScanConfig) {
        this.maskAuditTaskV1Service = maskAuditTaskV1Service;
        this.maskAuditTaskScanConfig = maskAuditTaskScanConfig;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskAuditTaskV1:list')")
    public void download(HttpServletResponse response, MaskAuditTaskV1QueryCriteria criteria) throws IOException {
        maskAuditTaskV1Service.download(maskAuditTaskV1Service.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询脱敏审计任务")
    // @ApiOperation("查询脱敏审计任务")
    @PreAuthorize("@el.check('maskAuditTaskV1:list')")
    public ResponseEntity<Object> getMaskAuditTaskV1s(MaskAuditTaskV1QueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(maskAuditTaskV1Service.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增脱敏审计任务")
    // @ApiOperation("新增脱敏审计任务")
    @PreAuthorize("@el.check('maskAuditTaskV1:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskAuditTaskV1 resources) {
        if ("".equals(resources.getDisstrategy())) {
            resources.setDisstrategy(null);
        }
        MaskAuditTaskV1Dto taskDto = maskAuditTaskV1Service.create(resources);
        if (taskDto != null && Const.TASK_STATE_USE.equals(taskDto.getStatus()) && Const.TASK_SUBMITTYPE_AUTO.equals(taskDto.getSubmitmethod())) {
            maskAuditTaskScanConfig.addJob(taskDto);
        }
        return new ResponseEntity<>(taskDto, HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改脱敏审计任务")
    // @ApiOperation("修改脱敏审计任务")
    @PreAuthorize("@el.check('maskAuditTaskV1:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskAuditTaskV1 resources) {
        //提交方式改为手动提交,将定时时间置空
        if (resources.getSubmitmethod().equals("1")) {
            resources.setCron("");
        }
        maskAuditTaskV1Service.update(resources);
        MaskAuditTaskV1Dto taskDto = maskAuditTaskV1Service.findById(resources.getId());
        if (taskDto != null && Const.TASK_SUBMITTYPE_AUTO.equals(taskDto.getSubmitmethod())) {
            maskAuditTaskScanConfig.updateJob(taskDto);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除脱敏审计任务")
    // @ApiOperation("删除脱敏审计任务")
    @PreAuthorize("@el.check('maskAuditTaskV1:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        for (Integer id : ids) {
            MaskAuditTaskV1Dto taskDto = maskAuditTaskV1Service.findById(id);
            maskAuditTaskScanConfig.deleteJob(taskDto);
        }
        maskAuditTaskV1Service.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("在引擎执行脱敏日志审计任务")
    // @ApiOperation("在引擎执行脱敏日志审计任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    @PreAuthorize("@el.check('maskAuditTaskV1:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Integer id, HttpServletRequest request) {
        maskAuditTaskV1Service.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("获取脱敏审计任务新增任务号")
    // @ApiOperation("获取脱敏日志审计任务新增任务号")
    @PreAuthorize("@el.check('maskAuditTaskV1:add')")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(maskAuditTaskV1Service.getMAXTaskName(), HttpStatus.CREATED);
    }

    @GetMapping(value = "/getMaskTaskNumber")
    public ResponseEntity<Object> getMaskTaskNumber() {
        return new ResponseEntity<>(maskAuditTaskV1Service.getMaskTaskNumber(), HttpStatus.OK);
    }
}
