package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.StatisticstaskOutline;
import com.wzsec.modules.statistics.service.dto.StatisticstaskOutlineDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class StatisticstaskOutlineMapperImpl implements StatisticstaskOutlineMapper {

    @Override
    public StatisticstaskOutlineDto toDto(StatisticstaskOutline entity) {
        if ( entity == null ) {
            return null;
        }

        StatisticstaskOutlineDto statisticstaskOutlineDto = new StatisticstaskOutlineDto();

        statisticstaskOutlineDto.setCreatetime( entity.getCreatetime() );
        statisticstaskOutlineDto.setCreateuser( entity.getCreateuser() );
        statisticstaskOutlineDto.setDbname( entity.getDbname() );
        statisticstaskOutlineDto.setId( entity.getId() );
        statisticstaskOutlineDto.setSparefield1( entity.getSparefield1() );
        statisticstaskOutlineDto.setSparefield2( entity.getSparefield2() );
        statisticstaskOutlineDto.setSparefield3( entity.getSparefield3() );
        statisticstaskOutlineDto.setSparefield4( entity.getSparefield4() );
        statisticstaskOutlineDto.setSrcname( entity.getSrcname() );
        statisticstaskOutlineDto.setTablecount( entity.getTablecount() );
        statisticstaskOutlineDto.setTaskname( entity.getTaskname() );

        return statisticstaskOutlineDto;
    }

    @Override
    public List<StatisticstaskOutlineDto> toDto(List<StatisticstaskOutline> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<StatisticstaskOutlineDto> list = new ArrayList<StatisticstaskOutlineDto>( entityList.size() );
        for ( StatisticstaskOutline statisticstaskOutline : entityList ) {
            list.add( toDto( statisticstaskOutline ) );
        }

        return list;
    }

    @Override
    public StatisticstaskOutline toEntity(StatisticstaskOutlineDto dto) {
        if ( dto == null ) {
            return null;
        }

        StatisticstaskOutline statisticstaskOutline = new StatisticstaskOutline();

        statisticstaskOutline.setCreatetime( dto.getCreatetime() );
        statisticstaskOutline.setCreateuser( dto.getCreateuser() );
        statisticstaskOutline.setDbname( dto.getDbname() );
        statisticstaskOutline.setId( dto.getId() );
        statisticstaskOutline.setSparefield1( dto.getSparefield1() );
        statisticstaskOutline.setSparefield2( dto.getSparefield2() );
        statisticstaskOutline.setSparefield3( dto.getSparefield3() );
        statisticstaskOutline.setSparefield4( dto.getSparefield4() );
        statisticstaskOutline.setSrcname( dto.getSrcname() );
        statisticstaskOutline.setTablecount( dto.getTablecount() );
        statisticstaskOutline.setTaskname( dto.getTaskname() );

        return statisticstaskOutline;
    }

    @Override
    public List<StatisticstaskOutline> toEntity(List<StatisticstaskOutlineDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<StatisticstaskOutline> list = new ArrayList<StatisticstaskOutline>( dtoList.size() );
        for ( StatisticstaskOutlineDto statisticstaskOutlineDto : dtoList ) {
            list.add( toEntity( statisticstaskOutlineDto ) );
        }

        return list;
    }
}
