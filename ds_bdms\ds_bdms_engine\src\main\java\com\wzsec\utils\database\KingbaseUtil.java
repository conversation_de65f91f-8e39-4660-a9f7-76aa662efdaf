package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

import static com.wzsec.utils.database.DatabaseUtil.closeCon;

/**
 * @ClassName KingbaseUtil
 * @Description
 * <AUTHOR>
 * @Date 2024/11/14 14:52
 */
@Slf4j
public class KingbaseUtil extends DatabaseUtil{
    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 14:53
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        // 1. 从输入参数中获取数据库连接信息
        String driverProgram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");
        // 解密密码（如果需要）
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }

        String dbname = map.get("dbname");
        String srcPort = map.get("srcport");
        String srcIp = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 注册 JDBC 驱动
            Class.forName(driverProgram);

            // 2. 打开连接到 Kingbase 数据库（这里我们连接到一个已经存在的数据库，比如 postgres，以检查新数据库是否存在）
            // 注意：Kingbase 的 JDBC URL 格式可能是这样的：**********************************
            // 但是，对于初始连接，我们可能需要连接到一个默认的数据库，如 test，或者根据Kingbase的实际配置来
            // 假设我们连接到一个名为 "test" 的数据库来检查其他数据库是否存在
            String jdbcUrl = "jdbc:kingbase://" + srcIp + ":" + srcPort + "/test";
            connection = DriverManager.getConnection(jdbcUrl, username, password);

            // 3. 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            // 在 Kingbase 中，我们可能需要通过查询系统表来检查数据库（实际是模式/schema）是否存在
            // 假设 Kingbase 中有一个系统表可以查询模式信息，这里使用假设的表名和字段名
            String checkDatabaseSQL = "SELECT schema_name FROM information_schema.schemata WHERE schema_name = '" + dbname + "'";
            statement = connection.createStatement();
            resultSet = statement.executeQuery(checkDatabaseSQL);
            boolean databaseExists = resultSet.next();

            // 4. 如果数据库（模式）不存在，则创建它
            // 注意：在 Kingbase 中创建数据库（模式）可能需要特定的权限
            if (!databaseExists) {
                String createDatabaseSQL = "CREATE SCHEMA " + dbname; // 在 Kingbase 中，可能是创建模式而不是数据库
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库（模式）" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库（模式）" + dbname + "已创建");
            } else {
                System.out.println("数据库（模式）" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库（模式）" + dbname + "已存在");
            }

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "JDBC驱动未找到：" + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
        return msgMap;
    }

    /**
     * 获取库表(Map<库名,(表1,表2)>)
     *
     * @param conn    连接
     * @param dbnames dbname
     * @return {@link Map}<{@link String}, {@link String}>
     * @throws SQLException sqlexception异常
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> dbTabMap = null;
        try {
            String strSQL = "select tablename from pg_tables where schemaname = 'public'"; //获取所有的库
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();

            dbTabMap = new TreeMap<String, String>();
            while (rs.next()) {
                String table_name = rs.getString(1);
                if (dbTabMap.containsKey(dbnames)) {
                    dbTabMap.put(dbnames, dbTabMap.get(dbnames) + "," + table_name);
                } else {
                    dbTabMap.put(dbnames, table_name);
                }
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return dbTabMap;
    }

    /**
     * 获取字段名称列表
     *
     * @param conn    连接
     * @param dbname  dbname
     * @param tabname tabname
     * @return {@link List}<{@link String}>
     * @throws SQLException sqlexception异常
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            String strSQL = "SELECT A.attname AS NAME FROM pg_class AS C, pg_attribute AS A WHERE C.relname='" + tabname + "' AND A.attrelid = C.oid AND A.attnum > 0";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1));
            }
        } catch (Exception ex) {
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return list;
    }

    /**
     * @Description:获取表数据数量
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + "\"" + tabname + "\"";
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, null);
        }
        return count;
    }

    /**
     * 获取数据库表数据
     *
     * @param conn    连接
     * @param dbname  dbname
     * @param tabname tabname
     * @param lineNum 行num
     * @return {@link List}<{@link String[]}>
     * @throws SQLException sqlexception异常
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        String strSQL = null;
        try {
            strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            //System.out.println("获取数据库中所有的库名表名出现异常");
            log.error("获取数据库中所有的库名表名出现异常,执行sql：" + strSQL);
            //throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * 获取表信息
     *
     * @param dbName    数据库名字
     * @param tableName 表名
     * @param conn      连接
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("select relname as tabname,cast (obj_description (relfilenode, 'pg_class') as varchar) as comment from pg_class c where relname in ( select tablename from pg_tables where schemaname = 'public') AND relname= '" + tableName + "' ");
            if (rs != null && rs.next()) {
                if (rs.getString("tabname").equals(tableName)) {
                    tableInfoMap.put("tableName", rs.getString("tabname"));
                    tableInfoMap.put("tableCName", rs.getString("comment"));
                    tableInfoMap.put("tableRows", "");
                    tableInfoMap.put("dataSize", "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return tableInfoMap;
    }

    /**
     * 获取字段信息
     *
     * @param dbName    数据库名字
     * @param tableName 表名
     * @param conn      连接
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("SELECT a.attname as name, col_description (a.attrelid, a.attnum) as comment FROM pg_class as c,pg_attribute as a where c.relname = '" + tableName + "' and a.attrelid = c.oid and a.attnum > 0");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("fieldName", rs.getString("name"));
                    fieldInfoMap.put("fieldCName", rs.getString("comment"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * 创建表sql
     * @param dbName    库名
     * @param tableName 表名
     * @param dburl     数据库连接信息
     * @param username  数据库用户名
     * @param password  数据库密码
     * @return
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username, String password) {
        List<Map<String, String>> fieldInfoList = null;
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        try {
            conn = getConn(Const.DB_DRIVER_KINGBASE,dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery("Select a.attnum,(select description from pg_catalog.pg_description where objoid=a.attrelid and objsubid=a.attnum) as descript ,a.attname,pg_catalog.format_type(a.atttypid,a.atttypmod) as data_type from pg_catalog.pg_attribute a where 1=1 and a.attrelid=(select oid from pg_class where relname='" + tableName + "' ) and a.attnum>0 and not a.attisdropped order by a.attnum;");
            if (rs != null) {
                fieldInfoList = new ArrayList<>();
                while (rs.next()) {
                    Map<String, String> fieldInfoMap = new HashMap<>();
                    fieldInfoMap.put("FieldEName", rs.getString("attname"));
                    fieldInfoMap.put("FieldType", rs.getString("data_type"));
                    fieldInfoMap.put("FieldCName", rs.getString("descript"));
                    fieldInfoList.add(fieldInfoMap);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(rs, stmt, null);
        }
        return fieldInfoList;
    }

    /**
     * 创建表sql
     * @param objList   Map数据库集合
     * @param dbName    库名
     * @param tableName 表名
     * @return
     */
    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbName, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 新增表sql语句
     * @param start     批量插入开始位
     * @param end       批量插入结束位
     * @param objList   Map数据库集合
     * @param dbname    库名
     * @param tableName 表名
     * @return
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * 新增表sql语句
     * @param start      批量插入开始位
     * @param end        批量插入结束位
     * @param objList    Map数据库集合
     * @param dbname     库名
     * @param tableName  表名
     * @param fieldnames 字段名
     * @return
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(tableName).append(" (");
            for (String column : fields) {
                sb.append("\"" + column + "\"").append(", ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @description: 生成创建表sql语句
     * @param fieldInfoList 字段信息列表
     * @param obj           Map对象
     * @param tableName     表名
     * @param maskfields    脱敏字段
     * @return:
     * @author: penglei
     * @date: 2024/11/25 9:46
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("\"" + fieldInfo.get("FieldEName") + "\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 加解密的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" varchar(255)");// 类型
                    //sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @description: 生成创建表sql语句
     * @param fieldInfoList 字段信息列表
     * @param obj           Map对象
     * @param tableName     表名
     * @param maskfields    脱敏字段
     * @param dbname        库名
     * @return:
     * @author: penglei
     * @date: 2024/11/25 9:46
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj, String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("CREATE TABLE ").append(tableName).append(" (\r\n");
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("\"" + fieldInfo.get("FieldEName") + "\"");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 加解密的字段类型更改为varchar
                    sb.append(" varchar(255)");// 类型
                } else {
                    sb.append(" varchar(255)");// 类型
                    //sb.append(" ").append(fieldInfo.get("Type"));// 类型
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + "\r)";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }
}
