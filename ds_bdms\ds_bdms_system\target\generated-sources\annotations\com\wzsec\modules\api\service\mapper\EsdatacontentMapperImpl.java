package com.wzsec.modules.api.service.mapper;

import com.wzsec.modules.api.domain.Esdatacontent;
import com.wzsec.modules.api.service.dto.EsdatacontentDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EsdatacontentMapperImpl implements EsdatacontentMapper {

    @Override
    public EsdatacontentDto toDto(Esdatacontent entity) {
        if ( entity == null ) {
            return null;
        }

        EsdatacontentDto esdatacontentDto = new EsdatacontentDto();

        esdatacontentDto.setDatacontent( entity.getDatacontent() );
        esdatacontentDto.setDatasourcecontent( entity.getDatasourcecontent() );
        esdatacontentDto.setId( entity.getId() );
        esdatacontentDto.setTime( entity.getTime() );

        return esdatacontentDto;
    }

    @Override
    public List<EsdatacontentDto> toDto(List<Esdatacontent> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EsdatacontentDto> list = new ArrayList<EsdatacontentDto>( entityList.size() );
        for ( Esdatacontent esdatacontent : entityList ) {
            list.add( toDto( esdatacontent ) );
        }

        return list;
    }

    @Override
    public Esdatacontent toEntity(EsdatacontentDto dto) {
        if ( dto == null ) {
            return null;
        }

        Esdatacontent esdatacontent = new Esdatacontent();

        esdatacontent.setDatacontent( dto.getDatacontent() );
        esdatacontent.setDatasourcecontent( dto.getDatasourcecontent() );
        esdatacontent.setId( dto.getId() );
        esdatacontent.setTime( dto.getTime() );

        return esdatacontent;
    }

    @Override
    public List<Esdatacontent> toEntity(List<EsdatacontentDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Esdatacontent> list = new ArrayList<Esdatacontent>( dtoList.size() );
        for ( EsdatacontentDto esdatacontentDto : dtoList ) {
            list.add( toEntity( esdatacontentDto ) );
        }

        return list;
    }
}
