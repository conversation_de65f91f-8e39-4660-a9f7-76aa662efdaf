package com.wzsec.dotask.mask.service.excute.file;

import cn.hutool.core.lang.Console;
import com.csvreader.CsvWriter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import com.wzsec.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.sl.usermodel.TextParagraph;
import org.apache.poi.xslf.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class WirteFileService {

    /**
     * @Title: wirteTxtFile
     * @Description: 写入txt文件
     * <AUTHOR>
     * @date 2019年11月12日
     */
    public static void wirteTxtFile(List<String> listData, String outputPath) {
        try {
            String line = System.getProperty("line.separator");//平台换行!
            File file = new File(outputPath);
            //如果没有文件就创建
            if (!file.isFile()) {
                //获取父目录
                File fileParent = file.getParentFile();
                //判断是否存在
                if (!fileParent.exists()) {
                    //创建父目录文件
                    fileParent.mkdirs();
                }
                file.createNewFile();
            }
            OutputStreamWriter write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
            //将数据写入文件中
            BufferedWriter writer = new BufferedWriter(write);
            for (String lineData : listData) {
                writer.write(lineData + line);
            }
            writer.close();
        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        }


    }

    /**
     * @Title: wirteCsvFile
     * @Description: 写入csv文件
     * <AUTHOR>
     * @date 2019年11月12日
     */
    public static void wirteCsvFile(List<String> maskDataList, String outputPath) {

        try {
            File file = new File(outputPath);
            //如果没有文件就创建
            if (!file.isFile()) {
                //获取父目录
                File fileParent = file.getParentFile();
                //判断是否存在
                if (!fileParent.exists()) {
                    //创建父目录文件
                    fileParent.mkdirs();
                }
            }
            // 创建CSV写对象 例如:CsvWriter(文件路径，分隔符，编码格式);
            CsvWriter csvWriter = new CsvWriter(outputPath, ',', Charset.forName("UTF-8"));
            // 写表头
            // String[] csvHeaders = { "编号", "姓名", "年龄" };
            // csvWriter.writeRecord(csvHeaders);
            for (String maskdata : maskDataList) {
                csvWriter.writeRecord(maskdata.split(","));
            }

            //使用缓冲区的刷新方法将数据刷到目的地中
            csvWriter.flush();

            //关闭缓冲区，缓冲区没有调用系统底层资源，真正调用底层资源的是FileWriter对象，缓冲区仅仅是一个提高效率的作用
            //因此，此处的close()方法关闭的是被缓存的流对象
            csvWriter.close();

        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        }

    }

    /**
     * @Title: wirteExcelFile
     * @Description: 写入excel文件(xls)
     * <AUTHOR>
     * @date 2020年2月12日
     */
    public static void wirteExcelFile(List<String> maskDataList, int length, String outputPath,
                                      String outputName) {
        // 创建HSSFWorkbook，调用模板
        String[][] data = new String[maskDataList.size()][length];
        for (int i = 0; i < maskDataList.size(); i++) {
            data[i] = new String[length];
            String[] dataarr = maskDataList.get(i).split(",");
            for (int j = 0; j < dataarr.length; j++) {
                data[i][j] = dataarr[j];
            }
        }
        HSSFWorkbook wb = ExcelUtil.getHSSFWorkbook(outputName, data, null);
        OutputStream os = null;
        try {
            File file = new File(outputPath + File.separator + outputName);
            //如果没有文件就创建
            if (!file.isFile()) {
                //获取父目录
                File fileParent = file.getParentFile();
                //判断是否存在
                if (!fileParent.exists()) {
                    //创建父目录文件
                    fileParent.mkdirs();
                }
            }
            os = new FileOutputStream(file);
            wb.write(os);
            wb.close();
        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        } finally {
            try {
                wb.close();
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }


    /**
     * @Title: wirteXSSFWorkbook
     * @Description: 写入excel文件(xlsx)
     * <AUTHOR>
     * @date 2020年10月22日
     */
    public static void wirteXSSFWorkbook(List<String> maskDataList, int length, String outputPath, String outputName, String splitstr) {
        // 创建HSSFWorkbook，调用模板
        String[][] data = new String[maskDataList.size()][length];
        for (int i = 0; i < maskDataList.size(); i++) {
            data[i] = new String[length];
            String[] dataarr = maskDataList.get(i).split(splitstr);
            for (int j = 0; j < dataarr.length; j++) {
                data[i][j] = dataarr[j];
            }
        }
        XSSFWorkbook wb = null;
        try {
            wb = ExcelUtil.getXSSFWorkbook(outputName, data, null);
        } catch (Exception e) {
            log.error(e.getMessage());
            Console.log("一次性写入数据长度为: {}", data.length);
        }
        OutputStream os = null;
        try {
            File file = new File(outputPath + File.separator + outputName);
            //如果没有文件就创建
            if (!file.isFile()) {
                //获取父目录
                File fileParent = file.getParentFile();
                //判断是否存在
                if (!fileParent.exists()) {
                    //创建父目录文件
                    fileParent.mkdirs();
                }
            }
            os = new FileOutputStream(file, true);
            wb.write(os);
        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        } finally {
            try {
                wb.close();
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * @Title: 将非格式化的数据写出到Excel
     * @Description: 写入excel文件(xlsx)
     * <AUTHOR>
     * @date 2020年10月22日
     */
    public static void wirteUnFormatXSSFWorkbook(List<String> maskDataList, String splitstr, String outputPath, String outputName) {
        // 创建HSSFWorkbook，调用模板
        XSSFWorkbook wb = ExcelUtil.getXSSFWorkbookForUnFormatData(outputName, maskDataList, splitstr, null);
        OutputStream os = null;
        try {
            File file = new File(outputPath + File.separator + outputName);
            //如果没有文件就创建
            if (!file.isFile()) {
                //获取父目录
                File fileParent = file.getParentFile();
                //判断是否存在
                if (!fileParent.exists()) {
                    //创建父目录文件
                    fileParent.mkdirs();
                }
            }
            os = new FileOutputStream(file);
            wb.write(os);
        } catch (FileNotFoundException e) {
            log.error("没有找到指定文件");
        } catch (IOException e) {
            log.error("文件读写出错");
        } finally {
            try {
                wb.close();
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * @Title: writeSql
     * @Description: 写入sql文件
     * <AUTHOR>
     * @date 2023年12月18日
     */
    public static void writeSqlFile(SingleTableSQLFileUtil singleTableSQLFileUtil, List<Integer> maskFieldIndex, List<String> maskDataList,
                                    String splitstr, String outputPath, String outputName) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append(singleTableSQLFileUtil.getDropTableSql() + "\n");

        //建表的sql语句更新
        String createTableStr = singleTableSQLFileUtil.createTableSqlUpdate(maskFieldIndex);
        if (createTableStr == null) {
            throw new Exception("生成建表sql失败，原sql："+singleTableSQLFileUtil.getCreateTableSql());
        }
        sb.append(createTableStr + "\n");

        //TODO 将脱敏后的数据，重新拼接成insert into语句，再写入到文件里
        if (maskDataList.size() > 0) {
            String sql = singleTableSQLFileUtil.getInsertSql().get(0);
            int index = sql.indexOf("(") + 1;
            String insertSql = sql.substring(0, index);

            List<String> maskDataSql = new ArrayList<>();
            for (String maskData : maskDataList) {
                String[] data = maskData.split(splitstr);
                String sqlStr = insertSql;
                for (String datum : data) {
                    sqlStr += datum + ", ";
                }
                sqlStr = sqlStr.substring(0, sqlStr.length() - 2);//去掉末尾的逗号和空格
                sqlStr += ");";
                maskDataSql.add(sqlStr);
            }
            for (String sqlStr : maskDataSql) {
                sb.append(sqlStr + "\n");
            }
        }

        List<String> restsSql = singleTableSQLFileUtil.getRestsSql();
        if (restsSql!=null){
            for (String sql : restsSql) {
                sb.append(sql + "\n");
            }
        }

        String filePath = outputPath + File.separator + outputName;

        File file = new File(filePath);
        //如果没有文件就创建
        if (!file.isFile()) {
            //获取父目录
            File fileParent = file.getParentFile();
            //判断是否存在
            if (!fileParent.exists()) {
                //创建父目录文件
                fileParent.mkdirs();
            }
            file.createNewFile();
        }
        BufferedWriter writer = new BufferedWriter(new FileWriter(filePath,true));
        writer.write(sb.toString()); // 写入内容
        writer.close();
    }


    /**
     * 写入word 文件
     * @param maskDataList
     * @param outputPath
     * <AUTHOR>
     * @date 2025年3月15日
     */
    public static void writeWordFile(List<String> maskDataList, String outputPath) {
        // 创建一个新的 Word 文档对象
        XWPFDocument document = new XWPFDocument();

        try {
            // 创建一个段落对象
            XWPFParagraph paragraph = document.createParagraph();
            // 创建一个运行对象，用于向段落中添加文本
            XWPFRun run = paragraph.createRun();

            // 遍历数据列表
            for (String maskdata : maskDataList) {
                // 将数据按逗号分割
                String[] parts = maskdata.split(",");
                for (int i = 0; i < parts.length; i++) {
                    // 向运行对象中添加分割后的数据
                    run.setText(parts[i]);
                    if (i < parts.length - 1) {
                        // 如果不是最后一个数据，添加制表符分隔
                        run.addTab();
                    }
                }
                // 每行数据添加完后换行
                run.addCarriageReturn();
            }

            // 创建文件对象
            File file = new File(outputPath);
            // 获取文件的父目录
            File fileParent = file.getParentFile();
            // 如果父目录不存在，则创建父目录
            if (!fileParent.exists()) {
                fileParent.mkdirs();
            }

            // 创建文件输出流，用于将文档写入文件
            try (FileOutputStream out = new FileOutputStream(file)) {
                // 将文档写入输出流
                document.write(out);
            }
        } catch (IOException e) {
            // 记录文件读写错误日志
            log.error("文件读写出错", e);
        } finally {
            try {
                // 关闭文档对象
                document.close();
            } catch (IOException e) {
                // 记录关闭文档对象时的错误日志
                log.error("关闭文档时出错", e);
            }
        }
    }


    public static void writePdfFile(List<String> maskDataList, String outputPath) {
        // 创建一个新的 PDF 文档对象
        PDDocument document = new PDDocument();
        // 创建一个新的页面
        PDPage page = new PDPage();
        // 将页面添加到文档中
        document.addPage(page);

        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            // 设置字体和字号
            contentStream.setFont(PDType1Font.HELVETICA, 12);
            // 设置起始位置
            float y = page.getMediaBox().getHeight() - 50;
            // 遍历数据列表
            for (String maskdata : maskDataList) {
                // 将数据按逗号分割
                String[] parts = maskdata.split(",");
                float x = 50;
                for (int i = 0; i < parts.length; i++) {
                    // 在指定位置添加文本
                    contentStream.beginText();
                    contentStream.newLineAtOffset(x, y);
                    contentStream.showText(parts[i]);
                    contentStream.endText();
                    // 计算下一个数据的位置
                    x += 100;
                }
                // 换行
                y -= 20;
            }

            // 创建文件对象
            File file = new File(outputPath);
            // 获取文件的父目录
            File fileParent = file.getParentFile();
            // 如果父目录不存在，则创建父目录
            if (!fileParent.exists()) {
                fileParent.mkdirs();
            }

            // 将文档保存到文件
            document.save(new FileOutputStream(file));
        } catch (IOException e) {
            // 记录文件读写错误日志
            log.error("文件读写出错", e);
        } finally {
            try {
                // 关闭文档对象
                document.close();
            } catch (IOException e) {
                // 记录关闭文档对象时的错误日志
                log.error("关闭文档时出错", e);
            }
        }
    }


    /**
     * 写入PowerPoint文件
     * @param maskDataList 包含要写入的数据的列表
     * @param outputPath 输出文件的路径
     * <AUTHOR>
     * @date 2025年3月15日
     */
    public static void writePowerPointFile(List<String> maskDataList, String outputPath) {
        // 创建一个新的PowerPoint演示文稿对象
        XMLSlideShow ppt = new XMLSlideShow();

        try {
            // 获取默认的字体大小
            int fontSize = 18;

            // 遍历数据列表，为每一行数据创建一个幻灯片
            for (String maskdata : maskDataList) {
                // 创建一个新的幻灯片
                XSLFSlide slide = ppt.createSlide();

                // 获取幻灯片的背景
                XSLFBackground background = slide.getBackground();
                // 设置背景颜色为白色
                background.setFillColor(Color.WHITE);

                // 创建一个标题占位符
                XSLFTextBox titleShape = slide.createTextBox();
                titleShape.setAnchor(new Rectangle(50, 50, 800, 60));
                XSLFTextParagraph titleParagraph = titleShape.addNewTextParagraph();
                XSLFTextRun titleRun = titleParagraph.addNewTextRun();
                titleRun.setText("数据展示");
                titleRun.setFontSize(24.0);
                titleRun.setBold(true);

                // 将数据按逗号分割
                String[] parts = maskdata.split(",");

                // 创建一个内容占位符
                XSLFTextBox contentShape = slide.createTextBox();
                contentShape.setAnchor(new Rectangle(100, 150, 700, 400));

                // 为每个数据部分创建一个文本段落
                for (int i = 0; i < parts.length; i++) {
                    XSLFTextParagraph paragraph = contentShape.addNewTextParagraph();
                    paragraph.setTextAlign(TextParagraph.TextAlign.LEFT);

                    XSLFTextRun run = paragraph.addNewTextRun();
                    run.setText(parts[i]);
                    run.setFontSize((double) fontSize);

                    // 如果不是最后一个数据，添加一个分隔线
                    if (i < parts.length - 1) {
                        XSLFConnectorShape line = slide.createConnector();
                        line.setAnchor(new Rectangle(100, 150 + (i + 1) * 40, 700, 1));
                        line.setLineWidth(1.0);
                        line.setLineColor(Color.GRAY);
                    }
                }
            }

            // 创建文件对象
            File file = new File(outputPath);
            // 获取文件的父目录
            File fileParent = file.getParentFile();
            // 如果父目录不存在，则创建父目录
            if (!fileParent.exists()) {
                fileParent.mkdirs();
            }

            // 创建文件输出流，用于将演示文稿写入文件
            try (FileOutputStream out = new FileOutputStream(file)) {
                // 将演示文稿写入输出流
                ppt.write(out);
            }
        } catch (IOException e) {
            // 记录文件读写错误日志
            log.error("文件读写出错", e);
        } finally {
            try {
                // 关闭演示文稿对象
                ppt.close();
            } catch (IOException e) {
                // 记录关闭演示文稿对象时的错误日志
                log.error("关闭演示文稿时出错", e);
            }
        }
    }

    public static void writePngFile(List<String> maskDataList, String outputPath) {
        // 定义图片的宽度和高度
        int width = 800;
        int height = 600;
        // 创建一个新的 BufferedImage 对象，用于绘制图片
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        // 获取 Graphics2D 对象，用于在图片上绘制文本
        Graphics2D g2d = image.createGraphics();
        // 设置背景颜色为白色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        // 设置字体和字号
        g2d.setFont(new Font("Arial", Font.PLAIN, 12));
        // 设置文本颜色为黑色
        g2d.setColor(Color.BLACK);

        // 设置起始位置
        float y = 50;
        // 遍历数据列表
        for (String maskdata : maskDataList) {
            // 将数据按逗号分割
            String[] parts = maskdata.split(",");
            float x = 50;
            for (int i = 0; i < parts.length; i++) {
                // 在指定位置绘制文本
                g2d.drawString(parts[i], (int) x, (int) y);
                // 计算下一个数据的位置
                x += 100;
            }
            // 换行
            y += 20;
        }

        // 释放 Graphics2D 对象
        g2d.dispose();

        try {
            // 创建文件对象
            File file = new File(outputPath);
            // 获取文件的父目录
            File fileParent = file.getParentFile();
            // 如果父目录不存在，则创建父目录
            if (!fileParent.exists()) {
                fileParent.mkdirs();
            }
            // 将 BufferedImage 对象保存为 PNG 图片
            ImageIO.write(image, "png", file);
        } catch (IOException e) {
            // 记录文件读写错误日志
            log.error("文件读写出错", e);
        }
    }
}
