package com.wzsec.modules.mask.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-21
*/
@Data
public class MaskstrategyIdentifymaskstrategiesdetailDto implements Serializable {

    /** 主键 */
    private Integer id;

    /** 策略id */
    private Integer strategyid;

    /** 数据名称 */
    private String dataname;

    /** 识别规则id */
    private Integer senruleid;

    /** 脱敏规则id */
    private Integer maskruleid;

    /** 脱敏算法id */
    private Integer algorithmid;

    /** 参数 */
    private String param;

    /** 密钥 */
    private String secretkey;

    /** 备用字段1 */
    private String sparefield1;

    /** 备用字段2 */
    private String sparefield2;

    /** 备用字段3 */
    private String sparefield3;

    /** 备用字段4 */
    private String sparefield4;

    /** 异常数据处置 */
    private String sparefield5;
}