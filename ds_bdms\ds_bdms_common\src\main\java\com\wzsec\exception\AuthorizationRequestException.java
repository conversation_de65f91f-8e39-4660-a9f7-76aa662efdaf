package com.wzsec.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

import static org.springframework.http.HttpStatus.BAD_REQUEST;

/**
 * <AUTHOR>
 * @date 2018-11-23
 * 统一异常处理
 */
@Getter
public class AuthorizationRequestException extends RuntimeException{

    private Integer status = BAD_REQUEST.value();

    public AuthorizationRequestException(String msg){
        super(msg);
    }

    public AuthorizationRequestException(HttpStatus status, String msg){
        super(msg);
        this.status = status.value();
    }
}
