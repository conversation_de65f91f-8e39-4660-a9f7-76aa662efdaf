package com.wzsec.modules.mask.rest;

import com.jcraft.jsch.*;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.*;
import com.wzsec.modules.mask.repository.AlgorithmRepository;
import com.wzsec.modules.mask.repository.MaskruleRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.*;
import com.wzsec.modules.metadata.service.MetadataService;
import com.wzsec.modules.quartz.config.FileTaskScanConfig;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.modules.system.service.dto.DictDetailDto;
import com.wzsec.modules.system.service.dto.DictDetailQueryCriteria;
import com.wzsec.utils.Const;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;
import com.wzsec.utils.ConstSystem;
import com.wzsec.utils.SecurityUtils;
import com.wzsec.utils.StringUtils;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2020-11-11
 */
// @Api(tags = "格式化文件脱敏任务配置管理")
@RestController
@RequestMapping("/api/fileTaskConfig")
public class FileTaskConfigController {

    private final FileTaskConfigService fileTaskConfigService;

    private final FileTaskScanConfig fileTaskScanConfig;

    private final AlgorithmService algorithmService;

    private final MetadataService metadataService;

    private final MaskStrategyFieldService maskStrategyFieldService;

    private final MaskStrategyTableService maskStrategyTableService;

    private final MaskStrategyFileMainService maskStrategyFileMainService;

    private final MaskStrategyFileFormatSubService maskStrategyFileFormatSubService;

    private final MaskruleRepository maskruleRepository;

    private final AlgorithmRepository algorithmRepository;

    private final DictDetailService dictDetailService;

    public FileTaskConfigController(FileTaskConfigService fileTaskConfigService, FileTaskScanConfig fileTaskScanConfig,
                                    MetadataService metadataService, AlgorithmService algorithmService,
                                    MaskStrategyFieldService maskStrategyFieldService, MaskStrategyTableService maskStrategyTableService,
                                    MaskStrategyFileMainService maskStrategyFileMainService, MaskStrategyFileFormatSubService maskStrategyFileFormatSubService,
                                    MaskruleRepository maskruleRepository, AlgorithmRepository algorithmRepository,DictDetailService dictDetailService) {
        this.fileTaskConfigService = fileTaskConfigService;
        this.fileTaskScanConfig = fileTaskScanConfig;
        this.metadataService = metadataService;
        this.algorithmService = algorithmService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.maskStrategyTableService = maskStrategyTableService;
        this.maskStrategyFileMainService = maskStrategyFileMainService;
        this.maskStrategyFileFormatSubService = maskStrategyFileFormatSubService;
        this.maskruleRepository = maskruleRepository;
        this.algorithmRepository = algorithmRepository;
        this.dictDetailService = dictDetailService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('fileTaskConfig:list')")
    public void download(HttpServletResponse response, FileTaskConfigQueryCriteria criteria) throws IOException {
        fileTaskConfigService.download(fileTaskConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询格式化文件脱敏任务配置")
    // @ApiOperation("查询格式化文件脱敏任务配置")
    @PreAuthorize("@el.check('fileTaskConfig:list')")
    public ResponseEntity<Object> getFileTaskConfigs(FileTaskConfigQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(fileTaskConfigService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增格式化文件脱敏任务配置")
    // @ApiOperation("新增格式化文件脱敏任务配置")
    @PreAuthorize("@el.check('fileTaskConfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody FileTaskConfig resources) {
        String strategyid = String.valueOf(resources.getStrategyid());
        MaskStrategyFileMainDto maskStrategyFileMainDto = maskStrategyFileMainService.findById(Integer.parseInt(strategyid));
        if ("0".equals(maskStrategyFileMainDto.getStrategytype())) {
            // 格式化文件脱敏策略，需要抽取字段
            String postionAlgoParasStr = getPostionAlgoParasStr(strategyid);
            //String extractingFieldStr = getExtractingFieldStr(strategyid);
            String extractingFieldStr = maskStrategyFileMainDto.getExtractcolum();
            resources.setMaskstrategystr(postionAlgoParasStr);
            resources.setExtractfield(extractingFieldStr);
        }

        FileTaskConfigDto fileTaskConfigDto = fileTaskConfigService.create(resources);
        if (fileTaskConfigDto != null && Const.TASK_STATE_USE.equals(fileTaskConfigDto.getState()) && Const.TASK_SUBMITTYPE_AUTO.equals(fileTaskConfigDto.getSubmitmethod())) {
            fileTaskScanConfig.addJob(fileTaskConfigDto);
        }
        return new ResponseEntity<>(fileTaskConfigService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改格式化文件脱敏任务配置")
    // @ApiOperation("修改格式化文件脱敏任务配置")
    @PreAuthorize("@el.check('fileTaskConfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody FileTaskConfig resources) {
        String strategyid = String.valueOf(resources.getStrategyid());
        MaskStrategyFileMainDto maskStrategyFileMainDto = maskStrategyFileMainService.findById(Integer.parseInt(strategyid));
        if ("0".equals(maskStrategyFileMainDto.getStrategytype())) {
            String postionAlgoParasStr = getPostionAlgoParasStr(strategyid);
            //String extractingFieldStr = getExtractingFieldStr(strategyid);
            String extractingFieldStr = maskStrategyFileMainDto.getExtractcolum();
            resources.setMaskstrategystr(postionAlgoParasStr);
            resources.setExtractfield(extractingFieldStr);
        }
        //提交方式改为手动提交,将定时时间置空
        if (resources.getSubmitmethod().equals("1")) {
            resources.setCron("");
        }
        resources.setUpdateuser(SecurityUtils.getUsername());
        fileTaskConfigService.update(resources);
        FileTaskConfigDto fileTaskConfigDto = fileTaskConfigService.findById(resources.getId());
        if (fileTaskConfigDto != null && Const.TASK_STATE_USE.equals(fileTaskConfigDto.getState()) && Const.TASK_SUBMITTYPE_AUTO.equals(fileTaskConfigDto.getSubmitmethod())) {
            fileTaskScanConfig.updateJob(fileTaskConfigDto);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除格式化文件脱敏任务配置")
    // @ApiOperation("删除格式化文件脱敏任务配置")
    @PreAuthorize("@el.check('fileTaskConfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        for (Integer id : ids) {
            FileTaskConfigDto byId = fileTaskConfigService.findById(id);
            fileTaskScanConfig.deleteJob(byId);
        }
        fileTaskConfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取格式化文件脱敏任务新增任务号")
    // @ApiOperation("获取格式化文件脱敏任务新增任务号")
    @PreAuthorize("@el.check('fileTaskConfig:add')")
    @GetMapping(value = "/getTaskNo")
    public ResponseEntity<Object> getTaskNo() {
        return new ResponseEntity<>(fileTaskConfigService.getMAXTaskNo(), HttpStatus.CREATED);
    }

    @Log("执行文件脱敏任务")
    // @ApiOperation("在引擎执行格式化文件脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    // @PreAuthorize("@el.check('fileTaskConfig:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable int id, HttpServletRequest request) {
        fileTaskConfigService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("根据数据源id获取表名")
    // @ApiOperation("根据数据源id获取表名")
    @GetMapping(value = "/getTabnameBySource/{sourceid}")
    @PreAuthorize("@el.check('dbtaskconfig:list')")
    public ResponseEntity<Object> getTabnameBySource(@PathVariable String sourceid) {
        return new ResponseEntity<>(fileTaskConfigService.getTabnameBySource(sourceid), HttpStatus.OK);
    }

    /**
     * @Description:根据脱敏策略获取算法位置、参数信息(库表类型脱敏策略)
     * <AUTHOR>
     * @date 2019-11-12
     */
    /*public String getPostionAlgoParasStr(String strategyid) {
        String postionAlgoParasStr = "";
        String fieldname = null;
        String[] fieldnamearr = null;
        StringBuilder maskingConfigItemsSB = new StringBuilder();
        MaskStrategyTableDto maskStrategyTableDto = maskStrategyTableService.findById(Integer.parseInt(strategyid));
        List<MaskStrategyField> maskStrategyFieldList = maskStrategyFieldService.getMaskStrategyFieldByStrategyid(Integer.parseInt(strategyid));
        for (MaskStrategyField maskStrategyField : maskStrategyFieldList) {
            if("0".equals(maskStrategyField.getSparefield1())){
                int postion = 1;
                fieldname = maskStrategyField.getFieldename();
                fieldnamearr = metadataService.getDbnameAndTabnameToFields(maskStrategyTableDto.getSourcetype(),
                        maskStrategyField.getDbname(), maskStrategyField.getTabename());
                for (int i = 0; i < fieldnamearr.length; i++) {
                    if (fieldname.equals(fieldnamearr[i])) {
                        postion = i + 1;
                        break;
                    }
                }
                // 获取算法id
                if (maskStrategyField.getAlgorithmid() != null) {
                    int algorithmid = maskStrategyField.getAlgorithmid();
                    if (algorithmid != 0L) {
                        // 获取脱敏算法名称（中文）
                        String algorithmname = "";
                        AlgorithmDto algorithmDto = algorithmService.findById(algorithmid); // 有算法id获取算法
                        if (algorithmDto != null) {
                            algorithmname = algorithmDto.getAlgorithmname(); // 获取算法名
                            String algEnglishName = "";
                            if (algorithmname != null && !"".equals(algorithmname)) {
                                // 2.获取算法英文名
                                algEnglishName = algorithmDto.getAlgenglishname();

                                // 3.脱敏算法参数
                                String param = maskStrategyField.getParam();
                                String secretKey = maskStrategyField.getSecretkey();

                                // 示例:2|MD5$9876543210123456;5|IDMASK$11,14
                                boolean isParamExist = (null != param && !param.equals(""));
                                boolean isSecretKeyExist = (null != secretKey && !secretKey.equals(""));
                                if (!isParamExist && !isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + ";";
                                }
                                if (isParamExist && isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + "$" + param + "," + secretKey + ";";
                                }
                                if (isParamExist && !isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + "$" + param + ";";
                                }
                                if (!isParamExist && isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + "$" + secretKey + ";";
                                }
                                maskingConfigItemsSB.append(postionAlgoParasStr);
                                postion++;
                            }
                        }
                    }
                } else {
                    postionAlgoParasStr = postion + "|" + ";";
                    maskingConfigItemsSB.append(postionAlgoParasStr);
                }
            }
        }
        return maskingConfigItemsSB.toString();
    }*/

    /**
     * @Description:根据脱敏策略获取算法位置、参数信息(文件类型脱敏策略)
     * <AUTHOR>
     * @date 2021-06-21
     */
    public String getPostionAlgoParasStr(String strategyid) {
        String postionAlgoParasStr = "";
        StringBuilder maskingConfigItemsSB = new StringBuilder();
        List<MaskStrategyFileFormatSub> maskStrategyFileFormatSubList = maskStrategyFileFormatSubService.getMaskStrategyFieldByStrategyid(Integer.parseInt(strategyid));
        List<Algorithm> algorithmList = algorithmRepository.findAll();
        Map<String, String> algorithmMap = new HashMap<>();
        for (Algorithm algorithm : algorithmList) {
            algorithmMap.put(algorithm.getId().toString(), algorithm.getAlgenglishname());
        }
        for (MaskStrategyFileFormatSub maskStrategyFileFormatSub : maskStrategyFileFormatSubList) {
            int postion = maskStrategyFileFormatSub.getColumnnum();
            // 获取算法id
            if (maskStrategyFileFormatSub.getAlgorithmid() != null && maskStrategyFileFormatSub.getRuleid() != null) {
                Integer ruleid = maskStrategyFileFormatSub.getRuleid();
                Maskrule maskrule = maskruleRepository.findById(ruleid).get();

                //TODO 整体配置字符串（初始逻辑）： 位数|算法英文名$参数或秘钥;  示例：1|HIDECODE$*,0,0;

                //TODO                  |第一段配置             |第二段配置             |
                //TODO 分段配置字符串： 位数|算法英文名$参数或秘钥$段数|算法英文名$参数或秘钥$段数;  示例：1|HIDECODE$*,0,0$1-6|RANDOMMAPPING$$15-18;
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(postion);
                if (Const.ALG_CONFIG_WAY_EXTRACT.equals(maskrule.getSparefield2())){
                    String extractconfig = maskrule.getSparefield3() != null ? maskrule.getSparefield3().toString() : null;
                    String extractalgid = maskrule.getSparefield4() != null ? maskrule.getSparefield4() : null;
                    if (StringUtils.isNotEmpty(extractconfig) && StringUtils.isNotEmpty(extractalgid)) {
                        String[] extractConfigArr = extractconfig.split(";");
                        String[] extractAlgIdArr = extractalgid.split(";");
                        for (int i = 0; i < extractAlgIdArr.length; i++) {
                            String algId = extractAlgIdArr[i];
                            String algName = algorithmMap.get(algId);
                            String parKey = Const.algorithmParameterSecretKeyMap().get(algName);
                            stringBuffer.append("|");
                            stringBuffer.append(algName + "$");
                            stringBuffer.append(parKey + "$");
                            stringBuffer.append(extractConfigArr[i]);
                        }
                        stringBuffer.append(";");
                    }
                    maskingConfigItemsSB.append(stringBuffer);
                } else {
                    int algorithmid = maskStrategyFileFormatSub.getAlgorithmid();
                    if (algorithmid != 0L) {
                        // 获取脱敏算法名称（中文）
                        String algorithmname = "";
                        AlgorithmDto algorithmDto = algorithmService.findById(algorithmid); // 有算法id获取算法
                        if (algorithmDto != null) {
                            algorithmname = algorithmDto.getAlgorithmname(); // 获取算法名
                            String algEnglishName = "";
                            if (algorithmname != null && !"".equals(algorithmname)) {
                                // 2.获取算法英文名
                                algEnglishName = algorithmDto.getAlgenglishname();

                                // 3.脱敏算法参数
                                String param = maskStrategyFileFormatSub.getParam();
                                String secretKey = maskStrategyFileFormatSub.getSecretkey();

                                // 示例:2|MD5$9876543210123456;5|IDMASK$11,14
                                boolean isParamExist = (null != param && !param.equals(""));
                                boolean isSecretKeyExist = (null != secretKey && !secretKey.equals(""));
                                if (!isParamExist && !isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + ";";
                                }
                                if (isParamExist && isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + "$" + param + "," + secretKey + ";";
                                }
                                if (isParamExist && !isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + "$" + param + ";";
                                }
                                if (!isParamExist && isSecretKeyExist) {
                                    postionAlgoParasStr = postion + "|" + algEnglishName + "$" + secretKey + ";";
                                }
                                maskingConfigItemsSB.append(postionAlgoParasStr);
                            }
                        }
                    }
                }

            } else {
                postionAlgoParasStr = postion + "|" + ";";
                maskingConfigItemsSB.append(postionAlgoParasStr);
            }
        }
        return maskingConfigItemsSB.toString();
    }

    /**
     * @Description:根据脱敏策略获取抽取字段信息(库表类型脱敏策略)
     * <AUTHOR>
     * @date 2019-11-14
     */
    public String getExtractingFieldStr(String strategyid) {
        String extractingFieldStr = "";
        StringBuilder maskingConfigItemsSB = new StringBuilder();
        List<MaskStrategyField> maskStrategyFieldList = maskStrategyFieldService.getMaskStrategyFieldByStrategyid(Integer.parseInt(strategyid));
        for (MaskStrategyField maskStrategyField : maskStrategyFieldList) {
            if ("0".equals(maskStrategyField.getSparefield1())) {
                maskingConfigItemsSB.append(maskStrategyField.getFieldename());
                maskingConfigItemsSB.append(",");
            }
        }
        extractingFieldStr = maskingConfigItemsSB.substring(0, maskingConfigItemsSB.length() - 1);
        return extractingFieldStr;
    }

    @Log("获取文件脱敏结果预览")
    @GetMapping("/maskResultPreview")
    public ResponseEntity<Object> maskResultPreview(@RequestParam Long id,  HttpServletRequest request) {
        Object data = fileTaskConfigService.getMaskResultPreview(id, request);
        return new ResponseEntity<>(data, HttpStatus.OK);
    }

    @Log("更改审批状态")
    @GetMapping(value = "/changeApprovalStatus/{id}/{approvalStatus}")
    public ResponseEntity<Object> changeApprovalStatus(@PathVariable Integer id, @PathVariable String approvalStatus) {
        fileTaskConfigService.changeApprovalStatus(id, approvalStatus);
        return new ResponseEntity<>(HttpStatus.OK);
    }



    @Log("文件上传")
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file,@RequestParam("taskname") String taskname) {
        Map<String, Object> response = new HashMap<>();
        if (file.isEmpty()) {
            response.put("code", 400);
            response.put("message", "未选择文件");
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        }
        String uploadDir = ConstSystem.sddConfs.get("file.upload.dir");
        uploadDir = uploadDir + File.separator + taskname;
        try {
            // 创建上传目录（如果不存在）
            java.io.File uploadDirectory = new java.io.File(uploadDir);
            if (!uploadDirectory.exists()) {
                uploadDirectory.mkdirs();
            }

            // 生成唯一的文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String uniqueFileName = originalFilename;
            // 构建文件保存路径
            Path filePath = Paths.get(uploadDir, uniqueFileName);
            // 使用 try-with-resources 确保输入流正确关闭
            try (var inputStream = file.getInputStream()) {
                // 将文件保存到指定路径
                Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
            }

            // 构建响应数据
            response.put("code", 200);
            Map<String, String> data = new HashMap<>();
            data.put("path", filePath.toString());
            //从字典中取出file_task_inputfiletype
            DictDetailQueryCriteria dictDetailQueryCriteria = new DictDetailQueryCriteria();
            dictDetailQueryCriteria.setDictName("file_task_inputfiletype");
            List<DictDetailDto> dictDetailList = dictDetailService.queryAll(dictDetailQueryCriteria);
            DictDetailDto dictDetailDto = dictDetailList.stream().filter(t -> t.getLabel().equals(fileExtension)).collect(Collectors.toList()).get(0);
            String value = dictDetailDto.getValue();
            data.put("fileType", value);
            response.put("data", data);

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (IOException e) {
            response.put("code", 500);
            response.put("message", "文件上传失败: " + e.getMessage());
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Log("文件下载")
    @GetMapping("/downloadFile")
    public ResponseEntity<Resource> downloadFile(@RequestParam("path") String taskname) {
        FileTaskConfig fileTaskConfig = fileTaskConfigService.queryTaskname(taskname);
        String outPutDirectory = fileTaskConfig.getOutputdirectory();
        Path fullFolderPath = Paths.get(outPutDirectory, taskname);
        File folder = fullFolderPath.toFile();
        if (!folder.exists() || !folder.isDirectory()) {
            throw new RuntimeException("文件不存在");
        }

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ZipOutputStream zipOut = new ZipOutputStream(bos)) {

            zipFolder(folder, "", zipOut);

            byte[] zipBytes = bos.toByteArray();
            ByteArrayResource resource = new ByteArrayResource(zipBytes);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            String folderName = folder.getName();
            // 对文件名进行 URL 编码，避免特殊字符问题
            String encodedFileName = java.net.URLEncoder.encode(folderName + ".zip", "UTF-8").replaceAll("\\+", "%20");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName);
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.add(HttpHeaders.PRAGMA, "no-cache");
            headers.add(HttpHeaders.EXPIRES, "0");
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(zipBytes.length)
                    .body(resource);
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    private void zipFolder(File folder, String parentFolderName, ZipOutputStream zipOut) throws IOException {
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    zipFolder(file, parentFolderName + file.getName() + File.separator, zipOut);
                } else {
                    try (FileInputStream fis = new FileInputStream(file)) {
                        ZipEntry zipEntry = new ZipEntry(parentFolderName + file.getName());
                        zipOut.putNextEntry(zipEntry);

                        byte[] bytes = new byte[1024];
                        int length;
                        while ((length = fis.read(bytes)) >= 0) {
                            zipOut.write(bytes, 0, length);
                        }
                        zipOut.closeEntry();
                    }
                }
            }
        }
    }
}
