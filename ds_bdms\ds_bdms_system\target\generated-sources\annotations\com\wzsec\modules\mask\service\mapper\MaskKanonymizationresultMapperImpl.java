package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskKanonymizationresult;
import com.wzsec.modules.mask.service.dto.MaskKanonymizationresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:02+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskKanonymizationresultMapperImpl implements MaskKanonymizationresultMapper {

    @Override
    public MaskKanonymizationresultDto toDto(MaskKanonymizationresult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskKanonymizationresultDto maskKanonymizationresultDto = new MaskKanonymizationresultDto();

        maskKanonymizationresultDto.setAftermaskdata( entity.getAftermaskdata() );
        maskKanonymizationresultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        maskKanonymizationresultDto.setDbname( entity.getDbname() );
        maskKanonymizationresultDto.setEndtime( entity.getEndtime() );
        maskKanonymizationresultDto.setId( entity.getId() );
        maskKanonymizationresultDto.setMaskline( entity.getMaskline() );
        maskKanonymizationresultDto.setOutputdirectory( entity.getOutputdirectory() );
        maskKanonymizationresultDto.setRemark( entity.getRemark() );
        maskKanonymizationresultDto.setSparefield1( entity.getSparefield1() );
        maskKanonymizationresultDto.setSparefield2( entity.getSparefield2() );
        maskKanonymizationresultDto.setSparefield3( entity.getSparefield3() );
        maskKanonymizationresultDto.setSparefield4( entity.getSparefield4() );
        maskKanonymizationresultDto.setSparefield5( entity.getSparefield5() );
        maskKanonymizationresultDto.setSparefield6( entity.getSparefield6() );
        maskKanonymizationresultDto.setStarttime( entity.getStarttime() );
        maskKanonymizationresultDto.setTabname( entity.getTabname() );
        maskKanonymizationresultDto.setTaskname( entity.getTaskname() );
        maskKanonymizationresultDto.setTaskresultestimate( entity.getTaskresultestimate() );
        maskKanonymizationresultDto.setTaskstatus( entity.getTaskstatus() );
        maskKanonymizationresultDto.setTotalline( entity.getTotalline() );

        return maskKanonymizationresultDto;
    }

    @Override
    public List<MaskKanonymizationresultDto> toDto(List<MaskKanonymizationresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskKanonymizationresultDto> list = new ArrayList<MaskKanonymizationresultDto>( entityList.size() );
        for ( MaskKanonymizationresult maskKanonymizationresult : entityList ) {
            list.add( toDto( maskKanonymizationresult ) );
        }

        return list;
    }

    @Override
    public MaskKanonymizationresult toEntity(MaskKanonymizationresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskKanonymizationresult maskKanonymizationresult = new MaskKanonymizationresult();

        maskKanonymizationresult.setAftermaskdata( dto.getAftermaskdata() );
        maskKanonymizationresult.setBeforemaskdata( dto.getBeforemaskdata() );
        maskKanonymizationresult.setDbname( dto.getDbname() );
        maskKanonymizationresult.setEndtime( dto.getEndtime() );
        maskKanonymizationresult.setId( dto.getId() );
        maskKanonymizationresult.setMaskline( dto.getMaskline() );
        maskKanonymizationresult.setOutputdirectory( dto.getOutputdirectory() );
        maskKanonymizationresult.setRemark( dto.getRemark() );
        maskKanonymizationresult.setSparefield1( dto.getSparefield1() );
        maskKanonymizationresult.setSparefield2( dto.getSparefield2() );
        maskKanonymizationresult.setSparefield3( dto.getSparefield3() );
        maskKanonymizationresult.setSparefield4( dto.getSparefield4() );
        maskKanonymizationresult.setSparefield5( dto.getSparefield5() );
        maskKanonymizationresult.setSparefield6( dto.getSparefield6() );
        maskKanonymizationresult.setStarttime( dto.getStarttime() );
        maskKanonymizationresult.setTabname( dto.getTabname() );
        maskKanonymizationresult.setTaskname( dto.getTaskname() );
        maskKanonymizationresult.setTaskresultestimate( dto.getTaskresultestimate() );
        maskKanonymizationresult.setTaskstatus( dto.getTaskstatus() );
        maskKanonymizationresult.setTotalline( dto.getTotalline() );

        return maskKanonymizationresult;
    }

    @Override
    public List<MaskKanonymizationresult> toEntity(List<MaskKanonymizationresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskKanonymizationresult> list = new ArrayList<MaskKanonymizationresult>( dtoList.size() );
        for ( MaskKanonymizationresultDto maskKanonymizationresultDto : dtoList ) {
            list.add( toEntity( maskKanonymizationresultDto ) );
        }

        return list;
    }
}
