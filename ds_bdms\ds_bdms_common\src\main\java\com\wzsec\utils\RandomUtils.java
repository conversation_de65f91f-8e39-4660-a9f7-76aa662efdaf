package com.wzsec.utils;

import java.util.HashSet;
import java.util.Random;

/**
 * @ClassName: RandomUtils
 * @Description: java生成随机数RandomUtils
 * <AUTHOR>
 * @date 2016-11-21
 */
public class RandomUtils {

	// 定义所有的字符组成的串
	public static final String allChar = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	// 定义所有的大小写字符组成的串（不包括数字）
	public static final String letterChar = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	// 定义所有的数字字符组成的串
	public static final String numberChar = "0123456789";

	// 定义36位数字和小写字母的组合
	public static final String numberAndlowerChar = "0123456789abcdefghijklmnopqrstuvwxyz";

	// 定义26位小写字母
	public static final String lowerChar = "abcdefghijklmnopqrstuvwxyz";

	// 定义前18位小写字母
	public static final String lower18Char = "abcdefghijklmnopqr";

	// 单个字符@
	public static final String singleChar = "@";

	private static Random random = new Random();

	/**
	 * @产生长度为length的随机字符串（数字） @param length
	 * @return
	 */
	public static String generateNumCharString(int length) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(numberChar.charAt(random.nextInt(numberChar.length())));
		}
		return sb.toString();
	}

	/**
	 * @ClassName: generateReorderStr
	 * @Description: 将字符串打乱输出
	 * <AUTHOR>
	 * @date 2017-9-28
	 */
	public static String generateReorderStr(String strSrc) {
		String tmp = "";
		for (int i = 0; i < strSrc.length(); i++) {
			int rand = (int) (Math.random() * strSrc.length());
			char ch = strSrc.charAt(rand);
			if (!tmp.contains(ch + "")) {
				tmp += ch;
			} else {
				i--;
			}
		}
		return tmp;
	}

	/**
	 * @ClassName: getRandomIntInRange
	 * @Description: 生成某区间的随机数
	 * <AUTHOR>
	 * @date 2017-9-28
	 */
	public static int getRandomIntInRange(int Min, int Max) {
		return (int) Math.round(Math.random() * (Max - Min) + Min);
	}

	/**
	 * @产生长度为length的随机字符串（小写字母） @param length
	 * @return
	 */
	public static String generateLowerCharString(int length) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(lowerChar.charAt(random.nextInt(lowerChar.length())));
		}
		return sb.toString();
	}

	/**
	 * @产生长度为length的随机字符串（所有字母和数字） @param length
	 * @return
	 */
	public static String generateNumAndCharString(int length) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(allChar.charAt(random.nextInt(allChar.length())));
		}
		return sb.toString();
	}

	/**
	 * @产生长度为length的随机字符串（小写字母和数字） @param length
	 * @return
	 */
	public static String generateNumAndLowCharString(int length) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(numberAndlowerChar.charAt(random.nextInt(numberAndlowerChar.length())));
		}
		return sb.toString();
	}

	/**
	 * @产生长度为length的随机字符串（0或者1） @param length
	 * @return
	 */
	public static String generateZeroAndOneCharString(int length) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(Math.round(random.nextDouble()));
		}
		return sb.toString();
	}

	/**
	 * @随机指定范围内N个不重复的数
	 * @利用HashSet的特征，只能存放不同的值
	 * @param min
	 *            指定范围最小值
	 * @param max
	 *            指定范围最大值
	 * @param n
	 *            随机数个数
	 * @param HashSet<Integer>
	 *            set 随机数结果集
	 */
	public static void generateRandomSet(int min, int max, int n, HashSet<Integer> set) {
		if (n > (max - min + 1) || max < min) {
			return;
		}
		for (int i = 0; i < n; i++) {
			// 调用Math.random()方法
			int num = (int) (Math.random() * (max - min)) + min;
			set.add(num);// 将不同的数存入HashSet中
		}
		int setSize = set.size();
		// 如果存入的数小于指定生成的个数，则调用递归再生成剩余个数的随机数，如此循环，直到达到指定大小
		if (setSize < n) {
			generateRandomSet(min, max, n - setSize, set);// 递归
		}
	}

	/**
	 * @随机产生区间值,如2,3
	 *
	 * @param length
	 * @return
	 */
	public static String generateRandomIntervalValueString(int length) {
		StringBuffer sb = new StringBuffer();
		int min = random.nextInt(length);
		if (min == 0) {
			min = 1;
		}
		sb.append(min);
		sb.append(",");
		sb.append(random.nextInt(length - min) + min);
		return sb.toString();
	}

	public static void main(String[] args) {
		System.out.println(generateRandomIntervalValueString(18));
		System.out.println(RandomUtils.generateReorderStr(RandomUtils.lowerChar));
	}
}
