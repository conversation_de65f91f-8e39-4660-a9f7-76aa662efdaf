package com.wzsec.utils.database;

import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 创建MySQL表并批量插入数据操作类
 *
 * <AUTHOR>
 * @Description 通过java List<Map<String,
 * Object>>生成SQL语句，批量插入语句，用于创建表，并批量插入表数据库操作类
 * @date 2019年10月11日 上午10:46:44
 */
@Slf4j
public class ByteHouseUtil extends DatabaseUtil {

    private static String JDBC_DRIVER = "com.clickhouse.jdbc.ClickHouseDriver";

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, String>> objList, String dbname, String tableName, String fieldnames) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, String> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    @Override
    protected Map<String, Object> getInsert2TableSqlAndPatams(List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (Map<String, Object> map : objList) {
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param objList：Map数据库集合
     * @param tableName：表名
     * @return Map<String, Object>：key为“sql”是批量插入语句，key为“params”是插入语句参数
     * @Description 通过Map数据集合生成批量插入SQL语句及插入语句参数（占位符形式）
     * <AUTHOR>
     * @date 2019年10月15日10:21:52
     */
    @Override
    public Map<String, Object> getInsert2TableSqlAndPatams(int start, int end, List<Map<String, Object>> objList, String dbname, String tableName) {
        Map<String, Object> sqlAndParams = null;
        try {
            List<Object> params = new ArrayList<>();
            Set<String> fields = objList.get(0).keySet();
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO `").append(tableName).append("` (");
            for (String column : fields) {
                sb.append("`").append(column).append("`, ");
            }
            String sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sb = new StringBuilder(sql);
            sb.append(") VALUES ");
            for (int i = start; i < end; i++) {
                Map<String, Object> map = objList.get(i);
                sb.append("(");
                for (String key : fields) {// 循环字段名，使用fields保证顺序一致
                    sb.append("?, ");
                    params.add(map.get(key));
                }
                sql = sb.toString();
                lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
                sql = sql.substring(0, lastIndex);
                sb = new StringBuilder(sql);
                sb.append("), ");
            }
            sql = sb.toString();
            lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            // sql += ";";
            sqlAndParams = new HashMap<>();
            sqlAndParams.put("sql", sql);
            sqlAndParams.put("params", params.toArray());
        } catch (Exception e) {
            e.printStackTrace();
            sqlAndParams = null;
        }
        return sqlAndParams;
    }

    /**
     * @param tableName：表名
     * @param dburl：数据库连接信息
     * @param username：数据库用户名
     * @param password：数据库密码
     * @return List<Map < String, String>>：数据库字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2019年10月12日 下午3:15:07
     */
    @Override
    protected List<Map<String, String>> getTableFieldInfo(String dbName, String tableName, String dburl, String username,
                                                          String password) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, dbName, tableName, null);
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String columnComment = columns.getString("REMARKS");
                String columnType = columns.getString("TYPE_NAME");

                Map<String, String> fieldInfoMap = new HashMap<>();
                //TODO 所有DBUtil字段信息尽量统一，以便支持不同数据源类型之间脱敏
                fieldInfoMap.put("FieldEName", columnName);//字段名
                fieldInfoMap.put("FieldCName", columnComment);//注释
                fieldInfoMap.put("FieldType", columnType);//格式：string

                fieldInfoList.add(fieldInfoMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return fieldInfoList;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("FieldEName")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" String");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    } else {
                        sb.append(" DEFAULT NULL");
                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("FieldCName") != null && !"".equals(fieldInfo.get("FieldCName"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("FieldCName")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + ")\rENGINE = Distributed('bytehouse', 'default', 'temp_ods_four_modernizations_dashboard_data_a_d_local')";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    /**
     * @param obj：Map对象
     * @param tableName：表名
     * @return String：生成的SQL语句
     * @Description 通过Map生成创建表SQL语句，自动检测字段名及类型
     * <AUTHOR>
     * @date 2019年10月11日 下午3:10:18
     */
    @Override
    protected String getCreateTableSql(List<Map<String, String>> fieldInfoList, Map<String, Object> obj,
                                       String tableName, List<String> maskfields, String dbname, String watermarkField) {
        String sql = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
            for (Map<String, String> fieldInfo : fieldInfoList) {
                if (!obj.keySet().contains(fieldInfo.get("FieldEName"))) {// 跳过没有抽取的列
                    continue;
                }
                sb.append("`").append(fieldInfo.get("FieldEName")).append("`");// 字段名
                if (maskfields != null && maskfields.contains(fieldInfo.get("FieldEName"))) {// 脱敏的字段类型更改为varchar
                    sb.append(" String");// 类型
                } else {
                    sb.append(" ").append(fieldInfo.get("FieldType"));// 类型
                }
                if ("NO".equalsIgnoreCase(fieldInfo.get("Null"))) {// 判断非空
                    sb.append(" NOT NULL");
                }
                if ("auto_increment".equalsIgnoreCase(fieldInfo.get("Extra"))) {// 判断非空
                    sb.append(" AUTO_INCREMENT");// 自增
                } else {
                    if (fieldInfo.get("Default") != null) {
                        sb.append(" DEFAULT '").append(fieldInfo.get("Default")).append("'");// 默认值
                    }
//                    else {
//                        sb.append(" DEFAULT NULL");
//                    }
                }
                if ("PRI".equalsIgnoreCase(fieldInfo.get("Key"))) {
                    sb.append(" PRIMARY KEY");// 主键
                }
                if (fieldInfo.get("FieldCName") != null && !"".equals(fieldInfo.get("FieldCName"))) {
                    sb.append(" COMMENT '").append(fieldInfo.get("FieldCName")).append("'");
                }
                sb.append(",\n");
            }
            sql = sb.toString();
            int lastIndex = sql.lastIndexOf(",");// 去掉最后一个逗号
            sql = sql.substring(0, lastIndex);
            sql = sql + ")\rENGINE = Distributed('bytehouse', '" + dbname + "', '" + tableName + "_local')";
        } catch (Exception e) {
            e.printStackTrace();
            sql = null;
        }
        return sql;
    }

    @Override
    public List<String> getStoredProcedureSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getFunctionSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getTriggerSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getViewSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    @Override
    public List<String> getSequenceSql(String dbUrl, String username, String password, String dbName) {
        return null;
    }

    @Override
    public List<String> getIndexesSql(String dbUrl, String username, String password, String inDBName, String outDBName) {
        return null;
    }

    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static Map<String, String> getAllDbAndTabMap(Connection conn, String dbnames) throws SQLException {
        Map<String, String> dbTabMap = new LinkedHashMap<>();
        ResultSet resultSet = null;
        try {
            DatabaseMetaData metaData = conn.getMetaData();
            resultSet = metaData.getTables(null, dbnames, "%", new String[]{"TABLE"});
            while (resultSet.next()) {
                String tableName = resultSet.getString("TABLE_NAME");
                String tableComment = resultSet.getString("REMARKS");
                if (dbTabMap.containsKey(dbnames)) {
                    dbTabMap.put(dbnames, dbTabMap.get(dbnames) + "," + tableName);
                } else {
                    dbTabMap.put(dbnames, tableName);
                }
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            // 关闭连接和结果集
            closeCon(resultSet, null, null);
            return dbTabMap;
        }
    }

    /**
     * @Description:获取数据库中所有的库名表名
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static Map<String, String> getAllDbAndTabMap(String dburl, String username, String password, String dbnames) {
        Map<String, String> dbTabMap = new LinkedHashMap<>();
        Connection conn = null;
        ResultSet resultSet = null;
        try {
            conn = DriverManager.getConnection(dburl, username, password);
            DatabaseMetaData metaData = conn.getMetaData();
            resultSet = metaData.getTables(null, dbnames, "%", new String[]{"TABLE"});
            while (resultSet.next()) {
                String tableName = resultSet.getString("TABLE_NAME");
                String tableComment = resultSet.getString("REMARKS");
                if (dbTabMap.containsKey(dbnames)) {
                    dbTabMap.put(dbnames, dbTabMap.get(dbnames) + "," + tableName);
                } else {
                    dbTabMap.put(dbnames, tableName);
                }
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            // 关闭连接和结果集
            closeCon(resultSet, null, conn);
            return dbTabMap;
        }
    }

    /**
     * @Description:获取数据库表数据
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static List<String[]> getTabDataList(Connection conn, String dbname, String tabname, Integer lineNum) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String[]> tabDataList = new ArrayList<String[]>();
        try {
            String strSQL = "select * from " + tabname;
            if (lineNum != null && lineNum != 0)
                strSQL += " order by rand() LIMIT " + lineNum;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                String[] row = new String[columnCount];
                for (int i = 0; i < columnCount; i++) {
                    row[i] = rs.getString(i + 1) == null ? "" : rs.getString(i + 1);
                }
                tabDataList.add(row);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-13
     */
    public static List<String> getTabDataList(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String> tabDataList = new ArrayList<String>();
        try {
            String strSQL = "select * from " + dbname + "." + tabname + " order by rand() LIMIT 100 ";
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            ResultSetMetaData md = rs.getMetaData(); //获得结果集结构信息,元数据
            int columnCount = md.getColumnCount();   //获得列数
            while (rs.next()) {
                StringBuffer sub = new StringBuffer();
                for (int i = 1; i <= columnCount; i++) {
                    if (i > 1) {
                        sub.append(Const.DB_TAB_DATA_JOIN);
                    }
                    sub.append(rs.getObject(i));
                }
                tabDataList.add(sub.toString());
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return tabDataList;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(String dburl, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + dbname + "." + tabname + " ";
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return count;
    }

    /**
     * @Description:获取数据库表中前100条数据
     * <AUTHOR>
     * @date 2020-02-18
     */
    public static int getTabDataCount(Connection conn, String dbname, String tabname) throws SQLException {
        PreparedStatement stmt = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String strSQL = "select count(*) from " + tabname;
            stmt = conn.prepareStatement(strSQL);
            rs = stmt.executeQuery();
            while (rs.next()) {
                count = rs.getInt(1);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            //log.error("获取数据库中所有的库名表名出现异常");
            throw ex;
        } finally {
            closeCon(rs, stmt, null);
        }
        return count;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(Connection conn, String dbname, String tabname) throws SQLException {
        List<String> list = new ArrayList<>();
        // 获取表中的所有列名
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet columns = metaData.getColumns(null, dbname, tabname, null);
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
            String columnComment = columns.getString("REMARKS");
            list.add(columnName);
        }
        columns.close();
        return list;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(String url, String username, String password, String dbname, String tabname) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            conn = getConn(JDBC_DRIVER, url, username, password);// 打开连接
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' ORDER BY  ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(rs.getString(1).toUpperCase());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return list;
    }

    /**
     * @Description:获取数据库表中所有字段名
     * <AUTHOR>
     * @date 2020-2-13
     */
    public static List<String> getFieldNameList(String url, String username, String password, String dbname, String tabname, String tablenameAlias) {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<String>();
        try {
            conn = getConn(JDBC_DRIVER, url, username, password);// 打开连接
            String strSQL = "select COLUMN_NAME from  INFORMATION_SCHEMA.Columns where table_name='" + tabname + "' and table_schema='" + dbname + "' ORDER BY  ORDINAL_POSITION";
            stmt = conn.createStatement();// 执行创建表
            rs = stmt.executeQuery(strSQL);
            while (rs.next()) {
                list.add(tablenameAlias + "." + rs.getString(1));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            //log.error("获取数据库url:"+url+"库:"+dbname+"表:"+tabname+"中所有字段名出现异常");
        } finally {
            closeCon(rs, stmt, conn);
        }
        return list;
    }


    /**
     * @Description: 拼接建表语句
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static String getCreateTableSql(String srcsql, List<Map<String, String>> fieldInfoList,
                                           Map<String, String> obj, String tableName, List<String> maskfields) {
        Set<String> keySet = obj.keySet();
        StringBuffer sb = new StringBuffer();
        StringBuffer end_sb = new StringBuffer();
        String end_sql = srcsql.substring(srcsql.lastIndexOf(")"), srcsql.length());
//		String middle_sql = srcsql.substring(srcsql.indexOf("(")+1, srcsql.lastIndexOf(")"));
//		String[] srcfilemiddlesql = middle_sql.split(",");
        String middle_sql = srcsql.substring(srcsql.indexOf("(") + 1, srcsql.lastIndexOf(")")).trim();
        String[] srcfilemiddlesql = middle_sql.split("\\n");
        HashMap<String, String> srcfieldsql = new HashMap<String, String>();
        for (String s : srcfilemiddlesql) {
            String str = "";
            if (s.endsWith(",")) {
                str = s.substring(0, s.length() - 1).trim();
            } else {
                str = s.trim();
            }
            String fieldname = str.substring(1, str.lastIndexOf("`"));
            if (str.startsWith("`")) {
                srcfieldsql.put(fieldname, str);
            } else {
                for (String extractfieldnamestr : keySet) {
                    if (str.contains(extractfieldnamestr)) {
                        end_sb.append(str).append(",\r\n");
                    }

                }
            }
        }
        sb.append("CREATE TABLE `").append(tableName).append("` (\r\n");
        for (Map<String, String> fieldInfo : fieldInfoList) {
            String field = fieldInfo.get("Field");
            if (!keySet.contains(field)) {// 跳过没有抽取的列
                continue;
            }
            String fieldsql = srcfieldsql.get(field);
            if (maskfields != null && maskfields.contains(field) && fieldsql.contains("varchar")) {// 脱敏的字段类型更改为varchar
                int post = appearNumber(fieldsql, " ", 2);
                String str = fieldsql.substring(post, fieldsql.length());
                sb.append("`" + field + "`");
                sb.append(" varchar(255) ");// 类型
                int autoIndex = str.indexOf("AUTO_INCREMENT");
                if (autoIndex >= 0) {
                    str = str.replace("AUTO_INCREMENT", "");
                }
                sb.append(str).append(",\r\n");
            } else {
                sb.append(fieldsql).append(",\r\n");
            }

        }
        sb.append(end_sb.toString());
        int lastIndex = sb.lastIndexOf(",");// 去掉最后一个逗号
        int endAutoIndex = end_sql.indexOf("AUTO_INCREMENT");
        if (endAutoIndex >= 0) {
            String autoSql = end_sql.substring(endAutoIndex);
            autoSql = autoSql.substring(autoSql.indexOf("AUTO_INCREMENT"), autoSql.indexOf(" "));
            end_sql = end_sql.replace(autoSql, "");
        }
        return sb.substring(0, lastIndex) + end_sql;
    }

    /**
     * @Description: 获取原有建表语句
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static String getSrcCreateTableSql(String tableName, String dburl, String username, String password) {
        String srccreatetablesql = null;
        Connection conn = null;
        Statement stmt = null;
        try {
            conn = getConn(JDBC_DRIVER, dburl, username, password);// 打开连接
            stmt = conn.createStatement();// 执行创建表
            ResultSet rs = stmt.executeQuery("show create table `" + tableName + "`;");
            if (rs != null) {
                while (rs.next()) {
                    srccreatetablesql = rs.getString("Create Table");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        closeCon(null, stmt, conn);
        return srccreatetablesql;
    }

    /**
     * 根据当前字段类别，获取脱敏字段类别（改变原有字段类型、扩充字段长度）
     *
     * @param fieldType 字段类别，示例：varchar(255)
     * @param fieldFlag 字段总长度是否超过建表限制
     * @return
     */
    private static String getModificationFiledType(String fieldType, boolean fieldFlag) {
        String returnType = "";
        //原本字段是text类型，原封不动
        if (fieldType.contains("text")) {
            returnType = " " + fieldType + " ";
            return returnType;
        }

        int begIndex = fieldType.indexOf("(") + 1;
        int endIndex = fieldType.indexOf(")");
        Integer filedExtent = 0;

        //fieldType可能没有长度，获取下标为-1时，脱敏后长度为100大都够用
        if (endIndex == -1) {
            filedExtent = 50;
        } else {
            //数据类型可能是 decimal(10,0) 这种，需要特殊处理长度，脱敏后长度为100大都够用
            int i = fieldType.substring(begIndex, endIndex).indexOf(",");
            if (i == -1) {
                filedExtent = Integer.valueOf(fieldType.substring(begIndex, endIndex));
            } else {
                filedExtent = 50;
            }
        }

        if (fieldFlag) {
            returnType = " text ";
        } else {
            //varchar类型，根据原本长度判断需要扩充多少长度
            if (fieldType.contains("varchar")) {
                //varchar长度不宜设置过长，最好不要超过255，过长的数据直接换成text
                if (filedExtent <= 60) {
                    returnType = " varchar(" + filedExtent * 2 + ") ";
                } else if (filedExtent <= 127) {
                    returnType = " varchar(255) ";
                } else if (filedExtent <= 255) {
                    returnType = " varchar(500) ";
                } else {
                    returnType = " text ";
                }
            } else {
                //其他类型换成原本长度*2大部分情况都够用
                returnType = " varchar(" + filedExtent * 2 + ") ";
            }
        }
        return returnType;
    }

    /**
     * @Description: 获取字符串出现次数的位置
     * <AUTHOR>
     * @date 2020年2月27日
     */
    private static int appearNumber(String fieldsql, String s, int i) {
        Pattern pattern = Pattern.compile(s);
        Matcher findMatcher = pattern.matcher(fieldsql);
        int number = 0;
        while (findMatcher.find()) {
            number++;
            if (number == i) {//当“i”次出现时停止
                break;
            }
        }
        return findMatcher.start();
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表字段信息
     * @Description 查询表字段信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:08
     */
    public static List<Map<String, String>> getTableFieldInfoBySchema(String dbName, String tableName, Connection conn) {
        List<Map<String, String>> fieldInfoList = new ArrayList<>();
        Statement stmt = null;
        ResultSet columns = null;
        try {
            DatabaseMetaData metaData = conn.getMetaData();
            columns = metaData.getColumns(null, dbName, tableName, null);
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String columnComment = columns.getString("REMARKS");
                Map<String, String> fieldInfoMap = new HashMap<>();
                fieldInfoMap.put("fieldName", columnName);
                fieldInfoMap.put("fieldCName", columnComment);
                fieldInfoList.add(fieldInfoMap);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeCon(columns, stmt, null);
            return fieldInfoList;
        }
    }

    /**
     * @param tableName：表名
     * @return List<Map < String, String>>：数据库表信息
     * @Description 查询表信息
     * <AUTHOR>
     * @date 2020年6月11日09:42:56
     */
    public static Map<String, String> getTableInfoBySchema(String dbName, String tableName, Connection conn) {
        Map<String, String> tableInfoMap = new HashMap<>();
        ResultSet resultSet = null;
        try {
            DatabaseMetaData metaData = conn.getMetaData();
            resultSet = metaData.getTables(null, dbName, tableName, new String[]{"TABLE"});
            while (resultSet.next()) {
                String tabName = resultSet.getString("TABLE_NAME");
                String tabComment = resultSet.getString("REMARKS");
                tableInfoMap.put("tableName", tabName);
                tableInfoMap.put("tableCName", tabComment);
            }
        } catch (Exception ex) {
            System.out.println("获取数据库中所有的库名表名出现异常");
            throw ex;
            //log.error("获取数据库中所有的库名表名出现异常");
        } finally {
            // 关闭连接和结果集
            closeCon(resultSet, null, null);
            return tableInfoMap;
        }
    }


    /**
     * @description: 判断数据源数据库是否存在，存在跳过，不存在创建
     * @param: map   连接数据库需要参数
     * @return:
     * @author: penglei
     * @date: 2024/11/14 16:19
     */
    public static Map<String, String> createDataBaseIfNoExist(Map<String, String> map) {
        Map<String, String> msgMap = new HashMap<>();

        // 1. 从输入参数中获取数据库连接信息
        String driverProgram = map.get("driverprogram");
        String username = map.get("username");
        String password = map.get("password");
        // 解密密码（如果需要）
        if (StringUtils.isNotEmpty(password)) {
            try {
                password = AES.decrypt(password, Const.AES_SECRET_KEY);
            } catch (Exception e) {
                e.printStackTrace();
                msgMap.put("code", Const.DATABASE_ERROR);
                msgMap.put("msg", "密码解密失败：" + e.getMessage());
                return msgMap;
            }
        }

        String dbname = map.get("dbname");
        String srcPort = map.get("srcport");
        String srcIp = map.get("srcip");

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 注册 JDBC 驱动
            Class.forName(driverProgram);

            // 2. 打开连接到 Bytehouse 数据库
            String jdbcUrl = "jdbc:bytehouse://" + srcIp + ":" + srcPort + "/default";
            connection = DriverManager.getConnection(jdbcUrl, username, password);

            // 3. 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();
            resultSet = metaData.getCatalogs();
            boolean databaseExists = false;
            while (resultSet.next()) {
                String databaseName = resultSet.getString(1);
                if (dbname.equals(databaseName)) {
                    databaseExists = true;
                    break;
                }
            }
            // 4. 如果数据库不存在，则创建它
            if (!databaseExists) {
                String createDatabaseSQL = "CREATE DATABASE " + dbname;
                statement = connection.createStatement();
                statement.executeUpdate(createDatabaseSQL);
                System.out.println("数据库" + dbname + "已创建！");
                msgMap.put("code", Const.DATABASE_CREATE);
                msgMap.put("msg", "数据库" + dbname + "已创建");
            } else {
                System.out.println("数据库" + dbname + "已存在，跳过创建步骤！");
                msgMap.put("code", Const.DATABASE_EXIST);
                msgMap.put("msg", "数据库" + dbname + "已存在");
            }

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "JDBC驱动未找到：" + e.getMessage());
        } catch (SQLException e) {
            e.printStackTrace();
            msgMap.put("code", Const.DATABASE_ERROR);
            msgMap.put("msg", "数据库操作失败：" + e.getMessage());
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
        return msgMap;
    }

}
