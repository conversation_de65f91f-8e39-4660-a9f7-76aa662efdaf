package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskKanonymizationtask;
import com.wzsec.modules.mask.service.MaskKanonymizationtaskService;
import com.wzsec.modules.mask.service.dto.MaskKanonymizationtaskQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2024-10-14
*/
// @Api(tags = "匿名化任务配置管理")
@RestController
@RequestMapping("/api/maskKanonymizationtask")
public class MaskKanonymizationtaskController {

    private final MaskKanonymizationtaskService maskKanonymizationtaskService;

    public MaskKanonymizationtaskController(MaskKanonymizationtaskService maskKanonymizationtaskService) {
        this.maskKanonymizationtaskService = maskKanonymizationtaskService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskKanonymizationtask:list')")
    public void download(HttpServletResponse response, MaskKanonymizationtaskQueryCriteria criteria) throws IOException {
        maskKanonymizationtaskService.download(maskKanonymizationtaskService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询匿名化任务配置")
    // @ApiOperation("查询匿名化任务配置")
    @PreAuthorize("@el.check('maskKanonymizationtask:list')")
    public ResponseEntity<Object> getMaskKanonymizationtasks(MaskKanonymizationtaskQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskKanonymizationtaskService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增匿名化任务配置")
    // @ApiOperation("新增匿名化任务配置")
    @PreAuthorize("@el.check('maskKanonymizationtask:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskKanonymizationtask resources){
        return new ResponseEntity<>(maskKanonymizationtaskService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改匿名化任务配置")
    // @ApiOperation("修改匿名化任务配置")
    @PreAuthorize("@el.check('maskKanonymizationtask:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskKanonymizationtask resources){
        maskKanonymizationtaskService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除匿名化任务配置")
    // @ApiOperation("删除匿名化任务配置")
    @PreAuthorize("@el.check('maskKanonymizationtask:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskKanonymizationtaskService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取新增任务号")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(maskKanonymizationtaskService.getMAXTaskName(), HttpStatus.CREATED);
    }

    @Log("执行匿名化脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Long id, HttpServletRequest request) {
        maskKanonymizationtaskService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
