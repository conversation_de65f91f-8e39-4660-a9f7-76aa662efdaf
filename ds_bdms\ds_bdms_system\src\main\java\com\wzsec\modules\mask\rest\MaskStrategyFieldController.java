package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.domain.ColumnInfo;
import com.wzsec.modules.mask.domain.Algorithm;
import com.wzsec.modules.mask.domain.MaskStrategyField;
import com.wzsec.modules.mask.domain.Maskrule;
import com.wzsec.modules.mask.service.MaskStrategyFieldService;
import com.wzsec.modules.mask.service.dto.MaskStrategyFieldDto;
import com.wzsec.modules.mask.service.dto.MaskStrategyFieldQueryCriteria;
import com.wzsec.modules.metadata.repository.MetaTableRepository;
import com.wzsec.modules.metadata.service.MetaTableService;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2020-11-09
*/
// @Api(tags = "敏感数据发现脱敏策略(字段)管理")
@RestController
@RequestMapping("/api/maskStrategyField")
public class MaskStrategyFieldController {

    private final MaskStrategyFieldService maskStrategyFieldService;

    public MaskStrategyFieldController(MaskStrategyFieldService maskStrategyFieldService) {
        this.maskStrategyFieldService = maskStrategyFieldService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, MaskStrategyFieldQueryCriteria criteria) throws IOException {
        maskStrategyFieldService.download(maskStrategyFieldService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询敏感数据发现脱敏策略(字段)")
    // @ApiOperation("查询敏感数据发现脱敏策略(字段)")
    public ResponseEntity<Object> getMaskStrategyFields(MaskStrategyFieldQueryCriteria criteria, Pageable pageable){
        List<MaskStrategyFieldDto> maskStrategyFieldDtos = maskStrategyFieldService.queryAll(criteria);
        return new ResponseEntity<>(PageUtil.toPage(maskStrategyFieldDtos,maskStrategyFieldDtos.size()),HttpStatus.OK);
    }

    // @ApiOperation("保存字段数据")
    @PutMapping(value = "save")
    public ResponseEntity<HttpStatus> save(@RequestBody List<MaskStrategyField> maskStrategyFields){
        maskStrategyFieldService.save(maskStrategyFields);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping
    @Log("新增敏感数据发现脱敏策略(字段)")
    // @ApiOperation("新增敏感数据发现脱敏策略(字段)")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskStrategyField resources){
        return new ResponseEntity<>(maskStrategyFieldService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改敏感数据发现脱敏策略(字段)")
    // @ApiOperation("修改敏感数据发现脱敏策略(字段)")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskStrategyField resources){
        maskStrategyFieldService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除敏感数据发现脱敏策略(字段)")
    // @ApiOperation("删除敏感数据发现脱敏策略(字段)")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskStrategyFieldService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/generateSeckey/{id}")
    @Log("生成密钥")
    // @ApiOperation("生成密钥")
    public ResponseEntity<Object> generateSeckey(@PathVariable Integer id){
        return new ResponseEntity<>(maskStrategyFieldService.generateSeckey(id),HttpStatus.OK);
    }
}
