package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.MaskTaskconfigrecords;
import com.wzsec.modules.statistics.service.dto.MaskTaskconfigrecordsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:01+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskTaskconfigrecordsMapperImpl implements MaskTaskconfigrecordsMapper {

    @Override
    public MaskTaskconfigrecordsDto toDto(MaskTaskconfigrecords entity) {
        if ( entity == null ) {
            return null;
        }

        MaskTaskconfigrecordsDto maskTaskconfigrecordsDto = new MaskTaskconfigrecordsDto();

        maskTaskconfigrecordsDto.setCreatetime( entity.getCreatetime() );
        maskTaskconfigrecordsDto.setCreateuser( entity.getCreateuser() );
        maskTaskconfigrecordsDto.setId( entity.getId() );
        maskTaskconfigrecordsDto.setOperation( entity.getOperation() );
        maskTaskconfigrecordsDto.setSparefield1( entity.getSparefield1() );
        maskTaskconfigrecordsDto.setSparefield2( entity.getSparefield2() );
        maskTaskconfigrecordsDto.setSparefield3( entity.getSparefield3() );
        maskTaskconfigrecordsDto.setSparefield4( entity.getSparefield4() );
        maskTaskconfigrecordsDto.setTaskname( entity.getTaskname() );
        maskTaskconfigrecordsDto.setTasktype( entity.getTasktype() );

        return maskTaskconfigrecordsDto;
    }

    @Override
    public List<MaskTaskconfigrecordsDto> toDto(List<MaskTaskconfigrecords> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskTaskconfigrecordsDto> list = new ArrayList<MaskTaskconfigrecordsDto>( entityList.size() );
        for ( MaskTaskconfigrecords maskTaskconfigrecords : entityList ) {
            list.add( toDto( maskTaskconfigrecords ) );
        }

        return list;
    }

    @Override
    public MaskTaskconfigrecords toEntity(MaskTaskconfigrecordsDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskTaskconfigrecords maskTaskconfigrecords = new MaskTaskconfigrecords();

        maskTaskconfigrecords.setCreatetime( dto.getCreatetime() );
        maskTaskconfigrecords.setCreateuser( dto.getCreateuser() );
        maskTaskconfigrecords.setId( dto.getId() );
        maskTaskconfigrecords.setOperation( dto.getOperation() );
        maskTaskconfigrecords.setSparefield1( dto.getSparefield1() );
        maskTaskconfigrecords.setSparefield2( dto.getSparefield2() );
        maskTaskconfigrecords.setSparefield3( dto.getSparefield3() );
        maskTaskconfigrecords.setSparefield4( dto.getSparefield4() );
        maskTaskconfigrecords.setTaskname( dto.getTaskname() );
        maskTaskconfigrecords.setTasktype( dto.getTasktype() );

        return maskTaskconfigrecords;
    }

    @Override
    public List<MaskTaskconfigrecords> toEntity(List<MaskTaskconfigrecordsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskTaskconfigrecords> list = new ArrayList<MaskTaskconfigrecords>( dtoList.size() );
        for ( MaskTaskconfigrecordsDto maskTaskconfigrecordsDto : dtoList ) {
            list.add( toEntity( maskTaskconfigrecordsDto ) );
        }

        return list;
    }
}
