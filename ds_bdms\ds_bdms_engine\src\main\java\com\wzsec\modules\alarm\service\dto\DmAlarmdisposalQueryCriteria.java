package com.wzsec.modules.alarm.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-07
 */
@Data
public class DmAlarmdisposalQueryCriteria {

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String circumstantiality;

    /** 精确 */
    @Query
    private String treatmentstate;
    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<String> createtime;

    @Query(blurry = "apicodeapinamedetectionmodelcircumstantialityrisktreatmentstate")
    private String blurry;
}
