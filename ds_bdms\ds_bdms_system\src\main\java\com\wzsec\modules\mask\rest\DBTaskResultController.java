package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.DBTaskResult;
import com.wzsec.modules.mask.service.DBTaskResultService;
import com.wzsec.modules.mask.service.dto.DBTaskResultDto;
import com.wzsec.modules.mask.service.dto.DBTaskResultQueryCriteria;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2020-11-12
*/
// @Api(tags = "敏感数据发现数据库脱敏结果管理")
@RestController
@RequestMapping("/api/dbtaskresult")
public class DBTaskResultController {

    private final DBTaskResultService dbtaskresultService;

    public DBTaskResultController(DBTaskResultService dbtaskresultService) {
        this.dbtaskresultService = dbtaskresultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('dbtaskresult:list')")
    public void download(HttpServletResponse response, DBTaskResultQueryCriteria criteria) throws IOException {
        dbtaskresultService.download(dbtaskresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询敏感数据发现数据库脱敏结果")
    // @ApiOperation("查询敏感数据发现数据库脱敏结果")
    @PreAuthorize("@el.check('dbtaskresult:list')")
    public ResponseEntity<Object> getDbtaskresults(DBTaskResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(dbtaskresultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增敏感数据发现数据库脱敏结果")
    // @ApiOperation("新增敏感数据发现数据库脱敏结果")
    @PreAuthorize("@el.check('dbtaskresult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody DBTaskResult resources){
        return new ResponseEntity<>(dbtaskresultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改敏感数据发现数据库脱敏结果")
    // @ApiOperation("修改敏感数据发现数据库脱敏结果")
    @PreAuthorize("@el.check('dbtaskresult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody DBTaskResult resources){
        dbtaskresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除敏感数据发现数据库脱敏结果")
    // @ApiOperation("删除敏感数据发现数据库脱敏结果")
    @PreAuthorize("@el.check('dbtaskresult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        dbtaskresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("敏感数据发现数据库脱敏结果预览")
    // @ApiOperation("敏感数据发现数据库脱敏结果预览")
    @GetMapping(value = "/dbTaskResultDetails")
    @PreAuthorize("@el.check('dbtaskresult:list')")
    public ResponseEntity<Object> dbTaskResultDetails(DBTaskResultQueryCriteria criteria){
        DBTaskResultDto byId = dbtaskresultService.findById(Integer.valueOf(criteria.getId()));
        String beforemaskdata = byId.getBeforemaskdata();
        String[] before = beforemaskdata.split("\\n");
        String aftermaskdata = byId.getAftermaskdata();
        String[] after = aftermaskdata.split("\\n");
        List<DBTaskResultDto> DBTaskResultList = new ArrayList<DBTaskResultDto>();
        if (before.length == after.length) {
            DBTaskResultDto DBTaskResulttmp =null;
            for (int i = 0; i < before.length; i++) {
                DBTaskResulttmp = new DBTaskResultDto();
                DBTaskResulttmp.setBeforemaskdata(before[i]);
                DBTaskResulttmp.setAftermaskdata(after[i]);
                DBTaskResultList.add(DBTaskResulttmp);
            }
        }
        return new ResponseEntity<>(PageUtil.toPage(DBTaskResultList,DBTaskResultList.size()),HttpStatus.OK);

    }


}
