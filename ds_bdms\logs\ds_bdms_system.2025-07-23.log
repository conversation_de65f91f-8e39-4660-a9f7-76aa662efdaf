2025-07-23 11:11:08,055 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 9852 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:11:08,062 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:11:12,318 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:11:12,320 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:11:14,073 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1627 ms. Found 111 JPA repository interfaces.
2025-07-23 11:11:14,901 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:11:14,902 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:11:14,906 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:11:14,906 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:11:14,910 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:11:14,912 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:11:14,915 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:11:14,916 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:11:14,916 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:11:16,368 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:11:16,406 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:11:16,412 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:11:17,468 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 11:11:17,538 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 11:11:17,546 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 11:11:17,546 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 11:11:17,661 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.6287545558823214479] which is part of the web application []
2025-07-23 11:11:18,124 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 11:11:18,125 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 9352 ms
2025-07-23 11:11:22,456 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:11:23,353 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:11:23,453 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:11:23,454 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:11:23,456 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:11:23,457 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:11:23,458 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:11:23,460 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:11:23,469 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:11:23,477 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:11:29,165 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:11:31,235 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:11:31,769 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:11:33,152 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:11:34,617 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:11:46,046 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:11:46,196 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:11:57,072 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 11:12:04,850 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:12:05,082 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:12:05,082 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:12:05,134 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:12:05,145 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:12:05,145 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:12:05,145 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:12:05,148 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@2a70d464
2025-07-23 11:12:05,148 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:12:30,276 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3d5edd7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@414331e4, org.springframework.security.web.context.SecurityContextPersistenceFilter@5665b26f, org.springframework.security.web.header.HeaderWriterFilter@294ce57a, org.springframework.security.web.authentication.logout.LogoutFilter@27f546, org.springframework.web.filter.CorsFilter@4e951fb1, com.wzsec.modules.security.security.TokenFilter@53a51411, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62ececb1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59c268e0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19f634ac, org.springframework.security.web.session.SessionManagementFilter@3772b95a, org.springframework.security.web.access.ExceptionTranslationFilter@14747c05, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7f6cf329]
2025-07-23 11:12:30,390 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 11:12:41,396 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:12:41,396 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:12:41,396 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:12:41,396 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:12:41,396 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:12:41,396 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:12:41,400 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:12:41,400 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@c95132f
2025-07-23 11:12:41,818 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-23 11:12:42,000 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 11:12:42,219 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-23 11:12:42,228 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 11:12:42,233 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 11:12:42,233 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 11:12:42,233 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 11:12:42,233 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:12:42,233 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:12:42,233 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:12:42,233 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:12:42,233 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:12:42,240 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 11:12:42,240 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 11:12:42,240 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:12:42,249 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-23 11:12:46,423 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-23 11:12:46,678 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-23 11:12:47,020 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-23 11:12:47,311 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-23 11:12:47,610 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-23 11:12:47,610 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-23 11:12:47,632 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 100.601 seconds (JVM running for 104.852)
2025-07-23 11:12:49,598 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-23 11:29:40,181 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 1916 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:29:40,196 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:29:47,357 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:29:47,359 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:29:50,511 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 2895 ms. Found 111 JPA repository interfaces.
2025-07-23 11:29:52,143 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:29:52,146 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:29:52,151 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:29:52,151 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:29:52,156 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:29:52,159 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:29:52,163 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:29:52,163 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:29:52,164 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:29:54,293 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:29:54,336 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:29:54,341 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:29:55,575 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 11:29:55,645 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 11:29:55,656 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 11:29:55,656 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 11:29:55,790 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.1892551654532033586] which is part of the web application []
2025-07-23 11:29:56,311 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 11:29:56,313 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 14603 ms
2025-07-23 11:30:00,178 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:30:01,057 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:30:01,141 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:30:01,141 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:30:01,141 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:30:01,141 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:30:01,141 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:30:01,146 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:30:01,153 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:30:01,158 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:30:07,234 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:30:09,469 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:30:10,043 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:30:11,533 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:30:13,134 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:30:24,478 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:30:24,629 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:30:33,151 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 11:30:48,237 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:30:48,738 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:30:48,738 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:30:48,812 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:30:48,827 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:30:48,827 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:30:48,827 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:30:48,831 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@21484c17
2025-07-23 11:30:48,831 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:31:25,669 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageController' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\rest\LocalStorageController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageServiceImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\service\impl\LocalStorageServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.wzsec.service.mapper.LocalStorageMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-23 11:31:25,715 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 11:31:25,715 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:31:25,716 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 11:31:25,721 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:31:25,793 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 11:31:25,942 INFO (DirectJDKLog.java:173)- Stopping service [Tomcat]
2025-07-23 11:31:25,999 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 11:31:26,241 ERROR (LoggingFailureAnalysisReporter.java:40)- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.wzsec.service.impl.LocalStorageServiceImpl required a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' that could not be found.


Action:

Consider defining a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' in your configuration.

2025-07-23 11:35:07,096 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 30416 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:35:07,105 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:35:09,970 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:35:09,972 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:35:11,409 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1295 ms. Found 111 JPA repository interfaces.
2025-07-23 11:35:12,264 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:35:12,265 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:35:12,268 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:35:12,269 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:35:12,273 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:35:12,275 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:35:12,278 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:35:12,278 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:35:12,278 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:35:13,296 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:35:13,317 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:35:13,321 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:35:13,920 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 11:35:13,951 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 11:35:13,956 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 11:35:13,957 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 11:35:14,009 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.8803382354394298864] which is part of the web application []
2025-07-23 11:35:14,282 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 11:35:14,282 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 6592 ms
2025-07-23 11:35:15,942 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:35:16,306 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:35:16,337 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:35:16,337 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:35:16,337 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:35:16,338 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:35:16,338 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:35:16,339 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:35:16,342 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:35:16,345 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:35:19,466 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:35:20,619 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:35:20,872 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:35:21,488 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:35:22,100 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:35:27,139 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:35:27,196 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:35:30,449 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 11:35:36,356 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:35:36,486 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:35:36,486 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:35:36,514 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:35:36,521 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:35:36,521 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:35:36,521 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:35:36,524 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@5454e13f
2025-07-23 11:35:36,524 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:35:46,290 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageController' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\rest\LocalStorageController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageServiceImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\service\impl\LocalStorageServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.wzsec.service.mapper.LocalStorageMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-23 11:35:46,341 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 11:35:46,342 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:35:46,342 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 11:35:46,352 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:35:46,385 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 11:35:46,452 INFO (DirectJDKLog.java:173)- Stopping service [Tomcat]
2025-07-23 11:35:46,475 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 11:35:46,623 ERROR (LoggingFailureAnalysisReporter.java:40)- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.wzsec.service.impl.LocalStorageServiceImpl required a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' that could not be found.


Action:

Consider defining a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' in your configuration.

2025-07-23 11:39:04,102 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 7496 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:39:04,114 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:39:07,243 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:39:07,245 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:39:09,240 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1859 ms. Found 111 JPA repository interfaces.
2025-07-23 11:39:10,345 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:39:10,348 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:39:10,348 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:39:10,348 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:39:10,356 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:39:10,360 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:39:10,360 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:39:10,362 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:39:10,362 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:39:11,507 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:39:11,530 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:39:11,532 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:39:12,293 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 11:39:12,342 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 11:39:12,350 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 11:39:12,350 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 11:39:12,432 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.4508882371505563529] which is part of the web application []
2025-07-23 11:39:12,845 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 11:39:12,845 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 8121 ms
2025-07-23 11:39:15,210 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:39:15,640 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:39:15,682 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:39:15,682 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:39:15,682 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:39:15,682 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:39:15,682 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:39:15,686 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:39:15,687 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:39:15,692 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:39:19,102 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:39:20,374 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:39:20,691 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:39:21,730 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:39:22,505 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:39:28,161 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:39:28,234 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:39:32,645 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 11:39:37,897 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:39:38,032 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:39:38,032 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:39:38,059 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:39:38,065 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:39:38,066 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:39:38,066 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:39:38,067 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6ec6adcc
2025-07-23 11:39:38,068 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:39:52,167 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'localStorageMapperImpl': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.wzsec.service.mapper.LocalStorageMapperImpl] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
2025-07-23 11:39:52,204 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 11:39:52,204 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:39:52,204 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 11:39:52,212 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:39:52,241 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 11:39:52,311 INFO (DirectJDKLog.java:173)- Stopping service [Tomcat]
2025-07-23 11:39:52,336 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 11:39:52,452 ERROR (SpringApplication.java:870)- Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'localStorageMapperImpl': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.wzsec.service.mapper.LocalStorageMapperImpl] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:289)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1302)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:920)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359)
	at com.wzsec.BDMSSystemRun.main(BDMSSystemRun.java:32)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.wzsec.service.mapper.LocalStorageMapperImpl] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:485)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:321)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:267)
	... 18 common frames omitted
Caused by: java.lang.NoClassDefFoundError: LocalStorageDto
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2701)
	at java.lang.Class.getDeclaredMethods(Class.java:1975)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:467)
	... 20 common frames omitted
Caused by: java.lang.ClassNotFoundException: LocalStorageDto
	at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:349)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	... 24 common frames omitted
2025-07-23 11:41:55,169 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 10020 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:41:55,178 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:41:58,094 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:41:58,097 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:42:00,115 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1896 ms. Found 111 JPA repository interfaces.
2025-07-23 11:42:01,459 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:42:01,461 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:42:01,465 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:42:01,467 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:42:01,473 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:42:01,476 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:42:01,481 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:42:01,481 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:42:01,481 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:42:03,365 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:42:03,412 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:42:03,420 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:42:04,340 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 11:42:04,397 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 11:42:04,403 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 11:42:04,404 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 11:42:04,494 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.5693867706769317935] which is part of the web application []
2025-07-23 11:42:04,857 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 11:42:04,857 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 9050 ms
2025-07-23 11:42:08,239 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:42:08,866 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:42:08,919 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:42:08,919 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:42:08,919 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:42:08,921 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:42:08,921 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:42:08,922 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:42:08,927 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:42:08,933 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:42:12,689 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:42:13,942 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:42:14,369 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:42:15,555 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:42:16,471 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:42:25,380 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:42:25,815 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:42:32,510 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 11:42:38,597 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:42:38,737 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:42:38,737 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:42:38,764 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:42:38,770 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:42:38,772 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:42:38,772 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:42:38,773 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@51e05e54
2025-07-23 11:42:38,774 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:42:49,553 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageController' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\rest\LocalStorageController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.wzsec.service.LocalStorageService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-23 11:42:49,604 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 11:42:49,604 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:42:49,607 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 11:42:49,619 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:42:49,660 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 11:42:49,763 INFO (DirectJDKLog.java:173)- Stopping service [Tomcat]
2025-07-23 11:42:49,799 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 11:42:49,952 ERROR (LoggingFailureAnalysisReporter.java:40)- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.wzsec.rest.LocalStorageController required a bean of type 'com.wzsec.service.LocalStorageService' that could not be found.


Action:

Consider defining a bean of type 'com.wzsec.service.LocalStorageService' in your configuration.

2025-07-23 11:46:20,826 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 15352 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:46:20,833 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:46:23,552 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:46:23,556 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:46:24,974 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1305 ms. Found 111 JPA repository interfaces.
2025-07-23 11:46:25,889 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:46:25,889 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:46:25,894 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:46:25,894 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:46:25,898 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:46:25,903 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:46:25,908 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:46:25,908 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:46:25,908 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:46:27,146 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:46:27,168 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:46:27,174 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:46:27,813 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 11:46:27,860 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 11:46:27,866 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 11:46:27,866 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 11:46:27,950 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.7669606570376784677] which is part of the web application []
2025-07-23 11:46:28,275 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 11:46:28,275 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 6851 ms
2025-07-23 11:46:30,570 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:46:31,018 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:46:31,066 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:46:31,067 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:46:31,068 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:46:31,068 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:46:31,070 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:46:31,070 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:46:31,073 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:46:31,078 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:46:34,343 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:46:35,401 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:46:35,694 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:46:36,419 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:46:37,077 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:46:42,549 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:46:42,617 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:46:45,978 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 11:46:50,437 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:46:50,550 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:46:50,550 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:46:50,574 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:46:50,579 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:46:50,579 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:46:50,579 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:46:50,580 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@669a46ae
2025-07-23 11:46:50,580 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:47:03,376 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4c8ad614, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6e958f2c, org.springframework.security.web.context.SecurityContextPersistenceFilter@3980c5a6, org.springframework.security.web.header.HeaderWriterFilter@537bdff0, org.springframework.security.web.authentication.logout.LogoutFilter@10c4eb9a, org.springframework.web.filter.CorsFilter@275902e1, com.wzsec.modules.security.security.TokenFilter@237c65fc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1c3e4055, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@30c6bd76, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c4dd006, org.springframework.security.web.session.SessionManagementFilter@f9c04e, org.springframework.security.web.access.ExceptionTranslationFilter@4b5d1d0c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4a824deb]
2025-07-23 11:47:03,441 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 11:47:08,652 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:47:08,654 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:47:08,654 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:47:08,654 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:47:08,654 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:47:08,655 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:47:08,655 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:47:08,655 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@50ccebbb
2025-07-23 11:47:08,873 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-23 11:47:08,962 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 11:47:09,065 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-23 11:47:09,070 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 11:47:09,071 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 11:47:09,071 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 11:47:09,071 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 11:47:09,071 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:47:09,071 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:47:09,071 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:47:09,071 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:47:09,071 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:47:09,075 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 11:47:09,075 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 11:47:09,075 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:47:09,086 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-23 11:47:10,537 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-23 11:47:10,790 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-23 11:47:11,071 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-23 11:47:11,341 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-23 11:47:11,612 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-23 11:47:11,613 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-23 11:47:11,629 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 51.752 seconds (JVM running for 55.237)
2025-07-23 11:47:12,433 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-23 11:47:34,086 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 11:47:34,086 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-23 11:47:34,089 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-23 12:25:39,200 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 8540 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 12:25:39,208 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 12:25:42,251 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 12:25:42,253 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 12:25:43,395 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1125 ms. Found 111 JPA repository interfaces.
2025-07-23 12:25:44,046 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 12:25:44,047 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 12:25:44,050 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 12:25:44,050 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 12:25:44,052 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 12:25:44,054 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 12:25:44,055 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 12:25:44,055 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 12:25:44,055 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 12:25:44,865 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 12:25:44,883 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 12:25:44,885 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 12:25:45,394 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 12:25:45,424 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 12:25:45,427 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 12:25:45,427 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 12:25:45,478 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.2170988199559231975] which is part of the web application []
2025-07-23 12:25:45,711 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 12:25:45,713 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 5906 ms
2025-07-23 12:25:47,292 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 12:25:47,644 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 12:25:47,696 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 12:25:47,696 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 12:25:47,696 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 12:25:47,697 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 12:25:47,697 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 12:25:47,698 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 12:25:47,701 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 12:25:47,704 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 12:25:50,682 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 12:25:51,666 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 12:25:51,911 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 12:25:52,514 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 12:25:53,111 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 12:25:57,799 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 12:25:57,858 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 12:26:00,791 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 12:26:04,782 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 12:26:04,892 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 12:26:04,893 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 12:26:04,916 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 12:26:04,921 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 12:26:04,921 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 12:26:04,921 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 12:26:04,922 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6e0fe9cd
2025-07-23 12:26:04,922 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 12:26:16,049 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@705813e1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@14f8e7fd, org.springframework.security.web.context.SecurityContextPersistenceFilter@4c00d817, org.springframework.security.web.header.HeaderWriterFilter@6074ed28, org.springframework.security.web.authentication.logout.LogoutFilter@4a689c76, org.springframework.web.filter.CorsFilter@73234691, com.wzsec.modules.security.security.TokenFilter@2e475f31, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@608e3638, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@43be51d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@20a73527, org.springframework.security.web.session.SessionManagementFilter@1d1e0f6e, org.springframework.security.web.access.ExceptionTranslationFilter@a74d87b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@72f57746]
2025-07-23 12:26:16,101 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 12:26:21,211 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 12:26:21,212 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 12:26:21,212 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 12:26:21,212 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 12:26:21,212 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 12:26:21,212 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 12:26:21,212 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 12:26:21,212 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2d1e49b3
2025-07-23 12:26:21,411 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-23 12:26:21,521 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 12:26:21,621 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-23 12:26:21,626 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 12:26:21,627 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 12:26:21,627 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 12:26:21,627 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 12:26:21,627 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 12:26:21,627 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 12:26:21,628 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 12:26:21,628 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 12:26:21,628 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 12:26:21,631 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 12:26:21,631 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 12:26:21,631 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 12:26:21,638 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-23 12:26:23,045 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-23 12:26:23,303 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-23 12:26:23,609 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-23 12:26:23,870 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-23 12:26:24,161 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-23 12:26:24,161 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-23 12:26:24,174 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 45.857 seconds (JVM running for 49.069)
2025-07-23 12:26:24,924 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-23 12:28:38,329 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 12:28:38,329 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-23 12:28:38,329 INFO (FrameworkServlet.java:547)- Completed initialization in 0 ms
2025-07-23 14:44:35,328 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 14:44:36,584 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-23 14:44:36,585 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 14:44:36,586 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 14:44:36,586 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 14:44:36,701 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 14:44:36,701 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 14:44:36,703 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 14:44:36,727 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 14:44:36,799 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 14:49:39,847 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 10568 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 14:49:39,853 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 14:49:42,566 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 14:49:42,568 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 14:49:44,020 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1375 ms. Found 111 JPA repository interfaces.
2025-07-23 14:49:45,422 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 14:49:45,424 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 14:49:45,428 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 14:49:45,428 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 14:49:45,435 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 14:49:45,439 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 14:49:45,441 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 14:49:45,442 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 14:49:45,443 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 14:49:46,734 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 14:49:46,750 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 14:49:46,750 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 14:49:47,370 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 14:49:47,420 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 14:49:47,423 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 14:49:47,423 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 14:49:47,470 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.3821051086920402501] which is part of the web application []
2025-07-23 14:49:47,791 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 14:49:47,792 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 7445 ms
2025-07-23 14:49:49,431 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 14:49:49,885 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 14:49:49,934 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 14:49:49,934 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 14:49:49,934 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 14:49:49,936 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 14:49:49,936 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 14:49:49,936 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 14:49:49,938 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 14:49:49,943 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 14:49:54,013 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 14:49:56,347 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 14:49:56,625 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 14:49:57,836 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 14:49:58,427 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 14:50:03,505 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 14:50:03,591 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 14:50:06,722 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 14:50:10,869 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 14:50:10,961 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 14:50:10,961 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 14:50:10,980 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 14:50:10,984 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 14:50:10,985 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 14:50:10,985 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 14:50:10,986 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@17d560b9
2025-07-23 14:50:10,986 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 14:50:22,864 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@24c39ce3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@64d05e02, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c9e7377, org.springframework.security.web.header.HeaderWriterFilter@5e1913ec, org.springframework.security.web.authentication.logout.LogoutFilter@5dfeb2e3, org.springframework.web.filter.CorsFilter@2862271a, com.wzsec.modules.security.security.TokenFilter@768f6f7e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@27c08b53, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@42f55913, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c77f8a9, org.springframework.security.web.session.SessionManagementFilter@1c3e4055, org.springframework.security.web.access.ExceptionTranslationFilter@7b9359ca, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1cd7cd44]
2025-07-23 14:50:22,938 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 14:50:27,515 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 14:50:27,515 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 14:50:27,516 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 14:50:27,516 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 14:50:27,516 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 14:50:27,516 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 14:50:27,517 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 14:50:27,517 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@232b7e94
2025-07-23 14:50:27,735 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-23 14:50:27,822 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 14:50:27,899 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-23 14:50:27,904 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 14:50:27,905 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 14:50:27,905 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 14:50:27,905 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 14:50:27,906 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 14:50:27,906 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 14:50:27,906 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 14:50:27,906 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 14:50:27,906 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 14:50:27,910 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 14:50:27,911 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 14:50:27,911 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 14:50:27,917 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-23 14:50:29,049 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-23 14:50:29,252 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-23 14:50:29,496 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-23 14:50:29,717 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-23 14:50:29,941 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-23 14:50:29,941 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-23 14:50:29,943 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 51.065 seconds (JVM running for 55.04)
2025-07-23 14:50:30,541 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-23 14:52:03,025 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 14:52:03,025 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-23 14:52:03,035 INFO (FrameworkServlet.java:547)- Completed initialization in 10 ms
2025-07-23 14:56:39,899 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 15:05:46,668 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 15092 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 15:05:46,676 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 15:05:49,619 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 15:05:49,621 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 15:05:51,514 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1730 ms. Found 111 JPA repository interfaces.
2025-07-23 15:05:52,285 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 15:05:52,286 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 15:05:52,289 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 15:05:52,289 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 15:05:52,292 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:05:52,294 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 15:05:52,296 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 15:05:52,297 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:05:52,297 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:05:53,627 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 15:05:53,657 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 15:05:53,662 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 15:05:54,511 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 15:05:54,570 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 15:05:54,576 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 15:05:54,576 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 15:05:54,649 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.5573670860414337809] which is part of the web application []
2025-07-23 15:05:54,922 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 15:05:54,922 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 7644 ms
2025-07-23 15:05:57,041 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 15:05:57,426 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 15:05:57,460 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 15:05:57,461 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 15:05:57,463 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 15:05:57,463 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 15:05:57,463 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 15:05:57,464 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 15:05:57,467 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 15:05:57,470 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 15:06:00,568 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 15:06:01,650 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 15:06:01,897 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 15:06:02,518 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 15:06:03,166 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 15:06:07,863 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 15:06:07,918 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 15:06:10,662 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 15:06:14,518 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 15:06:14,617 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 15:06:14,618 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 15:06:14,638 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 15:06:14,644 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 15:06:14,644 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 15:06:14,644 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 15:06:14,645 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@1631da37
2025-07-23 15:06:14,645 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 15:06:25,319 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7d425bf1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@ca44cba, org.springframework.security.web.context.SecurityContextPersistenceFilter@76f3f810, org.springframework.security.web.header.HeaderWriterFilter@549f0e14, org.springframework.security.web.authentication.logout.LogoutFilter@1d1e0f6e, org.springframework.web.filter.CorsFilter@413eaf5d, com.wzsec.modules.security.security.TokenFilter@6b4b8163, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@74df38c0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@13ef9007, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5c1b20e9, org.springframework.security.web.session.SessionManagementFilter@726f2d26, org.springframework.security.web.access.ExceptionTranslationFilter@486e6b30, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3a63653b]
2025-07-23 15:06:25,363 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 15:06:29,979 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 15:06:29,980 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 15:06:29,980 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 15:06:29,980 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 15:06:29,980 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 15:06:29,980 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 15:06:29,980 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 15:06:29,980 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4cb98b82
2025-07-23 15:06:30,195 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-23 15:06:30,276 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 15:06:30,364 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-23 15:06:30,368 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 15:06:30,370 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 15:06:30,370 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 15:06:30,370 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 15:06:30,370 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 15:06:30,370 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 15:06:30,370 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:06:30,370 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 15:06:30,370 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 15:06:30,374 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 15:06:30,374 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 15:06:30,374 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 15:06:30,381 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-23 15:06:31,700 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-23 15:06:31,968 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-23 15:06:32,247 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-23 15:06:32,499 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-23 15:06:32,763 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-23 15:06:32,763 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-23 15:06:32,772 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 46.958 seconds (JVM running for 49.966)
2025-07-23 15:06:33,411 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-23 15:07:18,864 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 15:07:18,864 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-23 15:07:18,866 INFO (FrameworkServlet.java:547)- Completed initialization in 2 ms
2025-07-23 16:11:01,289 ERROR (JobRunShell.java:211)- Job DEFAULT.131 threw an unhandled Exception: 
java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$76b24f50.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-23 16:11:01,298 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.131 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.NullPointerException: null
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:254)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$76b24f50.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-23 16:17:35,468 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 16:17:36,305 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-23 16:17:36,306 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 16:17:36,307 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 16:17:36,307 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 16:17:36,389 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 16:17:36,390 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 16:17:36,390 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 16:17:36,405 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 16:17:36,440 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 17:48:00,886 INFO (StartupInfoLogger.java:55)- Starting BDMSSystemRun using Java 1.8.0_211 on JOY with PID 20548 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_system\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 17:48:00,899 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 17:48:05,102 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 17:48:05,104 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 17:48:06,535 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1293 ms. Found 111 JPA repository interfaces.
2025-07-23 17:48:07,343 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 17:48:07,343 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 17:48:07,354 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 17:48:07,354 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 17:48:07,358 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 17:48:07,358 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 17:48:07,363 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 17:48:07,363 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 17:48:07,363 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 17:48:08,343 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 17:48:08,368 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 17:48:08,372 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 17:48:08,995 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8090 (http)
2025-07-23 17:48:09,028 INFO (DirectJDKLog.java:173)- Initializing ProtocolHandler ["http-nio-8090"]
2025-07-23 17:48:09,033 INFO (DirectJDKLog.java:173)- Starting service [Tomcat]
2025-07-23 17:48:09,033 INFO (DirectJDKLog.java:173)- Starting Servlet engine: [Apache Tomcat/9.0.99]
2025-07-23 17:48:09,089 WARN (DirectJDKLog.java:173)- Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8090.7943460542129400836] which is part of the web application []
2025-07-23 17:48:09,352 INFO (DirectJDKLog.java:173)- Initializing Spring embedded WebApplicationContext
2025-07-23 17:48:09,354 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 7790 ms
2025-07-23 17:48:10,977 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 17:48:11,351 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 17:48:11,376 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 17:48:11,376 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 17:48:11,376 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 17:48:11,376 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 17:48:11,376 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 17:48:11,376 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 17:48:11,390 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 17:48:11,390 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 17:48:14,535 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 17:48:15,497 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 17:48:15,714 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 17:48:16,293 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 17:48:16,971 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 17:48:21,560 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 17:48:21,613 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 17:48:24,381 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_system.properties
2025-07-23 17:48:28,903 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 17:48:28,994 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 17:48:28,996 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 17:48:29,015 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 17:48:29,018 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 17:48:29,020 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 17:48:29,020 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 17:48:29,021 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@2a3920fe
2025-07-23 17:48:29,021 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 17:48:39,399 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2a44a2e0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@98d2074, org.springframework.security.web.context.SecurityContextPersistenceFilter@38daeeff, org.springframework.security.web.header.HeaderWriterFilter@7ef622f7, org.springframework.security.web.authentication.logout.LogoutFilter@692751a9, org.springframework.web.filter.CorsFilter@174e1b99, com.wzsec.modules.security.security.TokenFilter@dfba755, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4ca761b8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@79a52138, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@36a5aa20, org.springframework.security.web.session.SessionManagementFilter@5f41e81d, org.springframework.security.web.access.ExceptionTranslationFilter@4f4ba1ea, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@e48dfaf]
2025-07-23 17:48:39,445 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 17:48:44,088 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 17:48:44,090 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 17:48:44,090 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 17:48:44,090 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 17:48:44,090 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 17:48:44,090 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 17:48:44,090 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 17:48:44,090 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7406f3ad
2025-07-23 17:48:44,288 INFO (DirectJDKLog.java:173)- Starting ProtocolHandler ["http-nio-8090"]
2025-07-23 17:48:44,441 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 17:48:44,532 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8090 (http) with context path ''
2025-07-23 17:48:44,537 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 17:48:44,537 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 17:48:44,537 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 17:48:44,537 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 17:48:44,537 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 17:48:44,539 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 17:48:44,539 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 17:48:44,539 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 17:48:44,539 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 17:48:44,543 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 17:48:44,543 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 17:48:44,543 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 17:48:44,551 INFO (ScanInstantiation.java:32)- 任务初始化---start---
2025-07-23 17:48:45,856 INFO (TaskScanConfig.java:36)- 敏感数据发现任务初始化数：5
2025-07-23 17:48:46,104 INFO (DBTaskScanConfig.java:37)- 数据库脱敏任务初始化数：0
2025-07-23 17:48:46,384 INFO (FileTaskScanConfig.java:37)- 文件脱敏任务初始化数：1
2025-07-23 17:48:46,639 INFO (MaskAuditTaskScanConfig.java:37)- 脱敏日志审计任务初始化数：2
2025-07-23 17:48:46,922 INFO (DbBatchTaskScanConfig.java:43)- 批量脱敏任务初始化数：5
2025-07-23 17:48:46,922 INFO (ScanInstantiation.java:39)- 任务初始化---end---
2025-07-23 17:48:46,929 INFO (StartupInfoLogger.java:61)- Started BDMSSystemRun in 46.942 seconds (JVM running for 50.782)
2025-07-23 17:48:47,617 INFO (BDMSSystemRun.java:33)- System service started successfully
2025-07-23 17:50:20,308 INFO (DirectJDKLog.java:173)- Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 17:50:20,309 INFO (FrameworkServlet.java:525)- Initializing Servlet 'dispatcherServlet'
2025-07-23 17:50:20,312 INFO (FrameworkServlet.java:547)- Completed initialization in 3 ms
2025-07-23 17:54:21,879 ERROR (JobRunShell.java:211)- Job DEFAULT.258 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$ad764050.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-23 17:54:21,881 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.258 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$ad764050.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-23 17:54:21,884 ERROR (JobRunShell.java:211)- Job DEFAULT.257 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$ad764050.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-23 17:54:21,884 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.257 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$ad764050.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
2025-07-23 17:54:21,896 ERROR (JobRunShell.java:211)- Job DEFAULT.256 threw an unhandled Exception: 
com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$ad764050.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
2025-07-23 17:54:21,896 ERROR (QuartzScheduler.java:2407)- Job (DEFAULT.256 threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: com.wzsec.exception.BadRequestException: 服务引擎不可用!
	at com.wzsec.utils.HttpUtil.sendToEngine(HttpUtil.java:70)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl.executionFromEngine(TaskServiceImpl.java:268)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$FastClassBySpringCGLIB$$4d154ccc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wzsec.modules.discover.service.impl.TaskServiceImpl$$EnhancerBySpringCGLIB$$ad764050.executionFromEngine(<generated>)
	at com.wzsec.modules.quartz.config.TaskConfigJob.execute(TaskConfigJob.java:40)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	... 1 common frames omitted
