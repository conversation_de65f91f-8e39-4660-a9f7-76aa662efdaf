package com.wzsec.dotask.mask.service.excute.audit;

import com.wzsec.dotask.sdd.service.excute.common.RuleManager;
import com.wzsec.modules.mask.domain.MaskAuditResultV1;
import com.wzsec.modules.mask.domain.MaskAuditResultdetailV1;
import com.wzsec.modules.mask.service.MaskAuditResultV1Service;
import com.wzsec.modules.mask.service.MaskAuditResultdetailV1Service;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1Dto;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.utils.AES;
import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import com.wzsec.utils.database.DatabaseUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Title: MaskDatabaseAudit
 * Decription:
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@Slf4j
public class MaskDatabaseAudit {

    private final DatasourceService datasourceService;
    private final MaskAuditResultdetailV1Service maskAuditResultdetailV1Service;
    private final MaskAuditResultV1Service maskAuditResultV1Service;

    public MaskDatabaseAudit(DatasourceService datasourceService, MaskAuditResultdetailV1Service maskAuditResultdetailV1Service, MaskAuditResultV1Service maskAuditResultV1Service) {
        this.datasourceService = datasourceService;
        this.maskAuditResultdetailV1Service = maskAuditResultdetailV1Service;
        this.maskAuditResultV1Service = maskAuditResultV1Service;
    }

    /**
     * 审计数据库表
     * @param maskAuditTaskDto
     */
    public boolean maskAuditForDB(MaskAuditTaskV1Dto maskAuditTaskDto, List<RuleDto> ruleList, String checkTime, String submituser,Map<String, String> lineAlgorithmInfo) {
        log.info("开始执行库表脱敏审计任务");
        boolean resultStatus = false;
        Map<String,Integer> riskCountMap = new HashMap<>();
        Map<String,String> resultMap = new HashMap<>();
        Map<String,String> strategyMap = new HashMap<>();
        String maskstrategy = maskAuditTaskDto.getMaskstrategy();
        String[] strategyArr = maskstrategy.split(",");
        for (String strategy : strategyArr) {
            String[] fieldAlg = strategy.split("\\|");
            if (fieldAlg.length>1){
                strategyMap.put(fieldAlg[0],fieldAlg[1]);
            }else {
                strategyMap.put(fieldAlg[0],"");
            }
        }

        Connection conn = null;
        PreparedStatement statement = null;
        PreparedStatement statementCount = null;
        ResultSet resultSet = null;
        ResultSet resultSetCount = null;
        // 根据数据源id查询数据源
        DatasourceDto datasource = datasourceService.findById(maskAuditTaskDto.getOutsourceid().longValue());
        String type = datasource.getType();
        String srcurl = datasource.getSrcurl();
        String username = datasource.getUsername();
        String password = datasource.getPassword();
        String dbname = datasource.getDbname();
        String tablename = maskAuditTaskDto.getTablename();
        //AES解密
        if (StringUtils.isNotEmpty(password)) {
            try {
                String decrypt = AES.decrypt(datasource.getPassword(), Const.AES_SECRET_KEY);
                password = decrypt;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            // 获取连接
            conn = (Connection) DatabaseUtil.getConn(type, srcurl, username, password, dbname);
            if (conn != null){
                String sql = "select * from "+ tablename +" limit 10";
                String countSql = "select count(*) from "+ tablename;
                statement = conn.prepareStatement(sql);
                resultSet = statement.executeQuery();
                statementCount = conn.prepareStatement(countSql);
                resultSetCount = statementCount.executeQuery();
                String linenumber = "";
                while (resultSetCount.next()){
                    Object object = resultSetCount.getObject(1);
                    linenumber = object.toString();
                    log.info("数据总条数：" + linenumber);
                }
                while (resultSet.next()){
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    for (int i = 0; i < metaData.getColumnCount(); i++) {
                        String columnName = metaData.getColumnName(i+1);
                        Object object = resultSet.getObject(i+1);
                        for (RuleDto ruleDto : ruleList) {
                            boolean result = RuleManager.checkDataByRuleDto(object.toString(), ruleDto,null);
                            if (result){
                                String applytypecname = ruleDto.getApplytypecname();
                                if (strategyMap.containsKey(columnName)){
                                    // 敏感  field-->alg-是否符合策略-敏感级别-样例
                                    //if (StringUtils.isNotEmpty(strategyMap.get(columnName))){
                                    if (lineAlgorithmInfo != null && strategyMap.get(columnName).equals(lineAlgorithmInfo.get(columnName))){
                                        // 符合策略-敏感 中
                                        resultMap.put(columnName,
                                                strategyMap.get(columnName)+Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_STRATEGY_TRUE+Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_RESULT_MIDDLE+Const.AUDIT_SPLIT_JOIN +
                                                        applytypecname+":"+object.toString());
                                    }else {
                                        // 不符合策略-敏感 高
                                        resultMap.put(columnName,
                                                "无" + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_STRATEGY_FALSE + Const.AUDIT_SPLIT_JOIN +
                                                        Const.MASKAUDIT_RESULT_HIGH + Const.AUDIT_SPLIT_JOIN +
                                                        applytypecname+":"+object.toString());
                                    }
                                }
                            }else {
                                // String applytypecname = Const.LEVEL_NO;
                                if (strategyMap.containsKey(columnName)){
                                    //if (StringUtils.isNotEmpty(strategyMap.get(columnName))){
                                    if (lineAlgorithmInfo != null && strategyMap.get(columnName).equals(lineAlgorithmInfo.get(columnName))){
                                        // 符合策略-不敏感 无
                                        if (!resultMap.containsKey(columnName)){
                                            resultMap.put(columnName,
                                                    strategyMap.get(columnName) + Const.AUDIT_SPLIT_JOIN +
                                                            Const.MASKAUDIT_STRATEGY_TRUE + Const.AUDIT_SPLIT_JOIN +
                                                            Const.MASKAUDIT_RESULT_NO + Const.AUDIT_SPLIT_JOIN +
                                                            object.toString());
                                        }
                                    }else {
                                        // 不符合策略-不敏感 低
                                        if (!resultMap.containsKey(columnName)){
                                            resultMap.put(columnName,
                                                    "无" + Const.AUDIT_SPLIT_JOIN +
                                                            Const.MASKAUDIT_STRATEGY_FALSE + Const.AUDIT_SPLIT_JOIN +
                                                            Const.MASKAUDIT_RESULT_LOW + Const.AUDIT_SPLIT_JOIN +
                                                            object.toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // 统计详情
                for (String field : resultMap.keySet()) {
                    String value = resultMap.get(field);
                    String[] values = value.split(Const.AUDIT_SPLIT_JOIN);
                    MaskAuditResultdetailV1 detail = new MaskAuditResultdetailV1();
                    detail.setTaskname(maskAuditTaskDto.getTaskname());
                    detail.setTablename(maskAuditTaskDto.getTablename());
                    detail.setLinenumber(Integer.parseInt(linenumber));
                    detail.setField(field);
                    detail.setAlgorithm(values[0]);
                    detail.setIssafesame(values[1]);
                    detail.setRisk(values[2]);
                    detail.setExample(values[3]);
                    detail.setChecktime(checkTime);
                    detail.setSubmitter(submituser);
                    if (riskCountMap.containsKey(detail.getRisk())){
                        riskCountMap.put(detail.getRisk(),riskCountMap.get(detail.getRisk())+1);
                    }else {
                        riskCountMap.put(detail.getRisk(),1);
                    }
                    maskAuditResultdetailV1Service.create(detail);
                }
                log.info("详情结果统计完成");

                // 统计概要
                for (String key : riskCountMap.keySet()) {
                    MaskAuditResultV1 result = new MaskAuditResultV1();
                    result.setChecktime(checkTime);
                    result.setChecktype(maskAuditTaskDto.getChecktype());
                    result.setOutpath(datasource.getSrcurl());
                    result.setCount(String.valueOf(riskCountMap.get(key)));
                    result.setRisk(key);
                    result.setSubmitter(submituser);
                    result.setTaskname(maskAuditTaskDto.getTaskname());
                    result.setSystemid(maskAuditTaskDto.getSystemid());
                    maskAuditResultV1Service.create(result);
                }
                log.info("概要结果统计完成");
                resultStatus = true;
            }else {
                log.info("获取数据源连接失败");
                resultStatus = false;
            }
        }catch (Exception e) {
            log.info("库表检测出现异常：" + e.getMessage());
            e.printStackTrace();
            resultStatus = false;
        }finally {
            // 清理数据
            DatabaseUtil.closeCon(resultSet,statement,conn);
            DatabaseUtil.closeCon(resultSetCount,statementCount,conn);
            riskCountMap = null;
            resultMap = null;
            strategyMap = null;
        }
        return resultStatus;
    }
}
