package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskVideotaskresult;
import com.wzsec.modules.mask.service.dto.MaskVideotaskresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskVideotaskresultMapperImpl implements MaskVideotaskresultMapper {

    @Override
    public MaskVideotaskresultDto toDto(MaskVideotaskresult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskVideotaskresultDto maskVideotaskresultDto = new MaskVideotaskresultDto();

        maskVideotaskresultDto.setAfterpicamount( entity.getAfterpicamount() );
        maskVideotaskresultDto.setBeforepicamount( entity.getBeforepicamount() );
        maskVideotaskresultDto.setId( entity.getId() );
        maskVideotaskresultDto.setInputdirectory( entity.getInputdirectory() );
        maskVideotaskresultDto.setInputfileformat( entity.getInputfileformat() );
        maskVideotaskresultDto.setJobendtime( entity.getJobendtime() );
        maskVideotaskresultDto.setJobtotaltime( entity.getJobtotaltime() );
        maskVideotaskresultDto.setMaskobject( entity.getMaskobject() );
        maskVideotaskresultDto.setOutputdirectory( entity.getOutputdirectory() );
        maskVideotaskresultDto.setOutputfileformat( entity.getOutputfileformat() );
        maskVideotaskresultDto.setRemark( entity.getRemark() );
        maskVideotaskresultDto.setSparefield1( entity.getSparefield1() );
        maskVideotaskresultDto.setSparefield2( entity.getSparefield2() );
        maskVideotaskresultDto.setSparefield3( entity.getSparefield3() );
        maskVideotaskresultDto.setSparefield4( entity.getSparefield4() );
        maskVideotaskresultDto.setSparefield5( entity.getSparefield5() );
        maskVideotaskresultDto.setTaskname( entity.getTaskname() );
        maskVideotaskresultDto.setTaskstatus( entity.getTaskstatus() );

        return maskVideotaskresultDto;
    }

    @Override
    public List<MaskVideotaskresultDto> toDto(List<MaskVideotaskresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskVideotaskresultDto> list = new ArrayList<MaskVideotaskresultDto>( entityList.size() );
        for ( MaskVideotaskresult maskVideotaskresult : entityList ) {
            list.add( toDto( maskVideotaskresult ) );
        }

        return list;
    }

    @Override
    public MaskVideotaskresult toEntity(MaskVideotaskresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskVideotaskresult maskVideotaskresult = new MaskVideotaskresult();

        maskVideotaskresult.setAfterpicamount( dto.getAfterpicamount() );
        maskVideotaskresult.setBeforepicamount( dto.getBeforepicamount() );
        maskVideotaskresult.setId( dto.getId() );
        maskVideotaskresult.setInputdirectory( dto.getInputdirectory() );
        maskVideotaskresult.setInputfileformat( dto.getInputfileformat() );
        maskVideotaskresult.setJobendtime( dto.getJobendtime() );
        maskVideotaskresult.setJobtotaltime( dto.getJobtotaltime() );
        maskVideotaskresult.setMaskobject( dto.getMaskobject() );
        maskVideotaskresult.setOutputdirectory( dto.getOutputdirectory() );
        maskVideotaskresult.setOutputfileformat( dto.getOutputfileformat() );
        maskVideotaskresult.setRemark( dto.getRemark() );
        maskVideotaskresult.setSparefield1( dto.getSparefield1() );
        maskVideotaskresult.setSparefield2( dto.getSparefield2() );
        maskVideotaskresult.setSparefield3( dto.getSparefield3() );
        maskVideotaskresult.setSparefield4( dto.getSparefield4() );
        maskVideotaskresult.setSparefield5( dto.getSparefield5() );
        maskVideotaskresult.setTaskname( dto.getTaskname() );
        maskVideotaskresult.setTaskstatus( dto.getTaskstatus() );

        return maskVideotaskresult;
    }

    @Override
    public List<MaskVideotaskresult> toEntity(List<MaskVideotaskresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskVideotaskresult> list = new ArrayList<MaskVideotaskresult>( dtoList.size() );
        for ( MaskVideotaskresultDto maskVideotaskresultDto : dtoList ) {
            list.add( toEntity( maskVideotaskresultDto ) );
        }

        return list;
    }
}
