package com.wzsec.modules.mask.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
//import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2024-10-21
*/
@Entity
@Data
@Table(name="sdd_maskstrategy_identifymaskstrategies")
public class MaskstrategyIdentifymaskstrategies implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 策略名称 */
    @Column(name = "strategyname")
    private String strategyname;

    /** 策略描述 */
    @Column(name = "strategydesc")
    private String strategydesc;

    /** 状态(0：启用，1：禁用) */
    @Column(name = "status")
    private String status;

    /** 创建用户 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    private Timestamp createtime;

    /** 更新用户 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    private Timestamp updatetime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 是否审批 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 审批状态 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 审批人 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 审批时间 */
    @Column(name = "sparefield4")
    private String sparefield4;

    @Column(name = "enabled")
    private Boolean enabled;

    public void copy(MaskstrategyIdentifymaskstrategies source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}