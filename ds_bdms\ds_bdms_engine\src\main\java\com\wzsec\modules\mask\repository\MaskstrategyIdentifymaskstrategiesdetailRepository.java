package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskstrategyIdentifymaskstrategiesdetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
* <AUTHOR>
* @date 2024-10-21
*/
public interface MaskstrategyIdentifymaskstrategiesdetailRepository extends JpaRepository<MaskstrategyIdentifymaskstrategiesdetail, Integer>, JpaSpecificationExecutor<MaskstrategyIdentifymaskstrategiesdetail> {
    List<MaskstrategyIdentifymaskstrategiesdetail> findByStrategyid(Integer id);

}