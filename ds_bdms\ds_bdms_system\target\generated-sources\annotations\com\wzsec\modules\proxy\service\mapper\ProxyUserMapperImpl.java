package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.ProxyUser;
import com.wzsec.modules.proxy.service.dto.ProxyUserDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:01+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ProxyUserMapperImpl implements ProxyUserMapper {

    @Override
    public ProxyUserDto toDto(ProxyUser entity) {
        if ( entity == null ) {
            return null;
        }

        ProxyUserDto proxyUserDto = new ProxyUserDto();

        proxyUserDto.setCname( entity.getCname() );
        proxyUserDto.setCreatetime( entity.getCreatetime() );
        proxyUserDto.setCreateuser( entity.getCreateuser() );
        proxyUserDto.setDbname( entity.getDbname() );
        proxyUserDto.setEndtime( entity.getEndtime() );
        proxyUserDto.setId( entity.getId() );
        proxyUserDto.setMemo( entity.getMemo() );
        proxyUserDto.setPassword( entity.getPassword() );
        proxyUserDto.setRole( entity.getRole() );
        proxyUserDto.setSparefield1( entity.getSparefield1() );
        proxyUserDto.setSparefield2( entity.getSparefield2() );
        proxyUserDto.setSparefield3( entity.getSparefield3() );
        proxyUserDto.setSparefield4( entity.getSparefield4() );
        proxyUserDto.setSparefield5( entity.getSparefield5() );
        proxyUserDto.setSrcid( entity.getSrcid() );
        proxyUserDto.setStarttime( entity.getStarttime() );
        proxyUserDto.setState( entity.getState() );
        proxyUserDto.setUpdatetime( entity.getUpdatetime() );
        proxyUserDto.setUpdateuser( entity.getUpdateuser() );
        proxyUserDto.setUsername( entity.getUsername() );

        return proxyUserDto;
    }

    @Override
    public List<ProxyUserDto> toDto(List<ProxyUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ProxyUserDto> list = new ArrayList<ProxyUserDto>( entityList.size() );
        for ( ProxyUser proxyUser : entityList ) {
            list.add( toDto( proxyUser ) );
        }

        return list;
    }

    @Override
    public ProxyUser toEntity(ProxyUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        ProxyUser proxyUser = new ProxyUser();

        proxyUser.setCname( dto.getCname() );
        proxyUser.setCreatetime( dto.getCreatetime() );
        proxyUser.setCreateuser( dto.getCreateuser() );
        proxyUser.setDbname( dto.getDbname() );
        proxyUser.setEndtime( dto.getEndtime() );
        proxyUser.setId( dto.getId() );
        proxyUser.setMemo( dto.getMemo() );
        proxyUser.setPassword( dto.getPassword() );
        proxyUser.setRole( dto.getRole() );
        proxyUser.setSparefield1( dto.getSparefield1() );
        proxyUser.setSparefield2( dto.getSparefield2() );
        proxyUser.setSparefield3( dto.getSparefield3() );
        proxyUser.setSparefield4( dto.getSparefield4() );
        proxyUser.setSparefield5( dto.getSparefield5() );
        proxyUser.setSrcid( dto.getSrcid() );
        proxyUser.setStarttime( dto.getStarttime() );
        proxyUser.setState( dto.getState() );
        proxyUser.setUpdatetime( dto.getUpdatetime() );
        proxyUser.setUpdateuser( dto.getUpdateuser() );
        proxyUser.setUsername( dto.getUsername() );

        return proxyUser;
    }

    @Override
    public List<ProxyUser> toEntity(List<ProxyUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ProxyUser> list = new ArrayList<ProxyUser>( dtoList.size() );
        for ( ProxyUserDto proxyUserDto : dtoList ) {
            list.add( toEntity( proxyUserDto ) );
        }

        return list;
    }
}
