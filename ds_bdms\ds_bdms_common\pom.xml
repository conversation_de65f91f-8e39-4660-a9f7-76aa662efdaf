<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ds_bdms</artifactId>
        <groupId>com.wzsec</groupId>
        <version>3.2.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <properties>
        <hutool.version>5.8.0</hutool.version>
    </properties>

    <artifactId>ds_bdms_common</artifactId>
    <name>ds_bdms_common</name>

    <dependencies>
        <!--工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <!--poi-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi-version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi-version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>${poi-version}</version>
        </dependency>

        <!--pdfbox-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.16</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
            <version>2.0.16</version>
        </dependency>

        <!--SCP-->
        <dependency>
            <groupId>ch.ethz.ganymed</groupId>
            <artifactId>ganymed-ssh2</artifactId>
            <version>build210</version>
        </dependency>

        <!-- gbase jdbc -->
        <dependency>
            <groupId>gbase</groupId>
            <artifactId>gbase-connector-java</artifactId>
            <version>8.3.81.53-build55.2.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/gbase-connector-java-8.3.81.53-build55.2.1-bin.jar</systemPath>
        </dependency>

        <!-- db2 jdbc -->
        <dependency>
            <groupId>DB2</groupId>
            <artifactId>db2jcc4</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/db2jcc4.jar</systemPath>
        </dependency>

        <!-- dm jdbc -->
        <dependency>
            <groupId>DM</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/DmJdbcDriver18.jar</systemPath>
        </dependency>

        <dependency>
            <!--注意：只有这个版本的hibernate兼容达梦数据库 -->
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>5.3.18.Final</version>
        </dependency>

        <!--<dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.1.193</version>
        </dependency>-->

        <dependency>
            <groupId>DM</groupId>
            <artifactId>DmDialect-for-hibernate</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/DmDialect-for-hibernate5.3.jar</systemPath>
        </dependency>

        <!-- sqlserver jdbc -->
        <dependency>
            <groupId>sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>7.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/mssql-jdbc-7.0.0.jar</systemPath>
        </dependency>

        <!-- postgre jdbc -->
        <dependency>
            <groupId>postgre</groupId>
            <artifactId>PROGRESS_DATADIRECT_JDBC_DRIVER_PIVOTAL_GREENPLUM</artifactId>
            <version>5.1.4.000275</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/PROGRESS_DATADIRECT_JDBC_DRIVER_PIVOTAL_GREENPLUM_5.1.4.000275.jar</systemPath>
        </dependency>

        <!-- kingbase8 jdbc -->
        <dependency>
            <groupId>kingbase8</groupId>
            <artifactId>kingbase8-8.6.0</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/kingbase8-8.6.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>kingbase8</groupId>
            <artifactId>KingBaseDialect-for-hibernate</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/hibernate-4.dialect.jar</systemPath>
        </dependency>

        <!-- oscar jdbc -->
        <dependency>
            <groupId>com.stdb</groupId>
            <artifactId>stoscarJDBC</artifactId>
            <version>16</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/oscarJDBC16.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.highgo.jdbc</groupId>
            <artifactId>highgo</artifactId>
            <version>6.2.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/HgdbJdbc-6.2.3.jar</systemPath>
        </dependency>

        <!--spring-webmvc-->
        <dependency>
            <groupId>web</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>5.3.41</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/lib/webmvc-5.3.41-2025.jar</systemPath>
        </dependency>

        <!-- informix jdbc -->
        <dependency>
            <groupId>com.ibm.informix</groupId>
            <artifactId>jdbc</artifactId>
            <version>4.50.9</version>
        </dependency>

        <!--elasticsearch-->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.9.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.9.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>7.9.1</version>
        </dependency>
    </dependencies>
</project>
