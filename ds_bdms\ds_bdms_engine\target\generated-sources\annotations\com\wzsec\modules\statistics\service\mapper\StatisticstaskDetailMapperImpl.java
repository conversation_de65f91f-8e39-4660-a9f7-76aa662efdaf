package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.StatisticstaskDetail;
import com.wzsec.modules.statistics.service.dto.StatisticstaskDetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class StatisticstaskDetailMapperImpl implements StatisticstaskDetailMapper {

    @Override
    public StatisticstaskDetailDto toDto(StatisticstaskDetail entity) {
        if ( entity == null ) {
            return null;
        }

        StatisticstaskDetailDto statisticstaskDetailDto = new StatisticstaskDetailDto();

        statisticstaskDetailDto.setCreatetime( entity.getCreatetime() );
        statisticstaskDetailDto.setCreateuser( entity.getCreateuser() );
        statisticstaskDetailDto.setDatacount( entity.getDatacount() );
        statisticstaskDetailDto.setFieldcount( entity.getFieldcount() );
        statisticstaskDetailDto.setId( entity.getId() );
        statisticstaskDetailDto.setSparefield1( entity.getSparefield1() );
        statisticstaskDetailDto.setSparefield2( entity.getSparefield2() );
        statisticstaskDetailDto.setSparefield3( entity.getSparefield3() );
        statisticstaskDetailDto.setSrcname( entity.getSrcname() );
        statisticstaskDetailDto.setTablename( entity.getTablename() );
        statisticstaskDetailDto.setTaskname( entity.getTaskname() );

        return statisticstaskDetailDto;
    }

    @Override
    public List<StatisticstaskDetailDto> toDto(List<StatisticstaskDetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<StatisticstaskDetailDto> list = new ArrayList<StatisticstaskDetailDto>( entityList.size() );
        for ( StatisticstaskDetail statisticstaskDetail : entityList ) {
            list.add( toDto( statisticstaskDetail ) );
        }

        return list;
    }

    @Override
    public StatisticstaskDetail toEntity(StatisticstaskDetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        StatisticstaskDetail statisticstaskDetail = new StatisticstaskDetail();

        statisticstaskDetail.setCreatetime( dto.getCreatetime() );
        statisticstaskDetail.setCreateuser( dto.getCreateuser() );
        statisticstaskDetail.setDatacount( dto.getDatacount() );
        statisticstaskDetail.setFieldcount( dto.getFieldcount() );
        statisticstaskDetail.setId( dto.getId() );
        statisticstaskDetail.setSparefield1( dto.getSparefield1() );
        statisticstaskDetail.setSparefield2( dto.getSparefield2() );
        statisticstaskDetail.setSparefield3( dto.getSparefield3() );
        statisticstaskDetail.setSrcname( dto.getSrcname() );
        statisticstaskDetail.setTablename( dto.getTablename() );
        statisticstaskDetail.setTaskname( dto.getTaskname() );

        return statisticstaskDetail;
    }

    @Override
    public List<StatisticstaskDetail> toEntity(List<StatisticstaskDetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<StatisticstaskDetail> list = new ArrayList<StatisticstaskDetail>( dtoList.size() );
        for ( StatisticstaskDetailDto statisticstaskDetailDto : dtoList ) {
            list.add( toEntity( statisticstaskDetailDto ) );
        }

        return list;
    }
}
