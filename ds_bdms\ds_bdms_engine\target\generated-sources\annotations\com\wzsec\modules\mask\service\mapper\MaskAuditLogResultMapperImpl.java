package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditLogResult;
import com.wzsec.modules.mask.service.dto.MaskAuditLogResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MaskAuditLogResultMapperImpl implements MaskAuditLogResultMapper {

    @Override
    public MaskAuditLogResultDto toDto(MaskAuditLogResult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditLogResultDto maskAuditLogResultDto = new MaskAuditLogResultDto();

        maskAuditLogResultDto.setApicode( entity.getApicode() );
        maskAuditLogResultDto.setApimethod( entity.getApimethod() );
        maskAuditLogResultDto.setApiname( entity.getApiname() );
        maskAuditLogResultDto.setApitype( entity.getApitype() );
        maskAuditLogResultDto.setAppid( entity.getAppid() );
        maskAuditLogResultDto.setAppname( entity.getAppname() );
        maskAuditLogResultDto.setCheckcount( entity.getCheckcount() );
        maskAuditLogResultDto.setChecktime( entity.getChecktime() );
        maskAuditLogResultDto.setCustSimplename( entity.getCustSimplename() );
        maskAuditLogResultDto.setCustname( entity.getCustname() );
        maskAuditLogResultDto.setId( entity.getId() );
        maskAuditLogResultDto.setLogsign( entity.getLogsign() );
        maskAuditLogResultDto.setRisk( entity.getRisk() );
        maskAuditLogResultDto.setSparefield1( entity.getSparefield1() );
        maskAuditLogResultDto.setSparefield2( entity.getSparefield2() );
        maskAuditLogResultDto.setSparefield3( entity.getSparefield3() );
        maskAuditLogResultDto.setSparefield4( entity.getSparefield4() );
        maskAuditLogResultDto.setTaskname( entity.getTaskname() );
        maskAuditLogResultDto.setUrl( entity.getUrl() );
        maskAuditLogResultDto.setUserid( entity.getUserid() );

        return maskAuditLogResultDto;
    }

    @Override
    public List<MaskAuditLogResultDto> toDto(List<MaskAuditLogResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditLogResultDto> list = new ArrayList<MaskAuditLogResultDto>( entityList.size() );
        for ( MaskAuditLogResult maskAuditLogResult : entityList ) {
            list.add( toDto( maskAuditLogResult ) );
        }

        return list;
    }

    @Override
    public MaskAuditLogResult toEntity(MaskAuditLogResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditLogResult maskAuditLogResult = new MaskAuditLogResult();

        maskAuditLogResult.setApicode( dto.getApicode() );
        maskAuditLogResult.setApimethod( dto.getApimethod() );
        maskAuditLogResult.setApiname( dto.getApiname() );
        maskAuditLogResult.setApitype( dto.getApitype() );
        maskAuditLogResult.setAppid( dto.getAppid() );
        maskAuditLogResult.setAppname( dto.getAppname() );
        maskAuditLogResult.setCheckcount( dto.getCheckcount() );
        maskAuditLogResult.setChecktime( dto.getChecktime() );
        maskAuditLogResult.setCustSimplename( dto.getCustSimplename() );
        maskAuditLogResult.setCustname( dto.getCustname() );
        maskAuditLogResult.setId( dto.getId() );
        maskAuditLogResult.setLogsign( dto.getLogsign() );
        maskAuditLogResult.setRisk( dto.getRisk() );
        maskAuditLogResult.setSparefield1( dto.getSparefield1() );
        maskAuditLogResult.setSparefield2( dto.getSparefield2() );
        maskAuditLogResult.setSparefield3( dto.getSparefield3() );
        maskAuditLogResult.setSparefield4( dto.getSparefield4() );
        maskAuditLogResult.setTaskname( dto.getTaskname() );
        maskAuditLogResult.setUrl( dto.getUrl() );
        maskAuditLogResult.setUserid( dto.getUserid() );

        return maskAuditLogResult;
    }

    @Override
    public List<MaskAuditLogResult> toEntity(List<MaskAuditLogResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditLogResult> list = new ArrayList<MaskAuditLogResult>( dtoList.size() );
        for ( MaskAuditLogResultDto maskAuditLogResultDto : dtoList ) {
            list.add( toEntity( maskAuditLogResultDto ) );
        }

        return list;
    }
}
