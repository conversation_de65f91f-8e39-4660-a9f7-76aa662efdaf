package com.wzsec.modules.metadata.service.mapper;

import com.wzsec.modules.metadata.domain.MetaField;
import com.wzsec.modules.metadata.service.dto.MetaFieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:00+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class MetaFieldMapperImpl implements MetaFieldMapper {

    @Override
    public MetaFieldDto toDto(MetaField entity) {
        if ( entity == null ) {
            return null;
        }

        MetaFieldDto metaFieldDto = new MetaFieldDto();

        metaFieldDto.setAlgorithmid( entity.getAlgorithmid() );
        metaFieldDto.setCategory( entity.getCategory() );
        metaFieldDto.setCreatetime( entity.getCreatetime() );
        metaFieldDto.setCreateuser( entity.getCreateuser() );
        metaFieldDto.setExample( entity.getExample() );
        metaFieldDto.setFieldcname( entity.getFieldcname() );
        metaFieldDto.setFieldname( entity.getFieldname() );
        metaFieldDto.setFieldscname( entity.getFieldscname() );
        metaFieldDto.setFieldsname( entity.getFieldsname() );
        metaFieldDto.setFieldstatus( entity.getFieldstatus() );
        metaFieldDto.setId( entity.getId() );
        metaFieldDto.setMtid( entity.getMtid() );
        metaFieldDto.setNote( entity.getNote() );
        metaFieldDto.setSenlevel( entity.getSenlevel() );
        metaFieldDto.setSparefield1( entity.getSparefield1() );
        metaFieldDto.setSparefield2( entity.getSparefield2() );
        metaFieldDto.setSparefield3( entity.getSparefield3() );
        metaFieldDto.setSparefield4( entity.getSparefield4() );
        metaFieldDto.setUpdatetime( entity.getUpdatetime() );
        metaFieldDto.setUpdateuser( entity.getUpdateuser() );

        return metaFieldDto;
    }

    @Override
    public List<MetaFieldDto> toDto(List<MetaField> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetaFieldDto> list = new ArrayList<MetaFieldDto>( entityList.size() );
        for ( MetaField metaField : entityList ) {
            list.add( toDto( metaField ) );
        }

        return list;
    }

    @Override
    public MetaField toEntity(MetaFieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        MetaField metaField = new MetaField();

        metaField.setAlgorithmid( dto.getAlgorithmid() );
        metaField.setCategory( dto.getCategory() );
        metaField.setCreatetime( dto.getCreatetime() );
        metaField.setCreateuser( dto.getCreateuser() );
        metaField.setExample( dto.getExample() );
        metaField.setFieldcname( dto.getFieldcname() );
        metaField.setFieldname( dto.getFieldname() );
        metaField.setFieldscname( dto.getFieldscname() );
        metaField.setFieldsname( dto.getFieldsname() );
        metaField.setFieldstatus( dto.getFieldstatus() );
        metaField.setId( dto.getId() );
        metaField.setMtid( dto.getMtid() );
        metaField.setNote( dto.getNote() );
        metaField.setSenlevel( dto.getSenlevel() );
        metaField.setSparefield1( dto.getSparefield1() );
        metaField.setSparefield2( dto.getSparefield2() );
        metaField.setSparefield3( dto.getSparefield3() );
        metaField.setSparefield4( dto.getSparefield4() );
        metaField.setUpdatetime( dto.getUpdatetime() );
        metaField.setUpdateuser( dto.getUpdateuser() );

        return metaField;
    }

    @Override
    public List<MetaField> toEntity(List<MetaFieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MetaField> list = new ArrayList<MetaField>( dtoList.size() );
        for ( MetaFieldDto metaFieldDto : dtoList ) {
            list.add( toEntity( metaFieldDto ) );
        }

        return list;
    }
}
