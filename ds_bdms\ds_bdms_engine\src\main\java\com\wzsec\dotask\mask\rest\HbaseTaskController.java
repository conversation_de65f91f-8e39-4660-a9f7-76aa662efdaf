package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.HbaseTaskConfigService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;

/**
 * 匿名化执行任务
 */
@RestController
@RequestMapping("/engine/hbase/task")
public class HbaseTaskController {

    private final HbaseTaskConfigService hbaseTaskConfigService;

    private final UserService userService;

    public HbaseTaskController(HbaseTaskConfigService hbaseTaskConfigService, UserService userService) {
        this.hbaseTaskConfigService = hbaseTaskConfigService;
        this.userService = userService;
    }

    @Log("执行HBase脱敏任务")
    @PostMapping(value = "/exec/{id}")
    public ResponseEntity<Object> execution(@PathVariable Long id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        hbaseTaskConfigService.execution(id, byName.getNickName(), byName.getId());//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
