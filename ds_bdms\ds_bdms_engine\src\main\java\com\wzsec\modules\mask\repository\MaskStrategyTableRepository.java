package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskStrategyTable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-09
 */
public interface MaskStrategyTableRepository extends JpaRepository<MaskStrategyTable, Integer>, JpaSpecificationExecutor<MaskStrategyTable> {

    /**
     * 根据策略名称查询策略数量
     *
     * @param strategyname 策略名称
     */
    @Query(value = "select count(*) from sdd_mask_strategy_table where strategyname = ?1", nativeQuery = true)
    int findStrategyCountByStrategyName(String strategyname);


    /**
     * 根据策略ID查询对应的对象
     *
     * @param id id
     * @return {@code String}
     */
    @Query(value = "SELECT * FROM sdd_mask_strategy_table WHERE id=? ", nativeQuery = true)
    String getStrategyTable(String id);


    @Query(value = "SELECT * FROM sdd_mask_strategy_table WHERE dbname =?", nativeQuery = true)
    List<MaskStrategyTable> libraryNameLookupTableName(String dbname);

    //    @Query(value = "select a.algenglishname,s.* from sdd_mask_strategy_table_all_sub s " +
//            "left join sdd_algorithm a on a.id = s.algorithmid " +
//            "where strategyid = ?1",nativeQuery = true)
    @Query(value = "select a.algenglishname,r.sparefield2 as 'isextract',r.sparefield3 as 'extractconfig',r.sparefield4 as 'extractalgid', " +
            "r.sparefield4 as 'abnormalhandle' ," + //异常数据处置
            "s.* from sdd_mask_strategy_table_all_sub s " +
            "left join sdd_mask_rule r on r.id = s.maskruleid " +
            "left join sdd_algorithm a on a.id = r.algorithmid " +
            "where strategyid = ?", nativeQuery = true)
    List<Map<String, Object>> getAllStrategyAlgoInfoByStrategyId(String strategyid);
}
