package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.mask.service.MaskStrategyFileUnformatSubService;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileUnformatSubQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-06-21
*/
// @Api(tags = "非格式化文件脱敏策略管理")
@RestController
@RequestMapping("/api/maskStrategyFileUnformatSub")
public class MaskStrategyFileUnformatSubController {

    private final MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService;

    public MaskStrategyFileUnformatSubController(MaskStrategyFileUnformatSubService maskStrategyFileUnformatSubService) {
        this.maskStrategyFileUnformatSubService = maskStrategyFileUnformatSubService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskStrategyFileUnformatSub:list')")
    public void download(HttpServletResponse response, MaskStrategyFileUnformatSubQueryCriteria criteria) throws IOException {
        maskStrategyFileUnformatSubService.download(maskStrategyFileUnformatSubService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询非格式化文件脱敏策略")
    // @ApiOperation("查询非格式化文件脱敏策略")
    @PreAuthorize("@el.check('maskStrategyFileUnformatSub:list')")
    public ResponseEntity<Object> getMaskStrategyFileUnformatSubs(MaskStrategyFileUnformatSubQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskStrategyFileUnformatSubService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增非格式化文件脱敏策略")
    // @ApiOperation("新增非格式化文件脱敏策略")
    @PreAuthorize("@el.check('maskStrategyFileUnformatSub:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskStrategyFileUnformatSub resources){
        return new ResponseEntity<>(maskStrategyFileUnformatSubService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改非格式化文件脱敏策略")
    // @ApiOperation("修改非格式化文件脱敏策略")
    @PreAuthorize("@el.check('maskStrategyFileUnformatSub:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskStrategyFileUnformatSub resources){
        maskStrategyFileUnformatSubService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除非格式化文件脱敏策略")
    // @ApiOperation("删除非格式化文件脱敏策略")
    @PreAuthorize("@el.check('maskStrategyFileUnformatSub:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskStrategyFileUnformatSubService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
