package com.wzsec.utils.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wzsec.utils.StringUtils;
import org.springframework.util.backoff.BackOff;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.math.MathContext;


/**
 * 加密算法规则工具类，自定义规则在Rule4ProgramUtil.java
 *
 * <AUTHOR>
 * @date 2020-4-28
 */
public class Rule4AlgorithmUtil {


    /**
     * 必须包含大小写字母及数字
     *
     * @param data
     * @param type 1为大写字母 ，2为小写字母 ,3为大小写字母
     * @return
     */
    private static boolean isContainLetterDigit(String data, String type) {
        if (StringUtils.isEmpty(data))
            return false;
        boolean isRight = false;// 定义一个boolean值，用来表示最后的结果
        boolean isDigit = false;// 定义一个boolean值，用来表示是否包含数字
        boolean isUpperCase = false;// 定义一个boolean值，用来表示是否包含大写字母
        boolean isLowerCase = false;// 定义一个boolean值，用来表示是否包含小写字母
        for (int i = 0; i < data.length(); i++) {
            if (Character.isDigit(data.charAt(i))) { // 用char包装类中的判断数字的方法判断每一个字符
                isDigit = true;
            } else if (Character.isUpperCase(data.charAt(i))) { // 用char包装类中的判断字母的方法判断每一个字符
                isUpperCase = true;
            } else if (Character.isLowerCase(data.charAt(i))) { // 用char包装类中的判断字母的方法判断每一个字符
                isLowerCase = true;
            } else {
                return isRight;
            }
        }
        if (type.equals("1")) {
            isRight = isDigit && isUpperCase;
        } else if (type.equals("2")) {
            isRight = isDigit && isLowerCase;
        } else {
            isRight = isDigit && isUpperCase && isLowerCase;
        }
        return isRight;
    }

    /**
     * @Description:AES校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkAES(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() == 32) {
            if (isContainLetterDigit(data, "1")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:3DES校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_check3DES(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        // 12和24位 最后一位是= 数字和大小写字母必须有 不包含汉字 没有|和,和分号
        // 32位 数字和大小写字母 不包含汉字 没有|和,和分号
        boolean isRight = false;// 定义一个boolean值，用来表示最后的结果
        boolean isDigit = false;// 定义一个boolean值，用来表示是否包含数字
        boolean isUpperCase = false;// 定义一个boolean值，用来表示是否包含大写字母
        boolean isLowerCase = false;// 定义一个boolean值，用来表示是否包含小写字母
        if (data.length() == 12 || data.length() == 24) {
            if (!"=".equals(data.substring(data.length() - 1))) {
                return false;
            }
            for (int i = 0; i < data.length(); i++) {
                char s = data.charAt(i);
                if (Character.isDigit(s)) { // 用char包装类中的判断数字的方法判断每一个字符
                    isDigit = true;
                } else if (Character.isUpperCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isUpperCase = true;
                } else if (Character.isLowerCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isLowerCase = true;
                } else if (String.valueOf(s).matches("[\u4e00-\u9fa5]")) { // 判断不包含汉字
                    return false;
                } else if ("|".equals(String.valueOf(s)) || ",".equals(String.valueOf(s))
                        || ";".equals(String.valueOf(s))) {
                    return false;
                } else if (data.contains(" ")) {
                    return false;
                }
            }
            isRight = isDigit && isUpperCase && isLowerCase;
        } else if (data.length() == 32) {
            for (int i = 0; i < data.length(); i++) {
                char s = data.charAt(i);
                if (Character.isDigit(s)) { // 用char包装类中的判断数字的方法判断每一个字符
                    isDigit = true;
                } else if (Character.isUpperCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isUpperCase = true;
                } else if (Character.isLowerCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isLowerCase = true;
                } else if (String.valueOf(s).matches("[\u4e00-\u9fa5]")) { // 判断不包含汉字
                    return false;
                } else if ("|".equals(String.valueOf(s)) || ",".equals(String.valueOf(s))
                        || ";".equals(String.valueOf(s))) { //
                    return false;
                } else if (data.contains(" ")) {
                    return false;
                }
            }
            isRight = isDigit && isUpperCase && isLowerCase;
        } else {
            return false;
        }

        return isRight;
    }

    /**
     * @Description:MD5校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkMD5(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() == 32) {
            if (isContainLetterDigit(data, "2")) {//数字和小写字母
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:sha256校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkSHA256(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() == 64) {
            if (isContainLetterDigit(data, "2")) {//数字和小写字母
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private static Random random = new Random();

    //数值保持均值算法
    public static List<Object> p_average(Object data) {
        List<Object> dataList = (List<Object>) data;
        Object desensitizeData = null;

        if (dataList.get(0) instanceof Double) {
            desensitizeData = desensitizeDoubleList((List<Double>) data);
        } else if (dataList.get(0) instanceof Integer) {
            desensitizeData = desensitizeIntegerList((List<Integer>) data);
        } else if (dataList.get(0) instanceof String) {
            //字符串类型数据，需要转换成数值型才可以继续执行
            if (((String) dataList.get(0)).contains(".")) {
                List<Double> doubleList = new ArrayList<>();
                for (Object object : dataList) {
                    double doubleData = object != null ? Double.valueOf(object.toString()) : 0.00;
                    doubleList.add(doubleData);
                }
                desensitizeData = desensitizeDoubleList(doubleList);
            } else {
                List<Integer> integerList = new ArrayList<>();
                for (Object object : dataList) {
                    int intData = object != null ? Integer.valueOf(object.toString()) : 0;
                    integerList.add(intData);
                }
                desensitizeData = desensitizeIntegerList(integerList);
            }
        }
        return (List<Object>) desensitizeData;
    }

    /**
     * 脱敏浮点型数据
     * @param originalList
     * @return
     */
    public static List<Double> desensitizeDoubleList(List<Double> originalList) {
        BigDecimal sum = BigDecimal.ZERO, minimum = BigDecimal.ZERO;
        for (Double num : originalList) {
            //以免数据出现0的情况下误取值
            if (minimum.doubleValue() == 0.0 && num != 0.0){
                minimum = new BigDecimal(num);
            }
            //取最小值为浮动值
            if (num != 0.0 && num < minimum.doubleValue()) {
                minimum = new BigDecimal(num);
            }
            sum = sum.add(new BigDecimal(num));
        }
        double minimum_d = minimum.setScale(2, RoundingMode.HALF_UP).doubleValue();
        List<Double> desensitizedList = new ArrayList<>();
        BigDecimal maskSum = BigDecimal.ZERO;
        // 调整脱敏后的数据使得平均值保持不变
        for (int i = 0; i < originalList.size(); i++) {
            Double intData = originalList.get(i);
            if (i < originalList.size() - 2) {
                intData = getDecimalsRandomDeviationNumber(intData, minimum_d);
                maskSum = maskSum.add(new BigDecimal(intData));
            } else {
                //最后2位补差值，确保脱敏前后总值与均值相等，且补值与原值不相等
                double difference = sum.doubleValue() - maskSum.doubleValue();
                complementDoubleData(difference, desensitizedList);
                break;
            }
            BigDecimal bdValue = new BigDecimal(intData).setScale(2, RoundingMode.HALF_UP);
            desensitizedList.add(bdValue.doubleValue());
        }
        return desensitizedList;
    }

    /**
     * 补值int类型数据
     * @param difference
     * @param list
     */
    public static void complementDoubleData(double difference, List<Double> list) {
        double firstNumber = 0.0, secondNumber = 0.0;
        if (difference != 0) {
            // 生成第一个随机数（范围在0到|difference|之间）
            firstNumber = random.nextDouble() * Math.abs(difference);

            // 计算第二个数，确保两个数的和等于difference
            secondNumber = difference - firstNumber;

            // 如果第二个数是负数，则调整第一个数使其为正数，并重新计算第二个数
            if (secondNumber < 0) {
                firstNumber = random.nextDouble() * (Math.abs(difference) - Math.abs(secondNumber));
                secondNumber = difference - firstNumber;
            }
        }
        //在追求精度的前提下，保留2位小数
        list.add(new BigDecimal(firstNumber).setScale(2, RoundingMode.HALF_UP).doubleValue());
        list.add(new BigDecimal(secondNumber).setScale(2, RoundingMode.HALF_UP).doubleValue());
    }

    /**
     * 脱敏数字型数据
     * @param originalList
     * @return
     */
    public static List<Integer> desensitizeIntegerList(List<Integer> originalList) {
        int sum = 0, minimum = 0;
        for (Integer num : originalList) {
            //以免数据出现0的情况下误取值
            if (minimum == 0 && num != 0){
                minimum = num;
            }
            //取最小值为浮动值
            if (num != 0 && num < minimum) {
                minimum = num;
            }
            sum += num;
        }

        List<Integer> desensitizedList = new ArrayList<>();
        int maskSum = 0;
        // 调整脱敏后的数据使得平均值保持不变
        for (int i = 0; i < originalList.size(); i++) {
            Integer intData = originalList.get(i);
            if (i < originalList.size() - 2) {
                intData = getIntRandomDeviationNumber(intData, minimum);
                maskSum += intData;
                desensitizedList.add(intData);
            } else {
                //最后2位补差值，确保脱敏前后总值与均值相等，且补值与原值不相等
                int difference = sum - maskSum;
                complementIntData(difference,desensitizedList);
                break;
            }
        }
        return desensitizedList;
    }

    /**
     * 补值int类型数据
     * @param difference
     * @param list
     */
    public static void complementIntData(int difference,List<Integer> list) {
        int a = 0, b = 0;
        if (difference != 0) {
            // 根据difference的符号和大小随机选择a的范围
            if (difference > 0) {
                // 对于正数difference，a的范围是[1, difference/2]
                a = 1 + random.nextInt(difference / 2);
            } else {
                // 对于负数difference，a的范围是[difference/2, -1]
                a = difference / 2 + random.nextInt(-difference / 2);
            }
            // 计算b的值
            b = difference - a;
        }
        list.add(a);
        list.add(b);
    }

    /**
     * 获取整数浮动值
     * @param data
     * @return
     */
    public static int getIntRandomDeviationNumber(int data, int fluctuating) {
        if (fluctuating == 0) {
            return data; // 浮动值为0，则直接返回data
        }

        int lowerBound = Math.max(data - Math.abs(fluctuating), Integer.MIN_VALUE);
        int upperBound = Math.min(data + Math.abs(fluctuating), Integer.MAX_VALUE);

        // 检查上下界是否有效
        if (lowerBound >= upperBound) {
            System.out.println("给定的fluctuating值导致无法生成有效的随机数范围。");
            return data;
        }

        // 生成随机数，但不等于data（除非data是唯一可能的值）
        int randomNumber;
        if (lowerBound == upperBound) {
            // 如果lowerBound和upperBound相同，那么只能返回这个唯一的值
            randomNumber = lowerBound;
        } else {
            do {
                randomNumber = lowerBound + random.nextInt(upperBound - lowerBound + 1);
            } while (randomNumber == data); // 当randomNumber等于data时重新生成
        }

        return randomNumber;
    }

    /**
     * 获取小数浮动值
     * @param data
     * @return
     */
    public static double getDecimalsRandomDeviationNumber(double data, double fluctuating) {
        if (fluctuating == 0.0) {
            return data; // 浮动值为0，则直接返回data
        }

        double randomNumber;

        // 计算随机数的范围
        double lowerBound = Math.max(data - Math.abs(fluctuating), Double.MIN_VALUE);
        double upperBound = Math.min(data + Math.abs(fluctuating), Double.MAX_VALUE);

        // 生成随机数
        randomNumber = lowerBound + (upperBound - lowerBound) * random.nextDouble();

        return randomNumber;
    }

    /**
     * 计算小数均值
     * @param list
     * @return
     */
    public static double decimalsCalculateAverage(List<Double> list) {
        double sum = 0.0;
        for (double value : list) {
            sum += value;
        }
        return sum / list.size();
    }

    /**
     * 计算整数均值
     * @param list
     * @return
     */
    public static int integerCalculateAverage(List<Integer> list) {
        int sum = 0;
        for (int value : list) {
            sum += value;
        }
        return sum / list.size();
    }

    public static void main(String[] args) {
        System.out.println("浮点型数据==========================================================");
        List<Double> doubleList = new ArrayList<>();
        doubleList.add(0.44);
        doubleList.add(20.22);
        doubleList.add(219.00);
        doubleList.add(44.21);
        doubleList.add(123.74);
        Object object = p_average(doubleList);
        List<Double> maskDoubleList = (List<Double>)object;

        System.out.println("脱敏前数据: " + JSONObject.toJSONString(doubleList));
        System.out.println("脱敏后数据: " + JSON.toJSONString(maskDoubleList));
        System.out.println("脱敏前均值: " + decimalsCalculateAverage(doubleList));
        System.out.println("脱敏后均值: " + decimalsCalculateAverage(maskDoubleList));



        System.out.println("整数型数据==========================================================");
        List<Integer> intList = new ArrayList<>();
        intList.add(1);
        intList.add(2);
        intList.add(3);
        intList.add(4);
        intList.add(2);
        Object intobject = p_average(intList);
        List<Integer> maskIntegerList = (List<Integer>)intobject;

        System.out.println("脱敏前数据: " + JSONObject.toJSONString(intList));
        System.out.println("脱敏后数据: " + JSON.toJSONString(maskIntegerList));
        System.out.println("脱敏前均值: " + integerCalculateAverage(intList));
        System.out.println("脱敏后均值: " + integerCalculateAverage(maskIntegerList));
    }
}
