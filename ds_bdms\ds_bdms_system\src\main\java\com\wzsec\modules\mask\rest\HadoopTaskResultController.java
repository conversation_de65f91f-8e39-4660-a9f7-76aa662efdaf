package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.HadoopTaskResult;
import com.wzsec.modules.mask.service.HadoopTaskResultService;
import com.wzsec.modules.mask.service.dto.HadoopTaskResultQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2020-11-17
*/
// @Api(tags = "HDFS文件脱敏任务结果管理")
@RestController
@RequestMapping("/api/hadoopTaskResult")
public class HadoopTaskResultController {

    private final HadoopTaskResultService hadooptaskresultService;

    public HadoopTaskResultController(HadoopTaskResultService hadooptaskresultService) {
        this.hadooptaskresultService = hadooptaskresultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('hadoopTaskResult:list')")
    public void download(HttpServletResponse response, HadoopTaskResultQueryCriteria criteria) throws IOException {
        hadooptaskresultService.download(hadooptaskresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询HDFS文件脱敏任务结果")
    // @ApiOperation("查询HDFS文件脱敏任务结果")
    @PreAuthorize("@el.check('hadoopTaskResult:list')")
    public ResponseEntity<Object> getHadooptaskresults(HadoopTaskResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(hadooptaskresultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增HDFS文件脱敏任务结果")
    // @ApiOperation("新增HDFS文件脱敏任务结果")
    @PreAuthorize("@el.check('hadoopTaskResult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody HadoopTaskResult resources){
        return new ResponseEntity<>(hadooptaskresultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改HDFS文件脱敏任务结果")
    // @ApiOperation("修改HDFS文件脱敏任务结果")
    @PreAuthorize("@el.check('hadoopTaskResult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody HadoopTaskResult resources){
        hadooptaskresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除HDFS文件脱敏任务结果")
    // @ApiOperation("删除HDFS文件脱敏任务结果")
    @PreAuthorize("@el.check('hadoopTaskResult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        hadooptaskresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
