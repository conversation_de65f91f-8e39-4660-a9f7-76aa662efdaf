package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.KafkaTaskResult;
import com.wzsec.modules.mask.service.KafkaTaskResultService;
import com.wzsec.modules.mask.service.dto.KafkaTaskResultDto;
import com.wzsec.modules.mask.service.dto.KafkaTaskResultQueryCriteria;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2021-02-26
*/
// @Api(tags = "Kafka脱敏任务结果管理")
@RestController
@RequestMapping("/api/kafkaresult")
public class KafkaTaskResultController {

    private final KafkaTaskResultService kafkaResultService;

    public KafkaTaskResultController(KafkaTaskResultService kafkaResultService) {
        this.kafkaResultService = kafkaResultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('kafkaresult:list')")
    public void download(HttpServletResponse response, KafkaTaskResultQueryCriteria criteria) throws IOException {
        kafkaResultService.download(kafkaResultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询Kafka脱敏任务结果")
    // @ApiOperation("查询Kafka脱敏任务结果")
    @PreAuthorize("@el.check('kafkaresult:list')")
    public ResponseEntity<Object> getKafkaresults(KafkaTaskResultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(kafkaResultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增Kafka脱敏任务结果")
    // @ApiOperation("新增Kafka脱敏任务结果")
    @PreAuthorize("@el.check('kafkaresult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody KafkaTaskResult resources){
        return new ResponseEntity<>(kafkaResultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改Kafka脱敏任务结果")
    // @ApiOperation("修改Kafka脱敏任务结果")
    @PreAuthorize("@el.check('kafkaresult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody KafkaTaskResult resources){
        kafkaResultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除Kafka脱敏任务结果")
    // @ApiOperation("删除Kafka脱敏任务结果")
    @PreAuthorize("@el.check('kafkaresult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        kafkaResultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("敏感数据发现Kafka脱敏结果预览")
    // @ApiOperation("敏感数据发现Kafka脱敏结果预览")
    @GetMapping(value = "/kafkaTaskResultDetails")
    @PreAuthorize("@el.check('kafkaresult:list')")
    public ResponseEntity<Object> kafkaTaskResultDetails(KafkaTaskResultQueryCriteria criteria){
        KafkaTaskResultDto byId = kafkaResultService.findById(Long.parseLong(criteria.getId()));
        String beforemaskdata = byId.getBeforemaskdata();
        String[] before = beforemaskdata.split("\\n");
        String aftermaskdata = byId.getAftermaskdata();
        String[] after = aftermaskdata.split("\\n");
        List<KafkaTaskResultDto> kafkaTaskResultDtoList = new ArrayList<KafkaTaskResultDto>();
        if (before.length == after.length) {
            KafkaTaskResultDto kafkaTaskTaskResulttmp =null;
            for (int i = 0; i < before.length; i++) {
                kafkaTaskTaskResulttmp = new KafkaTaskResultDto();
                kafkaTaskTaskResulttmp.setBeforemaskdata(before[i]);
                kafkaTaskTaskResulttmp.setAftermaskdata(after[i]);
                kafkaTaskResultDtoList.add(kafkaTaskTaskResulttmp);
            }
        }
        return new ResponseEntity<>(PageUtil.toPage(kafkaTaskResultDtoList,kafkaTaskResultDtoList.size()),HttpStatus.OK);
    }
}
