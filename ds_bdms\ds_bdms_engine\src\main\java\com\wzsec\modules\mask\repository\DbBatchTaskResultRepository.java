package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.DbBatchTaskResult;
import com.wzsec.modules.mask.service.dto.DbBatchTaskResultDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* <AUTHOR>
* @date 2023-02-07
*/
public interface DbBatchTaskResultRepository extends JpaRepository<DbBatchTaskResult, Integer>, JpaSpecificationExecutor<DbBatchTaskResult> {

    @Query(value = "SELECT r.* FROM sdd_mask_dbbatchtaskresult r " +
            "left join sdd_mask_dbbatchtaskconfig c on c.taskname = r.taskname " +
            "where r.isremove = '0' and r.taskstatus = '2'  and r.tabname = ?1 and r.outputname = ?2 and c.inputdatasourceid = ?3 and c.outputdatasourceid = ?4 " +
            "order by jobendtime desc limit 1", nativeQuery = true)
    DbBatchTaskResult getLastResult(String inTabname, String outTabname, Integer inDataSourceId, Integer outData);
}