2025-07-23 11:11:14,728 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 30236 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JO<PERSON> in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:11:14,738 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:11:19,922 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:11:19,927 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:11:23,785 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 3806 ms. Found 88 JPA repository interfaces.
2025-07-23 11:11:25,926 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:11:25,929 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:11:25,935 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:11:25,935 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:11:25,943 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:11:25,946 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:11:25,950 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:11:25,951 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:11:25,951 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:11:27,949 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:11:27,988 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:11:27,996 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:11:30,841 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:11:31,684 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:11:31,768 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:11:31,769 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:11:31,770 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:11:31,772 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:11:31,773 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:11:31,776 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:11:31,783 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:11:31,790 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:11:38,864 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:11:40,677 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:11:41,357 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:11:42,683 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:11:43,905 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:12:03,502 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:12:03,637 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:12:14,522 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-23 11:12:15,450 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 60089 ms
2025-07-23 11:12:30,693 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-23 11:12:31,125 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:12:31,340 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:12:31,340 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:12:31,387 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:12:31,400 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:12:31,402 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:12:31,402 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:12:31,403 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@46ec1c63
2025-07-23 11:12:31,403 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:12:40,549 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@313c05b9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@29dbf03f, org.springframework.security.web.context.SecurityContextPersistenceFilter@440f55ad, org.springframework.security.web.header.HeaderWriterFilter@36b085da, org.springframework.security.web.authentication.logout.LogoutFilter@7864ec9e, org.springframework.web.filter.CorsFilter@63782ad0, com.wzsec.modules.security.security.TokenFilter@7903007c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@371ce753, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6a6beec0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d5e2461, org.springframework.security.web.session.SessionManagementFilter@56b3dcff, org.springframework.security.web.access.ExceptionTranslationFilter@40fd8aa1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@25b56ad5]
2025-07-23 11:12:40,653 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 11:12:54,553 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:12:54,558 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:12:54,558 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:12:54,558 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:12:54,561 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:12:54,561 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:12:54,561 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:12:54,561 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@56208ee4
2025-07-23 11:12:55,508 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 11:12:55,739 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-23 11:12:55,761 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 11:12:55,761 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 11:12:55,761 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 11:12:55,761 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 11:12:55,765 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:12:55,765 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:12:55,765 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:12:55,765 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:12:55,765 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:12:55,777 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 11:12:55,779 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 11:12:55,779 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:12:55,821 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 101.938 seconds (JVM running for 105.037)
2025-07-23 11:12:57,771 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-23 11:12:57,771 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-23 11:29:42,700 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 21800 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:29:42,716 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:29:48,606 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:29:48,611 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:29:51,307 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 2661 ms. Found 88 JPA repository interfaces.
2025-07-23 11:29:52,767 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:29:52,770 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:29:52,775 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:29:52,775 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:29:52,783 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:29:52,788 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:29:52,791 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:29:52,791 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:29:52,791 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:29:54,799 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:29:54,844 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:29:54,848 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:29:57,875 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:29:58,651 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:29:58,725 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:29:58,727 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:29:58,727 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:29:58,729 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:29:58,730 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:29:58,730 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:29:58,737 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:29:58,742 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:30:04,415 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:30:06,089 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:30:06,607 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:30:07,847 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:30:09,141 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:30:23,558 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:30:23,651 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:30:37,037 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-23 11:30:38,437 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 54567 ms
2025-07-23 11:30:59,241 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-23 11:31:01,002 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:31:02,835 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:31:02,837 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:31:03,249 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:31:03,718 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:31:03,718 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:31:03,719 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:31:04,033 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@6eded737
2025-07-23 11:31:04,034 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:31:15,535 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageController' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\rest\LocalStorageController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageServiceImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\service\impl\LocalStorageServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.wzsec.service.mapper.LocalStorageMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-23 11:31:15,558 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 11:31:15,558 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:31:15,559 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 11:31:15,581 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:31:15,699 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 11:31:16,034 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 11:31:16,343 ERROR (LoggingFailureAnalysisReporter.java:40)- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.wzsec.service.impl.LocalStorageServiceImpl required a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' that could not be found.


Action:

Consider defining a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' in your configuration.

2025-07-23 11:35:05,347 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 9936 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:35:05,354 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:35:08,574 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:35:08,576 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:35:09,736 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1142 ms. Found 88 JPA repository interfaces.
2025-07-23 11:35:10,486 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:35:10,487 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:35:10,490 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:35:10,490 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:35:10,495 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:35:10,497 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:35:10,498 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:35:10,499 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:35:10,499 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:35:11,481 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:35:11,505 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:35:11,510 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:35:13,110 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:35:13,484 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:35:13,526 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:35:13,527 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:35:13,528 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:35:13,528 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:35:13,529 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:35:13,529 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:35:13,534 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:35:13,538 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:35:16,860 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:35:17,674 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:35:18,044 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:35:18,706 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:35:19,470 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:35:26,706 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:35:26,763 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:35:33,337 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-23 11:35:33,964 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 27984 ms
2025-07-23 11:35:40,931 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-23 11:35:41,157 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:35:41,278 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:35:41,279 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:35:41,306 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:35:41,312 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:35:41,312 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:35:41,313 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:35:41,314 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@5c0be4db
2025-07-23 11:35:41,314 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:35:43,372 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageController' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\rest\LocalStorageController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'localStorageServiceImpl' defined in file [D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_tools\target\classes\com\wzsec\service\impl\LocalStorageServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.wzsec.service.mapper.LocalStorageMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-23 11:35:43,386 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 11:35:43,387 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:35:43,387 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 11:35:43,398 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:35:43,430 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 11:35:43,550 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 11:35:43,692 ERROR (LoggingFailureAnalysisReporter.java:40)- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.wzsec.service.impl.LocalStorageServiceImpl required a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' that could not be found.


Action:

Consider defining a bean of type 'com.wzsec.service.mapper.LocalStorageMapper' in your configuration.

2025-07-23 11:39:00,625 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 29212 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:39:00,632 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:39:05,187 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:39:05,191 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:39:06,770 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1559 ms. Found 88 JPA repository interfaces.
2025-07-23 11:39:07,623 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:39:07,627 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:39:07,628 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:39:07,628 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:39:07,635 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:39:07,638 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:39:07,638 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:39:07,642 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:39:07,642 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:39:08,943 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:39:08,974 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:39:08,983 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:39:10,917 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:39:11,377 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:39:11,419 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:39:11,419 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:39:11,422 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:39:11,423 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:39:11,423 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:39:11,424 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:39:11,428 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:39:11,429 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:39:15,496 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:39:16,540 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:39:16,860 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:39:17,628 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:39:18,310 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:39:26,549 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:39:26,622 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:39:33,275 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-23 11:39:33,776 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 31291 ms
2025-07-23 11:39:41,581 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-23 11:39:41,851 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:39:41,983 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:39:41,983 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:39:42,013 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:39:42,019 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:39:42,019 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:39:42,019 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:39:42,019 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@7e325a4
2025-07-23 11:39:42,021 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:39:46,373 WARN (AbstractApplicationContext.java:591)- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'localStorageMapperImpl': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.wzsec.service.mapper.LocalStorageMapperImpl] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
2025-07-23 11:39:46,391 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 11:39:46,391 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:39:46,391 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 11:39:46,407 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:39:46,447 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
2025-07-23 11:39:46,563 INFO (ConditionEvaluationReportLoggingListener.java:136)- 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 11:39:46,713 ERROR (SpringApplication.java:870)- Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'localStorageMapperImpl': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.wzsec.service.mapper.LocalStorageMapperImpl] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:289)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1302)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:920)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359)
	at com.wzsec.BDMSEngineRun.main(BDMSEngineRun.java:67)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.wzsec.service.mapper.LocalStorageMapperImpl] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:485)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:321)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:267)
	... 18 common frames omitted
Caused by: java.lang.NoClassDefFoundError: LocalStorageDto
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2701)
	at java.lang.Class.getDeclaredMethods(Class.java:1975)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:467)
	... 20 common frames omitted
Caused by: java.lang.ClassNotFoundException: LocalStorageDto
	at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:349)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	... 24 common frames omitted
2025-07-23 11:42:37,661 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 27996 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:42:37,670 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:42:40,476 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:42:40,478 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:42:41,586 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1091 ms. Found 88 JPA repository interfaces.
2025-07-23 11:42:42,335 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:42:42,335 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:42:42,340 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:42:42,340 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:42:42,343 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:42:42,346 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:42:42,346 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:42:42,346 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:42:42,346 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:42:43,457 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:42:43,481 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:42:43,485 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:42:45,102 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:42:45,524 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:42:45,563 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:42:45,564 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:42:45,564 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:42:45,564 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:42:45,564 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:42:45,566 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:42:45,571 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:42:45,574 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:42:49,005 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:42:50,081 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:42:50,391 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:42:51,130 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:42:51,813 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:43:01,326 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:43:01,381 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:43:06,531 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-23 11:43:07,055 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 28532 ms
2025-07-23 11:43:13,575 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-23 11:43:13,812 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:43:13,936 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:43:13,936 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:43:13,958 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:43:13,970 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:43:13,970 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:43:13,970 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:43:13,972 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@2546604e
2025-07-23 11:43:13,972 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:43:18,313 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@64a82b3c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2d83ea1b, org.springframework.security.web.context.SecurityContextPersistenceFilter@774fa82b, org.springframework.security.web.header.HeaderWriterFilter@497adc9, org.springframework.security.web.authentication.logout.LogoutFilter@2e044892, org.springframework.web.filter.CorsFilter@17c45a57, com.wzsec.modules.security.security.TokenFilter@16a1738d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@39a00675, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@651c826c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d0c8cd0, org.springframework.security.web.session.SessionManagementFilter@1e792043, org.springframework.security.web.access.ExceptionTranslationFilter@50213a2f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7343135a]
2025-07-23 11:43:18,369 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 11:43:23,429 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:43:23,431 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:43:23,431 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:43:23,431 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:43:23,431 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:43:23,431 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:43:23,431 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:43:23,431 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@597d339e
2025-07-23 11:43:23,687 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 11:43:23,798 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-23 11:43:23,806 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 11:43:23,808 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 11:43:23,809 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 11:43:23,809 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 11:43:23,809 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:43:23,809 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:43:23,809 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:43:23,809 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:43:23,809 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:43:23,814 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 11:43:23,814 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 11:43:23,814 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:43:23,835 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 46.976 seconds (JVM running for 49.962)
2025-07-23 11:43:24,822 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-23 11:43:24,822 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-23 11:43:48,746 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 11:45:36,408 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 4192 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 11:45:36,417 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 11:45:40,069 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 11:45:40,071 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 11:45:42,370 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 2276 ms. Found 88 JPA repository interfaces.
2025-07-23 11:45:44,065 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 11:45:44,067 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:45:44,072 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:45:44,072 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:45:44,081 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:45:44,085 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 11:45:44,089 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 11:45:44,090 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:45:44,090 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:45:46,412 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 11:45:46,445 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 11:45:46,451 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 11:45:48,999 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 11:45:49,884 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 11:45:49,970 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 11:45:49,972 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 11:45:49,974 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 11:45:49,976 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 11:45:49,977 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 11:45:49,978 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 11:45:49,988 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 11:45:49,997 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 11:45:54,121 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 11:45:55,318 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 11:45:55,684 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 11:45:56,909 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 11:45:57,915 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 11:46:07,530 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 11:46:07,695 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 11:46:16,604 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-23 11:46:17,329 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 40284 ms
2025-07-23 11:46:24,305 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-23 11:46:24,534 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:46:24,649 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:46:24,649 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:46:24,673 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:46:24,679 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:46:24,679 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:46:24,679 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:46:24,679 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@216501a9
2025-07-23 11:46:24,682 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:46:29,161 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@450d0da9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@48b16f22, org.springframework.security.web.context.SecurityContextPersistenceFilter@3032aa88, org.springframework.security.web.header.HeaderWriterFilter@39ba0bff, org.springframework.security.web.authentication.logout.LogoutFilter@f3e6e05, org.springframework.web.filter.CorsFilter@51077e04, com.wzsec.modules.security.security.TokenFilter@737f4856, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@21a9e25a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4125a698, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@53da3955, org.springframework.security.web.session.SessionManagementFilter@903d2ee, org.springframework.security.web.access.ExceptionTranslationFilter@31cf5483, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2c36c11e]
2025-07-23 11:46:29,238 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 11:46:33,831 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 11:46:33,831 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 11:46:33,831 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 11:46:33,831 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 11:46:33,831 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 11:46:33,831 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 11:46:33,831 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 11:46:33,831 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@71efa55d
2025-07-23 11:46:34,046 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 11:46:34,140 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-23 11:46:34,145 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 11:46:34,146 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 11:46:34,146 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 11:46:34,146 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 11:46:34,146 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:46:34,146 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 11:46:34,146 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 11:46:34,146 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 11:46:34,146 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 11:46:34,150 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 11:46:34,150 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 11:46:34,150 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 11:46:34,165 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 58.994 seconds (JVM running for 62.067)
2025-07-23 11:46:35,088 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-23 11:46:35,088 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-23 15:05:53,848 INFO (StartupInfoLogger.java:55)- Starting BDMSEngineRun using Java 1.8.0_211 on JOY with PID 8496 (D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms\ds_bdms_engine\target\classes started by JOY in D:\develop\work_project\gitee_ds_bdms_staticmask\ds_bdms)
2025-07-23 15:05:53,857 INFO (SpringApplication.java:686)- The following 1 profile is active: "dev"
2025-07-23 15:05:56,694 INFO (RepositoryConfigurationDelegate.java:262)- Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 15:05:56,697 INFO (RepositoryConfigurationDelegate.java:132)- Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-23 15:05:57,726 INFO (RepositoryConfigurationDelegate.java:201)- Finished Spring Data repository scanning in 1013 ms. Found 88 JPA repository interfaces.
2025-07-23 15:05:58,208 INFO (EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40)- Post-processing PropertySource instances
2025-07-23 15:05:58,208 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 15:05:58,211 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 15:05:58,211 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 15:05:58,214 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:05:58,215 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-23 15:05:58,216 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-23 15:05:58,216 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:05:58,216 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:05:59,149 INFO (DefaultLazyPropertyFilter.java:31)- Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-23 15:05:59,171 INFO (DefaultLazyPropertyResolver.java:35)- Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-23 15:05:59,177 INFO (DefaultLazyPropertyDetector.java:35)- Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-23 15:06:00,452 INFO (DruidDataSourceAutoConfigure.java:56)- Init DruidDataSource
2025-07-23 15:06:00,785 INFO (DefaultLazyEncryptor.java:37)- String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-07-23 15:06:00,816 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWITHHMACSHA512ANDAES_256
2025-07-23 15:06:00,817 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.key-obtention-iterations, using default value: 1000
2025-07-23 15:06:00,818 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.pool-size, using default value: 1
2025-07-23 15:06:00,819 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-name, using default value: null
2025-07-23 15:06:00,820 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.provider-class-name, using default value: null
2025-07-23 15:06:00,820 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.salt-generator-classname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-07-23 15:06:00,823 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.iv-generator-classname, using default value: org.jasypt.iv.RandomIvGenerator
2025-07-23 15:06:00,827 INFO (StringEncryptorBuilder.java:102)- Encryptor config not found for property jasypt.encryptor.string-output-type, using default value: base64
2025-07-23 15:06:03,979 INFO (DruidDataSource.java:930)- {dataSource-1} inited
2025-07-23 15:06:04,832 INFO (LogHelper.java:31)- HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-23 15:06:05,055 INFO (Version.java:44)- HHH000412: Hibernate ORM core version 5.4.33
2025-07-23 15:06:05,566 INFO (JavaReflectionManager.java:56)- HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-23 15:06:06,096 INFO (Dialect.java:175)- HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2025-07-23 15:06:12,815 INFO (JtaPlatformInitiator.java:52)- HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-23 15:06:12,873 INFO (AbstractEntityManagerFactoryBean.java:437)- Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 15:06:17,269 INFO (TomcatWebServer.java:108)- Tomcat initialized with port(s): 8091 (http)
2025-07-23 15:06:17,692 INFO (ServletWebServerApplicationContext.java:290)- Root WebApplicationContext: initialization completed in 23176 ms
2025-07-23 15:06:23,299 INFO (FileUtil.java:612)- 读取资源文件路径为：内部 的 ds_bdms_engine.properties
2025-07-23 15:06:23,501 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 15:06:23,604 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 15:06:23,604 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 15:06:23,625 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 15:06:23,630 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'QuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 15:06:23,630 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'QuartzScheduler' initialized from an externally provided properties instance.
2025-07-23 15:06:23,630 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 15:06:23,631 INFO (QuartzScheduler.java:2293)- JobFactory set to: com.wzsec.modules.quartz.config.QuartzConfig$QuartzJobFactory@5be2a48f
2025-07-23 15:06:23,632 INFO (QuartzScheduler.java:547)- Scheduler QuartzScheduler_$_NON_CLUSTERED started.
2025-07-23 15:06:27,359 INFO (DefaultSecurityFilterChain.java:55)- Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@62c229bb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1b080b41, org.springframework.security.web.context.SecurityContextPersistenceFilter@310abad6, org.springframework.security.web.header.HeaderWriterFilter@4c47eb9b, org.springframework.security.web.authentication.logout.LogoutFilter@57a84ac4, org.springframework.web.filter.CorsFilter@719d7341, com.wzsec.modules.security.security.TokenFilter@7b13d53a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3241c573, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@78214685, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7affa3fb, org.springframework.security.web.session.SessionManagementFilter@bc8921c, org.springframework.security.web.access.ExceptionTranslationFilter@34da2b4a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@417430c8]
2025-07-23 15:06:27,409 INFO (RedisConfig.java:115)- 初始化 -> [Redis CacheErrorHandler]
2025-07-23 15:06:31,146 INFO (StdSchedulerFactory.java:1220)- Using default implementation for ThreadExecutor
2025-07-23 15:06:31,147 INFO (SchedulerSignalerImpl.java:61)- Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 15:06:31,148 INFO (QuartzScheduler.java:229)- Quartz Scheduler v.2.3.2 created.
2025-07-23 15:06:31,148 INFO (RAMJobStore.java:155)- RAMJobStore initialized.
2025-07-23 15:06:31,148 INFO (QuartzScheduler.java:294)- Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 15:06:31,148 INFO (StdSchedulerFactory.java:1374)- Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 15:06:31,148 INFO (StdSchedulerFactory.java:1378)- Quartz scheduler version: 2.3.2
2025-07-23 15:06:31,148 INFO (QuartzScheduler.java:2293)- JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6448398f
2025-07-23 15:06:31,349 INFO (Slf4jLog.java:67)- Logging to Logger[org.mortbay.log] via org.mortbay.log.Slf4jLog
2025-07-23 15:06:31,453 INFO (TomcatWebServer.java:220)- Tomcat started on port(s): 8091 (http) with context path ''
2025-07-23 15:06:31,465 INFO (RefreshScopeRefreshedEventListener.java:47)- Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-23 15:06:31,467 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemProperties refreshed
2025-07-23 15:06:31,467 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source systemEnvironment refreshed
2025-07-23 15:06:31,467 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source random refreshed
2025-07-23 15:06:31,467 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 15:06:31,468 INFO (CachingDelegateEncryptablePropertySource.java:55)- Property Source Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' refreshed
2025-07-23 15:06:31,468 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-23 15:06:31,468 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-23 15:06:31,468 INFO (EncryptablePropertySourceConverter.java:76)- Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-23 15:06:31,475 INFO (EncryptablePropertySourceConverter.java:81)- Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-23 15:06:31,477 INFO (SchedulerFactoryBean.java:729)- Starting Quartz Scheduler now
2025-07-23 15:06:31,477 INFO (QuartzScheduler.java:547)- Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 15:06:31,497 INFO (StartupInfoLogger.java:61)- Started BDMSEngineRun in 38.578 seconds (JVM running for 41.155)
2025-07-23 15:06:32,263 INFO (BDMSEngineRun.java:98)- Backend(Engine) service started successfully
2025-07-23 15:06:32,263 INFO (BDMSEngineRun.java:99)- 项目启动成功=======================
2025-07-23 16:17:39,917 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 16:17:40,744 INFO (SchedulerFactoryBean.java:847)- Shutting down Quartz Scheduler
2025-07-23 16:17:40,744 INFO (QuartzScheduler.java:666)- Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 16:17:40,744 INFO (QuartzScheduler.java:585)- Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 16:17:40,744 INFO (QuartzScheduler.java:740)- Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 16:17:40,789 INFO (QuartzScheduler.java:666)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 16:17:40,789 INFO (QuartzScheduler.java:585)- Scheduler QuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 16:17:40,790 INFO (QuartzScheduler.java:740)- Scheduler QuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 16:17:40,813 INFO (AbstractEntityManagerFactoryBean.java:651)- Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-23 16:17:40,882 INFO (DruidDataSource.java:1825)- {dataSource-1} closed
