package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.mask.domain.BatchTaskTabConfig;
import com.wzsec.modules.mask.domain.DbBatchTaskConfig;
import com.wzsec.modules.mask.service.DbBatchTaskConfigService;
import com.wzsec.modules.mask.service.dto.BatchTaskTabConfigDto;
import com.wzsec.modules.mask.service.dto.DbBatchTaskConfigDto;
import com.wzsec.modules.mask.service.dto.DbBatchTaskConfigQueryCriteria;
import com.wzsec.modules.quartz.config.DbBatchTaskScanConfig;
import com.wzsec.utils.Const;
import com.wzsec.utils.PageUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-07
 */
// @Api(tags = "批量脱敏任务管理")
@RestController
@RequestMapping("/api/dbbatchtaskconfig")
public class DbBatchTaskConfigController {

    private final DbBatchTaskConfigService dbbatchtaskconfigService;

    private final DbBatchTaskScanConfig dbBatchTaskScanConfig;

    public DbBatchTaskConfigController(DbBatchTaskConfigService dbbatchtaskconfigService, DbBatchTaskScanConfig dbBatchTaskScanConfig) {
        this.dbbatchtaskconfigService = dbbatchtaskconfigService;
        this.dbBatchTaskScanConfig = dbBatchTaskScanConfig;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('dbbatchtaskconfig:list')")
    public void download(HttpServletResponse response, DbBatchTaskConfigQueryCriteria criteria) throws IOException {
        dbbatchtaskconfigService.download(dbbatchtaskconfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询批量脱敏任务")
    // @ApiOperation("查询批量脱敏任务")
    @PreAuthorize("@el.check('dbbatchtaskconfig:list')")
    public ResponseEntity<Object> getDbbatchtaskconfigs(DbBatchTaskConfigQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(dbbatchtaskconfigService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增批量脱敏任务")
    // @ApiOperation("新增批量脱敏任务")
    @PreAuthorize("@el.check('dbbatchtaskconfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody DbBatchTaskConfig resources) {
        return new ResponseEntity<>(dbbatchtaskconfigService.create(resources), HttpStatus.CREATED);
    }

    @Log("获取批量脱敏任务新增任务号")
    // @ApiOperation("获取数据库脱敏任务新增任务号")
    @PreAuthorize("@el.check('dbtaskconfig:add')")
    @GetMapping(value = "/getTaskName")
    public ResponseEntity<Object> getTaskName() {
        return new ResponseEntity<>(dbbatchtaskconfigService.getMAXTaskName(), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改批量脱敏任务")
    // @ApiOperation("修改批量脱敏任务")
    @PreAuthorize("@el.check('dbbatchtaskconfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody DbBatchTaskConfig resources) {
        dbbatchtaskconfigService.update(resources);
        DbBatchTaskConfigDto dbBatchTaskConfigDto = dbbatchtaskconfigService.findById(resources.getId());
        // 定时任务修改
        if (resources.getSubmittype().equals(Const.TASK_SUBMITTYPE_AUTO) && resources.getState().equals(Const.STATE_ON)) {
            dbBatchTaskScanConfig.updateJob(dbBatchTaskConfigDto);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除批量脱敏任务")
    // @ApiOperation("删除批量脱敏任务")
    @PreAuthorize("@el.check('dbbatchtaskconfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        for (Integer id : ids) {
            DbBatchTaskConfigDto dbBatchTaskConfigDto = dbbatchtaskconfigService.findById(id);
            dbBatchTaskScanConfig.deleteJob(dbBatchTaskConfigDto);
        }
        dbbatchtaskconfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("获取批量任务表配置信息")
    @GetMapping("/getDBBatchTaskTableInfo")
    public ResponseEntity<Object> getDBBatchTaskTableInfo(@RequestParam Integer id,Integer page,Integer size) {
        return new ResponseEntity<>(dbbatchtaskconfigService.getDBBatchTaskTableInfo(id,page,size), HttpStatus.OK);
    }

    @Log("保存批量任务表配置信息")
    @PostMapping("/saveDBBatchTaskTableInfo")
    public void saveDBBatchTaskTableInfo(@RequestBody List<BatchTaskTabConfig> batchTaskTabStrategyConfigs) {
        dbbatchtaskconfigService.saveDBBatchTaskTableInfo(batchTaskTabStrategyConfigs);
        //return new ResponseEntity<>(dbbatchtaskconfigService.create(resources),HttpStatus.CREATED);
    }

    @Log("保存详情配置")
    @PostMapping("/saveDetailsConfig")
    public void saveDetailsConfig(@RequestBody BatchTaskTabConfig batchTaskTabStrategyConfigs) {
        dbbatchtaskconfigService.saveDBBatchTaskTableInfo(batchTaskTabStrategyConfigs);
    }

    @Log("保存批量任务表配置信息")
    @PostMapping("/alterTaskState")
    public void alterTaskState(@RequestBody DbBatchTaskConfigDto dbBatchTaskConfigDto) {
        // 定时任务新增,由任务状态触发
        if (dbBatchTaskConfigDto.getSubmittype().equals(Const.TASK_SUBMITTYPE_AUTO) && dbBatchTaskConfigDto.getState().equals(Const.STATE_ON)) {
            dbBatchTaskScanConfig.addJob(dbBatchTaskConfigDto);
        }
        // 任务状态关闭,则删除定时任务
        if (dbBatchTaskConfigDto.getSubmittype().equals(Const.TASK_SUBMITTYPE_AUTO) && dbBatchTaskConfigDto.getState().equals(Const.STATE_OFF)) {
            dbBatchTaskScanConfig.deleteJob(dbBatchTaskConfigDto);
        }
        dbbatchtaskconfigService.alterTaskState(dbBatchTaskConfigDto);
    }

    @Log("执行批量脱敏任务")
    // @ApiOperation("在引擎执行数据库脱敏任务")
    @PutMapping(value = "/executionFromEngine/{id}")
    // @PreAuthorize("@el.check('wptask:edit')")
    public ResponseEntity<Object> executionFromEngine(@PathVariable Integer id, HttpServletRequest request) {
        dbbatchtaskconfigService.executionFromEngine(id, request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("获取库表脱敏结果预览")
    @GetMapping("/maskResultPreview")
    public ResponseEntity<Object> maskResultPreview(@RequestParam Long id,  HttpServletRequest request) {
        Object data = dbbatchtaskconfigService.getMaskResultPreview(id, request);
        return new ResponseEntity<>(data, HttpStatus.OK);
    }

    @Log("更改审批状态")
    @GetMapping(value = "/changeApprovalStatus/{id}/{approvalStatus}")
    @PreAuthorize("@el.check('maskStrategyTable:add')")
    public ResponseEntity<Object> changeApprovalStatus(@PathVariable Integer id, @PathVariable String approvalStatus) {
        dbbatchtaskconfigService.changeApprovalStatus(id, approvalStatus);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
