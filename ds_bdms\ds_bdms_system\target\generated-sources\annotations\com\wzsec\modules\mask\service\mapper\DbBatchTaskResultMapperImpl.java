package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DbBatchTaskResult;
import com.wzsec.modules.mask.service.dto.DbBatchTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DbBatchTaskResultMapperImpl implements DbBatchTaskResultMapper {

    @Override
    public DbBatchTaskResultDto toDto(DbBatchTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        DbBatchTaskResultDto dbBatchTaskResultDto = new DbBatchTaskResultDto();

        dbBatchTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        dbBatchTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        dbBatchTaskResultDto.setDbname( entity.getDbname() );
        dbBatchTaskResultDto.setId( entity.getId() );
        dbBatchTaskResultDto.setIpaddress( entity.getIpaddress() );
        dbBatchTaskResultDto.setIsremove( entity.getIsremove() );
        dbBatchTaskResultDto.setJobendtime( entity.getJobendtime() );
        dbBatchTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        dbBatchTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        dbBatchTaskResultDto.setLastmasklines( entity.getLastmasklines() );
        dbBatchTaskResultDto.setMasklines( entity.getMasklines() );
        dbBatchTaskResultDto.setOutputipaddress( entity.getOutputipaddress() );
        dbBatchTaskResultDto.setOutputname( entity.getOutputname() );
        dbBatchTaskResultDto.setOutputpath( entity.getOutputpath() );
        dbBatchTaskResultDto.setOutputtype( entity.getOutputtype() );
        dbBatchTaskResultDto.setRemark( entity.getRemark() );
        dbBatchTaskResultDto.setSparefield1( entity.getSparefield1() );
        dbBatchTaskResultDto.setSparefield2( entity.getSparefield2() );
        dbBatchTaskResultDto.setSparefield3( entity.getSparefield3() );
        dbBatchTaskResultDto.setSparefield4( entity.getSparefield4() );
        dbBatchTaskResultDto.setSparefield5( entity.getSparefield5() );
        dbBatchTaskResultDto.setStrategyjson( entity.getStrategyjson() );
        dbBatchTaskResultDto.setTabname( entity.getTabname() );
        dbBatchTaskResultDto.setTaskname( entity.getTaskname() );
        dbBatchTaskResultDto.setTaskstatus( entity.getTaskstatus() );
        dbBatchTaskResultDto.setTotallines( entity.getTotallines() );
        dbBatchTaskResultDto.setUsername( entity.getUsername() );

        return dbBatchTaskResultDto;
    }

    @Override
    public List<DbBatchTaskResultDto> toDto(List<DbBatchTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DbBatchTaskResultDto> list = new ArrayList<DbBatchTaskResultDto>( entityList.size() );
        for ( DbBatchTaskResult dbBatchTaskResult : entityList ) {
            list.add( toDto( dbBatchTaskResult ) );
        }

        return list;
    }

    @Override
    public DbBatchTaskResult toEntity(DbBatchTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        DbBatchTaskResult dbBatchTaskResult = new DbBatchTaskResult();

        dbBatchTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        dbBatchTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        dbBatchTaskResult.setDbname( dto.getDbname() );
        dbBatchTaskResult.setId( dto.getId() );
        dbBatchTaskResult.setIpaddress( dto.getIpaddress() );
        dbBatchTaskResult.setIsremove( dto.getIsremove() );
        dbBatchTaskResult.setJobendtime( dto.getJobendtime() );
        dbBatchTaskResult.setJobstarttime( dto.getJobstarttime() );
        dbBatchTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        dbBatchTaskResult.setLastmasklines( dto.getLastmasklines() );
        dbBatchTaskResult.setMasklines( dto.getMasklines() );
        dbBatchTaskResult.setOutputipaddress( dto.getOutputipaddress() );
        dbBatchTaskResult.setOutputname( dto.getOutputname() );
        dbBatchTaskResult.setOutputpath( dto.getOutputpath() );
        dbBatchTaskResult.setOutputtype( dto.getOutputtype() );
        dbBatchTaskResult.setRemark( dto.getRemark() );
        dbBatchTaskResult.setSparefield1( dto.getSparefield1() );
        dbBatchTaskResult.setSparefield2( dto.getSparefield2() );
        dbBatchTaskResult.setSparefield3( dto.getSparefield3() );
        dbBatchTaskResult.setSparefield4( dto.getSparefield4() );
        dbBatchTaskResult.setSparefield5( dto.getSparefield5() );
        dbBatchTaskResult.setStrategyjson( dto.getStrategyjson() );
        dbBatchTaskResult.setTabname( dto.getTabname() );
        dbBatchTaskResult.setTaskname( dto.getTaskname() );
        dbBatchTaskResult.setTaskstatus( dto.getTaskstatus() );
        dbBatchTaskResult.setTotallines( dto.getTotallines() );
        dbBatchTaskResult.setUsername( dto.getUsername() );

        return dbBatchTaskResult;
    }

    @Override
    public List<DbBatchTaskResult> toEntity(List<DbBatchTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DbBatchTaskResult> list = new ArrayList<DbBatchTaskResult>( dtoList.size() );
        for ( DbBatchTaskResultDto dbBatchTaskResultDto : dtoList ) {
            list.add( toEntity( dbBatchTaskResultDto ) );
        }

        return list;
    }
}
