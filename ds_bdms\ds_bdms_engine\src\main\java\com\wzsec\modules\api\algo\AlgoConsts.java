package com.wzsec.modules.api.algo;

public class AlgoConsts {

    // 动态脱敏算法类型
    public static final String ALGO_TYPE_MASK = "掩码";
    public static final String ALGO_TYPE_MD5 = "散列";
    public static final String ALGO_TYPE_FIXED_REPLACE = "固定替换";
    public static final String ALGO_TYPE_CLEARTEXT = "明文";
    public static final String ALGO_TYPE_REPLACE = "替换";
    public static final String ALGO_TYPE_CONTAIN_TRUNCATE = "包含指定字符截断";
    public static final String ALGO_TYPE_UNCONTAIN_TRUNCATE = "不包含指定字符截断";
    public static final String ALGO_TYPE_DATE_RANGE_RANDOM = "时间范围内随机";
    public static final String ALGO_TYPE_NUM_RANGE_RANDOM = "数字范围内随机";
    public static final String ALGO_TYPE_CUTOUT = "截取";
    public static final String ALGO_TYPE_EXCISION = "截除";
    public static final String ALGO_TYPE_SETZERO = "归零";
    public static final String ALGO_TYPE_NUM_FLOAT = "数字浮动";
    public static final String ALGO_TYPE_SETNULL = "置空";
    public static final String ALGO_TYPE_AGEGROUP = "分档";
    public static final String ALGO_TYPE_RANDOM_REPLACE = "随机替换";
    public static final String ALGO_TYPE_ADD_MASK = "地址掩码";
    public static final String ALGO_TYPE_AES_ENCRYPT = "AES加密";
    public static final String ALGO_TYPE_AES_DECRYPT = "AES解密";
    public static final String ALGO_TYPE_DES_ENCRYPT = "DES加密";
    public static final String ALGO_TYPE_DES_DECRYPT = "DES解密";

    // 动态脱敏字段类型
    public static final String FIELD_TYPE_ADDRESS = "地址";
    public static final String FIELD_TYPE_EMAIL = "电子邮箱地址";
    public static final String FIELD_TYPE_MOBILENUMBER = "手机号码";
    public static final String FIELD_TYPE_FIXPHONENUMBER = "固话号码";
    public static final String FIELD_TYPE_DATE = "时间";

    // 动态脱敏改写结果算法类型
    public static final String RESULTALGO_TYPE_MASK = "掩码";
    public static final String RESULTALGO_TYPE_ADD_MASK = "地址掩码";
    public static final String RESULTALGO_TYPE_NUMBERROUND = "数字取整";
    public static final String RESULTALGO_TYPE_CUTOUT = "截断";
    public static final String RESULTALGO_TYPE_REPLACE = "替换";
    public static final String RESULTALGO_TYPE_AESALGORITHM = "AES算法";
    public static final String RESULTALGO_TYPE_TIMERANDOM = "时间随机";

    public AlgoConsts() {
    }
}
