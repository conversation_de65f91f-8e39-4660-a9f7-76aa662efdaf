package com.wzsec.dotask.mask.service.excute.file;

import lombok.Data;

/**
 *@Description:脱敏配置信息
 *<AUTHOR>
 *@date 2020-11-13
 */

@Data
public class MaskingConfigInfo {

	/**
	 * 脱敏位置
	 */
	private int maskingPostion;

	/**
	 * 算法名称
	 */
	private String algoName;

	/**
	 * 算法参数或秘钥
	 */
	private String algoParameter;


	//TODO 上述参数适用于整体配置（原始逻辑），下述参数适用于分段规则配置，在具体脱敏时再将数据拆分后脱敏
	/**
	 * 是否分段
	 *
	 * @return
	 */
	private boolean isSubsection;

	/**
	 * 原始脱敏策略字符串
	 */
	private String oriMaskStrategyStr;

	public MaskingConfigInfo() {

	}

	public MaskingConfigInfo(int maskingPos, String algName, String algPara, boolean isSubsection, String oriMaskStrategyStr) {
		this.maskingPostion = maskingPos;
		this.algoName = algName;
		this.algoParameter = algPara;
		this.isSubsection = isSubsection;
		this.oriMaskStrategyStr = oriMaskStrategyStr;
	}
}