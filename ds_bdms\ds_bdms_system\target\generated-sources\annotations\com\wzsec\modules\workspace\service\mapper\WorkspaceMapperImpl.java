package com.wzsec.modules.workspace.service.mapper;

import com.wzsec.modules.workspace.domain.Workspace;
import com.wzsec.modules.workspace.service.dto.WorkspaceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T12:21:20+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class WorkspaceMapperImpl implements WorkspaceMapper {

    @Override
    public Workspace toEntity(WorkspaceDto dto) {
        if ( dto == null ) {
            return null;
        }

        Workspace workspace = new Workspace();

        workspace.setId( dto.getId() );
        workspace.setWorkspacename( dto.getWorkspacename() );
        workspace.setWorkspaceintroduce( dto.getWorkspaceintroduce() );
        workspace.setWorkspacescope( dto.getWorkspacescope() );
        workspace.setOperationlists( dto.getOperationlists() );
        workspace.setBelongusers( dto.getBelongusers() );
        workspace.setState( dto.getState() );
        workspace.setNote( dto.getNote() );
        workspace.setCreateuser( dto.getCreateuser() );
        workspace.setCreatetime( dto.getCreatetime() );
        workspace.setUpdateuser( dto.getUpdateuser() );
        workspace.setUpdatetime( dto.getUpdatetime() );
        workspace.setSparefield1( dto.getSparefield1() );
        workspace.setSparefield2( dto.getSparefield2() );
        workspace.setSparefield3( dto.getSparefield3() );
        workspace.setSparefield4( dto.getSparefield4() );
        workspace.setSparefield5( dto.getSparefield5() );

        return workspace;
    }

    @Override
    public WorkspaceDto toDto(Workspace entity) {
        if ( entity == null ) {
            return null;
        }

        WorkspaceDto workspaceDto = new WorkspaceDto();

        workspaceDto.setId( entity.getId() );
        workspaceDto.setWorkspacename( entity.getWorkspacename() );
        workspaceDto.setWorkspaceintroduce( entity.getWorkspaceintroduce() );
        workspaceDto.setWorkspacescope( entity.getWorkspacescope() );
        workspaceDto.setOperationlists( entity.getOperationlists() );
        workspaceDto.setBelongusers( entity.getBelongusers() );
        workspaceDto.setState( entity.getState() );
        workspaceDto.setNote( entity.getNote() );
        workspaceDto.setCreateuser( entity.getCreateuser() );
        workspaceDto.setCreatetime( entity.getCreatetime() );
        workspaceDto.setUpdateuser( entity.getUpdateuser() );
        workspaceDto.setUpdatetime( entity.getUpdatetime() );
        workspaceDto.setSparefield1( entity.getSparefield1() );
        workspaceDto.setSparefield2( entity.getSparefield2() );
        workspaceDto.setSparefield3( entity.getSparefield3() );
        workspaceDto.setSparefield4( entity.getSparefield4() );
        workspaceDto.setSparefield5( entity.getSparefield5() );

        return workspaceDto;
    }

    @Override
    public List<Workspace> toEntity(List<WorkspaceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Workspace> list = new ArrayList<Workspace>( dtoList.size() );
        for ( WorkspaceDto workspaceDto : dtoList ) {
            list.add( toEntity( workspaceDto ) );
        }

        return list;
    }

    @Override
    public List<WorkspaceDto> toDto(List<Workspace> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<WorkspaceDto> list = new ArrayList<WorkspaceDto>( entityList.size() );
        for ( Workspace workspace : entityList ) {
            list.add( toDto( workspace ) );
        }

        return list;
    }
}
