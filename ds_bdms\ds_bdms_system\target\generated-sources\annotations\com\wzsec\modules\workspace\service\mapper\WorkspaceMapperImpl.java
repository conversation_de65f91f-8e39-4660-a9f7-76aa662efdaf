package com.wzsec.modules.workspace.service.mapper;

import com.wzsec.modules.workspace.domain.Workspace;
import com.wzsec.modules.workspace.service.dto.WorkspaceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:03+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WorkspaceMapperImpl implements WorkspaceMapper {

    @Override
    public WorkspaceDto toDto(Workspace entity) {
        if ( entity == null ) {
            return null;
        }

        WorkspaceDto workspaceDto = new WorkspaceDto();

        workspaceDto.setBelongusers( entity.getBelongusers() );
        workspaceDto.setCreatetime( entity.getCreatetime() );
        workspaceDto.setCreateuser( entity.getCreateuser() );
        workspaceDto.setId( entity.getId() );
        workspaceDto.setNote( entity.getNote() );
        workspaceDto.setOperationlists( entity.getOperationlists() );
        workspaceDto.setSparefield1( entity.getSparefield1() );
        workspaceDto.setSparefield2( entity.getSparefield2() );
        workspaceDto.setSparefield3( entity.getSparefield3() );
        workspaceDto.setSparefield4( entity.getSparefield4() );
        workspaceDto.setSparefield5( entity.getSparefield5() );
        workspaceDto.setState( entity.getState() );
        workspaceDto.setUpdatetime( entity.getUpdatetime() );
        workspaceDto.setUpdateuser( entity.getUpdateuser() );
        workspaceDto.setWorkspaceintroduce( entity.getWorkspaceintroduce() );
        workspaceDto.setWorkspacename( entity.getWorkspacename() );
        workspaceDto.setWorkspacescope( entity.getWorkspacescope() );

        return workspaceDto;
    }

    @Override
    public List<WorkspaceDto> toDto(List<Workspace> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<WorkspaceDto> list = new ArrayList<WorkspaceDto>( entityList.size() );
        for ( Workspace workspace : entityList ) {
            list.add( toDto( workspace ) );
        }

        return list;
    }

    @Override
    public Workspace toEntity(WorkspaceDto dto) {
        if ( dto == null ) {
            return null;
        }

        Workspace workspace = new Workspace();

        workspace.setBelongusers( dto.getBelongusers() );
        workspace.setCreatetime( dto.getCreatetime() );
        workspace.setCreateuser( dto.getCreateuser() );
        workspace.setId( dto.getId() );
        workspace.setNote( dto.getNote() );
        workspace.setOperationlists( dto.getOperationlists() );
        workspace.setSparefield1( dto.getSparefield1() );
        workspace.setSparefield2( dto.getSparefield2() );
        workspace.setSparefield3( dto.getSparefield3() );
        workspace.setSparefield4( dto.getSparefield4() );
        workspace.setSparefield5( dto.getSparefield5() );
        workspace.setState( dto.getState() );
        workspace.setUpdatetime( dto.getUpdatetime() );
        workspace.setUpdateuser( dto.getUpdateuser() );
        workspace.setWorkspaceintroduce( dto.getWorkspaceintroduce() );
        workspace.setWorkspacename( dto.getWorkspacename() );
        workspace.setWorkspacescope( dto.getWorkspacescope() );

        return workspace;
    }

    @Override
    public List<Workspace> toEntity(List<WorkspaceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Workspace> list = new ArrayList<Workspace>( dtoList.size() );
        for ( WorkspaceDto workspaceDto : dtoList ) {
            list.add( toEntity( workspaceDto ) );
        }

        return list;
    }
}
