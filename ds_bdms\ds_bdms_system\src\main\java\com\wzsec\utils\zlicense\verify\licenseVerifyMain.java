package com.wzsec.utils.zlicense.verify;

import com.wzsec.utils.zlicense.verify.VerifyLicense;

public class licenseVerifyMain {
	public static void main(String[] args) {
		VerifyLicense vLicense = new VerifyLicense();
		try {
			vLicense.setParam("src/main/resources/verifyparam.properties");
			//vLicense.setParam("./resources/verifyparam.properties");
			Boolean valid = vLicense.verify();
			if (valid) {
				System.out.println("证书有效");
			} else {
				System.out.println("证书无效");
			}
		} catch (Exception er) {
			er.printStackTrace();
		}
	}
}
