package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskHbasetaskconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-10-14
 */
public interface MaskHbasetaskconfigRepository extends JpaRepository<MaskHbasetaskconfig, Integer>, JpaSpecificationExecutor<MaskHbasetaskconfig> {

    @Query(value = "select MAX(batchnumber) from sdd_mask_hbasetaskconfig where batchnumber like concat('%',?1,'%')", nativeQuery = true)
    String findMAXTaskNameByPrefix(String prefix);


    @Modifying
    @Transactional
    @Query(value = "update sdd_mask_hbasetaskconfig set status = ?1 where id = ?2", nativeQuery = true)
    void updateHBaseTaskConfig(String state, Long id);


    @Modifying
    @Transactional
    @Query(value = "update sdd_mask_hbasetaskresult jobstatus=?1,jobstarttime=?2,jobendtime=?3,jobtotaltime=?4,updatetime=?5,sparefield1=?6,sparefield2=?7 where id=?8", nativeQuery = true)
    void updateHBaseTaskResult(String status, String startTime, String endTime, String totalTime,
                               String updateTime, String datalinestr, String maskdatalinestr, Long resultid);
}
