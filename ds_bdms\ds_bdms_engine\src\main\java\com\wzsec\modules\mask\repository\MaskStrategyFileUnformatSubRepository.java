package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileUnformatSubDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
* <AUTHOR>
* @date 2021-06-21
*/
public interface MaskStrategyFileUnformatSubRepository extends JpaRepository<MaskStrategyFileUnformatSub, Integer>, JpaSpecificationExecutor<MaskStrategyFileUnformatSub> {
    List<MaskStrategyFileUnformatSub> findByStrategyid(Integer id);
}
