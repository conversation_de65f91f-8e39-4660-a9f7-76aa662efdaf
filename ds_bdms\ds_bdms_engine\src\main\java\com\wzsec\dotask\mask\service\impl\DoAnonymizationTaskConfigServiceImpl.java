package com.wzsec.dotask.mask.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.wzsec.dotask.mask.service.DoAnonymizationTaskConfigService;
import com.wzsec.modules.mask.domain.MaskKanonymizationresult;
import com.wzsec.modules.mask.domain.MaskKanonymizationtask;
import com.wzsec.modules.mask.domain.MaskTablestructure;
import com.wzsec.modules.mask.repository.AlgorithmRepository;
import com.wzsec.modules.mask.repository.MaskKanonymizationtaskRepository;
import com.wzsec.modules.mask.repository.MaskTablestructureRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.MaskKanonymizationtaskDto;
import com.wzsec.modules.sdd.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.utils.*;
//import org.deidentifier.arx.*;
//import org.deidentifier.arx.criteria.KMap;
//import org.deidentifier.arx.metric.Metric;
import org.deidentifier.arx.*;
import org.deidentifier.arx.criteria.KMap;
import org.deidentifier.arx.metric.Metric;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.sql.*;
import java.util.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @discription
 * @date 2020/12/3
 */
@Service
public class DoAnonymizationTaskConfigServiceImpl implements DoAnonymizationTaskConfigService {

    private final static Logger log = LoggerFactory.getLogger(DoAnonymizationTaskConfigServiceImpl.class);

    private final MaskKanonymizationtaskService maskKanonymizationtaskService;
    private final MaskKanonymizationresultService maskKanonymizationresultService;
    private final MaskTablestructureRepository maskTablestructureRepository;
    private final MaskKanonymizationtaskRepository maskKanonymizationtaskRepository;
    private final AlgorithmRepository algorithmRepository;
    private final SdkOperationrecordRepository sdkOperationrecordRepository;
    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    public DoAnonymizationTaskConfigServiceImpl(MaskKanonymizationtaskService maskKanonymizationtaskService,
                                                MaskKanonymizationresultService maskKanonymizationresultService,
                                                MaskTablestructureRepository maskTablestructureRepository,
                                                MaskKanonymizationtaskRepository maskKanonymizationtaskRepository,
                                                AlgorithmRepository algorithmRepository,SdkOperationrecordRepository sdkOperationrecordRepository,
                                                SdkApplyconfigRepository sdkApplyconfigRepository) {
        this.maskKanonymizationtaskService = maskKanonymizationtaskService;

        this.maskKanonymizationresultService = maskKanonymizationresultService;
        this.maskTablestructureRepository = maskTablestructureRepository;
        this.maskKanonymizationtaskRepository = maskKanonymizationtaskRepository;
        this.algorithmRepository = algorithmRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }

    /**
     * @Description:执行K匿名化任务
     * <AUTHOR>
     * @date 2019-09-18
     */
    @Async
    @Override
    public void execution(Long id, String submituser) {
        MaskKanonymizationtask kanonymizationTask = maskKanonymizationtaskRepository.findById(Math.toIntExact(id)).orElseGet(MaskKanonymizationtask::new);
        String taskname = kanonymizationTask.getTaskname();
        String dbname = kanonymizationTask.getDbname();
        String tabname = kanonymizationTask.getTabname();
        String kVaule = kanonymizationTask.getSparefield1(); // K值
        String suppressLimit = kanonymizationTask.getSparefield2(); // 抑制度
        String outputDir = kanonymizationTask.getOutputdirectory(); // 输出目录
        String engine = kanonymizationTask.getSparefield4();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String ip = ipPort[0];
        String port = ipPort[1];

        SdkApplyconfig sdkApplyconfig = sdkApplyconfigRepository.findInfoBySrcurl(ip+":"+port);
        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        String strStartTime = StrUtils.getNowTimeString("yyyy-MM-dd HH:mm:ss");

        // 存储匿名化数据
        MaskKanonymizationresult kAnonymizationResult = new MaskKanonymizationresult();
        kAnonymizationResult.setTaskname(taskname);
        kAnonymizationResult.setDbname(dbname);
        kAnonymizationResult.setTabname(tabname);
        kAnonymizationResult.setOutputdirectory(outputDir);
        kAnonymizationResult.setTaskstatus(Const.MASKAUDIT_TASK_EXECUTESTATE_EXECUTING);
        kAnonymizationResult.setStarttime(strStartTime);
        maskKanonymizationresultService.create(kAnonymizationResult);

        boolean isSuccess = true;
        try {
            // 1.获取表数据
            List<String[]> dataList = getNeedAnoDataFromDB(kanonymizationTask);
            // 匿名化结果
            kAnonymizationResult.setTotalline(String.valueOf(dataList.size()));
            kAnonymizationResult.setMaskline(String.valueOf(dataList.size()));

            // 3.从表结构中获取字段、该字段对应的数据进行存储
            List<MaskTablestructure> tableStructureList = maskTablestructureRepository.getTableStructureByName(dbname, tabname);
            LinkedHashMap<String, MaskTablestructure> linkedHashMap = new LinkedHashMap<>(); // 有序存储
            int fieldIndex = 0;
            String[] fieldList = new String[tableStructureList.size()];
            for (MaskTablestructure tableStructure : tableStructureList) {
                linkedHashMap.put(tableStructure.getFieldename(), tableStructure);
                fieldList[fieldIndex] = tableStructure.getFieldename();
                fieldIndex++;
            }

            // 4.执行数据匿名化
            isSuccess = doKAnonymizationDataMask(fieldList, linkedHashMap, dataList, kVaule, suppressLimit,
                    kAnonymizationResult);

        } catch (Exception ex) {
            isSuccess = false;
            String loginfo = "执行匿名化任务失败";
            sdkOperationrecord.setObjectname(kanonymizationTask.getTaskname());
            sdkOperationrecord.setOperation("匿名化脱敏任务执行失败");
            ex.printStackTrace();
        } finally {
            String strEndTime = StrUtils.getNowTimeString("yyyy-MM-dd HH:mm:ss");
            kAnonymizationResult.setEndtime(strEndTime);
            // 5.判断处理结果
            if (isSuccess) {
                //更新任务状态
                kanonymizationTask.setSparefield3(Const.ANONYMIZATION_CARRY_SUCCES);
                maskKanonymizationtaskService.update(kanonymizationTask);
                kAnonymizationResult.setTaskstatus(Const.HDFS_TASK_EXECUTESTATE_EXECUTE_SUCCESS);
                //更新K匿名化结果
                maskKanonymizationresultService.update(kAnonymizationResult);
                System.out.println("匿名化执行成功");
                sdkOperationrecord.setObjectname(kanonymizationTask.getTaskname());
                sdkOperationrecord.setOperation("匿名化脱敏任务执行成功");
            } else {
                //更新任务状态
                maskKanonymizationtaskService.update(kanonymizationTask);
                //kAnonymizationTaskService.
                kAnonymizationResult.setTaskstatus(Const.HDFS_TASK_EXECUTESTATE_EXECUTE_FAIL);
                //更新K匿名化结果
                maskKanonymizationresultService.update(kAnonymizationResult);
                sdkOperationrecord.setObjectname(kanonymizationTask.getTaskname());
                sdkOperationrecord.setOperation("匿名化脱敏任务执行失败");
                System.out.println("匿名化执行失败");
            }
            //插入SDK操作记录
            sdkOperationrecord.setSdkid(sdkApplyconfig.getSdkid());
            sdkOperationrecord.setSdkname(sdkApplyconfig.getSdkname());
            sdkOperationrecord.setVersion(sdkApplyconfig.getVersion());
            sdkOperationrecord.setApplysystemname(sdkApplyconfig.getApplysystemname());
            sdkOperationrecord.setObjecttype(Const.SDK_OPERATION_KANONYMASK);
            sdkOperationrecord.setOperationtime(Timestamp.valueOf(cn.hutool.core.date.DateUtil.now()));
            sdkOperationrecordRepository.save(sdkOperationrecord);
            System.out.println("------------匿名化结束-------------------");
        }
    }


    /**
     * @param kanonymizationTask
     * @return
     * @Desc 根据匿名化任务获取要处理的表数据
     * <AUTHOR>
     */
    public static List<String[]> getNeedAnoDataFromDB(MaskKanonymizationtask kanonymizationTask) {
        List<String[]> tabDataList = null;
        if (kanonymizationTask != null) {
            String dbType = kanonymizationTask.getDatabasename();
            if (dbType.equalsIgnoreCase(Constants.DBTYPE_MYSQL)) {
                // 数据库连接信息
                String url = kanonymizationTask.getUrl();
                String username = kanonymizationTask.getUsername();
                String password = kanonymizationTask.getPass();
                String dbname = kanonymizationTask.getDbname();
                String tabname = kanonymizationTask.getTabname();

                // 连接要K匿名化数据所在的数据库
                StringBuffer stringbuffer = new StringBuffer();
                StringBuffer sbDBURL = stringbuffer.append("jdbc:mysql://").append(url).append(":3306/").append(dbname);
                String strDBURL = new String(sbDBURL);
                System.out.println("K匿名化数据所在的数据库连接：" + strDBURL);

                Connection con = null;
                PreparedStatement preparedStatement = null;
                ResultSet resultSet = null;
                try {
                    con = (Connection) DriverManager.getConnection(strDBURL, username, password);
                    System.out.println("连接数据库成功");
                    // 查数据
                    String sql = "select * from " + tabname + "";
                    preparedStatement = con.prepareStatement(sql);
                    resultSet = preparedStatement.executeQuery();

                    tabDataList = new ArrayList<String[]>(); // 存放数据
                    // 遍历查询结果集
                    while (resultSet.next()) {
                        String[] lineData = new String[resultSet.getMetaData().getColumnCount()]; // 每行多少列数据
                        for (int i = 0; i < lineData.length; i++) {
                            lineData[i] = resultSet.getObject(i + 1).toString();
                        }
                        tabDataList.add(lineData);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    if (resultSet != null) {
                        try {
                            resultSet.close();
                        } catch (SQLException e) {
                            e.printStackTrace();
                        }
                    }
                    if (preparedStatement != null) {
                        try {
                            preparedStatement.close();
                        } catch (SQLException e) {
                            e.printStackTrace();
                        }
                    }
                    if (con != null) {
                        try {
                            con.close();
                        } catch (SQLException e) {
                            e.printStackTrace();
                        }
                    }
                }

                return tabDataList;
            }

        }

        return null;
    }


    /**
     * @param fieldList     字段列表
     * @param linkedHashMap
     * @param dataList      数据列表
     * @param kValue        K值
     * @param supressLimit  抑制度
     * @return
     */
    public boolean doKAnonymizationDataMask(String[] fieldList, LinkedHashMap<String, MaskTablestructure> linkedHashMap,
                                            List<String[]> dataList, String kValue, String supressLimit, MaskKanonymizationresult kAnonymizationResult) {
        // 判断
        if (fieldList != null && dataList != null) {

            Data.DefaultData data = Data.create();
            data.add(fieldList); // 添加表头

            for (String[] lineData : dataList) {
                data.add(lineData); // 添加行数据
            }

            String strShowData = "";
            for (int i = 0; i < 5; i++) { //
                StringBuilder sbBeforeMaskLineData = new StringBuilder();
                String[] dataArr = dataList.get(i);
                for (int j = 0; j < dataArr.length; j++) {
                    if (j != dataArr.length - 1) {
                        sbBeforeMaskLineData.append(dataArr[j] + ",");
                    } else {
                        sbBeforeMaskLineData.append(dataArr[j]);
                    }
                }
                // 暂不截取
                strShowData = strShowData + sbBeforeMaskLineData.toString() + "\n";
            }

            strShowData = strShowData.substring(0, strShowData.lastIndexOf("\n"));
            System.out.println("显示前五条:" + strShowData);

            // Define research subset
//            HashSet<Integer> hashSet = new HashSet<Integer>(Arrays.asList(1, 2, 5, 7, 8));
            DataSubset subset = DataSubset.create(data, new HashSet<Integer>(Arrays.asList(0,1, 2, 3, 4)));

            int index = 0;
            // 按字段遍历
            for (Map.Entry<String, MaskTablestructure> entry : linkedHashMap.entrySet()) {
                if (entry.getValue().getAttribute().equals(Constants.IDENTIFYING_ATTRIBUTE)) { // 标识属性
                    data.getDefinition().setAttributeType(entry.getKey(), AttributeType.IDENTIFYING_ATTRIBUTE);
                    index++;
                    continue;
                } else if (entry.getValue().getAttribute().equals(Constants.QUASI_IDENTIFYING_ATTRIBUTE)) { // 准标识属性
                    data.getDefinition().setAttributeType(entry.getKey(), AttributeType.QUASI_IDENTIFYING_ATTRIBUTE);
                    index++;
                    continue;
                } else if (entry.getValue().getAttribute().equals(Constants.INSENSITIVE_ATTRIBUTE)) { // 非敏感属性
                    data.getDefinition().setAttributeType(entry.getKey(), AttributeType.INSENSITIVE_ATTRIBUTE);
                    index++;
                    continue;
                } else if (entry.getValue().getAttribute().equals(Constants.SENSITIVE_ATTRIBUTE)) { // 敏感属性需要处理
                    List<String> fieldDataList = new ArrayList<>();
                    for (String[] strArr : dataList) {
                        fieldDataList.add(strArr[index]); // 需要取出要K匿名化的列数据
                    }
                    String fieldKMaskAlgoId = entry.getValue().getSparefield1(); // 字段层次脱敏算法id
                    String fieldKMaskName = algorithmRepository.getAlgoENameByAlgoId(fieldKMaskAlgoId); //根据算法id查算法英文名
                    data.getDefinition().setAttributeType(entry.getKey(),
                            MaskAlgFactory.getDataHierarchyMask(fieldKMaskName, fieldDataList));
                    index++;
                    continue;
                } else {
                    return false;
                }
            }

            int k = Integer.parseInt(kValue);
            double supLimit = Double.valueOf(supressLimit);
            // create an instance of the anonymizer
            ARXAnonymizer anonymizer = new ARXAnonymizer();
            ARXConfiguration config = ARXConfiguration.create();
            config.addPrivacyModel(new KMap(k, subset));
            config.setSuppressionLimit(supLimit);
            config.setQualityModel(Metric.createLossMetric());

            // Now Anonymize
            ARXResult result = null;
            try {
                result = anonymizer.anonymize(data, config);
            } catch (IOException ex) {
                ex.printStackTrace();
            }

            // Print input
            System.out.println(" - Input Data:");
            AnonymizationUtils.print(data.getHandle().iterator());

            // Print input
            System.out.println(" - Input Research Subset:");
            AnonymizationUtils.print(data.getHandle().getView().iterator());

            // Print info
            AnonymizationUtils.printResult(result, data);

            // Print Results
            System.out.println(" - Transformed Data:");
            DataHandle output = result.getOutput(false);
            List<String> resultList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(output)){
                AnonymizationUtils.print(output.iterator());
                // 结果输出到
                Iterator<String[]> iterator = output.iterator();
                while (iterator.hasNext()) {
                    String resultLine = Arrays.toString(iterator.next());
                    System.out.println(resultLine);
                    resultList.add(resultLine);
                }
            }
            String strShowMaskData = "";
            for (int j = 1; j < resultList.size(); j++) {
                String strLineData = resultList.get(j).substring(1, resultList.get(j).length() - 1);
                strShowMaskData = strShowMaskData + strLineData + "\n";
            }
            if (StringUtils.isNotEmpty(strShowMaskData)){
                strShowMaskData = strShowMaskData.substring(0, strShowMaskData.lastIndexOf("\n"));
                System.out.println("匿名化后前5行：" + strShowMaskData);
            }

            FileUtils.writeListToFile(resultList, kAnonymizationResult.getOutputdirectory() + File.separator
                    + StrUtils.DateToNewStr(new Date()) + ".txt");

            // Print Subset Results
            if (ObjectUtil.isNotEmpty(output)){
                System.out.println(" - Transformed Research Subset:");
                AnonymizationUtils.print(output.getView().iterator());
            }

            kAnonymizationResult.setBeforemaskdata(strShowData);
            kAnonymizationResult.setAftermaskdata(strShowMaskData);
            kAnonymizationResult.setTaskresultestimate(AnonymizationUtils.KAnoResultInfo(result, data));

            return true;
        }

        return false;
    }
}
