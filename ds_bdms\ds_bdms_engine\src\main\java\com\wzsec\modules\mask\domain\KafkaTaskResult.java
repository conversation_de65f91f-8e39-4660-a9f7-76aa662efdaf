package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2021-02-26
*/
@Entity
@Data
@Table(name="sdd_mask_kafkataskresult")
public class KafkaTaskResult implements Serializable {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 任务号 */
    @Column(name = "taskname")
    private String taskname;

    /** 库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 表名 */
    @Column(name = "tabname")
    private String tabname;

    /** 脱敏行数 */
    @Column(name = "maskline")
    private String maskline;

    /** 策略名称 */
    @Column(name = "strategyname")
    private String strategyname;

    /** 策略详情JSON */
    @Column(name = "strategydetail")
    private String strategydetail;

    /** redis存储key */
    @Column(name = "rediskey")
    private String rediskey;

    /** 任务状态 */
    @Column(name = "taskstatus")
    private String taskstatus;

    /** 脱敏前数据 */
    @Column(name = "beforemaskdata")
    private String beforemaskdata;

    /** 脱敏后数据 */
    @Column(name = "aftermaskdata")
    private String aftermaskdata;

    /** 开始时间 */
    @Column(name = "starttime")
    @CreationTimestamp
    private Timestamp starttime;

    /** 结束时间 */
    @Column(name = "endtime")
    @UpdateTimestamp
    private Timestamp endtime;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(KafkaTaskResult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}