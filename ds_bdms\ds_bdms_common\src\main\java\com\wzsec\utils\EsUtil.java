package com.wzsec.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;

import java.io.IOException;
import java.util.Date;

@Slf4j
public class EsUtil {

    /**
     * 获取ES连接
     *
     * <AUTHOR>
     * @date 2021/04/29
     */
    public static RestHighLevelClient getClient(String srcUrl, String username, String password) {
        try {
            synchronized (EsUtil.class) {
                long t = System.currentTimeMillis();

                /** 用户认证对象 */
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                /** 设置账号密码 */
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
                /** 创建rest client对象 */
                // 集群地址 *************:9200,*************:9200,*************:9200
                String[] ipAndPort = srcUrl.split(",");
                HttpHost httpHosts [] = new HttpHost[ipAndPort.length];
                for (int i = 0; i < ipAndPort.length; i++) {
                    String ip = ipAndPort[i].split(":")[0];
                    int port = Integer.parseInt(ipAndPort[i].split(":")[1]);
                    httpHosts[i] = new HttpHost(ip,port);
                }
                RestClientBuilder builder = RestClient.builder(httpHosts)
                        .setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                            @Override
                            public HttpAsyncClientBuilder customizeHttpClient(
                                    HttpAsyncClientBuilder httpClientBuilder) {
                                return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                            }
                        });
                RestHighLevelClient client = new RestHighLevelClient(builder);
                long t1 = new Date().getTime();
                log.info("获得连接，耗时:" + (t1 - t) + "s");
                return client;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("获取ES出现错误：" + e.getMessage());
            throw e;
        }
    }

    /**
     * 关闭ES连接
     *
     * <AUTHOR>
     * @date 2021/04/29
     */
    public static void close(RestHighLevelClient client) {
        try {
            if (client != null) {
                client.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
