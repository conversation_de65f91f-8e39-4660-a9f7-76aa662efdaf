package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskstrategyIdentifymaskstrategies;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* <AUTHOR>
* @date 2024-10-21
*/
public interface MaskstrategyIdentifymaskstrategiesRepository extends JpaRepository<MaskstrategyIdentifymaskstrategies, Integer>, JpaSpecificationExecutor<MaskstrategyIdentifymaskstrategies> {
    /**
     * 根据策略名称查询策略数量
     * @param strategyname 策略名称
     * @return
     */
    @Query(value = "select count(*) from sdd_maskstrategy_identifymaskstrategies where strategyname = ?1", nativeQuery = true)
    int findStrategyCountByStrategyName(String strategyname);
}