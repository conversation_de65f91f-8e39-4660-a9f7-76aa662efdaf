package com.wzsec.modules.monitor.service.mapper;

import com.wzsec.modules.monitor.domain.Server;
import com.wzsec.modules.monitor.service.dto.ServerDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T13:47:05+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ServerMapperImpl implements ServerMapper {

    @Override
    public ServerDTO toDto(Server entity) {
        if ( entity == null ) {
            return null;
        }

        ServerDTO serverDTO = new ServerDTO();

        serverDTO.setAddress( entity.getAddress() );
        serverDTO.setCpuCore( entity.getCpuCore() );
        if ( entity.getCpuRate() != null ) {
            serverDTO.setCpuRate( entity.getCpuRate().floatValue() );
        }
        if ( entity.getDiskTotal() != null ) {
            serverDTO.setDiskTotal( entity.getDiskTotal().floatValue() );
        }
        if ( entity.getDiskUsed() != null ) {
            serverDTO.setDiskUsed( entity.getDiskUsed().floatValue() );
        }
        serverDTO.setId( entity.getId() );
        if ( entity.getMemTotal() != null ) {
            serverDTO.setMemTotal( entity.getMemTotal().floatValue() );
        }
        if ( entity.getMemUsed() != null ) {
            serverDTO.setMemUsed( entity.getMemUsed().floatValue() );
        }
        serverDTO.setName( entity.getName() );
        serverDTO.setNode( entity.getNode() );
        serverDTO.setNodetime( entity.getNodetime() );
        serverDTO.setPort( entity.getPort() );
        serverDTO.setSort( entity.getSort() );
        serverDTO.setState( entity.getState() );
        if ( entity.getSwapTotal() != null ) {
            serverDTO.setSwapTotal( entity.getSwapTotal().floatValue() );
        }
        if ( entity.getSwapUsed() != null ) {
            serverDTO.setSwapUsed( entity.getSwapUsed().floatValue() );
        }

        return serverDTO;
    }

    @Override
    public List<ServerDTO> toDto(List<Server> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ServerDTO> list = new ArrayList<ServerDTO>( entityList.size() );
        for ( Server server : entityList ) {
            list.add( toDto( server ) );
        }

        return list;
    }

    @Override
    public Server toEntity(ServerDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Server server = new Server();

        server.setAddress( dto.getAddress() );
        server.setCpuCore( dto.getCpuCore() );
        if ( dto.getCpuRate() != null ) {
            server.setCpuRate( dto.getCpuRate().doubleValue() );
        }
        if ( dto.getDiskTotal() != null ) {
            server.setDiskTotal( dto.getDiskTotal().doubleValue() );
        }
        if ( dto.getDiskUsed() != null ) {
            server.setDiskUsed( dto.getDiskUsed().doubleValue() );
        }
        server.setId( dto.getId() );
        if ( dto.getMemTotal() != null ) {
            server.setMemTotal( dto.getMemTotal().doubleValue() );
        }
        if ( dto.getMemUsed() != null ) {
            server.setMemUsed( dto.getMemUsed().doubleValue() );
        }
        server.setName( dto.getName() );
        server.setNode( dto.getNode() );
        server.setNodetime( dto.getNodetime() );
        server.setPort( dto.getPort() );
        server.setSort( dto.getSort() );
        server.setState( dto.getState() );
        if ( dto.getSwapTotal() != null ) {
            server.setSwapTotal( dto.getSwapTotal().doubleValue() );
        }
        if ( dto.getSwapUsed() != null ) {
            server.setSwapUsed( dto.getSwapUsed().doubleValue() );
        }

        return server;
    }

    @Override
    public List<Server> toEntity(List<ServerDTO> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Server> list = new ArrayList<Server>( dtoList.size() );
        for ( ServerDTO serverDTO : dtoList ) {
            list.add( toEntity( serverDTO ) );
        }

        return list;
    }
}
