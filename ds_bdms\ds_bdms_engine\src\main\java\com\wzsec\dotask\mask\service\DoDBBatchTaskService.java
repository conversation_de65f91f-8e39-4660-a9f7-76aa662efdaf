package com.wzsec.dotask.mask.service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-11-12
 */
public interface DoDBBatchTaskService {

    /**
     * 执行
     *
     * @param id         任务id
     * @param submituser 提交人
     */
    void execution(Integer id, String submituser);


    /**
     * 操作指令
     *
     * @param id  id
     * @param cid cid
     */
    void operationInstructions(Integer id, Integer cid);

    /**
     * 获取脱敏结果预览
     * @param id
     * @return
     */
    List<Object> getMaskResultPreview(Integer id);
}
