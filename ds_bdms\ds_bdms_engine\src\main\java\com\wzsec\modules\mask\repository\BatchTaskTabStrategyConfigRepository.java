package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.BatchTaskTabConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @date 2023-02-07
*/
@Transactional
public interface BatchTaskTabStrategyConfigRepository extends JpaRepository<BatchTaskTabConfig, Long>, JpaSpecificationExecutor<BatchTaskTabConfig> {
    @Query(value = "select * from sdd_mask_batchtasktabconfig where batchtaskid = ?1", nativeQuery = true)
    List<BatchTaskTabConfig> getDbBatchTaskTabStrategyInfo(Integer batchTaskId);

    @Modifying
    @Query(value = "delete from sdd_mask_batchtasktabconfig where batchtaskid = ?1", nativeQuery = true)
    void deleteByBatchTaskId(Integer batchTaskId);
}