package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.DbBatchTaskConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-02-07
 */
public interface DbBatchTaskConfigRepository extends JpaRepository<DbBatchTaskConfig, Integer>, JpaSpecificationExecutor<DbBatchTaskConfig> {
    @Query(value = "select MAX(`taskname`) from sdd_mask_dbbatchtaskconfig where taskname like concat('%',?1,'%')", nativeQuery = true)
    String findMAXTaskNameByPrefix(String prefix);

    @Modifying
    @Transactional
    @Query(value = "update sdd_mask_dbbatchtaskconfig set executionstate = ?1 where id = ?2", nativeQuery = true)
    void alterTaskExecutionState(String state, Integer id);

    @Query(value = "select a.strategyid,t.tablename,t.sourceid from sdd_mask_batchtasktabconfig a " +
            "left join sdd_mask_dbbatchtaskconfig b on b.id = a. batchtaskid " +
            "left join sdd_meta_table t on t.id = a.tableid where b.taskname = ?1", nativeQuery = true)
    List<Map<String, Object>> findByTaskName(String taskName);


    @Modifying
    @Transactional
    @Query(value = "update sdd_mask_dbbatchtaskconfig set sparefield5 = ?1 where id = ?2", nativeQuery = true)
    void alterTaskExecutionAnticipate(String anticipate, Integer id);
}
