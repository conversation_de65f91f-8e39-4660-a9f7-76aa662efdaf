package com.wzsec.modules.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.mask.domain.MaskKanonymizationresult;
import com.wzsec.modules.mask.service.MaskKanonymizationresultService;
import com.wzsec.modules.mask.service.dto.MaskKanonymizationresultQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2024-10-14
*/
// @Api(tags = "匿名化结果展示管理")
@RestController
@RequestMapping("/api/maskKanonymizationresult")
public class MaskKanonymizationresultController {

    private final MaskKanonymizationresultService maskKanonymizationresultService;

    public MaskKanonymizationresultController(MaskKanonymizationresultService maskKanonymizationresultService) {
        this.maskKanonymizationresultService = maskKanonymizationresultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('maskKanonymizationresult:list')")
    public void download(HttpServletResponse response, MaskKanonymizationresultQueryCriteria criteria) throws IOException {
        maskKanonymizationresultService.download(maskKanonymizationresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询匿名化结果展示")
    // @ApiOperation("查询匿名化结果展示")
    @PreAuthorize("@el.check('maskKanonymizationresult:list')")
    public ResponseEntity<Object> getMaskKanonymizationresults(MaskKanonymizationresultQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(maskKanonymizationresultService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增匿名化结果展示")
    // @ApiOperation("新增匿名化结果展示")
    @PreAuthorize("@el.check('maskKanonymizationresult:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody MaskKanonymizationresult resources){
        return new ResponseEntity<>(maskKanonymizationresultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改匿名化结果展示")
    // @ApiOperation("修改匿名化结果展示")
    @PreAuthorize("@el.check('maskKanonymizationresult:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody MaskKanonymizationresult resources){
        maskKanonymizationresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除匿名化结果展示")
    // @ApiOperation("删除匿名化结果展示")
    @PreAuthorize("@el.check('maskKanonymizationresult:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        maskKanonymizationresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
