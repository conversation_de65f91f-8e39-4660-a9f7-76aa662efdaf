package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
* <AUTHOR>
* @date 2022-06-13
*/
@Entity
@Data
@Table(name="sdd_engine_server")
public class EngineServer implements Serializable {

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 执行引擎名称 */
    @Column(name = "name")
    private String name;

    /** IP */
    @Column(name = "ip")
    //@NotBlank
    private String ip;

    /** 端口 */
    @Column(name = "port")
    //@NotBlank
    private String port;

    /** 用途 */
    @Column(name = "application")
    //@NotBlank
    private String application;

    /** 工作状态 */
    @Column(name = "state")
    private String state;

    /** 是否可用 */
    @Column(name = "isvalid")
    private String isvalid;

    /** 备注 */
    @Column(name = "note")
    private String note;

    /** 创建用户名 */
    @Column(name = "createuser")
    private String createuser;

    /** 创建时间 */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /** 更新用户名 */
    @Column(name = "updateuser")
    private String updateuser;

    /** 更新时间 */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;



    public void copy(EngineServer source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}