package com.wzsec.dotask.mask.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.dotask.mask.service.DoHiveTaskService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.modules.system.service.dto.UserDto;
import com.wzsec.utils.Const;
import com.wzsec.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2021-01-26
 */
// @Api(tags = "Hive脱敏任务管理")
@RestController
@RequestMapping("/engine/hive/task")
public class HiveTaskController {

    private final DoHiveTaskService doHiveTaskService;

    private final UserService userService;

    public HiveTaskController(DoHiveTaskService doHiveTaskService, UserService userService) {
        this.doHiveTaskService = doHiveTaskService;
        this.userService = userService;
    }

    @Log("执行Hive脱敏脱敏任务")
    // @ApiOperation("执行Hive脱敏任务")
    @PostMapping(value = "/exec/{id}")
    @PreAuthorize("@el.check('hiveTaskConfig:edit')")
    public ResponseEntity<Object> execution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        UserDto byName = userService.findByName(SecurityUtils.getUsername());
        doHiveTaskService.execution(id, byName);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("定时执行Hive脱敏任务")
    // @ApiOperation("定时执行Hive脱敏任务")
    @PostMapping(value = "/timingexec/{id}")
    //@PreAuthorize("@el.check('hiveTaskConfig:edit')")
    public ResponseEntity<Object> timingexecution(@PathVariable Integer id) {
        System.out.println("开始执行：" + id);
        doHiveTaskService.execution(id, null);//异步执行此方法，立刻返回数据
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
