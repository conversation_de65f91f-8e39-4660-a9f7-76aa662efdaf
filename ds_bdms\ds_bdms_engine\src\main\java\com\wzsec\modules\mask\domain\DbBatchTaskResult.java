package com.wzsec.modules.mask.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

import javax.persistence.*;
//import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
* <AUTHOR>
* @date 2023-02-07
*/
@Entity
@Data
@Table(name="sdd_mask_dbbatchtaskresult")
public class DbBatchTaskResult implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /** 任务名 */
    @Column(name = "taskname")
    private String taskname;

    /** 数据源表名 */
    @Column(name = "tabname")
    private String tabname;

    /** 数据源库名 */
    @Column(name = "dbname")
    private String dbname;

    /** 数据源主机IP地址 */
    @Column(name = "ipaddress")
    private String ipaddress;

    /** 目标源表名或文件名 */
    @Column(name = "outputname")
    private String outputname;

    /** 目标源库名或目录 */
    @Column(name = "outputpath")
    private String outputpath;

    /** 目标源主机IP地址 */
    @Column(name = "outputipaddress")
    private String outputipaddress;

    /** 输出类型（1库表，2文件） */
    @Column(name = "outputtype")
    private String outputtype;

    /** 数据总行数 */
    @Column(name = "totallines")
    private Integer totallines;

    /** 本次敏行数 */
    @Column(name = "masklines")
    private Integer masklines;

    /** 上次脱敏行数 */
    @Column(name = "lastlines")
    private Integer lastmasklines;

    /** 脱敏前数据（前5条） */
    @Column(name = "beforemaskdata")
    private String beforemaskdata;

    /** 脱敏后数据（前5条） */
    @Column(name = "aftermaskdata")
    private String aftermaskdata;

    /** 本次任务使用的策略JSON串 */
    @Column(name = "strategyjson")
    private String strategyjson;

    /** 状态（1：执行中,2：成功,3:失败） */
    @Column(name = "taskstatus")
    //@NotBlank
    private String taskstatus;

    /** 作业提交人姓名 */
    @Column(name = "username")
    private String username;

    /** 起始时间 */
    @Column(name = "jobstarttime")
    private String jobstarttime;

    /** 结束时间 */
    @Column(name = "jobendtime")
    private String jobendtime;

    /** 用时 */
    @Column(name = "jobtotaltime")
    private String jobtotaltime;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 是否删除(0正常，1删除) */
    @Column(name = "isremove")
    private String isremove;

    /** 备用字段1 */
    @Column(name = "sparefield1")
    private String sparefield1;

    /** 备用字段2 */
    @Column(name = "sparefield2")
    private String sparefield2;

    /** 备用字段3 */
    @Column(name = "sparefield3")
    private String sparefield3;

    /** 备用字段4 */
    @Column(name = "sparefield4")
    private String sparefield4;

    /** 备用字段5 */
    @Column(name = "sparefield5")
    private String sparefield5;

    public void copy(DbBatchTaskResult source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}