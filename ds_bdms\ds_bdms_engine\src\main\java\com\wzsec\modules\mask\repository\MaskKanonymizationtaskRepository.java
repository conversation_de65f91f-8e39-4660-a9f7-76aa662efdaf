package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskKanonymizationtask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* <AUTHOR>
* @date 2024-10-14
*/
public interface MaskKanonymizationtaskRepository extends JpaRepository<MaskKanonymizationtask, Integer>, JpaSpecificationExecutor<MaskKanonymizationtask> {

    @Query(value = "select MAX(taskname) from sdd_mask_kanonymizationtask where taskname like concat('%',?1,'%')", nativeQuery = true)
    String findMAXTaskNameByPrefix(String prefix);

}
