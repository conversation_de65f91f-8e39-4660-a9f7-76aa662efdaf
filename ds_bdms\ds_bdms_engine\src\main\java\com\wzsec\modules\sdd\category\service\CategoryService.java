package com.wzsec.modules.sdd.category.service;

import com.wzsec.modules.sdd.category.domain.Category;
import com.wzsec.modules.sdd.category.service.dto.CategoryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-04
 */
public interface CategoryService {


    /**
     * 查询所有数据不分页
     *
     * @return List<CategoryDto>
     */
    List<CategoryDto> queryAll();

    /**
     * 根据pid查询
     *
     * @param pid /
     * @return /
     */
    List<Category> findByPid(Long pid);

    /**
     * 根据type查询
     *
     * @param type /
     * @return /
     */
    List<Category> findByType(String type);

    /**
     * 根据type获取父类别
     *
     * @param type /
     * @return /
     */
    Object getParentCategorysByType(String type);

    /**
     * 根据type获取类别Json
     *
     * @param type /
     * @return /
     */
    Object getCategorysJsonByType(String type);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return CategoryDto
     */
    CategoryDto findById(Long id);

    /**
     * 创建
     *
     * @param resources /
     * @return CategoryDto
     */
    CategoryDto create(Category resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(Category resources);

}